using System;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.JettyApproval.Approvals.ApprovalDelegations;

/// <summary>
/// Application service interface for ApprovalDelegation entity
/// </summary>
public interface IApprovalDelegationAppService :
    ICrudAppService<
        ApprovalDelegationDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateApprovalDelegationDto,
        CreateUpdateApprovalDelegationDto>
{
}