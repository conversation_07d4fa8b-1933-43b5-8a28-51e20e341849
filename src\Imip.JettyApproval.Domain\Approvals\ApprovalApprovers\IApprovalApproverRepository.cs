using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace Imip.JettyApproval.Approvals.ApprovalApprovers;

/// <summary>
/// Repository interface for ApprovalApprover entity
/// </summary>
public interface IApprovalApproverRepository : IRepository<ApprovalApprover, Guid>
{
    /// <summary>
    /// Gets approvers by approval template ID
    /// </summary>
    Task<List<ApprovalApprover>> GetByApprovalIdAsync(Guid approvalId);

    /// <summary>
    /// Gets approvers by approver ID
    /// </summary>
    Task<List<ApprovalApprover>> GetByApproverIdAsync(Guid approverId);

    /// <summary>
    /// Gets approvers by status
    /// </summary>
    Task<List<ApprovalApprover>> GetByStatusAsync(string status);

    /// <summary>
    /// Gets approvers ordered by sequence
    /// </summary>
    Task<List<ApprovalApprover>> GetOrderedBySequenceAsync(Guid approvalId);
}