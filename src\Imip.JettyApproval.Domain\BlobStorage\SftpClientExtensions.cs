using System.IO;
using System.Threading.Tasks;
using Renci.SshNet;

namespace Imip.JettyApproval.BlobStorage;

public static class SftpClientExtensions
{
    /// <summary>
    /// Asynchronously checks if a file or directory exists on the SFTP server.
    /// </summary>
    /// <param name="client">The SFTP client.</param>
    /// <param name="path">The path to check.</param>
    /// <returns>True if the file or directory exists, false otherwise.</returns>
    public static Task<bool> ExistsAsync(this SftpClient client, string path)
    {
        return Task.Run(() => client.Exists(path));
    }

    /// <summary>
    /// Asynchronously opens a file for reading on the SFTP server.
    /// </summary>
    /// <param name="client">The SFTP client.</param>
    /// <param name="path">The path to the file.</param>
    /// <returns>A stream for reading the file.</returns>
    public static Task<Stream> OpenReadAsync(this SftpClient client, string path)
    {
        return Task.Run(() => (Stream)client.OpenRead(path));
    }

    /// <summary>
    /// Asynchronously opens a file for writing on the SFTP server.
    /// </summary>
    /// <param name="client">The SFTP client.</param>
    /// <param name="path">The path to the file.</param>
    /// <returns>A stream for writing to the file.</returns>
    public static Task<Stream> OpenWriteAsync(this SftpClient client, string path)
    {
        return Task.Run(() => (Stream)client.OpenWrite(path));
    }

    /// <summary>
    /// Asynchronously deletes a file on the SFTP server.
    /// </summary>
    /// <param name="client">The SFTP client.</param>
    /// <param name="path">The path to the file.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    public static Task DeleteFileAsync(this SftpClient client, string path)
    {
        return Task.Run(() => client.DeleteFile(path));
    }
}