import{r as o,j as t}from"./vendor-CrSBzUoz.js";import{A as u}from"./app-layout-CNB1Wtrx.js";import{C as h,c as g}from"./card-BAJCNJxm.js";import{D as P}from"./DataTable-BL4SJdnU.js";import"./radix-DaY-mnHi.js";import"./App-CGHLK9xH.js";import"./table-CAbNlII1.js";import"./popover-7NwOVASC.js";import"./checkbox-CayrCcBd.js";import"./tiny-invariant-CopsF_GD.js";import"./search-YAT3sv3T.js";import"./index-CZZWNLgr.js";const c=[{id:"1",docnum:"APP-001",vesselName:"MV. Sea Princess",applicationDate:"2024-03-01",status:"Pending"},{id:"2",docnum:"APP-002",vesselName:"MV. Ocean Queen",applicationDate:"2024-03-05",status:"Approved"},{id:"3",docnum:"APP-003",vesselName:"MV. River King",applicationDate:"2024-03-10",status:"Rejected"},{id:"4",docnum:"APP-004",vesselName:"MV. Star Light",applicationDate:"2024-03-15",status:"Draft"},{id:"5",docnum:"APP-005",vesselName:"MV. Blue Sky",applicationDate:"2024-03-20",status:"Pending"}],f=r=>r.map(e=>({id:parseInt(e.id),header:e.docnum,owner:e.docnum,type:e.vesselName,status:e.status,target:e.applicationDate,limit:"N/A",reviewer:""})),S=[{accessorKey:"header",header:"Document Number"},{accessorKey:"type",header:"Vessel Name"},{accessorKey:"status",header:"Status"},{accessorKey:"target",header:"Application Date"},{accessorKey:"limit",header:"Limit"},{accessorKey:"reviewer",header:"Reviewer"}],b=()=>{const[r,e]=o.useState([]),[s,l]=o.useState(""),[d,n]=o.useState({pageIndex:0,pageSize:10});o.useEffect(()=>{const a=c.filter(i=>i.docnum.toLowerCase().includes(s.toLowerCase())||i.vesselName.toLowerCase().includes(s.toLowerCase()));e(f(a))},[s]);const m=a=>{l(a),n(i=>({...i,pageIndex:0}))},p=a=>{n(a)};return t.jsx(u,{children:t.jsx("div",{className:"flex flex-col space-y-4 p-4",children:t.jsx(h,{children:t.jsx(g,{children:t.jsx(P,{title:"Jetty Application List",columns:S,data:r,totalCount:c.length,isLoading:!1,manualPagination:!1,pageSize:d.pageSize,onPaginationChange:p,onSearch:m,searchValue:s,hideDefaultFilterbar:!0,enableRowSelection:!1})})})})})};export{b as default};
//# sourceMappingURL=page-CVV5sbnq.js.map
