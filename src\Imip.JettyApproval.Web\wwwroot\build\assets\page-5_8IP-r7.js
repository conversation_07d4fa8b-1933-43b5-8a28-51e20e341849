import{r as p,j as o,b as Me}from"./vendor-CrSBzUoz.js";import{u as Bn,C as Vn,a as zn,b as Gn,c as qe,D as qn,d as Xn,P as Jn,T as Un,M as Qn}from"./dnd-DoiScGU0.js";import{n as Jt,M as G,P as Ut,R as Ct,L as se,I as _t,B as ne,S as nt,a as at,b as rt,c as ot,d as st,D as Kn,e as Zn,U as ea,f as ta,j as Re,V as Ie,A as na}from"./app-layout-CNB1Wtrx.js";import{B as aa,P as it,b as lt,R as Et,c as dt,M as ra,N as oa}from"./popover-7NwOVASC.js";import{b as Qt,d as sa,e as ia,f as Kt,g as la,P as $e,h as vt,I as da,i as ct,j as ca,k as ua,l as fa}from"./radix-DaY-mnHi.js";import{C as Zt}from"./chevron-left-BtkzUODq.js";import{C as ma}from"./checkbox-CayrCcBd.js";import{D as ha,b as va,c as ga,d as pa,f as ba,e as ya}from"./dialog-DAr_Mtxm.js";import{T as xa}from"./textarea-P1Up2ORZ.js";import{C as wa,a as Da,b as ka,c as Ma}from"./card-BAJCNJxm.js";import"./App-CGHLK9xH.js";/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ja=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],Na=Jt("circle",ja);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sa=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],Ca=Jt("plus",Sa),gt=6048e5,_a=864e5,en=6e4,Ea=36e5,Pt=Symbol.for("constructDateFrom");function V(e,n){return typeof e=="function"?e(n):e&&typeof e=="object"&&Pt in e?e[Pt](n):e instanceof Date?new e.constructor(n):new Date(n)}function _(e,n){return V(n||e,e)}function z(e,n,t){const a=_(e,t?.in);return isNaN(n)?V(t?.in||e,NaN):(n&&a.setDate(a.getDate()+n),a)}function Q(e,n,t){const a=_(e,t?.in);if(isNaN(n))return V(t?.in||e,NaN);if(!n)return a;const r=a.getDate(),s=V(t?.in||e,a.getTime());s.setMonth(a.getMonth()+n+1,0);const i=s.getDate();return r>=i?s:(a.setFullYear(s.getFullYear(),s.getMonth(),r),a)}function Pa(e,n,t){return V(e,+_(e)+n)}function xe(e,n,t){return Pa(e,n*Ea)}let Oa={};function Oe(){return Oa}function X(e,n){const t=Oe(),a=n?.weekStartsOn??n?.locale?.options?.weekStartsOn??t.weekStartsOn??t.locale?.options?.weekStartsOn??0,r=_(e,n?.in),s=r.getDay(),i=(s<a?7:0)+s-a;return r.setDate(r.getDate()-i),r.setHours(0,0,0,0),r}function me(e,n){return X(e,{...n,weekStartsOn:1})}function tn(e,n){const t=_(e,n?.in),a=t.getFullYear(),r=V(t,0);r.setFullYear(a+1,0,4),r.setHours(0,0,0,0);const s=me(r),i=V(t,0);i.setFullYear(a,0,4),i.setHours(0,0,0,0);const l=me(i);return t.getTime()>=s.getTime()?a+1:t.getTime()>=l.getTime()?a:a-1}function Le(e){const n=_(e),t=new Date(Date.UTC(n.getFullYear(),n.getMonth(),n.getDate(),n.getHours(),n.getMinutes(),n.getSeconds(),n.getMilliseconds()));return t.setUTCFullYear(n.getFullYear()),+e-+t}function de(e,...n){const t=V.bind(null,e||n.find(a=>typeof a=="object"));return n.map(t)}function ae(e,n){const t=_(e,n?.in);return t.setHours(0,0,0,0),t}function ee(e,n,t){const[a,r]=de(t?.in,e,n),s=ae(a),i=ae(r),l=+s-Le(s),d=+i-Le(i);return Math.round((l-d)/_a)}function Ta(e,n){const t=tn(e,n),a=V(e,0);return a.setFullYear(t,0,4),a.setHours(0,0,0,0),me(a)}function Wa(e,n,t){const a=_(e,t?.in);return a.setTime(a.getTime()+n*en),a}function Se(e,n,t){return z(e,n*7,t)}function Fa(e,n,t){return Q(e,n*12,t)}function nn(e,n,t){const[a,r]=[+_(e.start,t?.in),+_(e.end,t?.in)].sort((l,d)=>l-d),[s,i]=[+_(n.start,t?.in),+_(n.end,t?.in)].sort((l,d)=>l-d);return a<i&&s<r}function Ra(e,n){let t,a=n?.in;return e.forEach(r=>{!a&&typeof r=="object"&&(a=V.bind(null,r));const s=_(r,a);(!t||t<s||isNaN(+s))&&(t=s)}),V(a,t||NaN)}function Ia(e,n){let t,a=n?.in;return e.forEach(r=>{!a&&typeof r=="object"&&(a=V.bind(null,r));const s=_(r,a);(!t||t>s||isNaN(+s))&&(t=s)}),V(a,t||NaN)}function Ya(e){return V(e,Date.now())}function C(e,n,t){const[a,r]=de(t?.in,e,n);return+ae(a)==+ae(r)}function pt(e){return e instanceof Date||typeof e=="object"&&Object.prototype.toString.call(e)==="[object Date]"}function Ha(e){return!(!pt(e)&&typeof e!="number"||isNaN(+_(e)))}function Ce(e,n,t){const[a,r]=de(t?.in,e,n),s=a.getFullYear()-r.getFullYear(),i=a.getMonth()-r.getMonth();return s*12+i}function La(e,n,t){const[a,r]=de(t?.in,e,n),s=X(a,t),i=X(r,t),l=+s-Le(s),d=+i-Le(i);return Math.round((l-d)/gt)}function Aa(e,n,t){const[a,r]=de(t?.in,e,n),s=Ot(a,r),i=Math.abs(ee(a,r));a.setDate(a.getDate()-s*i);const l=+(Ot(a,r)===-s),d=s*(i-l);return d===0?0:d}function Ot(e,n){const t=e.getFullYear()-n.getFullYear()||e.getMonth()-n.getMonth()||e.getDate()-n.getDate()||e.getHours()-n.getHours()||e.getMinutes()-n.getMinutes()||e.getSeconds()-n.getSeconds()||e.getMilliseconds()-n.getMilliseconds();return t<0?-1:t>0?1:t}function $a(e){return n=>{const a=(e?Math[e]:Math.trunc)(n);return a===0?0:a}}function Ba(e,n){return+_(e)-+_(n)}function De(e,n,t){const a=Ba(e,n)/en;return $a(t?.roundingMethod)(a)}function Be(e,n){const t=_(e,n?.in),a=t.getMonth();return t.setFullYear(t.getFullYear(),a+1,0),t.setHours(23,59,59,999),t}function an(e,n){const[t,a]=de(e,n.start,n.end);return{start:t,end:a}}function rn(e,n){const{start:t,end:a}=an(n?.in,e);let r=+t>+a;const s=r?+t:+a,i=r?a:t;i.setHours(0,0,0,0);let l=1;const d=[];for(;+i<=s;)d.push(V(t,i)),i.setDate(i.getDate()+l),i.setHours(0,0,0,0);return r?d.reverse():d}function on(e,n){const{start:t,end:a}=an(n?.in,e);let r=+t>+a;const s=r?+t:+a,i=r?a:t;i.setMinutes(0,0,0);let l=1;const d=[];for(;+i<=s;)d.push(V(t,i)),i.setHours(i.getHours()+l);return r?d.reverse():d}function J(e,n){const t=_(e,n?.in);return t.setDate(1),t.setHours(0,0,0,0),t}function sn(e,n){const t=_(e,n?.in);return t.setFullYear(t.getFullYear(),0,1),t.setHours(0,0,0,0),t}function ge(e,n){const t=Oe(),a=n?.weekStartsOn??n?.locale?.options?.weekStartsOn??t.weekStartsOn??t.locale?.options?.weekStartsOn??0,r=_(e,n?.in),s=r.getDay(),i=(s<a?-7:0)+6-(s-a);return r.setDate(r.getDate()+i),r.setHours(23,59,59,999),r}function ln(e,n){return ge(e,{...n,weekStartsOn:1})}const Va={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},za=(e,n,t)=>{let a;const r=Va[e];return typeof r=="string"?a=r:n===1?a=r.one:a=r.other.replace("{{count}}",n.toString()),t?.addSuffix?t.comparison&&t.comparison>0?"in "+a:a+" ago":a};function Xe(e){return(n={})=>{const t=n.width?String(n.width):e.defaultWidth;return e.formats[t]||e.formats[e.defaultWidth]}}const Ga={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},qa={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},Xa={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},Ja={date:Xe({formats:Ga,defaultWidth:"full"}),time:Xe({formats:qa,defaultWidth:"full"}),dateTime:Xe({formats:Xa,defaultWidth:"full"})},Ua={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},Qa=(e,n,t,a)=>Ua[e];function je(e){return(n,t)=>{const a=t?.context?String(t.context):"standalone";let r;if(a==="formatting"&&e.formattingValues){const i=e.defaultFormattingWidth||e.defaultWidth,l=t?.width?String(t.width):i;r=e.formattingValues[l]||e.formattingValues[i]}else{const i=e.defaultWidth,l=t?.width?String(t.width):e.defaultWidth;r=e.values[l]||e.values[i]}const s=e.argumentCallback?e.argumentCallback(n):n;return r[s]}}const Ka={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},Za={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},er={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},tr={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},nr={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},ar={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},rr=(e,n)=>{const t=Number(e),a=t%100;if(a>20||a<10)switch(a%10){case 1:return t+"st";case 2:return t+"nd";case 3:return t+"rd"}return t+"th"},or={ordinalNumber:rr,era:je({values:Ka,defaultWidth:"wide"}),quarter:je({values:Za,defaultWidth:"wide",argumentCallback:e=>e-1}),month:je({values:er,defaultWidth:"wide"}),day:je({values:tr,defaultWidth:"wide"}),dayPeriod:je({values:nr,defaultWidth:"wide",formattingValues:ar,defaultFormattingWidth:"wide"})};function Ne(e){return(n,t={})=>{const a=t.width,r=a&&e.matchPatterns[a]||e.matchPatterns[e.defaultMatchWidth],s=n.match(r);if(!s)return null;const i=s[0],l=a&&e.parsePatterns[a]||e.parsePatterns[e.defaultParseWidth],d=Array.isArray(l)?ir(l,f=>f.test(i)):sr(l,f=>f.test(i));let u;u=e.valueCallback?e.valueCallback(d):d,u=t.valueCallback?t.valueCallback(u):u;const m=n.slice(i.length);return{value:u,rest:m}}}function sr(e,n){for(const t in e)if(Object.prototype.hasOwnProperty.call(e,t)&&n(e[t]))return t}function ir(e,n){for(let t=0;t<e.length;t++)if(n(e[t]))return t}function lr(e){return(n,t={})=>{const a=n.match(e.matchPattern);if(!a)return null;const r=a[0],s=n.match(e.parsePattern);if(!s)return null;let i=e.valueCallback?e.valueCallback(s[0]):s[0];i=t.valueCallback?t.valueCallback(i):i;const l=n.slice(r.length);return{value:i,rest:l}}}const dr=/^(\d+)(th|st|nd|rd)?/i,cr=/\d+/i,ur={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},fr={any:[/^b/i,/^(a|c)/i]},mr={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},hr={any:[/1/i,/2/i,/3/i,/4/i]},vr={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},gr={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},pr={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},br={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},yr={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},xr={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},wr={ordinalNumber:lr({matchPattern:dr,parsePattern:cr,valueCallback:e=>parseInt(e,10)}),era:Ne({matchPatterns:ur,defaultMatchWidth:"wide",parsePatterns:fr,defaultParseWidth:"any"}),quarter:Ne({matchPatterns:mr,defaultMatchWidth:"wide",parsePatterns:hr,defaultParseWidth:"any",valueCallback:e=>e+1}),month:Ne({matchPatterns:vr,defaultMatchWidth:"wide",parsePatterns:gr,defaultParseWidth:"any"}),day:Ne({matchPatterns:pr,defaultMatchWidth:"wide",parsePatterns:br,defaultParseWidth:"any"}),dayPeriod:Ne({matchPatterns:yr,defaultMatchWidth:"any",parsePatterns:xr,defaultParseWidth:"any"})},dn={code:"en-US",formatDistance:za,formatLong:Ja,formatRelative:Qa,localize:or,match:wr,options:{weekStartsOn:0,firstWeekContainsDate:1}};function Dr(e,n){const t=_(e,n?.in);return ee(t,sn(t))+1}function cn(e,n){const t=_(e,n?.in),a=+me(t)-+Ta(t);return Math.round(a/gt)+1}function un(e,n){const t=_(e,n?.in),a=t.getFullYear(),r=Oe(),s=n?.firstWeekContainsDate??n?.locale?.options?.firstWeekContainsDate??r.firstWeekContainsDate??r.locale?.options?.firstWeekContainsDate??1,i=V(n?.in||e,0);i.setFullYear(a+1,0,s),i.setHours(0,0,0,0);const l=X(i,n),d=V(n?.in||e,0);d.setFullYear(a,0,s),d.setHours(0,0,0,0);const u=X(d,n);return+t>=+l?a+1:+t>=+u?a:a-1}function kr(e,n){const t=Oe(),a=n?.firstWeekContainsDate??n?.locale?.options?.firstWeekContainsDate??t.firstWeekContainsDate??t.locale?.options?.firstWeekContainsDate??1,r=un(e,n),s=V(n?.in||e,0);return s.setFullYear(r,0,a),s.setHours(0,0,0,0),X(s,n)}function fn(e,n){const t=_(e,n?.in),a=+X(t,n)-+kr(t,n);return Math.round(a/gt)+1}function I(e,n){const t=e<0?"-":"",a=Math.abs(e).toString().padStart(n,"0");return t+a}const ie={y(e,n){const t=e.getFullYear(),a=t>0?t:1-t;return I(n==="yy"?a%100:a,n.length)},M(e,n){const t=e.getMonth();return n==="M"?String(t+1):I(t+1,2)},d(e,n){return I(e.getDate(),n.length)},a(e,n){const t=e.getHours()/12>=1?"pm":"am";switch(n){case"a":case"aa":return t.toUpperCase();case"aaa":return t;case"aaaaa":return t[0];case"aaaa":default:return t==="am"?"a.m.":"p.m."}},h(e,n){return I(e.getHours()%12||12,n.length)},H(e,n){return I(e.getHours(),n.length)},m(e,n){return I(e.getMinutes(),n.length)},s(e,n){return I(e.getSeconds(),n.length)},S(e,n){const t=n.length,a=e.getMilliseconds(),r=Math.trunc(a*Math.pow(10,t-3));return I(r,n.length)}},pe={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},Tt={G:function(e,n,t){const a=e.getFullYear()>0?1:0;switch(n){case"G":case"GG":case"GGG":return t.era(a,{width:"abbreviated"});case"GGGGG":return t.era(a,{width:"narrow"});case"GGGG":default:return t.era(a,{width:"wide"})}},y:function(e,n,t){if(n==="yo"){const a=e.getFullYear(),r=a>0?a:1-a;return t.ordinalNumber(r,{unit:"year"})}return ie.y(e,n)},Y:function(e,n,t,a){const r=un(e,a),s=r>0?r:1-r;if(n==="YY"){const i=s%100;return I(i,2)}return n==="Yo"?t.ordinalNumber(s,{unit:"year"}):I(s,n.length)},R:function(e,n){const t=tn(e);return I(t,n.length)},u:function(e,n){const t=e.getFullYear();return I(t,n.length)},Q:function(e,n,t){const a=Math.ceil((e.getMonth()+1)/3);switch(n){case"Q":return String(a);case"QQ":return I(a,2);case"Qo":return t.ordinalNumber(a,{unit:"quarter"});case"QQQ":return t.quarter(a,{width:"abbreviated",context:"formatting"});case"QQQQQ":return t.quarter(a,{width:"narrow",context:"formatting"});case"QQQQ":default:return t.quarter(a,{width:"wide",context:"formatting"})}},q:function(e,n,t){const a=Math.ceil((e.getMonth()+1)/3);switch(n){case"q":return String(a);case"qq":return I(a,2);case"qo":return t.ordinalNumber(a,{unit:"quarter"});case"qqq":return t.quarter(a,{width:"abbreviated",context:"standalone"});case"qqqqq":return t.quarter(a,{width:"narrow",context:"standalone"});case"qqqq":default:return t.quarter(a,{width:"wide",context:"standalone"})}},M:function(e,n,t){const a=e.getMonth();switch(n){case"M":case"MM":return ie.M(e,n);case"Mo":return t.ordinalNumber(a+1,{unit:"month"});case"MMM":return t.month(a,{width:"abbreviated",context:"formatting"});case"MMMMM":return t.month(a,{width:"narrow",context:"formatting"});case"MMMM":default:return t.month(a,{width:"wide",context:"formatting"})}},L:function(e,n,t){const a=e.getMonth();switch(n){case"L":return String(a+1);case"LL":return I(a+1,2);case"Lo":return t.ordinalNumber(a+1,{unit:"month"});case"LLL":return t.month(a,{width:"abbreviated",context:"standalone"});case"LLLLL":return t.month(a,{width:"narrow",context:"standalone"});case"LLLL":default:return t.month(a,{width:"wide",context:"standalone"})}},w:function(e,n,t,a){const r=fn(e,a);return n==="wo"?t.ordinalNumber(r,{unit:"week"}):I(r,n.length)},I:function(e,n,t){const a=cn(e);return n==="Io"?t.ordinalNumber(a,{unit:"week"}):I(a,n.length)},d:function(e,n,t){return n==="do"?t.ordinalNumber(e.getDate(),{unit:"date"}):ie.d(e,n)},D:function(e,n,t){const a=Dr(e);return n==="Do"?t.ordinalNumber(a,{unit:"dayOfYear"}):I(a,n.length)},E:function(e,n,t){const a=e.getDay();switch(n){case"E":case"EE":case"EEE":return t.day(a,{width:"abbreviated",context:"formatting"});case"EEEEE":return t.day(a,{width:"narrow",context:"formatting"});case"EEEEEE":return t.day(a,{width:"short",context:"formatting"});case"EEEE":default:return t.day(a,{width:"wide",context:"formatting"})}},e:function(e,n,t,a){const r=e.getDay(),s=(r-a.weekStartsOn+8)%7||7;switch(n){case"e":return String(s);case"ee":return I(s,2);case"eo":return t.ordinalNumber(s,{unit:"day"});case"eee":return t.day(r,{width:"abbreviated",context:"formatting"});case"eeeee":return t.day(r,{width:"narrow",context:"formatting"});case"eeeeee":return t.day(r,{width:"short",context:"formatting"});case"eeee":default:return t.day(r,{width:"wide",context:"formatting"})}},c:function(e,n,t,a){const r=e.getDay(),s=(r-a.weekStartsOn+8)%7||7;switch(n){case"c":return String(s);case"cc":return I(s,n.length);case"co":return t.ordinalNumber(s,{unit:"day"});case"ccc":return t.day(r,{width:"abbreviated",context:"standalone"});case"ccccc":return t.day(r,{width:"narrow",context:"standalone"});case"cccccc":return t.day(r,{width:"short",context:"standalone"});case"cccc":default:return t.day(r,{width:"wide",context:"standalone"})}},i:function(e,n,t){const a=e.getDay(),r=a===0?7:a;switch(n){case"i":return String(r);case"ii":return I(r,n.length);case"io":return t.ordinalNumber(r,{unit:"day"});case"iii":return t.day(a,{width:"abbreviated",context:"formatting"});case"iiiii":return t.day(a,{width:"narrow",context:"formatting"});case"iiiiii":return t.day(a,{width:"short",context:"formatting"});case"iiii":default:return t.day(a,{width:"wide",context:"formatting"})}},a:function(e,n,t){const r=e.getHours()/12>=1?"pm":"am";switch(n){case"a":case"aa":return t.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"aaa":return t.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return t.dayPeriod(r,{width:"narrow",context:"formatting"});case"aaaa":default:return t.dayPeriod(r,{width:"wide",context:"formatting"})}},b:function(e,n,t){const a=e.getHours();let r;switch(a===12?r=pe.noon:a===0?r=pe.midnight:r=a/12>=1?"pm":"am",n){case"b":case"bb":return t.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"bbb":return t.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return t.dayPeriod(r,{width:"narrow",context:"formatting"});case"bbbb":default:return t.dayPeriod(r,{width:"wide",context:"formatting"})}},B:function(e,n,t){const a=e.getHours();let r;switch(a>=17?r=pe.evening:a>=12?r=pe.afternoon:a>=4?r=pe.morning:r=pe.night,n){case"B":case"BB":case"BBB":return t.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"BBBBB":return t.dayPeriod(r,{width:"narrow",context:"formatting"});case"BBBB":default:return t.dayPeriod(r,{width:"wide",context:"formatting"})}},h:function(e,n,t){if(n==="ho"){let a=e.getHours()%12;return a===0&&(a=12),t.ordinalNumber(a,{unit:"hour"})}return ie.h(e,n)},H:function(e,n,t){return n==="Ho"?t.ordinalNumber(e.getHours(),{unit:"hour"}):ie.H(e,n)},K:function(e,n,t){const a=e.getHours()%12;return n==="Ko"?t.ordinalNumber(a,{unit:"hour"}):I(a,n.length)},k:function(e,n,t){let a=e.getHours();return a===0&&(a=24),n==="ko"?t.ordinalNumber(a,{unit:"hour"}):I(a,n.length)},m:function(e,n,t){return n==="mo"?t.ordinalNumber(e.getMinutes(),{unit:"minute"}):ie.m(e,n)},s:function(e,n,t){return n==="so"?t.ordinalNumber(e.getSeconds(),{unit:"second"}):ie.s(e,n)},S:function(e,n){return ie.S(e,n)},X:function(e,n,t){const a=e.getTimezoneOffset();if(a===0)return"Z";switch(n){case"X":return Ft(a);case"XXXX":case"XX":return fe(a);case"XXXXX":case"XXX":default:return fe(a,":")}},x:function(e,n,t){const a=e.getTimezoneOffset();switch(n){case"x":return Ft(a);case"xxxx":case"xx":return fe(a);case"xxxxx":case"xxx":default:return fe(a,":")}},O:function(e,n,t){const a=e.getTimezoneOffset();switch(n){case"O":case"OO":case"OOO":return"GMT"+Wt(a,":");case"OOOO":default:return"GMT"+fe(a,":")}},z:function(e,n,t){const a=e.getTimezoneOffset();switch(n){case"z":case"zz":case"zzz":return"GMT"+Wt(a,":");case"zzzz":default:return"GMT"+fe(a,":")}},t:function(e,n,t){const a=Math.trunc(+e/1e3);return I(a,n.length)},T:function(e,n,t){return I(+e,n.length)}};function Wt(e,n=""){const t=e>0?"-":"+",a=Math.abs(e),r=Math.trunc(a/60),s=a%60;return s===0?t+String(r):t+String(r)+n+I(s,2)}function Ft(e,n){return e%60===0?(e>0?"-":"+")+I(Math.abs(e)/60,2):fe(e,n)}function fe(e,n=""){const t=e>0?"-":"+",a=Math.abs(e),r=I(Math.trunc(a/60),2),s=I(a%60,2);return t+r+n+s}const Rt=(e,n)=>{switch(e){case"P":return n.date({width:"short"});case"PP":return n.date({width:"medium"});case"PPP":return n.date({width:"long"});case"PPPP":default:return n.date({width:"full"})}},mn=(e,n)=>{switch(e){case"p":return n.time({width:"short"});case"pp":return n.time({width:"medium"});case"ppp":return n.time({width:"long"});case"pppp":default:return n.time({width:"full"})}},Mr=(e,n)=>{const t=e.match(/(P+)(p+)?/)||[],a=t[1],r=t[2];if(!r)return Rt(e,n);let s;switch(a){case"P":s=n.dateTime({width:"short"});break;case"PP":s=n.dateTime({width:"medium"});break;case"PPP":s=n.dateTime({width:"long"});break;case"PPPP":default:s=n.dateTime({width:"full"});break}return s.replace("{{date}}",Rt(a,n)).replace("{{time}}",mn(r,n))},jr={p:mn,P:Mr},Nr=/^D+$/,Sr=/^Y+$/,Cr=["D","DD","YY","YYYY"];function _r(e){return Nr.test(e)}function Er(e){return Sr.test(e)}function Pr(e,n,t){const a=Or(e,n,t);if(Cr.includes(e))throw new RangeError(a)}function Or(e,n,t){const a=e[0]==="Y"?"years":"days of the month";return`Use \`${e.toLowerCase()}\` instead of \`${e}\` (in \`${n}\`) for formatting ${a} to the input \`${t}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}const Tr=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,Wr=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,Fr=/^'([^]*?)'?$/,Rr=/''/g,Ir=/[a-zA-Z]/;function O(e,n,t){const a=Oe(),r=t?.locale??a.locale??dn,s=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??a.firstWeekContainsDate??a.locale?.options?.firstWeekContainsDate??1,i=t?.weekStartsOn??t?.locale?.options?.weekStartsOn??a.weekStartsOn??a.locale?.options?.weekStartsOn??0,l=_(e,t?.in);if(!Ha(l))throw new RangeError("Invalid time value");let d=n.match(Wr).map(m=>{const f=m[0];if(f==="p"||f==="P"){const v=jr[f];return v(m,r.formatLong)}return m}).join("").match(Tr).map(m=>{if(m==="''")return{isToken:!1,value:"'"};const f=m[0];if(f==="'")return{isToken:!1,value:Yr(m)};if(Tt[f])return{isToken:!0,value:m};if(f.match(Ir))throw new RangeError("Format string contains an unescaped latin alphabet character `"+f+"`");return{isToken:!1,value:m}});r.localize.preprocessor&&(d=r.localize.preprocessor(l,d));const u={firstWeekContainsDate:s,weekStartsOn:i,locale:r};return d.map(m=>{if(!m.isToken)return m.value;const f=m.value;(!t?.useAdditionalWeekYearTokens&&Er(f)||!t?.useAdditionalDayOfYearTokens&&_r(f))&&Pr(f,n,String(e));const v=Tt[f[0]];return v(l,f,r.localize,u)}).join("")}function Yr(e){const n=e.match(Fr);return n?n[1].replace(Rr,"'"):e}function Hr(e,n){const t=_(e,n?.in),a=t.getFullYear(),r=t.getMonth(),s=V(t,0);return s.setFullYear(a,r+1,0),s.setHours(0,0,0,0),s.getDate()}function we(e,n){return _(e,n?.in).getHours()}function _e(e,n){return _(e,n?.in).getMinutes()}function Lr(e){return Math.trunc(+_(e)/1e3)}function Ar(e,n){const t=_(e,n?.in),a=t.getMonth();return t.setFullYear(t.getFullYear(),a+1,0),t.setHours(0,0,0,0),_(t,n?.in)}function $r(e,n){const t=_(e,n?.in);return La(Ar(t,n),J(t,n),n)+1}function ut(e,n){return+_(e)>+_(n)}function Ee(e,n){return+_(e)<+_(n)}function hn(e){return+_(e)<Date.now()}function ke(e,n,t){const[a,r]=de(t?.in,e,n);return a.getFullYear()===r.getFullYear()&&a.getMonth()===r.getMonth()}function Br(e,n,t){const[a,r]=de(t?.in,e,n);return a.getFullYear()===r.getFullYear()}function be(e,n){return C(V(e,e),Ya(e))}function Vr(e,n,t){const a=+_(e,t?.in),[r,s]=[+_(n.start,t?.in),+_(n.end,t?.in)].sort((i,l)=>i-l);return a>=r&&a<=s}function Je(e,n,t){return z(e,-n,t)}function Ue(e,n,t){const a=_(e,t?.in),r=a.getFullYear(),s=a.getDate(),i=V(e,0);i.setFullYear(r,n,15),i.setHours(0,0,0,0);const l=Hr(i);return a.setMonth(n,Math.min(s,l)),a}function It(e,n,t){const a=_(e,t?.in);return isNaN(+a)?V(e,NaN):(a.setFullYear(n),a)}function zr(e,n,t){return Q(e,-1,t)}function Gr(e,n,t){return Se(e,-1,t)}function qr({currentDate:e,events:n,onEventSelect:t}){const a=p.useMemo(()=>Array.from({length:ht},(i,l)=>z(new Date(e),l)),[e]),r=(i,l)=>{l.stopPropagation(),t(i)},s=a.some(i=>Xt(n,i).length>0);return o.jsx("div",{className:"border-border/70 border-t px-4",children:s?a.map(i=>{const l=Xt(n,i);return l.length===0?null:o.jsxs("div",{className:"border-border/70 relative my-12 border-t",children:[o.jsx("span",{className:"bg-background absolute -top-3 left-0 flex h-6 items-center pe-4 text-[10px] uppercase data-today:font-medium sm:pe-4 sm:text-xs","data-today":be(i)||void 0,children:O(i,"d MMM, EEEE")}),o.jsx("div",{className:"mt-6 space-y-2",children:l.map(d=>o.jsx(he,{event:d,view:"agenda",onClick:u=>r(d,u)},d.id))})]},i.toString())}):o.jsxs("div",{className:"flex min-h-[70svh] flex-col items-center justify-center py-16 text-center",children:[o.jsx(aa,{size:32,className:"text-muted-foreground/50 mb-2"}),o.jsx("h3",{className:"text-lg font-medium",children:"No events found"}),o.jsx("p",{className:"text-muted-foreground",children:"There are no events scheduled for this time period."})]})})}const le=0,ye=24,Yt=9,Ht=10;function Xr({currentDate:e,events:n,onEventSelect:t,onEventCreate:a}){const r=p.useMemo(()=>{const c=ae(e);return on({start:xe(c,le),end:xe(c,ye-1)})},[e]),s=p.useMemo(()=>n.filter(c=>{const h=new Date(c.start),b=new Date(c.end);return C(e,h)||C(e,b)||e>h&&e<b}).sort((c,h)=>new Date(c.start).getTime()-new Date(h.start).getTime()),[e,n]),i=p.useMemo(()=>s.filter(c=>c.allDay||ve(c)),[s]),l=p.useMemo(()=>s.filter(c=>!c.allDay&&!ve(c)),[s]),d=p.useMemo(()=>{const c=[],h=ae(e),b=[...l].sort((y,M)=>{const T=new Date(y.start),E=new Date(M.start),R=new Date(y.end),W=new Date(M.end);if(T<E)return-1;if(T>E)return 1;const x=De(R,T);return De(W,E)-x}),g=[];return b.forEach(y=>{const M=new Date(y.start),T=new Date(y.end),E=C(e,M)?M:h,R=C(e,T)?T:xe(h,24),W=we(E)+_e(E)/60,x=we(R)+_e(R)/60,N=(W-le)*Pe,P=(x-W)*Pe;let k=0,Y=!1;for(;!Y;){const L=g[k]||[];L.length===0?(g[k]=L,Y=!0):L.some(w=>nn({start:E,end:R},{start:new Date(w.event.start),end:new Date(w.event.end)}))?k++:Y=!0}const F=g[k]||[];g[k]=F,F.push({event:y,end:R});const H=k===0?1:.9,$=k===0?0:k*.1;c.push({event:y,top:N,height:P,left:$,width:H,zIndex:10+k})}),c},[e,l]),u=(c,h)=>{h.stopPropagation(),t(c)},m=i.length>0,{currentTimePosition:f,currentTimeVisible:v}=An(e,"day");return o.jsxs("div",{"data-slot":"day-view",className:"contents",children:[m&&o.jsx("div",{className:"border-border/70 bg-muted/50 border-t",children:o.jsxs("div",{className:"grid grid-cols-[3rem_1fr] sm:grid-cols-[4rem_1fr]",children:[o.jsx("div",{className:"relative",children:o.jsx("span",{className:"text-muted-foreground/70 absolute bottom-0 left-0 h-6 w-16 max-w-full pe-2 text-right text-[10px] sm:pe-4 sm:text-xs",children:"All day"})}),o.jsx("div",{className:"border-border/70 relative border-r p-1 last:border-r-0",children:i.map(c=>{const h=new Date(c.start),b=new Date(c.end),g=C(e,h),y=C(e,b);return o.jsx(he,{onClick:M=>u(c,M),event:c,view:"month",isFirstDay:g,isLastDay:y,children:o.jsx("div",{children:c.title})},`spanning-${c.id}`)})})]})}),o.jsxs("div",{className:"border-border/70 grid flex-1 grid-cols-[3rem_1fr] overflow-hidden border-t sm:grid-cols-[4rem_1fr]",children:[o.jsx("div",{children:r.map((c,h)=>o.jsx("div",{className:"border-border/70 relative h-[var(--week-cells-height)] border-b last:border-b-0",children:h>0&&o.jsx("span",{className:"bg-background text-muted-foreground/70 absolute -top-3 left-0 flex h-6 w-16 max-w-full items-center justify-end pe-2 text-[10px] sm:pe-4 sm:text-xs",children:O(c,"h a")})},c.toString()))}),o.jsxs("div",{className:"relative",children:[d.map(c=>o.jsx("div",{className:"absolute z-10 px-0.5",style:{top:`${c.top}px`,height:`${c.height}px`,left:`${c.left*100}%`,width:`${c.width*100}%`,zIndex:c.zIndex},children:o.jsx("div",{className:"size-full",children:o.jsx(bt,{event:c.event,view:"day",onClick:h=>u(c.event,h),showTime:!0,height:c.height})})},c.event.id)),v&&o.jsx("div",{className:"pointer-events-none absolute right-0 left-0 z-20",style:{top:`${f}%`},children:o.jsxs("div",{className:"relative flex items-center",children:[o.jsx("div",{className:"bg-primary absolute -left-1 h-2 w-2 rounded-full"}),o.jsx("div",{className:"bg-primary h-[2px] w-full"})]})}),r.map(c=>{const h=we(c);return o.jsx("div",{className:"border-border/70 relative h-[var(--week-cells-height)] border-b last:border-b-0",children:[0,1,2,3].map(b=>{const g=h+b*.25;return o.jsx(yt,{id:`day-cell-${e.toISOString()}-${g}`,date:e,time:g,className:G("absolute h-[calc(var(--week-cells-height)/4)] w-full",b===0&&"top-0",b===1&&"top-[calc(var(--week-cells-height)/4)]",b===2&&"top-[calc(var(--week-cells-height)/4*2)]",b===3&&"top-[calc(var(--week-cells-height)/4*3)]"),onClick:()=>{const y=new Date(e);y.setHours(h),y.setMinutes(b*15),a(y)}},`${c.toString()}-${b}`)})},c.toString())})]})]})]})}function bt({event:e,view:n,showTime:t,onClick:a,height:r,isMultiDay:s,multiDayWidth:i,isFirstDay:l=!0,isLastDay:d=!0,"aria-hidden":u}){const{activeId:m}=Hn(),f=p.useRef(null),[v,c]=p.useState(null),h=new Date(e.start),b=new Date(e.end),g=s||e.allDay||Aa(b,h)>=1,{attributes:y,listeners:M,setNodeRef:T,transform:E,isDragging:R}=Bn({id:`${e.id}-${n}`,data:{event:e,view:n,height:r||f.current?.offsetHeight||null,isMultiDay:g,multiDayWidth:i,dragHandlePosition:v,isFirstDay:l,isLastDay:d}}),W=P=>{if(f.current){const k=f.current.getBoundingClientRect();c({x:P.clientX-k.left,y:P.clientY-k.top})}};if(R||m===`${e.id}-${n}`)return o.jsx("div",{ref:T,className:"opacity-0",style:{height:r||"auto"}});const x=E?{transform:Vn.Translate.toString(E),height:r||"auto",width:g&&i?`${i}%`:void 0}:{height:r||"auto",width:g&&i?`${i}%`:void 0},N=P=>{if(f.current){const k=f.current.getBoundingClientRect(),Y=P.touches[0];Y&&c({x:Y.clientX-k.left,y:Y.clientY-k.top})}};return o.jsx("div",{ref:P=>{T(P),f&&(f.current=P)},style:x,className:"touch-none",children:o.jsx(he,{event:e,view:n,showTime:t,isFirstDay:l,isLastDay:d,isDragging:R,onClick:a,onMouseDown:W,onTouchStart:N,dndListeners:M,dndAttributes:y,"aria-hidden":u})})}function yt({id:e,date:n,time:t,children:a,className:r,onClick:s}){const{activeEvent:i}=Hn(),{setNodeRef:l,isOver:d}=zn({id:e,data:{date:n,time:t}}),u=t!==void 0?`${Math.floor(t)}:${Math.round((t-Math.floor(t))*60).toString().padStart(2,"0")}`:null;return o.jsx("div",{ref:l,onClick:s,className:G("data-dragging:bg-accent flex h-full flex-col overflow-hidden px-0.5 py-1 sm:px-1",r),title:u?`${u}`:void 0,"data-dragging":d&&i?!0:void 0,children:a})}var S=function(){return S=Object.assign||function(n){for(var t,a=1,r=arguments.length;a<r;a++){t=arguments[a];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(n[s]=t[s])}return n},S.apply(this,arguments)};function Jr(e,n){var t={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&n.indexOf(a)<0&&(t[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,a=Object.getOwnPropertySymbols(e);r<a.length;r++)n.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(t[a[r]]=e[a[r]]);return t}function vn(e,n,t){for(var a=0,r=n.length,s;a<r;a++)(s||!(a in n))&&(s||(s=Array.prototype.slice.call(n,0,a)),s[a]=n[a]);return e.concat(s||Array.prototype.slice.call(n))}function Te(e){return e.mode==="multiple"}function We(e){return e.mode==="range"}function Ve(e){return e.mode==="single"}var Ur={root:"rdp",multiple_months:"rdp-multiple_months",with_weeknumber:"rdp-with_weeknumber",vhidden:"rdp-vhidden",button_reset:"rdp-button_reset",button:"rdp-button",caption:"rdp-caption",caption_start:"rdp-caption_start",caption_end:"rdp-caption_end",caption_between:"rdp-caption_between",caption_label:"rdp-caption_label",caption_dropdowns:"rdp-caption_dropdowns",dropdown:"rdp-dropdown",dropdown_month:"rdp-dropdown_month",dropdown_year:"rdp-dropdown_year",dropdown_icon:"rdp-dropdown_icon",months:"rdp-months",month:"rdp-month",table:"rdp-table",tbody:"rdp-tbody",tfoot:"rdp-tfoot",head:"rdp-head",head_row:"rdp-head_row",head_cell:"rdp-head_cell",nav:"rdp-nav",nav_button:"rdp-nav_button",nav_button_previous:"rdp-nav_button_previous",nav_button_next:"rdp-nav_button_next",nav_icon:"rdp-nav_icon",row:"rdp-row",weeknumber:"rdp-weeknumber",cell:"rdp-cell",day:"rdp-day",day_today:"rdp-day_today",day_outside:"rdp-day_outside",day_selected:"rdp-day_selected",day_disabled:"rdp-day_disabled",day_hidden:"rdp-day_hidden",day_range_start:"rdp-day_range_start",day_range_end:"rdp-day_range_end",day_range_middle:"rdp-day_range_middle"};function Qr(e,n){return O(e,"LLLL y",n)}function Kr(e,n){return O(e,"d",n)}function Zr(e,n){return O(e,"LLLL",n)}function eo(e){return"".concat(e)}function to(e,n){return O(e,"cccccc",n)}function no(e,n){return O(e,"yyyy",n)}var ao=Object.freeze({__proto__:null,formatCaption:Qr,formatDay:Kr,formatMonthCaption:Zr,formatWeekNumber:eo,formatWeekdayName:to,formatYearCaption:no}),ro=function(e,n,t){return O(e,"do MMMM (EEEE)",t)},oo=function(){return"Month: "},so=function(){return"Go to next month"},io=function(){return"Go to previous month"},lo=function(e,n){return O(e,"cccc",n)},co=function(e){return"Week n. ".concat(e)},uo=function(){return"Year: "},fo=Object.freeze({__proto__:null,labelDay:ro,labelMonthDropdown:oo,labelNext:so,labelPrevious:io,labelWeekNumber:co,labelWeekday:lo,labelYearDropdown:uo});function mo(){var e="buttons",n=Ur,t=dn,a={},r={},s=1,i={},l=new Date;return{captionLayout:e,classNames:n,formatters:ao,labels:fo,locale:t,modifiersClassNames:a,modifiers:r,numberOfMonths:s,styles:i,today:l,mode:"default"}}function ho(e){var n=e.fromYear,t=e.toYear,a=e.fromMonth,r=e.toMonth,s=e.fromDate,i=e.toDate;return a?s=J(a):n&&(s=new Date(n,0,1)),r?i=Be(r):t&&(i=new Date(t,11,31)),{fromDate:s?ae(s):void 0,toDate:i?ae(i):void 0}}var gn=p.createContext(void 0);function vo(e){var n,t=e.initialProps,a=mo(),r=ho(t),s=r.fromDate,i=r.toDate,l=(n=t.captionLayout)!==null&&n!==void 0?n:a.captionLayout;l!=="buttons"&&(!s||!i)&&(l="buttons");var d;(Ve(t)||Te(t)||We(t))&&(d=t.onSelect);var u=S(S(S({},a),t),{captionLayout:l,classNames:S(S({},a.classNames),t.classNames),components:S({},t.components),formatters:S(S({},a.formatters),t.formatters),fromDate:s,labels:S(S({},a.labels),t.labels),mode:t.mode||a.mode,modifiers:S(S({},a.modifiers),t.modifiers),modifiersClassNames:S(S({},a.modifiersClassNames),t.modifiersClassNames),onSelect:d,styles:S(S({},a.styles),t.styles),toDate:i});return o.jsx(gn.Provider,{value:u,children:e.children})}function A(){var e=p.useContext(gn);if(!e)throw new Error("useDayPicker must be used within a DayPickerProvider.");return e}function pn(e){var n=A(),t=n.locale,a=n.classNames,r=n.styles,s=n.formatters.formatCaption;return o.jsx("div",{className:a.caption_label,style:r.caption_label,"aria-live":"polite",role:"presentation",id:e.id,children:s(e.displayMonth,{locale:t})})}function go(e){return o.jsx("svg",S({width:"8px",height:"8px",viewBox:"0 0 120 120","data-testid":"iconDropdown"},e,{children:o.jsx("path",{d:"M4.22182541,48.2218254 C8.44222828,44.0014225 15.2388494,43.9273804 19.5496459,47.9996989 L19.7781746,48.2218254 L60,88.443 L100.221825,48.2218254 C104.442228,44.0014225 111.238849,43.9273804 115.549646,47.9996989 L115.778175,48.2218254 C119.998577,52.4422283 120.07262,59.2388494 116.000301,63.5496459 L115.778175,63.7781746 L67.7781746,111.778175 C63.5577717,115.998577 56.7611506,116.07262 52.4503541,112.000301 L52.2218254,111.778175 L4.22182541,63.7781746 C-0.**********,59.4824074 -0.**********,52.5175926 4.22182541,48.2218254 Z",fill:"currentColor",fillRule:"nonzero"})}))}function bn(e){var n,t,a=e.onChange,r=e.value,s=e.children,i=e.caption,l=e.className,d=e.style,u=A(),m=(t=(n=u.components)===null||n===void 0?void 0:n.IconDropdown)!==null&&t!==void 0?t:go;return o.jsxs("div",{className:l,style:d,children:[o.jsx("span",{className:u.classNames.vhidden,children:e["aria-label"]}),o.jsx("select",{name:e.name,"aria-label":e["aria-label"],className:u.classNames.dropdown,style:u.styles.dropdown,value:r,onChange:a,children:s}),o.jsxs("div",{className:u.classNames.caption_label,style:u.styles.caption_label,"aria-hidden":"true",children:[i,o.jsx(m,{className:u.classNames.dropdown_icon,style:u.styles.dropdown_icon})]})]})}function po(e){var n,t=A(),a=t.fromDate,r=t.toDate,s=t.styles,i=t.locale,l=t.formatters.formatMonthCaption,d=t.classNames,u=t.components,m=t.labels.labelMonthDropdown;if(!a)return o.jsx(o.Fragment,{});if(!r)return o.jsx(o.Fragment,{});var f=[];if(Br(a,r))for(var v=J(a),c=a.getMonth();c<=r.getMonth();c++)f.push(Ue(v,c));else for(var v=J(new Date),c=0;c<=11;c++)f.push(Ue(v,c));var h=function(g){var y=Number(g.target.value),M=Ue(J(e.displayMonth),y);e.onChange(M)},b=(n=u?.Dropdown)!==null&&n!==void 0?n:bn;return o.jsx(b,{name:"months","aria-label":m(),className:d.dropdown_month,style:s.dropdown_month,onChange:h,value:e.displayMonth.getMonth(),caption:l(e.displayMonth,{locale:i}),children:f.map(function(g){return o.jsx("option",{value:g.getMonth(),children:l(g,{locale:i})},g.getMonth())})})}function bo(e){var n,t=e.displayMonth,a=A(),r=a.fromDate,s=a.toDate,i=a.locale,l=a.styles,d=a.classNames,u=a.components,m=a.formatters.formatYearCaption,f=a.labels.labelYearDropdown,v=[];if(!r)return o.jsx(o.Fragment,{});if(!s)return o.jsx(o.Fragment,{});for(var c=r.getFullYear(),h=s.getFullYear(),b=c;b<=h;b++)v.push(It(sn(new Date),b));var g=function(M){var T=It(J(t),Number(M.target.value));e.onChange(T)},y=(n=u?.Dropdown)!==null&&n!==void 0?n:bn;return o.jsx(y,{name:"years","aria-label":f(),className:d.dropdown_year,style:l.dropdown_year,onChange:g,value:t.getFullYear(),caption:m(t,{locale:i}),children:v.map(function(M){return o.jsx("option",{value:M.getFullYear(),children:m(M,{locale:i})},M.getFullYear())})})}function yo(e,n){var t=p.useState(e),a=t[0],r=t[1],s=n===void 0?a:n;return[s,r]}function xo(e){var n=e.month,t=e.defaultMonth,a=e.today,r=n||t||a||new Date,s=e.toDate,i=e.fromDate,l=e.numberOfMonths,d=l===void 0?1:l;if(s&&Ce(s,r)<0){var u=-1*(d-1);r=Q(s,u)}return i&&Ce(r,i)<0&&(r=i),J(r)}function wo(){var e=A(),n=xo(e),t=yo(n,e.month),a=t[0],r=t[1],s=function(i){var l;if(!e.disableNavigation){var d=J(i);r(d),(l=e.onMonthChange)===null||l===void 0||l.call(e,d)}};return[a,s]}function Do(e,n){for(var t=n.reverseMonths,a=n.numberOfMonths,r=J(e),s=J(Q(r,a)),i=Ce(s,r),l=[],d=0;d<i;d++){var u=Q(r,d);l.push(u)}return t&&(l=l.reverse()),l}function ko(e,n){if(!n.disableNavigation){var t=n.toDate,a=n.pagedNavigation,r=n.numberOfMonths,s=r===void 0?1:r,i=a?s:1,l=J(e);if(!t)return Q(l,i);var d=Ce(t,e);if(!(d<s))return Q(l,i)}}function Mo(e,n){if(!n.disableNavigation){var t=n.fromDate,a=n.pagedNavigation,r=n.numberOfMonths,s=r===void 0?1:r,i=a?s:1,l=J(e);if(!t)return Q(l,-i);var d=Ce(l,t);if(!(d<=0))return Q(l,-i)}}var yn=p.createContext(void 0);function jo(e){var n=A(),t=wo(),a=t[0],r=t[1],s=Do(a,n),i=ko(a,n),l=Mo(a,n),d=function(f){return s.some(function(v){return ke(f,v)})},u=function(f,v){d(f)||(v&&Ee(f,v)?r(Q(f,1+n.numberOfMonths*-1)):r(f))},m={currentMonth:a,displayMonths:s,goToMonth:r,goToDate:u,previousMonth:l,nextMonth:i,isDateDisplayed:d};return o.jsx(yn.Provider,{value:m,children:e.children})}function Fe(){var e=p.useContext(yn);if(!e)throw new Error("useNavigation must be used within a NavigationProvider");return e}function Lt(e){var n,t=A(),a=t.classNames,r=t.styles,s=t.components,i=Fe().goToMonth,l=function(m){i(Q(m,e.displayIndex?-e.displayIndex:0))},d=(n=s?.CaptionLabel)!==null&&n!==void 0?n:pn,u=o.jsx(d,{id:e.id,displayMonth:e.displayMonth});return o.jsxs("div",{className:a.caption_dropdowns,style:r.caption_dropdowns,children:[o.jsx("div",{className:a.vhidden,children:u}),o.jsx(po,{onChange:l,displayMonth:e.displayMonth}),o.jsx(bo,{onChange:l,displayMonth:e.displayMonth})]})}function No(e){return o.jsx("svg",S({width:"16px",height:"16px",viewBox:"0 0 120 120"},e,{children:o.jsx("path",{d:"M69.490332,3.34314575 C72.6145263,0.218951416 77.6798462,0.218951416 80.8040405,3.34314575 C83.8617626,6.40086786 83.9268205,11.3179931 80.9992143,14.4548388 L80.8040405,14.6568542 L35.461,60 L80.8040405,105.343146 C83.8617626,108.400868 83.9268205,113.317993 80.9992143,116.454839 L80.8040405,116.656854 C77.7463184,119.714576 72.8291931,119.779634 69.6923475,116.852028 L69.490332,116.656854 L18.490332,65.6568542 C15.4326099,62.5991321 15.367552,57.6820069 18.2951583,54.5451612 L18.490332,54.3431458 L69.490332,3.34314575 Z",fill:"currentColor",fillRule:"nonzero"})}))}function So(e){return o.jsx("svg",S({width:"16px",height:"16px",viewBox:"0 0 120 120"},e,{children:o.jsx("path",{d:"M49.8040405,3.34314575 C46.6798462,0.218951416 41.6145263,0.218951416 38.490332,3.34314575 C35.4326099,6.40086786 35.367552,11.3179931 38.2951583,14.4548388 L38.490332,14.6568542 L83.8333725,60 L38.490332,105.343146 C35.4326099,108.400868 35.367552,113.317993 38.2951583,116.454839 L38.490332,116.656854 C41.5480541,119.714576 46.4651794,119.779634 49.602025,116.852028 L49.8040405,116.656854 L100.804041,65.6568542 C103.861763,62.5991321 103.926821,57.6820069 100.999214,54.5451612 L100.804041,54.3431458 L49.8040405,3.34314575 Z",fill:"currentColor"})}))}var Ae=p.forwardRef(function(e,n){var t=A(),a=t.classNames,r=t.styles,s=[a.button_reset,a.button];e.className&&s.push(e.className);var i=s.join(" "),l=S(S({},r.button_reset),r.button);return e.style&&Object.assign(l,e.style),o.jsx("button",S({},e,{ref:n,type:"button",className:i,style:l}))});function Co(e){var n,t,a=A(),r=a.dir,s=a.locale,i=a.classNames,l=a.styles,d=a.labels,u=d.labelPrevious,m=d.labelNext,f=a.components;if(!e.nextMonth&&!e.previousMonth)return o.jsx(o.Fragment,{});var v=u(e.previousMonth,{locale:s}),c=[i.nav_button,i.nav_button_previous].join(" "),h=m(e.nextMonth,{locale:s}),b=[i.nav_button,i.nav_button_next].join(" "),g=(n=f?.IconRight)!==null&&n!==void 0?n:So,y=(t=f?.IconLeft)!==null&&t!==void 0?t:No;return o.jsxs("div",{className:i.nav,style:l.nav,children:[!e.hidePrevious&&o.jsx(Ae,{name:"previous-month","aria-label":v,className:c,style:l.nav_button_previous,disabled:!e.previousMonth,onClick:e.onPreviousClick,children:r==="rtl"?o.jsx(g,{className:i.nav_icon,style:l.nav_icon}):o.jsx(y,{className:i.nav_icon,style:l.nav_icon})}),!e.hideNext&&o.jsx(Ae,{name:"next-month","aria-label":h,className:b,style:l.nav_button_next,disabled:!e.nextMonth,onClick:e.onNextClick,children:r==="rtl"?o.jsx(y,{className:i.nav_icon,style:l.nav_icon}):o.jsx(g,{className:i.nav_icon,style:l.nav_icon})})]})}function At(e){var n=A().numberOfMonths,t=Fe(),a=t.previousMonth,r=t.nextMonth,s=t.goToMonth,i=t.displayMonths,l=i.findIndex(function(h){return ke(e.displayMonth,h)}),d=l===0,u=l===i.length-1,m=n>1&&(d||!u),f=n>1&&(u||!d),v=function(){a&&s(a)},c=function(){r&&s(r)};return o.jsx(Co,{displayMonth:e.displayMonth,hideNext:m,hidePrevious:f,nextMonth:r,previousMonth:a,onPreviousClick:v,onNextClick:c})}function _o(e){var n,t=A(),a=t.classNames,r=t.disableNavigation,s=t.styles,i=t.captionLayout,l=t.components,d=(n=l?.CaptionLabel)!==null&&n!==void 0?n:pn,u;return r?u=o.jsx(d,{id:e.id,displayMonth:e.displayMonth}):i==="dropdown"?u=o.jsx(Lt,{displayMonth:e.displayMonth,id:e.id}):i==="dropdown-buttons"?u=o.jsxs(o.Fragment,{children:[o.jsx(Lt,{displayMonth:e.displayMonth,displayIndex:e.displayIndex,id:e.id}),o.jsx(At,{displayMonth:e.displayMonth,displayIndex:e.displayIndex,id:e.id})]}):u=o.jsxs(o.Fragment,{children:[o.jsx(d,{id:e.id,displayMonth:e.displayMonth,displayIndex:e.displayIndex}),o.jsx(At,{displayMonth:e.displayMonth,id:e.id})]}),o.jsx("div",{className:a.caption,style:s.caption,children:u})}function Eo(e){var n=A(),t=n.footer,a=n.styles,r=n.classNames.tfoot;return t?o.jsx("tfoot",{className:r,style:a.tfoot,children:o.jsx("tr",{children:o.jsx("td",{colSpan:8,children:t})})}):o.jsx(o.Fragment,{})}function Po(e,n,t){for(var a=t?me(new Date):X(new Date,{locale:e,weekStartsOn:n}),r=[],s=0;s<7;s++){var i=z(a,s);r.push(i)}return r}function Oo(){var e=A(),n=e.classNames,t=e.styles,a=e.showWeekNumber,r=e.locale,s=e.weekStartsOn,i=e.ISOWeek,l=e.formatters.formatWeekdayName,d=e.labels.labelWeekday,u=Po(r,s,i);return o.jsxs("tr",{style:t.head_row,className:n.head_row,children:[a&&o.jsx("td",{style:t.head_cell,className:n.head_cell}),u.map(function(m,f){return o.jsx("th",{scope:"col",className:n.head_cell,style:t.head_cell,"aria-label":d(m,{locale:r}),children:l(m,{locale:r})},f)})]})}function To(){var e,n=A(),t=n.classNames,a=n.styles,r=n.components,s=(e=r?.HeadRow)!==null&&e!==void 0?e:Oo;return o.jsx("thead",{style:a.head,className:t.head,children:o.jsx(s,{})})}function Wo(e){var n=A(),t=n.locale,a=n.formatters.formatDay;return o.jsx(o.Fragment,{children:a(e.date,{locale:t})})}var xt=p.createContext(void 0);function Fo(e){if(!Te(e.initialProps)){var n={selected:void 0,modifiers:{disabled:[]}};return o.jsx(xt.Provider,{value:n,children:e.children})}return o.jsx(Ro,{initialProps:e.initialProps,children:e.children})}function Ro(e){var n=e.initialProps,t=e.children,a=n.selected,r=n.min,s=n.max,i=function(u,m,f){var v,c;(v=n.onDayClick)===null||v===void 0||v.call(n,u,m,f);var h=!!(m.selected&&r&&a?.length===r);if(!h){var b=!!(!m.selected&&s&&a?.length===s);if(!b){var g=a?vn([],a):[];if(m.selected){var y=g.findIndex(function(M){return C(u,M)});g.splice(y,1)}else g.push(u);(c=n.onSelect)===null||c===void 0||c.call(n,g,u,m,f)}}},l={disabled:[]};a&&l.disabled.push(function(u){var m=s&&a.length>s-1,f=a.some(function(v){return C(v,u)});return!!(m&&!f)});var d={selected:a,onDayClick:i,modifiers:l};return o.jsx(xt.Provider,{value:d,children:t})}function wt(){var e=p.useContext(xt);if(!e)throw new Error("useSelectMultiple must be used within a SelectMultipleProvider");return e}function Io(e,n){var t=n||{},a=t.from,r=t.to;return a&&r?C(r,e)&&C(a,e)?void 0:C(r,e)?{from:r,to:void 0}:C(a,e)?void 0:ut(a,e)?{from:e,to:r}:{from:a,to:e}:r?ut(e,r)?{from:r,to:e}:{from:e,to:r}:a?Ee(e,a)?{from:e,to:a}:{from:a,to:e}:{from:e,to:void 0}}var Dt=p.createContext(void 0);function Yo(e){if(!We(e.initialProps)){var n={selected:void 0,modifiers:{range_start:[],range_end:[],range_middle:[],disabled:[]}};return o.jsx(Dt.Provider,{value:n,children:e.children})}return o.jsx(Ho,{initialProps:e.initialProps,children:e.children})}function Ho(e){var n=e.initialProps,t=e.children,a=n.selected,r=a||{},s=r.from,i=r.to,l=n.min,d=n.max,u=function(c,h,b){var g,y;(g=n.onDayClick)===null||g===void 0||g.call(n,c,h,b);var M=Io(c,a);(y=n.onSelect)===null||y===void 0||y.call(n,M,c,h,b)},m={range_start:[],range_end:[],range_middle:[],disabled:[]};if(s?(m.range_start=[s],i?(m.range_end=[i],C(s,i)||(m.range_middle=[{after:s,before:i}])):m.range_end=[s]):i&&(m.range_start=[i],m.range_end=[i]),l&&(s&&!i&&m.disabled.push({after:Je(s,l-1),before:z(s,l-1)}),s&&i&&m.disabled.push({after:s,before:z(s,l-1)}),!s&&i&&m.disabled.push({after:Je(i,l-1),before:z(i,l-1)})),d){if(s&&!i&&(m.disabled.push({before:z(s,-d+1)}),m.disabled.push({after:z(s,d-1)})),s&&i){var f=ee(i,s)+1,v=d-f;m.disabled.push({before:Je(s,v)}),m.disabled.push({after:z(i,v)})}!s&&i&&(m.disabled.push({before:z(i,-d+1)}),m.disabled.push({after:z(i,d-1)}))}return o.jsx(Dt.Provider,{value:{selected:a,onDayClick:u,modifiers:m},children:t})}function kt(){var e=p.useContext(Dt);if(!e)throw new Error("useSelectRange must be used within a SelectRangeProvider");return e}function He(e){return Array.isArray(e)?vn([],e):e!==void 0?[e]:[]}function Lo(e){var n={};return Object.entries(e).forEach(function(t){var a=t[0],r=t[1];n[a]=He(r)}),n}var te;(function(e){e.Outside="outside",e.Disabled="disabled",e.Selected="selected",e.Hidden="hidden",e.Today="today",e.RangeStart="range_start",e.RangeEnd="range_end",e.RangeMiddle="range_middle"})(te||(te={}));var Ao=te.Selected,re=te.Disabled,$o=te.Hidden,Bo=te.Today,Qe=te.RangeEnd,Ke=te.RangeMiddle,Ze=te.RangeStart,Vo=te.Outside;function zo(e,n,t){var a,r=(a={},a[Ao]=He(e.selected),a[re]=He(e.disabled),a[$o]=He(e.hidden),a[Bo]=[e.today],a[Qe]=[],a[Ke]=[],a[Ze]=[],a[Vo]=[],a);return e.fromDate&&r[re].push({before:e.fromDate}),e.toDate&&r[re].push({after:e.toDate}),Te(e)?r[re]=r[re].concat(n.modifiers[re]):We(e)&&(r[re]=r[re].concat(t.modifiers[re]),r[Ze]=t.modifiers[Ze],r[Ke]=t.modifiers[Ke],r[Qe]=t.modifiers[Qe]),r}var xn=p.createContext(void 0);function Go(e){var n=A(),t=wt(),a=kt(),r=zo(n,t,a),s=Lo(n.modifiers),i=S(S({},r),s);return o.jsx(xn.Provider,{value:i,children:e.children})}function wn(){var e=p.useContext(xn);if(!e)throw new Error("useModifiers must be used within a ModifiersProvider");return e}function qo(e){return!!(e&&typeof e=="object"&&"before"in e&&"after"in e)}function Xo(e){return!!(e&&typeof e=="object"&&"from"in e)}function Jo(e){return!!(e&&typeof e=="object"&&"after"in e)}function Uo(e){return!!(e&&typeof e=="object"&&"before"in e)}function Qo(e){return!!(e&&typeof e=="object"&&"dayOfWeek"in e)}function Ko(e,n){var t,a=n.from,r=n.to;if(a&&r){var s=ee(r,a)<0;s&&(t=[r,a],a=t[0],r=t[1]);var i=ee(e,a)>=0&&ee(r,e)>=0;return i}return r?C(r,e):a?C(a,e):!1}function Zo(e){return pt(e)}function es(e){return Array.isArray(e)&&e.every(pt)}function ts(e,n){return n.some(function(t){if(typeof t=="boolean")return t;if(Zo(t))return C(e,t);if(es(t))return t.includes(e);if(Xo(t))return Ko(e,t);if(Qo(t))return t.dayOfWeek.includes(e.getDay());if(qo(t)){var a=ee(t.before,e),r=ee(t.after,e),s=a>0,i=r<0,l=ut(t.before,t.after);return l?i&&s:s||i}return Jo(t)?ee(e,t.after)>0:Uo(t)?ee(t.before,e)>0:typeof t=="function"?t(e):!1})}function Mt(e,n,t){var a=Object.keys(n).reduce(function(s,i){var l=n[i];return ts(e,l)&&s.push(i),s},[]),r={};return a.forEach(function(s){return r[s]=!0}),t&&!ke(e,t)&&(r.outside=!0),r}function ns(e,n){for(var t=J(e[0]),a=Be(e[e.length-1]),r,s,i=t;i<=a;){var l=Mt(i,n),d=!l.disabled&&!l.hidden;if(!d){i=z(i,1);continue}if(l.selected)return i;l.today&&!s&&(s=i),r||(r=i),i=z(i,1)}return s||r}var as=365;function Dn(e,n){var t=n.moveBy,a=n.direction,r=n.context,s=n.modifiers,i=n.retry,l=i===void 0?{count:0,lastFocused:e}:i,d=r.weekStartsOn,u=r.fromDate,m=r.toDate,f=r.locale,v={day:z,week:Se,month:Q,year:Fa,startOfWeek:function(g){return r.ISOWeek?me(g):X(g,{locale:f,weekStartsOn:d})},endOfWeek:function(g){return r.ISOWeek?ln(g):ge(g,{locale:f,weekStartsOn:d})}},c=v[t](e,a==="after"?1:-1);a==="before"&&u?c=Ra([u,c]):a==="after"&&m&&(c=Ia([m,c]));var h=!0;if(s){var b=Mt(c,s);h=!b.disabled&&!b.hidden}return h?c:l.count>as?l.lastFocused:Dn(c,{moveBy:t,direction:a,context:r,modifiers:s,retry:S(S({},l),{count:l.count+1})})}var kn=p.createContext(void 0);function rs(e){var n=Fe(),t=wn(),a=p.useState(),r=a[0],s=a[1],i=p.useState(),l=i[0],d=i[1],u=ns(n.displayMonths,t),m=r??(l&&n.isDateDisplayed(l))?l:u,f=function(){d(r),s(void 0)},v=function(g){s(g)},c=A(),h=function(g,y){if(r){var M=Dn(r,{moveBy:g,direction:y,context:c,modifiers:t});C(r,M)||(n.goToDate(M,r),v(M))}},b={focusedDay:r,focusTarget:m,blur:f,focus:v,focusDayAfter:function(){return h("day","after")},focusDayBefore:function(){return h("day","before")},focusWeekAfter:function(){return h("week","after")},focusWeekBefore:function(){return h("week","before")},focusMonthBefore:function(){return h("month","before")},focusMonthAfter:function(){return h("month","after")},focusYearBefore:function(){return h("year","before")},focusYearAfter:function(){return h("year","after")},focusStartOfWeek:function(){return h("startOfWeek","before")},focusEndOfWeek:function(){return h("endOfWeek","after")}};return o.jsx(kn.Provider,{value:b,children:e.children})}function jt(){var e=p.useContext(kn);if(!e)throw new Error("useFocusContext must be used within a FocusProvider");return e}function os(e,n){var t=wn(),a=Mt(e,t,n);return a}var Nt=p.createContext(void 0);function ss(e){if(!Ve(e.initialProps)){var n={selected:void 0};return o.jsx(Nt.Provider,{value:n,children:e.children})}return o.jsx(is,{initialProps:e.initialProps,children:e.children})}function is(e){var n=e.initialProps,t=e.children,a=function(s,i,l){var d,u,m;if((d=n.onDayClick)===null||d===void 0||d.call(n,s,i,l),i.selected&&!n.required){(u=n.onSelect)===null||u===void 0||u.call(n,void 0,s,i,l);return}(m=n.onSelect)===null||m===void 0||m.call(n,s,s,i,l)},r={selected:n.selected,onDayClick:a};return o.jsx(Nt.Provider,{value:r,children:t})}function Mn(){var e=p.useContext(Nt);if(!e)throw new Error("useSelectSingle must be used within a SelectSingleProvider");return e}function ls(e,n){var t=A(),a=Mn(),r=wt(),s=kt(),i=jt(),l=i.focusDayAfter,d=i.focusDayBefore,u=i.focusWeekAfter,m=i.focusWeekBefore,f=i.blur,v=i.focus,c=i.focusMonthBefore,h=i.focusMonthAfter,b=i.focusYearBefore,g=i.focusYearAfter,y=i.focusStartOfWeek,M=i.focusEndOfWeek,T=function(w){var D,j,B,U;Ve(t)?(D=a.onDayClick)===null||D===void 0||D.call(a,e,n,w):Te(t)?(j=r.onDayClick)===null||j===void 0||j.call(r,e,n,w):We(t)?(B=s.onDayClick)===null||B===void 0||B.call(s,e,n,w):(U=t.onDayClick)===null||U===void 0||U.call(t,e,n,w)},E=function(w){var D;v(e),(D=t.onDayFocus)===null||D===void 0||D.call(t,e,n,w)},R=function(w){var D;f(),(D=t.onDayBlur)===null||D===void 0||D.call(t,e,n,w)},W=function(w){var D;(D=t.onDayMouseEnter)===null||D===void 0||D.call(t,e,n,w)},x=function(w){var D;(D=t.onDayMouseLeave)===null||D===void 0||D.call(t,e,n,w)},N=function(w){var D;(D=t.onDayPointerEnter)===null||D===void 0||D.call(t,e,n,w)},P=function(w){var D;(D=t.onDayPointerLeave)===null||D===void 0||D.call(t,e,n,w)},k=function(w){var D;(D=t.onDayTouchCancel)===null||D===void 0||D.call(t,e,n,w)},Y=function(w){var D;(D=t.onDayTouchEnd)===null||D===void 0||D.call(t,e,n,w)},F=function(w){var D;(D=t.onDayTouchMove)===null||D===void 0||D.call(t,e,n,w)},H=function(w){var D;(D=t.onDayTouchStart)===null||D===void 0||D.call(t,e,n,w)},$=function(w){var D;(D=t.onDayKeyUp)===null||D===void 0||D.call(t,e,n,w)},L=function(w){var D;switch(w.key){case"ArrowLeft":w.preventDefault(),w.stopPropagation(),t.dir==="rtl"?l():d();break;case"ArrowRight":w.preventDefault(),w.stopPropagation(),t.dir==="rtl"?d():l();break;case"ArrowDown":w.preventDefault(),w.stopPropagation(),u();break;case"ArrowUp":w.preventDefault(),w.stopPropagation(),m();break;case"PageUp":w.preventDefault(),w.stopPropagation(),w.shiftKey?b():c();break;case"PageDown":w.preventDefault(),w.stopPropagation(),w.shiftKey?g():h();break;case"Home":w.preventDefault(),w.stopPropagation(),y();break;case"End":w.preventDefault(),w.stopPropagation(),M();break}(D=t.onDayKeyDown)===null||D===void 0||D.call(t,e,n,w)},q={onClick:T,onFocus:E,onBlur:R,onKeyDown:L,onKeyUp:$,onMouseEnter:W,onMouseLeave:x,onPointerEnter:N,onPointerLeave:P,onTouchCancel:k,onTouchEnd:Y,onTouchMove:F,onTouchStart:H};return q}function ds(){var e=A(),n=Mn(),t=wt(),a=kt(),r=Ve(e)?n.selected:Te(e)?t.selected:We(e)?a.selected:void 0;return r}function cs(e){return Object.values(te).includes(e)}function us(e,n){var t=[e.classNames.day];return Object.keys(n).forEach(function(a){var r=e.modifiersClassNames[a];if(r)t.push(r);else if(cs(a)){var s=e.classNames["day_".concat(a)];s&&t.push(s)}}),t}function fs(e,n){var t=S({},e.styles.day);return Object.keys(n).forEach(function(a){var r;t=S(S({},t),(r=e.modifiersStyles)===null||r===void 0?void 0:r[a])}),t}function ms(e,n,t){var a,r,s,i=A(),l=jt(),d=os(e,n),u=ls(e,d),m=ds(),f=!!(i.onDayClick||i.mode!=="default");p.useEffect(function(){var W;d.outside||l.focusedDay&&f&&C(l.focusedDay,e)&&((W=t.current)===null||W===void 0||W.focus())},[l.focusedDay,e,t,f,d.outside]);var v=us(i,d).join(" "),c=fs(i,d),h=!!(d.outside&&!i.showOutsideDays||d.hidden),b=(s=(r=i.components)===null||r===void 0?void 0:r.DayContent)!==null&&s!==void 0?s:Wo,g=o.jsx(b,{date:e,displayMonth:n,activeModifiers:d}),y={style:c,className:v,children:g,role:"gridcell"},M=l.focusTarget&&C(l.focusTarget,e)&&!d.outside,T=l.focusedDay&&C(l.focusedDay,e),E=S(S(S({},y),(a={disabled:d.disabled,role:"gridcell"},a["aria-selected"]=d.selected,a.tabIndex=T||M?0:-1,a)),u),R={isButton:f,isHidden:h,activeModifiers:d,selectedDays:m,buttonProps:E,divProps:y};return R}function hs(e){var n=p.useRef(null),t=ms(e.date,e.displayMonth,n);return t.isHidden?o.jsx("div",{role:"gridcell"}):t.isButton?o.jsx(Ae,S({name:"day",ref:n},t.buttonProps)):o.jsx("div",S({},t.divProps))}function vs(e){var n=e.number,t=e.dates,a=A(),r=a.onWeekNumberClick,s=a.styles,i=a.classNames,l=a.locale,d=a.labels.labelWeekNumber,u=a.formatters.formatWeekNumber,m=u(Number(n),{locale:l});if(!r)return o.jsx("span",{className:i.weeknumber,style:s.weeknumber,children:m});var f=d(Number(n),{locale:l}),v=function(c){r(n,t,c)};return o.jsx(Ae,{name:"week-number","aria-label":f,className:i.weeknumber,style:s.weeknumber,onClick:v,children:m})}function gs(e){var n,t,a=A(),r=a.styles,s=a.classNames,i=a.showWeekNumber,l=a.components,d=(n=l?.Day)!==null&&n!==void 0?n:hs,u=(t=l?.WeekNumber)!==null&&t!==void 0?t:vs,m;return i&&(m=o.jsx("td",{className:s.cell,style:r.cell,children:o.jsx(u,{number:e.weekNumber,dates:e.dates})})),o.jsxs("tr",{className:s.row,style:r.row,children:[m,e.dates.map(function(f){return o.jsx("td",{className:s.cell,style:r.cell,role:"presentation",children:o.jsx(d,{displayMonth:e.displayMonth,date:f})},Lr(f))})]})}function $t(e,n,t){for(var a=t?.ISOWeek?ln(n):ge(n,t),r=t?.ISOWeek?me(e):X(e,t),s=ee(a,r),i=[],l=0;l<=s;l++)i.push(z(r,l));var d=i.reduce(function(u,m){var f=t?.ISOWeek?cn(m):fn(m,t),v=u.find(function(c){return c.weekNumber===f});return v?(v.dates.push(m),u):(u.push({weekNumber:f,dates:[m]}),u)},[]);return d}function ps(e,n){var t=$t(J(e),Be(e),n);if(n?.useFixedWeeks){var a=$r(e,n);if(a<6){var r=t[t.length-1],s=r.dates[r.dates.length-1],i=Se(s,6-a),l=$t(Se(s,1),i,n);t.push.apply(t,l)}}return t}function bs(e){var n,t,a,r=A(),s=r.locale,i=r.classNames,l=r.styles,d=r.hideHead,u=r.fixedWeeks,m=r.components,f=r.weekStartsOn,v=r.firstWeekContainsDate,c=r.ISOWeek,h=ps(e.displayMonth,{useFixedWeeks:!!u,ISOWeek:c,locale:s,weekStartsOn:f,firstWeekContainsDate:v}),b=(n=m?.Head)!==null&&n!==void 0?n:To,g=(t=m?.Row)!==null&&t!==void 0?t:gs,y=(a=m?.Footer)!==null&&a!==void 0?a:Eo;return o.jsxs("table",{id:e.id,className:i.table,style:l.table,role:"grid","aria-labelledby":e["aria-labelledby"],children:[!d&&o.jsx(b,{}),o.jsx("tbody",{className:i.tbody,style:l.tbody,children:h.map(function(M){return o.jsx(g,{displayMonth:e.displayMonth,dates:M.dates,weekNumber:M.weekNumber},M.weekNumber)})}),o.jsx(y,{displayMonth:e.displayMonth})]})}function ys(){return!!(typeof window<"u"&&window.document&&window.document.createElement)}var xs=ys()?p.useLayoutEffect:p.useEffect,et=!1,ws=0;function Bt(){return"react-day-picker-".concat(++ws)}function Ds(e){var n,t=e??(et?Bt():null),a=p.useState(t),r=a[0],s=a[1];return xs(function(){r===null&&s(Bt())},[]),p.useEffect(function(){et===!1&&(et=!0)},[]),(n=e??r)!==null&&n!==void 0?n:void 0}function ks(e){var n,t,a=A(),r=a.dir,s=a.classNames,i=a.styles,l=a.components,d=Fe().displayMonths,u=Ds(a.id?"".concat(a.id,"-").concat(e.displayIndex):void 0),m=a.id?"".concat(a.id,"-grid-").concat(e.displayIndex):void 0,f=[s.month],v=i.month,c=e.displayIndex===0,h=e.displayIndex===d.length-1,b=!c&&!h;r==="rtl"&&(n=[c,h],h=n[0],c=n[1]),c&&(f.push(s.caption_start),v=S(S({},v),i.caption_start)),h&&(f.push(s.caption_end),v=S(S({},v),i.caption_end)),b&&(f.push(s.caption_between),v=S(S({},v),i.caption_between));var g=(t=l?.Caption)!==null&&t!==void 0?t:_o;return o.jsxs("div",{className:f.join(" "),style:v,children:[o.jsx(g,{id:u,displayMonth:e.displayMonth,displayIndex:e.displayIndex}),o.jsx(bs,{id:m,"aria-labelledby":u,displayMonth:e.displayMonth})]},e.displayIndex)}function Ms(e){var n=A(),t=n.classNames,a=n.styles;return o.jsx("div",{className:t.months,style:a.months,children:e.children})}function js(e){var n,t,a=e.initialProps,r=A(),s=jt(),i=Fe(),l=p.useState(!1),d=l[0],u=l[1];p.useEffect(function(){r.initialFocus&&s.focusTarget&&(d||(s.focus(s.focusTarget),u(!0)))},[r.initialFocus,d,s.focus,s.focusTarget,s]);var m=[r.classNames.root,r.className];r.numberOfMonths>1&&m.push(r.classNames.multiple_months),r.showWeekNumber&&m.push(r.classNames.with_weeknumber);var f=S(S({},r.styles.root),r.style),v=Object.keys(a).filter(function(h){return h.startsWith("data-")}).reduce(function(h,b){var g;return S(S({},h),(g={},g[b]=a[b],g))},{}),c=(t=(n=a.components)===null||n===void 0?void 0:n.Months)!==null&&t!==void 0?t:Ms;return o.jsx("div",S({className:m.join(" "),style:f,dir:r.dir,id:r.id,nonce:a.nonce,title:a.title,lang:a.lang},v,{children:o.jsx(c,{children:i.displayMonths.map(function(h,b){return o.jsx(ks,{displayIndex:b,displayMonth:h},b)})})}))}function Ns(e){var n=e.children,t=Jr(e,["children"]);return o.jsx(vo,{initialProps:t,children:o.jsx(jo,{children:o.jsx(ss,{initialProps:t,children:o.jsx(Fo,{initialProps:t,children:o.jsx(Yo,{initialProps:t,children:o.jsx(Go,{children:o.jsx(rs,{children:n})})})})})})})}function Ss(e){return o.jsx(Ns,S({},e,{children:o.jsx(js,{initialProps:e})}))}function Vt({className:e,classNames:n,showOutsideDays:t=!0,...a}){return o.jsx(Ss,{showOutsideDays:t,className:G("p-3",e),classNames:{months:"flex flex-col sm:flex-row gap-2",month:"flex flex-col gap-4",caption:"flex justify-center pt-1 relative items-center w-full",caption_label:"text-sm font-medium",nav:"flex items-center gap-1",nav_button:G(Ct({variant:"outline"}),"size-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-x-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:G("relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-range-end)]:rounded-r-md",a.mode==="range"?"[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md":"[&:has([aria-selected])]:rounded-md"),day:G(Ct({variant:"ghost"}),"size-8 p-0 font-normal aria-selected:opacity-100"),day_range_start:"day-range-start aria-selected:bg-primary aria-selected:text-primary-foreground",day_range_end:"day-range-end aria-selected:bg-primary aria-selected:text-primary-foreground",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground aria-selected:text-muted-foreground",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...n},components:{IconLeft:({className:r,...s})=>o.jsx(Zt,{className:G("size-4",r),...s}),IconRight:({className:r,...s})=>o.jsx(Ut,{className:G("size-4",r),...s})},...a})}var St="Radio",[Cs,jn]=Kt(St),[_s,Es]=Cs(St),Nn=p.forwardRef((e,n)=>{const{__scopeRadio:t,name:a,checked:r=!1,required:s,disabled:i,value:l="on",onCheck:d,form:u,...m}=e,[f,v]=p.useState(null),c=vt(n,g=>v(g)),h=p.useRef(!1),b=f?u||!!f.closest("form"):!0;return o.jsxs(_s,{scope:t,checked:r,disabled:i,children:[o.jsx($e.button,{type:"button",role:"radio","aria-checked":r,"data-state":En(r),"data-disabled":i?"":void 0,disabled:i,value:l,...m,ref:c,onClick:ct(e.onClick,g=>{r||d?.(),b&&(h.current=g.isPropagationStopped(),h.current||g.stopPropagation())})}),b&&o.jsx(_n,{control:f,bubbles:!h.current,name:a,value:l,checked:r,required:s,disabled:i,form:u,style:{transform:"translateX(-100%)"}})]})});Nn.displayName=St;var Sn="RadioIndicator",Cn=p.forwardRef((e,n)=>{const{__scopeRadio:t,forceMount:a,...r}=e,s=Es(Sn,t);return o.jsx(ca,{present:a||s.checked,children:o.jsx($e.span,{"data-state":En(s.checked),"data-disabled":s.disabled?"":void 0,...r,ref:n})})});Cn.displayName=Sn;var Ps="RadioBubbleInput",_n=p.forwardRef(({__scopeRadio:e,control:n,checked:t,bubbles:a=!0,...r},s)=>{const i=p.useRef(null),l=vt(i,s),d=ua(t),u=fa(n);return p.useEffect(()=>{const m=i.current;if(!m)return;const f=window.HTMLInputElement.prototype,c=Object.getOwnPropertyDescriptor(f,"checked").set;if(d!==t&&c){const h=new Event("click",{bubbles:a});c.call(m,t),m.dispatchEvent(h)}},[d,t,a]),o.jsx($e.input,{type:"radio","aria-hidden":!0,defaultChecked:t,...r,tabIndex:-1,ref:l,style:{...r.style,...u,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});_n.displayName=Ps;function En(e){return e?"checked":"unchecked"}var Os=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],ze="RadioGroup",[Ts,yi]=Kt(ze,[Qt,jn]),Pn=Qt(),On=jn(),[Ws,Fs]=Ts(ze),Tn=p.forwardRef((e,n)=>{const{__scopeRadioGroup:t,name:a,defaultValue:r,value:s,required:i=!1,disabled:l=!1,orientation:d,dir:u,loop:m=!0,onValueChange:f,...v}=e,c=Pn(t),h=sa(u),[b,g]=ia({prop:s,defaultProp:r??null,onChange:f,caller:ze});return o.jsx(Ws,{scope:t,name:a,required:i,disabled:l,value:b,onValueChange:g,children:o.jsx(la,{asChild:!0,...c,orientation:d,dir:h,loop:m,children:o.jsx($e.div,{role:"radiogroup","aria-required":i,"aria-orientation":d,"data-disabled":l?"":void 0,dir:h,...v,ref:n})})})});Tn.displayName=ze;var Wn="RadioGroupItem",Fn=p.forwardRef((e,n)=>{const{__scopeRadioGroup:t,disabled:a,...r}=e,s=Fs(Wn,t),i=s.disabled||a,l=Pn(t),d=On(t),u=p.useRef(null),m=vt(n,u),f=s.value===r.value,v=p.useRef(!1);return p.useEffect(()=>{const c=b=>{Os.includes(b.key)&&(v.current=!0)},h=()=>v.current=!1;return document.addEventListener("keydown",c),document.addEventListener("keyup",h),()=>{document.removeEventListener("keydown",c),document.removeEventListener("keyup",h)}},[]),o.jsx(da,{asChild:!0,...l,focusable:!i,active:f,children:o.jsx(Nn,{disabled:i,required:s.required,checked:f,...d,...r,name:s.name,ref:m,onCheck:()=>s.onValueChange(r.value),onKeyDown:ct(c=>{c.key==="Enter"&&c.preventDefault()}),onFocus:ct(r.onFocus,()=>{v.current&&u.current?.click()})})})});Fn.displayName=Wn;var Rs="RadioGroupIndicator",Rn=p.forwardRef((e,n)=>{const{__scopeRadioGroup:t,...a}=e,r=On(t);return o.jsx(Cn,{...r,...a,ref:n})});Rn.displayName=Rs;var Is=Tn,Ys=Fn,Hs=Rn;function Ls({className:e,...n}){return o.jsx(Is,{"data-slot":"radio-group",className:G("grid gap-3",e),...n})}function As({className:e,...n}){return o.jsx(Ys,{"data-slot":"radio-group-item",className:G("border-input text-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 aspect-square size-4 shrink-0 rounded-full border shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...n,children:o.jsx(Hs,{"data-slot":"radio-group-indicator",className:"relative flex items-center justify-center",children:o.jsx(Na,{className:"fill-primary absolute top-1/2 left-1/2 size-2 -translate-x-1/2 -translate-y-1/2"})})})}function $s({event:e,isOpen:n,onClose:t,onSave:a,onDelete:r}){const[s,i]=p.useState(""),[l,d]=p.useState(""),[u,m]=p.useState(new Date),[f,v]=p.useState(new Date),[c,h]=p.useState(`${Yt}:00`),[b,g]=p.useState(`${Ht}:00`),[y,M]=p.useState(!1),[T,E]=p.useState(""),[R,W]=p.useState("sky"),[x,N]=p.useState(null),[P,k]=p.useState(!1),[Y,F]=p.useState(!1);p.useEffect(()=>{},[e]),p.useEffect(()=>{if(e){i(e.title||""),d(e.description||"");const j=new Date(e.start),B=new Date(e.end);m(j),v(B),h($(j)),g($(B)),M(e.allDay||!1),E(e.location||""),W(e.color||"sky"),N(null)}else H()},[e]);const H=()=>{i(""),d(""),m(new Date),v(new Date),h(`${Yt}:00`),g(`${Ht}:00`),M(!1),E(""),W("sky"),N(null)},$=j=>{const B=j.getHours().toString().padStart(2,"0"),U=Math.floor(j.getMinutes()/15)*15;return`${B}:${U.toString().padStart(2,"0")}`},L=p.useMemo(()=>{const j=[];for(let B=le;B<=ye;B++)for(let U=0;U<60;U+=15){const oe=B.toString().padStart(2,"0"),ce=U.toString().padStart(2,"0"),Z=`${oe}:${ce}`,Ge=new Date(2e3,0,1,B,U),$n=O(Ge,"h:mm a");j.push({value:Z,label:$n})}return j},[]),q=()=>{const j=new Date(u),B=new Date(f);if(y)j.setHours(0,0,0,0),B.setHours(23,59,59,999);else{const[oe=0,ce=0]=c.split(":").map(Number),[Z=0,Ge=0]=b.split(":").map(Number);if(oe<le||oe>ye||Z<le||Z>ye){N(`Selected time must be between ${le}:00 and ${ye}:00`);return}j.setHours(oe,ce,0),B.setHours(Z,Ge,0)}if(Ee(B,j)){N("End date cannot be before start date");return}const U=s.trim()?s:"(no title)";a({id:e?.id||"",title:U,description:l,start:j,end:B,allDay:y,location:T,color:R})},w=()=>{e?.id&&r(e.id)},D=[{value:"sky",label:"Sky",bgClass:"bg-sky-400 data-[state=checked]:bg-sky-400",borderClass:"border-sky-400 data-[state=checked]:border-sky-400"},{value:"amber",label:"Amber",bgClass:"bg-amber-400 data-[state=checked]:bg-amber-400",borderClass:"border-amber-400 data-[state=checked]:border-amber-400"},{value:"violet",label:"Violet",bgClass:"bg-violet-400 data-[state=checked]:bg-violet-400",borderClass:"border-violet-400 data-[state=checked]:border-violet-400"},{value:"rose",label:"Rose",bgClass:"bg-rose-400 data-[state=checked]:bg-rose-400",borderClass:"border-rose-400 data-[state=checked]:border-rose-400"},{value:"emerald",label:"Emerald",bgClass:"bg-emerald-400 data-[state=checked]:bg-emerald-400",borderClass:"border-emerald-400 data-[state=checked]:border-emerald-400"},{value:"orange",label:"Orange",bgClass:"bg-orange-400 data-[state=checked]:bg-orange-400",borderClass:"border-orange-400 data-[state=checked]:border-orange-400"}];return o.jsx(ha,{open:n,onOpenChange:j=>!j&&t(),children:o.jsxs(va,{className:"sm:max-w-[425px]",children:[o.jsxs(ga,{children:[o.jsx(pa,{children:e?.id?"Edit Event":"Create Event"}),o.jsx(ba,{className:"sr-only",children:e?.id?"Edit the details of this event":"Add a new event to your calendar"})]}),x&&o.jsx("div",{className:"bg-destructive/15 text-destructive rounded-md px-3 py-2 text-sm",children:x}),o.jsxs("div",{className:"grid gap-4 py-4",children:[o.jsxs("div",{className:"*:not-first:mt-1.5",children:[o.jsx(se,{htmlFor:"title",children:"Title"}),o.jsx(_t,{id:"title",value:s,onChange:j=>i(j.target.value)})]}),o.jsxs("div",{className:"*:not-first:mt-1.5",children:[o.jsx(se,{htmlFor:"description",children:"Description"}),o.jsx(xa,{id:"description",value:l,onChange:j=>d(j.target.value),rows:3})]}),o.jsxs("div",{className:"flex gap-4",children:[o.jsxs("div",{className:"flex-1 *:not-first:mt-1.5",children:[o.jsx(se,{htmlFor:"start-date",children:"Start Date"}),o.jsxs(it,{open:P,onOpenChange:k,children:[o.jsx(lt,{asChild:!0,children:o.jsxs(ne,{id:"start-date",variant:"outline",className:G("group bg-background hover:bg-background border-input w-full justify-between px-3 font-normal outline-offset-0 outline-none focus-visible:outline-[3px]",!u&&"text-muted-foreground"),children:[o.jsx("span",{className:G("truncate",!u&&"text-muted-foreground"),children:u?O(u,"PPP"):"Pick a date"}),o.jsx(Et,{size:16,className:"text-muted-foreground/80 shrink-0","aria-hidden":"true"})]})}),o.jsx(dt,{className:"w-auto p-2",align:"start",children:o.jsx(Vt,{mode:"single",selected:u,defaultMonth:u,onSelect:j=>{j&&(m(j),Ee(f,j)&&v(j),N(null),k(!1))}})})]})]}),!y&&o.jsxs("div",{className:"min-w-28 *:not-first:mt-1.5",children:[o.jsx(se,{htmlFor:"start-time",children:"Start Time"}),o.jsxs(nt,{value:c,onValueChange:h,children:[o.jsx(at,{id:"start-time",children:o.jsx(rt,{placeholder:"Select time"})}),o.jsx(ot,{children:L.map(j=>o.jsx(st,{value:j.value,children:j.label},j.value))})]})]})]}),o.jsxs("div",{className:"flex gap-4",children:[o.jsxs("div",{className:"flex-1 *:not-first:mt-1.5",children:[o.jsx(se,{htmlFor:"end-date",children:"End Date"}),o.jsxs(it,{open:Y,onOpenChange:F,children:[o.jsx(lt,{asChild:!0,children:o.jsxs(ne,{id:"end-date",variant:"outline",className:G("group bg-background hover:bg-background border-input w-full justify-between px-3 font-normal outline-offset-0 outline-none focus-visible:outline-[3px]",!f&&"text-muted-foreground"),children:[o.jsx("span",{className:G("truncate",!f&&"text-muted-foreground"),children:f?O(f,"PPP"):"Pick a date"}),o.jsx(Et,{size:16,className:"text-muted-foreground/80 shrink-0","aria-hidden":"true"})]})}),o.jsx(dt,{className:"w-auto p-2",align:"start",children:o.jsx(Vt,{mode:"single",selected:f,defaultMonth:f,disabled:{before:u},onSelect:j=>{j&&(v(j),N(null),F(!1))}})})]})]}),!y&&o.jsxs("div",{className:"min-w-28 *:not-first:mt-1.5",children:[o.jsx(se,{htmlFor:"end-time",children:"End Time"}),o.jsxs(nt,{value:b,onValueChange:g,children:[o.jsx(at,{id:"end-time",children:o.jsx(rt,{placeholder:"Select time"})}),o.jsx(ot,{children:L.map(j=>o.jsx(st,{value:j.value,children:j.label},j.value))})]})]})]}),o.jsxs("div",{className:"flex items-center gap-2",children:[o.jsx(ma,{id:"all-day",checked:y,onCheckedChange:j=>M(j===!0)}),o.jsx(se,{htmlFor:"all-day",children:"All day"})]}),o.jsxs("div",{className:"*:not-first:mt-1.5",children:[o.jsx(se,{htmlFor:"location",children:"Location"}),o.jsx(_t,{id:"location",value:T,onChange:j=>E(j.target.value)})]}),o.jsxs("fieldset",{className:"space-y-4",children:[o.jsx("legend",{className:"text-foreground text-sm leading-none font-medium",children:"Etiquette"}),o.jsx(Ls,{className:"flex gap-1.5",defaultValue:D[0]?.value,value:R,onValueChange:j=>W(j),children:D.map(j=>o.jsx(As,{id:`color-${j.value}`,value:j.value,"aria-label":j.label,className:G("size-6 shadow-none",j.bgClass,j.borderClass)},j.value))})]})]}),o.jsxs(ya,{className:"flex-row sm:justify-between",children:[e?.id&&o.jsx(ne,{variant:"outline",size:"icon",onClick:w,"aria-label":"Delete event",children:o.jsx(ra,{size:16,"aria-hidden":"true"})}),o.jsxs("div",{className:"flex flex-1 justify-end gap-2",children:[o.jsx(ne,{variant:"outline",onClick:t,children:"Cancel"}),o.jsx(ne,{onClick:q,children:"Save"})]})]})]})})}const ue=e=>O(e,_e(e)===0?"ha":"h:mma").toLowerCase();function zt({event:e,isFirstDay:n=!0,isLastDay:t=!0,isDragging:a,onClick:r,className:s,children:i,currentTime:l,dndListeners:d,dndAttributes:u,onMouseDown:m,onTouchStart:f}){const v=l?new Date(new Date(l).getTime()+(new Date(e.end).getTime()-new Date(e.start).getTime())):new Date(e.end),c=hn(v);return o.jsx("button",{className:G("focus-visible:border-ring focus-visible:ring-ring/50 flex size-full overflow-hidden px-1 text-left font-medium backdrop-blur-md transition outline-none select-none focus-visible:ring-[3px] data-dragging:cursor-grabbing data-dragging:shadow-lg data-past-event:line-through sm:px-2",Ln(e.color),ni(n,t),s),"data-dragging":a||void 0,"data-past-event":c||void 0,onClick:r,onMouseDown:m,onTouchStart:f,...d,...u,children:i})}function he({event:e,view:n,isDragging:t,onClick:a,showTime:r,currentTime:s,isFirstDay:i=!0,isLastDay:l=!0,children:d,className:u,dndListeners:m,dndAttributes:f,onMouseDown:v,onTouchStart:c}){const h=e.color,b=p.useMemo(()=>s||new Date(e.start),[s,e.start]),g=p.useMemo(()=>s?new Date(new Date(s).getTime()+(new Date(e.end).getTime()-new Date(e.start).getTime())):new Date(e.end),[s,e.start,e.end]),y=p.useMemo(()=>De(g,b),[b,g]),M=()=>e.allDay?"All day":y<45?ue(b):`${ue(b)} - ${ue(g)}`;return n==="month"?o.jsx(zt,{event:e,isFirstDay:i,isLastDay:l,isDragging:t,onClick:a,className:G("mt-[var(--event-gap)] h-[var(--event-height)] items-center text-[10px] sm:text-xs",u),currentTime:s,dndListeners:m,dndAttributes:f,onMouseDown:v,onTouchStart:c,children:d||o.jsxs("span",{className:"truncate",children:[!e.allDay&&o.jsxs("span",{className:"truncate font-normal opacity-70 sm:text-[11px]",children:[ue(b)," "]}),e.title]})}):n==="week"||n==="day"?o.jsx(zt,{event:e,isFirstDay:i,isLastDay:l,isDragging:t,onClick:a,className:G("py-1",y<45?"items-center":"flex-col",n==="week"?"text-[10px] sm:text-xs":"text-xs",u),currentTime:s,dndListeners:m,dndAttributes:f,onMouseDown:v,onTouchStart:c,children:y<45?o.jsxs("div",{className:"truncate",children:[e.title," ",r&&o.jsx("span",{className:"opacity-70",children:ue(b)})]}):o.jsxs(o.Fragment,{children:[o.jsx("div",{className:"truncate font-medium",children:e.title}),r&&o.jsx("div",{className:"truncate font-normal opacity-70 sm:text-[11px]",children:M()})]})}):o.jsxs("button",{className:G("focus-visible:border-ring focus-visible:ring-ring/50 flex w-full flex-col gap-1 rounded p-2 text-left transition outline-none focus-visible:ring-[3px] data-past-event:line-through data-past-event:opacity-90",Ln(h),u),"data-past-event":hn(new Date(e.end))||void 0,onClick:a,onMouseDown:v,onTouchStart:c,...m,...f,children:[o.jsx("div",{className:"text-sm font-medium",children:e.title}),o.jsxs("div",{className:"text-xs opacity-70",children:[e.allDay?o.jsx("span",{children:"All day"}):o.jsxs("span",{className:"uppercase",children:[ue(b)," -"," ",ue(g)]}),e.location&&o.jsxs(o.Fragment,{children:[o.jsx("span",{className:"px-1 opacity-35",children:" · "}),o.jsx("span",{children:e.location})]})]}),e.description&&o.jsx("div",{className:"my-1 text-xs opacity-90",children:e.description})]})}function Bs(e){if(typeof document>"u")return;let n=document.head||document.getElementsByTagName("head")[0],t=document.createElement("style");t.type="text/css",n.appendChild(t),t.styleSheet?t.styleSheet.cssText=e:t.appendChild(document.createTextNode(e))}Array(12).fill(0);let ft=1;class Vs{constructor(){this.subscribe=n=>(this.subscribers.push(n),()=>{const t=this.subscribers.indexOf(n);this.subscribers.splice(t,1)}),this.publish=n=>{this.subscribers.forEach(t=>t(n))},this.addToast=n=>{this.publish(n),this.toasts=[...this.toasts,n]},this.create=n=>{var t;const{message:a,...r}=n,s=typeof n?.id=="number"||((t=n.id)==null?void 0:t.length)>0?n.id:ft++,i=this.toasts.find(d=>d.id===s),l=n.dismissible===void 0?!0:n.dismissible;return this.dismissedToasts.has(s)&&this.dismissedToasts.delete(s),i?this.toasts=this.toasts.map(d=>d.id===s?(this.publish({...d,...n,id:s,title:a}),{...d,...n,id:s,dismissible:l,title:a}):d):this.addToast({title:a,...r,dismissible:l,id:s}),s},this.dismiss=n=>(n?(this.dismissedToasts.add(n),requestAnimationFrame(()=>this.subscribers.forEach(t=>t({id:n,dismiss:!0})))):this.toasts.forEach(t=>{this.subscribers.forEach(a=>a({id:t.id,dismiss:!0}))}),n),this.message=(n,t)=>this.create({...t,message:n}),this.error=(n,t)=>this.create({...t,message:n,type:"error"}),this.success=(n,t)=>this.create({...t,type:"success",message:n}),this.info=(n,t)=>this.create({...t,type:"info",message:n}),this.warning=(n,t)=>this.create({...t,type:"warning",message:n}),this.loading=(n,t)=>this.create({...t,type:"loading",message:n}),this.promise=(n,t)=>{if(!t)return;let a;t.loading!==void 0&&(a=this.create({...t,promise:n,type:"loading",message:t.loading,description:typeof t.description!="function"?t.description:void 0}));const r=Promise.resolve(n instanceof Function?n():n);let s=a!==void 0,i;const l=r.then(async u=>{if(i=["resolve",u],Me.isValidElement(u))s=!1,this.create({id:a,type:"default",message:u});else if(Gs(u)&&!u.ok){s=!1;const f=typeof t.error=="function"?await t.error(`HTTP error! status: ${u.status}`):t.error,v=typeof t.description=="function"?await t.description(`HTTP error! status: ${u.status}`):t.description,h=typeof f=="object"&&!Me.isValidElement(f)?f:{message:f};this.create({id:a,type:"error",description:v,...h})}else if(u instanceof Error){s=!1;const f=typeof t.error=="function"?await t.error(u):t.error,v=typeof t.description=="function"?await t.description(u):t.description,h=typeof f=="object"&&!Me.isValidElement(f)?f:{message:f};this.create({id:a,type:"error",description:v,...h})}else if(t.success!==void 0){s=!1;const f=typeof t.success=="function"?await t.success(u):t.success,v=typeof t.description=="function"?await t.description(u):t.description,h=typeof f=="object"&&!Me.isValidElement(f)?f:{message:f};this.create({id:a,type:"success",description:v,...h})}}).catch(async u=>{if(i=["reject",u],t.error!==void 0){s=!1;const m=typeof t.error=="function"?await t.error(u):t.error,f=typeof t.description=="function"?await t.description(u):t.description,c=typeof m=="object"&&!Me.isValidElement(m)?m:{message:m};this.create({id:a,type:"error",description:f,...c})}}).finally(()=>{s&&(this.dismiss(a),a=void 0),t.finally==null||t.finally.call(t)}),d=()=>new Promise((u,m)=>l.then(()=>i[0]==="reject"?m(i[1]):u(i[1])).catch(m));return typeof a!="string"&&typeof a!="number"?{unwrap:d}:Object.assign(a,{unwrap:d})},this.custom=(n,t)=>{const a=t?.id||ft++;return this.create({jsx:n(a),id:a,...t}),a},this.getActiveToasts=()=>this.toasts.filter(n=>!this.dismissedToasts.has(n.id)),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}}const K=new Vs,zs=(e,n)=>{const t=n?.id||ft++;return K.addToast({title:e,...n,id:t}),t},Gs=e=>e&&typeof e=="object"&&"ok"in e&&typeof e.ok=="boolean"&&"status"in e&&typeof e.status=="number",qs=zs,Xs=()=>K.toasts,Js=()=>K.getActiveToasts(),Ye=Object.assign(qs,{success:K.success,info:K.info,warning:K.warning,error:K.error,custom:K.custom,message:K.message,promise:K.promise,dismiss:K.dismiss,loading:K.loading},{getHistory:Xs,getToasts:Js});Bs("[data-sonner-toaster][dir=ltr],html[dir=ltr]{--toast-icon-margin-start:-3px;--toast-icon-margin-end:4px;--toast-svg-margin-start:-1px;--toast-svg-margin-end:0px;--toast-button-margin-start:auto;--toast-button-margin-end:0;--toast-close-button-start:0;--toast-close-button-end:unset;--toast-close-button-transform:translate(-35%, -35%)}[data-sonner-toaster][dir=rtl],html[dir=rtl]{--toast-icon-margin-start:4px;--toast-icon-margin-end:-3px;--toast-svg-margin-start:0px;--toast-svg-margin-end:-1px;--toast-button-margin-start:0;--toast-button-margin-end:auto;--toast-close-button-start:unset;--toast-close-button-end:0;--toast-close-button-transform:translate(35%, -35%)}[data-sonner-toaster]{position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1:hsl(0, 0%, 99%);--gray2:hsl(0, 0%, 97.3%);--gray3:hsl(0, 0%, 95.1%);--gray4:hsl(0, 0%, 93%);--gray5:hsl(0, 0%, 90.9%);--gray6:hsl(0, 0%, 88.7%);--gray7:hsl(0, 0%, 85.8%);--gray8:hsl(0, 0%, 78%);--gray9:hsl(0, 0%, 56.1%);--gray10:hsl(0, 0%, 52.3%);--gray11:hsl(0, 0%, 43.5%);--gray12:hsl(0, 0%, 9%);--border-radius:8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:0;z-index:999999999;transition:transform .4s ease}@media (hover:none) and (pointer:coarse){[data-sonner-toaster][data-lifted=true]{transform:none}}[data-sonner-toaster][data-x-position=right]{right:var(--offset-right)}[data-sonner-toaster][data-x-position=left]{left:var(--offset-left)}[data-sonner-toaster][data-x-position=center]{left:50%;transform:translateX(-50%)}[data-sonner-toaster][data-y-position=top]{top:var(--offset-top)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--offset-bottom)}[data-sonner-toast]{--y:translateY(100%);--lift-amount:calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:0;overflow-wrap:anywhere}[data-sonner-toast][data-styled=true]{padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px rgba(0,0,0,.1);width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}[data-sonner-toast]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-y-position=top]{top:0;--y:translateY(-100%);--lift:1;--lift-amount:calc(1 * var(--gap))}[data-sonner-toast][data-y-position=bottom]{bottom:0;--y:translateY(100%);--lift:-1;--lift-amount:calc(var(--lift) * var(--gap))}[data-sonner-toast][data-styled=true] [data-description]{font-weight:400;line-height:1.4;color:#3f3f3f}[data-rich-colors=true][data-sonner-toast][data-styled=true] [data-description]{color:inherit}[data-sonner-toaster][data-sonner-theme=dark] [data-description]{color:#e8e8e8}[data-sonner-toast][data-styled=true] [data-title]{font-weight:500;line-height:1.5;color:inherit}[data-sonner-toast][data-styled=true] [data-icon]{display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}[data-sonner-toast][data-promise=true] [data-icon]>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}[data-sonner-toast][data-styled=true] [data-icon]>*{flex-shrink:0}[data-sonner-toast][data-styled=true] [data-icon] svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}[data-sonner-toast][data-styled=true] [data-content]{display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;font-weight:500;cursor:pointer;outline:0;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}[data-sonner-toast][data-styled=true] [data-button]:focus-visible{box-shadow:0 0 0 2px rgba(0,0,0,.4)}[data-sonner-toast][data-styled=true] [data-button]:first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}[data-sonner-toast][data-styled=true] [data-cancel]{color:var(--normal-text);background:rgba(0,0,0,.08)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-styled=true] [data-cancel]{background:rgba(255,255,255,.3)}[data-sonner-toast][data-styled=true] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);background:var(--normal-bg);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast][data-styled=true] [data-close-button]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-styled=true] [data-disabled=true]{cursor:not-allowed}[data-sonner-toast][data-styled=true]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}[data-sonner-toast][data-swiping=true]::before{content:'';position:absolute;left:-100%;right:-100%;height:100%;z-index:-1}[data-sonner-toast][data-y-position=top][data-swiping=true]::before{bottom:50%;transform:scaleY(3) translateY(50%)}[data-sonner-toast][data-y-position=bottom][data-swiping=true]::before{top:50%;transform:scaleY(3) translateY(-50%)}[data-sonner-toast][data-swiping=false][data-removed=true]::before{content:'';position:absolute;inset:0;transform:scaleY(2)}[data-sonner-toast][data-expanded=true]::after{content:'';position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}[data-sonner-toast][data-mounted=true]{--y:translateY(0);opacity:1}[data-sonner-toast][data-expanded=false][data-front=false]{--scale:var(--toasts-before) * 0.05 + 1;--y:translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}[data-sonner-toast]>*{transition:opacity .4s}[data-sonner-toast][data-x-position=right]{right:0}[data-sonner-toast][data-x-position=left]{left:0}[data-sonner-toast][data-expanded=false][data-front=false][data-styled=true]>*{opacity:0}[data-sonner-toast][data-visible=false]{opacity:0;pointer-events:none}[data-sonner-toast][data-mounted=true][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}[data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]{--y:translateY(calc(var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]{--y:translateY(40%);opacity:0;transition:transform .5s,opacity .2s}[data-sonner-toast][data-removed=true][data-front=false]::before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y,0)) translateX(var(--swipe-amount-x,0));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width:600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-sonner-theme=light]{--normal-bg:#fff;--normal-border:var(--gray4);--normal-text:var(--gray12);--success-bg:hsl(143, 85%, 96%);--success-border:hsl(145, 92%, 87%);--success-text:hsl(140, 100%, 27%);--info-bg:hsl(208, 100%, 97%);--info-border:hsl(221, 91%, 93%);--info-text:hsl(210, 92%, 45%);--warning-bg:hsl(49, 100%, 97%);--warning-border:hsl(49, 91%, 84%);--warning-text:hsl(31, 92%, 45%);--error-bg:hsl(359, 100%, 97%);--error-border:hsl(359, 100%, 94%);--error-text:hsl(360, 100%, 45%)}[data-sonner-toaster][data-sonner-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg:#000;--normal-border:hsl(0, 0%, 20%);--normal-text:var(--gray1)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg:#fff;--normal-border:var(--gray3);--normal-text:var(--gray12)}[data-sonner-toaster][data-sonner-theme=dark]{--normal-bg:#000;--normal-bg-hover:hsl(0, 0%, 12%);--normal-border:hsl(0, 0%, 20%);--normal-border-hover:hsl(0, 0%, 25%);--normal-text:var(--gray1);--success-bg:hsl(150, 100%, 6%);--success-border:hsl(147, 100%, 12%);--success-text:hsl(150, 86%, 65%);--info-bg:hsl(215, 100%, 6%);--info-border:hsl(223, 43%, 17%);--info-text:hsl(216, 87%, 65%);--warning-bg:hsl(64, 100%, 6%);--warning-border:hsl(60, 100%, 9%);--warning-text:hsl(46, 87%, 65%);--error-bg:hsl(358, 76%, 10%);--error-border:hsl(357, 89%, 16%);--error-text:hsl(358, 100%, 81%)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size:16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:first-child{animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}100%{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}100%{opacity:.15}}@media (prefers-reduced-motion){.sonner-loading-bar,[data-sonner-toast],[data-sonner-toast]>*{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}");function Us({events:e=[],onEventAdd:n,onEventUpdate:t,onEventDelete:a,className:r,initialView:s="month"}){const[i,l]=p.useState(new Date),[d,u]=p.useState(s),[m,f]=p.useState(!1),[v,c]=p.useState(null);p.useEffect(()=>{const x=N=>{if(!(m||N.target instanceof HTMLInputElement||N.target instanceof HTMLTextAreaElement||N.target instanceof HTMLElement&&N.target.isContentEditable))switch(N.key.toLowerCase()){case"m":u("month");break;case"w":u("week");break;case"d":u("day");break;case"a":u("agenda");break}};return window.addEventListener("keydown",x),()=>{window.removeEventListener("keydown",x)}},[m]);const h=()=>{d==="month"?l(zr(i)):d==="week"?l(Gr(i)):d==="day"?l(z(i,-1)):d==="agenda"&&l(z(i,-30))},b=()=>{d==="month"?l(Q(i,1)):d==="week"?l(Se(i,1)):d==="day"?l(z(i,1)):d==="agenda"&&l(z(i,ht))},g=()=>{l(new Date)},y=x=>{c(x),f(!0)},M=x=>{const N=x.getMinutes(),P=N%15;P!==0&&(P<7.5?x.setMinutes(N-P):x.setMinutes(N+(15-P)),x.setSeconds(0),x.setMilliseconds(0));const k={id:"",title:"",start:x,end:si(x,1),allDay:!1};c(k),f(!0)},T=x=>{x.id?(t?.(x),Ye(`Event "${x.title}" updated`,{description:O(new Date(x.start),"MMM d, yyyy"),position:"bottom-left"})):(n?.({...x,id:Math.random().toString(36).substring(2,11)}),Ye(`Event "${x.title}" added`,{description:O(new Date(x.start),"MMM d, yyyy"),position:"bottom-left"})),f(!1),c(null)},E=x=>{const N=e.find(P=>P.id===x);a?.(x),f(!1),c(null),N&&Ye(`Event "${N.title}" deleted`,{description:O(new Date(N.start),"MMM d, yyyy"),position:"bottom-left"})},R=x=>{t?.(x),Ye(`Event "${x.title}" moved`,{description:O(new Date(x.start),"MMM d, yyyy"),position:"bottom-left"})},W=p.useMemo(()=>{if(d==="month")return O(i,"MMMM yyyy");if(d==="week"){const x=X(i,{weekStartsOn:0}),N=ge(i,{weekStartsOn:0});return ke(x,N)?O(x,"MMMM yyyy"):`${O(x,"MMM")} - ${O(N,"MMM yyyy")}`}else{if(d==="day")return o.jsxs(o.Fragment,{children:[o.jsx("span",{className:"min-[480px]:hidden","aria-hidden":"true",children:O(i,"MMM d, yyyy")}),o.jsx("span",{className:"max-[479px]:hidden min-md:hidden","aria-hidden":"true",children:O(i,"MMMM d, yyyy")}),o.jsx("span",{className:"max-md:hidden",children:O(i,"EEE MMMM d, yyyy")})]});if(d==="agenda"){const x=i,N=z(i,ht-1);return ke(x,N)?O(x,"MMMM yyyy"):`${O(x,"MMM")} - ${O(N,"MMM yyyy")}`}else return O(i,"MMMM yyyy")}},[i,d]);return o.jsx("div",{className:"flex flex-col rounded-lg border has-data-[slot=month-view]:flex-1",style:{"--event-height":`${mt}px`,"--event-gap":`${In}px`,"--week-cells-height":`${Pe}px`},children:o.jsxs(ti,{onEventUpdate:R,children:[o.jsxs("div",{className:G("flex items-center justify-between p-2 sm:p-4",r),children:[o.jsxs("div",{className:"flex items-center gap-1 sm:gap-4",children:[o.jsxs(ne,{variant:"outline",className:"aspect-square max-[479px]:p-0!",onClick:g,children:[o.jsx(oa,{className:"min-[480px]:hidden",size:16,"aria-hidden":"true"}),o.jsx("span",{className:"max-[479px]:sr-only",children:"Today"})]}),o.jsxs("div",{className:"flex items-center sm:gap-2",children:[o.jsx(ne,{variant:"ghost",size:"icon",onClick:h,"aria-label":"Previous",children:o.jsx(Zt,{size:16,"aria-hidden":"true"})}),o.jsx(ne,{variant:"ghost",size:"icon",onClick:b,"aria-label":"Next",children:o.jsx(Ut,{size:16,"aria-hidden":"true"})})]}),o.jsx("h2",{className:"text-sm font-semibold sm:text-lg md:text-xl",children:W})]}),o.jsxs("div",{className:"flex items-center gap-2",children:[o.jsxs(Kn,{children:[o.jsx(Zn,{asChild:!0,children:o.jsxs(ne,{variant:"outline",className:"gap-1.5 max-[479px]:h-8",children:[o.jsxs("span",{children:[o.jsx("span",{className:"min-[480px]:hidden","aria-hidden":"true",children:d.charAt(0).toUpperCase()}),o.jsx("span",{className:"max-[479px]:sr-only",children:d.charAt(0).toUpperCase()+d.slice(1)})]}),o.jsx(ea,{className:"-me-1 opacity-60",size:16,"aria-hidden":"true"})]})}),o.jsxs(ta,{align:"end",className:"min-w-32",children:[o.jsxs(Re,{onClick:()=>u("month"),children:["Month ",o.jsx(Ie,{children:"M"})]}),o.jsxs(Re,{onClick:()=>u("week"),children:["Week ",o.jsx(Ie,{children:"W"})]}),o.jsxs(Re,{onClick:()=>u("day"),children:["Day ",o.jsx(Ie,{children:"D"})]}),o.jsxs(Re,{onClick:()=>u("agenda"),children:["Agenda ",o.jsx(Ie,{children:"A"})]})]})]}),o.jsxs(ne,{className:"aspect-square max-[479px]:p-0!",onClick:()=>{c(null),f(!0)},children:[o.jsx(Ca,{className:"opacity-60 sm:-ms-1",size:16,"aria-hidden":"true"}),o.jsx("span",{className:"max-sm:sr-only",children:"New event"})]})]})]}),o.jsxs("div",{className:"flex flex-1 flex-col",children:[d==="month"&&o.jsx(Zs,{currentDate:i,events:e,onEventSelect:y,onEventCreate:M}),d==="week"&&o.jsx(ei,{currentDate:i,events:e,onEventSelect:y,onEventCreate:M}),d==="day"&&o.jsx(Xr,{currentDate:i,events:e,onEventSelect:y,onEventCreate:M}),d==="agenda"&&o.jsx(qr,{currentDate:i,events:e,onEventSelect:y})]}),o.jsx($s,{event:v,isOpen:m,onClose:()=>{f(!1),c(null)},onSave:T,onDelete:E})]})})}const mt=24,In=4,Pe=64,ht=30,Gt=0,Qs=24,Ks=9;function Zs({currentDate:e,events:n,onEventSelect:t,onEventCreate:a}){const r=p.useMemo(()=>{const v=J(e),c=Be(v),h=X(v,{weekStartsOn:0}),b=ge(c,{weekStartsOn:0});return rn({start:h,end:b})},[e]),s=p.useMemo(()=>Array.from({length:7}).map((v,c)=>{const h=z(X(new Date),c);return O(h,"EEE")}),[]),i=p.useMemo(()=>{const v=[];let c=[];for(let h=0;h<r.length;h++)c.push(r[h]),(c.length===7||h===r.length-1)&&(v.push(c),c=[]);return v},[r]),l=(v,c)=>{c.stopPropagation(),t(v)},[d,u]=p.useState(!1),{contentRef:m,getVisibleEventCount:f}=ii({eventHeight:mt,eventGap:In});return p.useEffect(()=>{u(!0)},[]),o.jsxs("div",{"data-slot":"month-view",className:"contents",children:[o.jsx("div",{className:"border-border/70 grid grid-cols-7 border-b",children:s.map(v=>o.jsx("div",{className:"text-muted-foreground/70 py-2 text-center text-sm",children:v},v))}),o.jsx("div",{className:"grid flex-1 auto-rows-fr",children:i.map((v,c)=>o.jsx("div",{className:"grid grid-cols-7 [&:last-child>*]:border-b-0",children:v.map((h,b)=>{if(!h)return null;const g=ai(n,h),y=ri(n,h),M=ke(h,e),T=`month-cell-${h.toISOString()}`,E=[...y,...g],R=oi(n,h),W=c===0&&b===0,x=d?f(E.length):void 0,N=x!==void 0&&E.length>x,P=N?E.length-x:0;return o.jsx("div",{className:"group border-border/70 data-outside-cell:bg-muted/25 data-outside-cell:text-muted-foreground/70 border-r border-b last:border-r-0","data-today":be(h)||void 0,"data-outside-cell":!M||void 0,children:o.jsxs(yt,{id:T,date:h,onClick:()=>{const k=new Date(h);k.setHours(Ks,0,0),a(k)},children:[o.jsx("div",{className:"group-data-today:bg-primary group-data-today:text-primary-foreground mt-1 inline-flex size-6 items-center justify-center rounded-full text-sm",children:O(h,"d")}),o.jsxs("div",{ref:W?m:null,className:"min-h-[calc((var(--event-height)+var(--event-gap))*2)] sm:min-h-[calc((var(--event-height)+var(--event-gap))*3)] lg:min-h-[calc((var(--event-height)+var(--event-gap))*4)]",children:[qt(E).map((k,Y)=>{const F=new Date(k.start),H=new Date(k.end),$=C(h,F),L=C(h,H),q=d&&x&&Y>=x;return x?$?o.jsx("div",{className:"aria-hidden:hidden","aria-hidden":q?"true":void 0,children:o.jsx(bt,{event:k,view:"month",onClick:w=>l(k,w),isFirstDay:$,isLastDay:L})},k.id):o.jsx("div",{className:"aria-hidden:hidden","aria-hidden":q?"true":void 0,children:o.jsx(he,{onClick:w=>l(k,w),event:k,view:"month",isFirstDay:$,isLastDay:L,children:o.jsxs("div",{className:"invisible","aria-hidden":!0,children:[!k.allDay&&o.jsxs("span",{children:[O(new Date(k.start),"h:mm")," "]}),k.title]})})},`spanning-${k.id}-${h.toISOString().slice(0,10)}`):null}),N&&o.jsxs(it,{modal:!0,children:[o.jsx(lt,{asChild:!0,children:o.jsx("button",{className:"focus-visible:border-ring focus-visible:ring-ring/50 text-muted-foreground hover:text-foreground hover:bg-muted/50 mt-[var(--event-gap)] flex h-[var(--event-height)] w-full items-center overflow-hidden px-1 text-left text-[10px] backdrop-blur-md transition outline-none select-none focus-visible:ring-[3px] sm:px-2 sm:text-xs",onClick:k=>k.stopPropagation(),children:o.jsxs("span",{children:["+ ",P," ",o.jsx("span",{className:"max-sm:sr-only",children:"more"})]})})}),o.jsx(dt,{align:"center",className:"max-w-52 p-3",style:{"--event-height":`${mt}px`},children:o.jsxs("div",{className:"space-y-2",children:[o.jsx("div",{className:"text-sm font-medium",children:O(h,"EEE d")}),o.jsx("div",{className:"space-y-1",children:qt(R).map(k=>{const Y=new Date(k.start),F=new Date(k.end),H=C(h,Y),$=C(h,F);return o.jsx(he,{onClick:L=>l(k,L),event:k,view:"month",isFirstDay:H,isLastDay:$},k.id)})})]})})]})]})]})},h.toString())})},`week-${c}`))})]})}function ei({currentDate:e,events:n,onEventSelect:t,onEventCreate:a}){const r=p.useMemo(()=>{const c=X(e,{weekStartsOn:0}),h=ge(e,{weekStartsOn:0});return rn({start:c,end:h})},[e]),s=p.useMemo(()=>X(e,{weekStartsOn:0}),[e]),i=p.useMemo(()=>{const c=ae(e);return on({start:xe(c,Gt),end:xe(c,Qs-1)})},[e]),l=p.useMemo(()=>n.filter(c=>c.allDay||ve(c)).filter(c=>{const h=new Date(c.start),b=new Date(c.end);return r.some(g=>C(g,h)||C(g,b)||g>h&&g<b)}),[n,r]),d=p.useMemo(()=>r.map(h=>{const g=[...n.filter(E=>{if(E.allDay||ve(E))return!1;const R=new Date(E.start),W=new Date(E.end);return C(h,R)||C(h,W)||R<h&&W>h})].sort((E,R)=>{const W=new Date(E.start),x=new Date(R.start),N=new Date(E.end),P=new Date(R.end);if(W<x)return-1;if(W>x)return 1;const k=De(N,W);return De(P,x)-k}),y=[],M=ae(h),T=[];return g.forEach(E=>{const R=new Date(E.start),W=new Date(E.end),x=C(h,R)?R:M,N=C(h,W)?W:xe(M,24),P=we(x)+_e(x)/60,k=we(N)+_e(N)/60,Y=(P-Gt)*Pe,F=(k-P)*Pe;let H=0,$=!1;for(;!$;){const D=T[H]||[];D.length===0?(T[H]=D,$=!0):D.some(B=>nn({start:x,end:N},{start:new Date(B.event.start),end:new Date(B.event.end)}))?H++:$=!0}const L=T[H]||[];T[H]=L,L.push({event:E,end:N});const q=H===0?1:.9,w=H===0?0:H*.1;y.push({event:E,top:Y,height:F,left:w,width:q,zIndex:10+H})}),y}),[r,n]),u=(c,h)=>{h.stopPropagation(),t(c)},m=l.length>0,{currentTimePosition:f,currentTimeVisible:v}=An(e,"week");return o.jsxs("div",{"data-slot":"week-view",className:"flex h-full flex-col",children:[o.jsxs("div",{className:"bg-background/80 border-border/70 sticky top-0 z-30 grid grid-cols-8 border-b backdrop-blur-md",children:[o.jsx("div",{className:"text-muted-foreground/70 py-2 text-center text-sm",children:o.jsx("span",{className:"max-[479px]:sr-only",children:O(new Date,"O")})}),r.map(c=>o.jsxs("div",{className:"data-today:text-foreground text-muted-foreground/70 py-2 text-center text-sm data-today:font-medium","data-today":be(c)||void 0,children:[o.jsxs("span",{className:"sm:hidden","aria-hidden":"true",children:[O(c,"E")[0]," ",O(c,"d")]}),o.jsx("span",{className:"max-sm:hidden",children:O(c,"EEE dd")})]},c.toString()))]}),m&&o.jsx("div",{className:"border-border/70 bg-muted/50 border-b",children:o.jsxs("div",{className:"grid grid-cols-8",children:[o.jsx("div",{className:"border-border/70 relative border-r",children:o.jsx("span",{className:"text-muted-foreground/70 absolute bottom-0 left-0 h-6 w-16 max-w-full pe-2 text-right text-[10px] sm:pe-4 sm:text-xs",children:"All day"})}),r.map((c,h)=>{const b=l.filter(g=>{const y=new Date(g.start),M=new Date(g.end);return C(c,y)||c>y&&c<M||C(c,M)});return o.jsx("div",{className:"border-border/70 relative border-r p-1 last:border-r-0","data-today":be(c)||void 0,children:b.map(g=>{const y=new Date(g.start),M=new Date(g.end),T=C(c,y),E=C(c,M),R=h===0&&Ee(y,s),W=T||R;return o.jsx(he,{onClick:x=>u(g,x),event:g,view:"month",isFirstDay:T,isLastDay:E,children:o.jsx("div",{className:G("truncate",!W&&"invisible"),"aria-hidden":!W,children:g.title})},`spanning-${g.id}`)})},c.toString())})]})}),o.jsxs("div",{className:"grid flex-1 grid-cols-8 overflow-hidden",children:[o.jsx("div",{className:"border-border/70 grid auto-cols-fr border-r",children:i.map((c,h)=>o.jsx("div",{className:"border-border/70 relative min-h-[var(--week-cells-height)] border-b last:border-b-0",children:h>0&&o.jsx("span",{className:"bg-background text-muted-foreground/70 absolute -top-3 left-0 flex h-6 w-16 max-w-full items-center justify-end pe-2 text-[10px] sm:pe-4 sm:text-xs",children:O(c,"h a")})},c.toString()))}),r.map((c,h)=>o.jsxs("div",{className:"border-border/70 relative grid auto-cols-fr border-r last:border-r-0","data-today":be(c)||void 0,children:[(d[h]??[]).map(b=>o.jsx("div",{className:"absolute z-10 px-0.5",style:{top:`${b.top}px`,height:`${b.height}px`,left:`${b.left*100}%`,width:`${b.width*100}%`,zIndex:b.zIndex},onClick:g=>g.stopPropagation(),children:o.jsx("div",{className:"size-full",children:o.jsx(bt,{event:b.event,view:"week",onClick:g=>u(b.event,g),showTime:!0,height:b.height})})},b.event.id)),v&&be(c)&&o.jsx("div",{className:"pointer-events-none absolute right-0 left-0 z-20",style:{top:`${f}%`},children:o.jsxs("div",{className:"relative flex items-center",children:[o.jsx("div",{className:"bg-primary absolute -left-1 h-2 w-2 rounded-full"}),o.jsx("div",{className:"bg-primary h-[2px] w-full"})]})}),i.map(b=>{const g=we(b);return o.jsx("div",{className:"border-border/70 relative min-h-[var(--week-cells-height)] border-b last:border-b-0",children:[0,1,2,3].map(y=>{const M=g+y*.25;return o.jsx(yt,{id:`week-cell-${c.toISOString()}-${M}`,date:c,time:M,className:G("absolute h-[calc(var(--week-cells-height)/4)] w-full",y===0&&"top-0",y===1&&"top-[calc(var(--week-cells-height)/4)]",y===2&&"top-[calc(var(--week-cells-height)/4*2)]",y===3&&"top-[calc(var(--week-cells-height)/4*3)]"),onClick:()=>{const T=new Date(c);T.setHours(g),T.setMinutes(y*15),a(T)}},`${b.toString()}-${y}`)})},b.toString())})]},c.toString()))]})]})}const Yn=p.createContext({activeEvent:null,activeId:null,activeView:null,currentTime:null,eventHeight:null,isMultiDay:!1,multiDayWidth:null,dragHandlePosition:null}),Hn=()=>p.useContext(Yn);function ti({children:e,onEventUpdate:n}){const[t,a]=p.useState(null),[r,s]=p.useState(null),[i,l]=p.useState(null),[d,u]=p.useState(null),[m,f]=p.useState(null),[v,c]=p.useState(!1),[h,b]=p.useState(null),[g,y]=p.useState(null),M=p.useRef({height:0}),T=Gn(qe(Qn,{activationConstraint:{distance:5}}),qe(Un,{activationConstraint:{delay:250,tolerance:5}}),qe(Jn,{activationConstraint:{distance:5}})),E=p.useId(),R=N=>{const{active:P}=N;if(!P.data.current)return;const{event:k,view:Y,height:F,isMultiDay:H,multiDayWidth:$,dragHandlePosition:L}=P.data.current;a(k),s(P.id),l(Y),u(new Date(k.start)),c(H||!1),b($||null),y(L||null),F&&(M.current.height=F,f(F))},W=N=>{const{over:P}=N;if(P&&t&&P.data.current){const{date:k,time:Y}=P.data.current;if(Y!==void 0&&i!=="month"){const F=new Date(k),H=Math.floor(Y),$=Y-H;let L=0;$<.125?L=0:$<.375?L=15:$<.625?L=30:L=45,F.setHours(H,L,0,0),(!d||F.getHours()!==d.getHours()||F.getMinutes()!==d.getMinutes()||F.getDate()!==d.getDate()||F.getMonth()!==d.getMonth()||F.getFullYear()!==d.getFullYear())&&u(F)}else if(i==="month"){const F=new Date(k);d&&F.setHours(d.getHours(),d.getMinutes(),d.getSeconds(),d.getMilliseconds()),(!d||F.getDate()!==d.getDate()||F.getMonth()!==d.getMonth()||F.getFullYear()!==d.getFullYear())&&u(F)}}},x=N=>{const{active:P,over:k}=N;if(!k||!t||!d){a(null),s(null),l(null),u(null),f(null),c(!1),b(null),y(null);return}try{if(!P.data.current||!k.data.current)throw new Error("Missing data in drag event");const Y=P.data.current,F=k.data.current;if(!Y.event||!F.date)throw new Error("Missing required event data");const H=Y.event,$=F.date,L=F.time,q=new Date($);if(L!==void 0){const oe=Math.floor(L),ce=L-oe;let Z=0;ce<.125?Z=0:ce<.375?Z=15:ce<.625?Z=30:Z=45,q.setHours(oe,Z,0,0)}else q.setHours(d.getHours(),d.getMinutes(),d.getSeconds(),d.getMilliseconds());const w=new Date(H.start),D=new Date(H.end),j=De(D,w),B=Wa(q,j);(w.getFullYear()!==q.getFullYear()||w.getMonth()!==q.getMonth()||w.getDate()!==q.getDate()||w.getHours()!==q.getHours()||w.getMinutes()!==q.getMinutes())&&n({...H,start:q,end:B})}catch{}finally{a(null),s(null),l(null),u(null),f(null),c(!1),b(null),y(null)}};return o.jsx(qn,{id:E,sensors:T,onDragStart:R,onDragOver:W,onDragEnd:x,children:o.jsxs(Yn.Provider,{value:{activeEvent:t,activeId:r,activeView:i,currentTime:d,eventHeight:m,isMultiDay:v,multiDayWidth:h,dragHandlePosition:g},children:[e,o.jsx(Xn,{adjustScale:!1,dropAnimation:null,children:t&&i&&o.jsx("div",{style:{height:m?`${m}px`:"auto",width:v&&h?`${h}%`:"100%"},children:o.jsx(he,{event:t,view:i,isDragging:!0,showTime:i!=="month",currentTime:d||void 0,isFirstDay:g?.data?.isFirstDay!==!1,isLastDay:g?.data?.isLastDay!==!1})})})]})})}function Ln(e){switch(e||"sky"){case"sky":return"bg-sky-200/50 hover:bg-sky-200/40 text-sky-950/80 dark:bg-sky-400/25 dark:hover:bg-sky-400/20 dark:text-sky-200 shadow-sky-700/8";case"amber":return"bg-amber-200/50 hover:bg-amber-200/40 text-amber-950/80 dark:bg-amber-400/25 dark:hover:bg-amber-400/20 dark:text-amber-200 shadow-amber-700/8";case"violet":return"bg-violet-200/50 hover:bg-violet-200/40 text-violet-950/80 dark:bg-violet-400/25 dark:hover:bg-violet-400/20 dark:text-violet-200 shadow-violet-700/8";case"rose":return"bg-rose-200/50 hover:bg-rose-200/40 text-rose-950/80 dark:bg-rose-400/25 dark:hover:bg-rose-400/20 dark:text-rose-200 shadow-rose-700/8";case"emerald":return"bg-emerald-200/50 hover:bg-emerald-200/40 text-emerald-950/80 dark:bg-emerald-400/25 dark:hover:bg-emerald-400/20 dark:text-emerald-200 shadow-emerald-700/8";case"orange":return"bg-orange-200/50 hover:bg-orange-200/40 text-orange-950/80 dark:bg-orange-400/25 dark:hover:bg-orange-400/20 dark:text-orange-200 shadow-orange-700/8";default:return"bg-sky-200/50 hover:bg-sky-200/40 text-sky-950/80 dark:bg-sky-400/25 dark:hover:bg-sky-400/20 dark:text-sky-200 shadow-sky-700/8"}}function ni(e,n){return e&&n?"rounded":e?"rounded-l rounded-r-none":n?"rounded-r rounded-l-none":"rounded-none"}function ve(e){const n=new Date(e.start),t=new Date(e.end);return e.allDay||n.getDate()!==t.getDate()}function ai(e,n){return e.filter(t=>{const a=new Date(t.start);return C(n,a)}).sort((t,a)=>new Date(t.start).getTime()-new Date(a.start).getTime())}function qt(e){return[...e].sort((n,t)=>{const a=ve(n),r=ve(t);return a&&!r?-1:!a&&r?1:new Date(n.start).getTime()-new Date(t.start).getTime()})}function ri(e,n){return e.filter(t=>{if(!ve(t))return!1;const a=new Date(t.start),r=new Date(t.end);return!C(n,a)&&(C(n,r)||n>a&&n<r)})}function oi(e,n){return e.filter(t=>{const a=new Date(t.start),r=new Date(t.end);return C(n,a)||C(n,r)||n>a&&n<r})}function Xt(e,n){return e.filter(t=>{const a=new Date(t.start),r=new Date(t.end);return C(n,a)||C(n,r)||n>a&&n<r}).sort((t,a)=>new Date(t.start).getTime()-new Date(a.start).getTime())}function si(e,n){const t=new Date(e);return t.setHours(t.getHours()+n),t}function An(e,n){const[t,a]=p.useState(0),[r,s]=p.useState(!1);return p.useEffect(()=>{const i=()=>{const d=new Date,u=d.getHours(),m=d.getMinutes(),f=(u-le)*60+m,v=0,c=(ye-le)*60,h=(f-v)/(c-v)*100;let b=!1;if(n==="day")b=C(d,e);else if(n==="week"){const g=X(e,{weekStartsOn:0}),y=ge(e,{weekStartsOn:0});b=Vr(d,{start:g,end:y})}a(h),s(b)};i();const l=setInterval(i,6e4);return()=>clearInterval(l)},[e,n]),{currentTimePosition:t,currentTimeVisible:r}}function ii({eventHeight:e,eventGap:n}){const t=p.useRef(null),a=p.useRef(null),[r,s]=p.useState(null);p.useLayoutEffect(()=>{if(!t.current)return;const l=()=>{t.current&&s(t.current.clientHeight)};return l(),a.current||(a.current=new ResizeObserver(()=>{l()})),a.current.observe(t.current),()=>{a.current&&a.current.disconnect()}},[]);const i=p.useMemo(()=>l=>{if(!r)return l;const d=Math.floor(r/(e+n));return l<=d?l:d>0?d-1:0},[r,e,n]);return{contentRef:t,contentHeight:r,getVisibleEventCount:i}}const tt=[{name:"Jetty A",events:[{id:"1",title:"Vessel A - Docking",start:new Date(2025,4,10,8,0),end:new Date(2025,4,10,12,0),color:"sky",location:"Jetty A"},{id:"2",title:"Maintenance",start:new Date(2025,4,11,14,0),end:new Date(2025,4,11,17,0),color:"rose",location:"Jetty A"},{id:"6",title:"Annual Inspection",start:new Date(2025,4,15),end:new Date(2025,4,16),allDay:!0,color:"orange",location:"Jetty A"},{id:"7",title:"Vessel D - Loading",start:new Date(2025,4,18,9,30),end:new Date(2025,4,18,16,0),color:"emerald",location:"Jetty A"}]},{name:"Jetty B",events:[{id:"3",title:"Vessel B - Loading",start:new Date(2025,4,12,9,0),end:new Date(2025,4,12,18,0),color:"amber",location:"Jetty B"},{id:"4",title:"Inspection",start:new Date(2025,4,13,10,0),end:new Date(2025,4,13,11,0),color:"violet",location:"Jetty B"},{id:"8",title:"Emergency Repair",start:new Date(2025,4,20,7,0),end:new Date(2025,4,20,20,0),color:"rose",location:"Jetty B"},{id:"9",title:"Vessel E - Unloading",start:new Date(2025,4,22,11,0),end:new Date(2025,4,23,10,0),color:"sky",location:"Jetty B"}]},{name:"Jetty C",events:[{id:"5",title:"Vessel C - Unloading",start:new Date(2025,4,14,7,0),end:new Date(2025,4,14,16,0),color:"emerald",location:"Jetty C"},{id:"10",title:"Routine Cleaning",start:new Date(2025,4,25,8,0),end:new Date(2025,4,25,12,0),color:"violet",location:"Jetty C"},{id:"11",title:"Vessel F - Docking",start:new Date(2025,4,28,6,0),end:new Date(2025,4,29,14,0),color:"amber",location:"Jetty C"}]}];function xi(){const[e,n]=p.useState(tt[0]?.name||null),[t,a]=p.useState([]),r=l=>{a(d=>[...d,l])},s=l=>{a(d=>d.map(u=>u.id===l.id?l:u))},i=l=>{a(d=>d.filter(u=>u.id!==l))};return p.useEffect(()=>{if(e){const l=tt.find(d=>d.name===e);a(l?l.events:[])}else a([])},[e]),o.jsx(na,{children:o.jsx("div",{className:"container mx-auto p-4",children:o.jsxs(wa,{children:[o.jsx(Da,{children:o.jsx(ka,{className:"text-2xl font-bold",children:"Jetty Schedule"})}),o.jsxs(Ma,{children:[o.jsxs("div",{className:"mb-4 flex items-center gap-4",children:[o.jsx("label",{htmlFor:"jetty-select",className:"font-medium",children:"Select Jetty:"}),o.jsxs(nt,{onValueChange:n,value:e||"",children:[o.jsx(at,{id:"jetty-select",className:"w-[200px]",children:o.jsx(rt,{placeholder:"Select a jetty"})}),o.jsx(ot,{children:tt.map(l=>o.jsx(st,{value:l.name,children:l.name},l.name))})]})]}),o.jsx(Us,{events:t,initialView:"month",onEventAdd:r,onEventUpdate:s,onEventDelete:i})]})]})})})}export{xi as default};
//# sourceMappingURL=page-5_8IP-r7.js.map
