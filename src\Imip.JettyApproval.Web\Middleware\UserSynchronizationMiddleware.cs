using Imip.JettyApproval.Web.Services.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text.Json;
using System.Threading.Tasks;

namespace Imip.JettyApproval.Web.Middleware;

/// <summary>
/// Middleware to handle deferred user synchronization after successful authentication
/// </summary>
public class UserSynchronizationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<UserSynchronizationMiddleware> _logger;

    public UserSynchronizationMiddleware(RequestDelegate next, ILogger<UserSynchronizationMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // Check if user is authenticated and has pending synchronization
        if (context.User.Identity?.IsAuthenticated == true && 
            context.Session.Keys.Contains("PendingUserSync"))
        {
            try
            {
                var claimsJson = context.Session.GetString("PendingUserSync");
                if (!string.IsNullOrEmpty(claimsJson))
                {
                    _logger.LogInformation("Processing deferred user synchronization");

                    // Deserialize claims
                    var claimsDict = JsonSerializer.Deserialize<Dictionary<string, string>>(claimsJson);
                    if (claimsDict != null && claimsDict.Count > 0)
                    {
                        // Convert to claims list
                        var claims = claimsDict.Select(kvp => new Claim(kvp.Key, kvp.Value)).ToList();

                        // Get user synchronization service
                        using var scope = context.RequestServices.CreateScope();
                        var userSyncService = scope.ServiceProvider.GetService<IUserSynchronizationService>();

                        if (userSyncService != null)
                        {
                            var user = await userSyncService.SynchronizeUserFromClaimsAsync(claims);
                            _logger.LogInformation("Deferred user synchronization completed successfully: {UserId} ({UserName})", 
                                user.Id, user.UserName);

                            // Remove the pending sync flag
                            context.Session.Remove("PendingUserSync");
                        }
                        else
                        {
                            _logger.LogWarning("UserSynchronizationService not available for deferred sync");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during deferred user synchronization");
                // Remove the pending sync flag even on error to prevent infinite retries
                context.Session.Remove("PendingUserSync");
            }
        }

        await _next(context);
    }
}
