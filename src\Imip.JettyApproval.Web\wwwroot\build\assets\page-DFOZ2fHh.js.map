{"version": 3, "file": "page-DFOZ2fHh.js", "sources": ["../../../../../frontend/src/pages/jetty/docked-vessel/page.tsx"], "sourcesContent": ["import { Button } from '@/components/ui/button';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Checkbox } from '@/components/ui/checkbox';\r\nimport { DropdownMenu, DropdownMenuCheckboxItem, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';\r\nimport AppLayout from '@/layouts/app-layout';\r\nimport { IconChevronDown, IconDotsVertical } from '@tabler/icons-react';\r\nimport React, { useState } from 'react';\r\n\r\ninterface DockedVessel {\r\n    id: string;\r\n    vesselName: string;\r\n    jetty: string;\r\n    arrivalDateTime: string;\r\n    departureDateTime: string;\r\n    status: string;\r\n    cargo: string;\r\n    agent: string;\r\n}\r\n\r\nconst mockDockedVesselData: DockedVessel[] = Array(10).fill(null).map((_, i) => ({\r\n    id: `docked_${i + 1}`,\r\n    vesselName: `MV. SEAWORTHY ${i + 1}`,\r\n    jetty: `Jetty ${i % 3 === 0 ? 'A' : i % 3 === 1 ? 'B' : 'C'}`,\r\n    arrivalDateTime: `2025-05-${10 + i} 08:00`,\r\n    departureDateTime: `2025-05-${12 + i} 17:00`,\r\n    status: i % 2 === 0 ? 'Loading' : 'Unloading',\r\n    cargo: i % 2 === 0 ? 'Containers' : 'Bulk Grain',\r\n    agent: `Agent Corp ${i + 1}`,\r\n}));\r\n\r\nexport default function DockedVesselList() {\r\n    const [filter, setFilter] = useState('');\r\n    const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set());\r\n    const [visibleColumns, setVisibleColumns] = useState<Set<keyof DockedVessel>>(\r\n        new Set(Object.keys(mockDockedVesselData[0]) as (keyof DockedVessel)[])\r\n    );\r\n\r\n    const filteredData = mockDockedVesselData.filter(vessel =>\r\n        Object.values(vessel).some(value =>\r\n            value.toString().toLowerCase().includes(filter.toLowerCase())\r\n        )\r\n    );\r\n\r\n    const handleSelectAll = (checked: boolean) => {\r\n        if (checked) {\r\n            setSelectedRows(new Set(filteredData.map(row => row.id)));\r\n        } else {\r\n            setSelectedRows(new Set());\r\n        }\r\n    };\r\n\r\n    const handleRowSelect = (id: string, checked: boolean) => {\r\n        const newSelection = new Set(selectedRows);\r\n        if (checked) {\r\n            newSelection.add(id);\r\n        } else {\r\n            newSelection.delete(id);\r\n        }\r\n        setSelectedRows(newSelection);\r\n    };\r\n\r\n    const handleToggleColumn = (columnKey: keyof DockedVessel, checked: boolean) => {\r\n        const newVisibleColumns = new Set(visibleColumns);\r\n        if (checked) {\r\n            newVisibleColumns.add(columnKey);\r\n        } else {\r\n            newVisibleColumns.delete(columnKey);\r\n        }\r\n        setVisibleColumns(newVisibleColumns);\r\n    };\r\n\r\n    const handleViewDetails = (id: string) => {\r\n        console.log(`Viewing details for docked vessel ID: ${id}`);\r\n        // Implement navigation or dialog for viewing details\r\n    };\r\n\r\n    const handleUpdateStatus = (id: string) => {\r\n        console.log(`Updating status for docked vessel ID: ${id}`);\r\n        // Implement logic to update vessel status\r\n    };\r\n\r\n    return (\r\n        <AppLayout>\r\n            <div className=\"container mx-auto p-4\">\r\n                <Card>\r\n                    <CardHeader>\r\n                        <CardTitle className=\"text-2xl font-bold\">Docked Vessels</CardTitle>\r\n                    </CardHeader>\r\n                    <CardContent>\r\n                        <div className=\"flex items-center justify-between mb-4\">\r\n                            <Input\r\n                                placeholder=\"Filter vessels...\"\r\n                                value={filter}\r\n                                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFilter(e.target.value)}\r\n                                className=\"max-w-sm\"\r\n                            />\r\n                            <DropdownMenu>\r\n                                <DropdownMenuTrigger asChild>\r\n                                    <Button variant=\"outline\" className=\"ml-auto\">\r\n                                        Columns <IconChevronDown className=\"ml-2 h-4 w-4\" />\r\n                                    </Button>\r\n                                </DropdownMenuTrigger>\r\n                                <DropdownMenuContent align=\"end\">\r\n                                    {Object.keys(mockDockedVesselData[0]).map((key) => (\r\n                                        <DropdownMenuCheckboxItem\r\n                                            key={key}\r\n                                            className=\"capitalize\"\r\n                                            checked={visibleColumns.has(key as keyof DockedVessel)}\r\n                                            onCheckedChange={(checked: boolean | 'indeterminate') => handleToggleColumn(key as keyof DockedVessel, checked === true)}\r\n                                        >\r\n                                            {key.replace(/([A-Z])/g, ' $1').trim()}\r\n                                        </DropdownMenuCheckboxItem>\r\n                                    ))}\r\n                                </DropdownMenuContent>\r\n                            </DropdownMenu>\r\n                        </div>\r\n\r\n                        <div className=\"rounded-md border\">\r\n                            <Table>\r\n                                <TableHeader>\r\n                                    <TableRow>\r\n                                        <TableHead className=\"w-[30px]\">\r\n                                            <Checkbox\r\n                                                checked={selectedRows.size === filteredData.length && filteredData.length > 0}\r\n                                                onCheckedChange={(checked: boolean | 'indeterminate') => handleSelectAll(checked === true)}\r\n                                            />\r\n                                        </TableHead>\r\n                                        {Object.keys(mockDockedVesselData[0]).map((key) => (visibleColumns.has(key as keyof DockedVessel) && key !== 'id' &&\r\n                                            <TableHead key={key} className=\"capitalize\">\r\n                                                {key.replace(/([A-Z])/g, ' $1').trim()}\r\n                                            </TableHead>\r\n                                        ))}\r\n                                        <TableHead className=\"text-right\">Actions</TableHead>\r\n                                    </TableRow>\r\n                                </TableHeader>\r\n                                <TableBody>\r\n                                    {filteredData.map((vessel) => (\r\n                                        <TableRow key={vessel.id}>\r\n                                            <TableCell>\r\n                                                <Checkbox\r\n                                                    checked={selectedRows.has(vessel.id)}\r\n                                                    onCheckedChange={(checked: boolean | 'indeterminate') => handleRowSelect(vessel.id, checked === true)}\r\n                                                />\r\n                                            </TableCell>\r\n                                            {Object.entries(vessel).map(([key, value]) => (visibleColumns.has(key as keyof DockedVessel) && key !== 'id' &&\r\n                                                <TableCell key={key}>{value}</TableCell>\r\n                                            ))}\r\n                                            <TableCell className=\"text-right\">\r\n                                                <DropdownMenu>\r\n                                                    <DropdownMenuTrigger asChild>\r\n                                                        <Button variant=\"ghost\" className=\"h-8 w-8 p-0\">\r\n                                                            <span className=\"sr-only\">Open menu</span>\r\n                                                            <IconDotsVertical className=\"h-4 w-4\" />\r\n                                                        </Button>\r\n                                                    </DropdownMenuTrigger>\r\n                                                    <DropdownMenuContent align=\"end\">\r\n                                                        <DropdownMenuLabel>Actions</DropdownMenuLabel>\r\n                                                        <DropdownMenuItem onClick={() => handleViewDetails(vessel.id)}>\r\n                                                            View Details\r\n                                                        </DropdownMenuItem>\r\n                                                        <DropdownMenuItem onClick={() => handleUpdateStatus(vessel.id)}>\r\n                                                            Update Status\r\n                                                        </DropdownMenuItem>\r\n                                                    </DropdownMenuContent>\r\n                                                </DropdownMenu>\r\n                                            </TableCell>\r\n                                        </TableRow>\r\n                                    ))}\r\n                                </TableBody>\r\n                            </Table>\r\n                        </div>\r\n\r\n                        <div className=\"flex items-center justify-between mt-4\">\r\n                            <div className=\"text-sm text-gray-500\">\r\n                                {selectedRows.size} of {filteredData.length} row(s) selected.\r\n                            </div>\r\n                            <div className=\"space-x-2\">\r\n                                <Button variant=\"outline\" size=\"sm\">Previous</Button>\r\n                                <Button variant=\"outline\" size=\"sm\">Next</Button>\r\n                            </div>\r\n                        </div>\r\n                    </CardContent>\r\n                </Card>\r\n            </div>\r\n        </AppLayout>\r\n    );\r\n}\r\n"], "names": ["mockDockedVesselData", "_", "i", "DockedVesselList", "filter", "setFilter", "useState", "selectedRows", "setSelectedRows", "visibleColumns", "setVisibleColumns", "filteredData", "vessel", "value", "handleSelectAll", "checked", "row", "handleRowSelect", "id", "newSelection", "handleToggleColumn", "column<PERSON>ey", "newVisibleColumns", "handleViewDetails", "handleUpdateStatus", "AppLayout", "jsx", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "<PERSON><PERSON><PERSON><PERSON>", "jsxs", "Input", "e", "DropdownMenu", "DropdownMenuTrigger", "<PERSON><PERSON>", "IconChevronDown", "DropdownMenuContent", "key", "DropdownMenuCheckboxItem", "Table", "TableHeader", "TableRow", "TableHead", "Checkbox", "TableBody", "TableCell", "IconDotsVertical", "DropdownMenuLabel", "DropdownMenuItem"], "mappings": "ibAqBA,MAAMA,EAAuC,MAAM,EAAE,EAAE,KAAK,IAAI,EAAE,IAAI,CAACC,EAAGC,KAAO,CAC7E,GAAI,UAAUA,EAAI,CAAC,GACnB,WAAY,iBAAiBA,EAAI,CAAC,GAClC,MAAO,SAASA,EAAI,IAAM,EAAI,IAAMA,EAAI,IAAM,EAAI,IAAM,GAAG,GAC3D,gBAAiB,WAAW,GAAKA,CAAC,SAClC,kBAAmB,WAAW,GAAKA,CAAC,SACpC,OAAQA,EAAI,IAAM,EAAI,UAAY,YAClC,MAAOA,EAAI,IAAM,EAAI,aAAe,aACpC,MAAO,cAAcA,EAAI,CAAC,EAC9B,EAAE,EAEF,SAAwBC,GAAmB,CACvC,KAAM,CAACC,EAAQC,CAAS,EAAIC,EAAAA,SAAS,EAAE,EACjC,CAACC,EAAcC,CAAe,EAAIF,EAAAA,SAAsB,IAAI,GAAK,EACjE,CAACG,EAAgBC,CAAiB,EAAIJ,EAAA,SACxC,IAAI,IAAI,OAAO,KAAKN,EAAqB,CAAC,CAAC,CAA2B,CAC1E,EAEMW,EAAeX,EAAqB,OACtCY,GAAA,OAAO,OAAOA,CAAM,EAAE,KAAKC,GACvBA,EAAM,SAAS,EAAE,cAAc,SAAST,EAAO,YAAa,CAAA,CAAA,CAEpE,EAEMU,EAAmBC,GAAqB,CAEtBP,EADhBO,EACgB,IAAI,IAAIJ,EAAa,OAAWK,EAAI,EAAE,CAAC,EAEvC,IAAI,GAFoC,CAIhE,EAEMC,EAAkB,CAACC,EAAYH,IAAqB,CAChD,MAAAI,EAAe,IAAI,IAAIZ,CAAY,EACrCQ,EACAI,EAAa,IAAID,CAAE,EAEnBC,EAAa,OAAOD,CAAE,EAE1BV,EAAgBW,CAAY,CAChC,EAEMC,EAAqB,CAACC,EAA+BN,IAAqB,CACtE,MAAAO,EAAoB,IAAI,IAAIb,CAAc,EAC5CM,EACAO,EAAkB,IAAID,CAAS,EAE/BC,EAAkB,OAAOD,CAAS,EAEtCX,EAAkBY,CAAiB,CACvC,EAEMC,EAAqBL,GAAe,CAG1C,EAEMM,EAAsBN,GAAe,CAG3C,EAEA,aACKO,EACG,CAAA,SAAAC,EAAA,IAAC,OAAI,UAAU,wBACX,gBAACC,EACG,CAAA,SAAA,CAAAD,EAAAA,IAACE,GACG,SAACF,EAAA,IAAAG,EAAA,CAAU,UAAU,qBAAqB,0BAAc,CAC5D,CAAA,SACCC,EACG,CAAA,SAAA,CAACC,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACX,SAAA,CAAAL,EAAA,IAACM,EAAA,CACG,YAAY,oBACZ,MAAO5B,EACP,SAAW6B,GAA2C5B,EAAU4B,EAAE,OAAO,KAAK,EAC9E,UAAU,UAAA,CACd,SACCC,EACG,CAAA,SAAA,CAACR,EAAAA,IAAAS,EAAA,CAAoB,QAAO,GACxB,SAAAJ,EAAAA,KAACK,GAAO,QAAQ,UAAU,UAAU,UAAU,SAAA,CAAA,WAClCV,EAAAA,IAACW,EAAgB,CAAA,UAAU,cAAe,CAAA,CAAA,CAAA,CACtD,CACJ,CAAA,EACCX,EAAAA,IAAAY,EAAA,CAAoB,MAAM,MACtB,SAAO,OAAA,KAAKtC,EAAqB,CAAC,CAAC,EAAE,IAAKuC,GACvCb,EAAA,IAACc,EAAA,CAEG,UAAU,aACV,QAAS/B,EAAe,IAAI8B,CAAyB,EACrD,gBAAkBxB,GAAuCK,EAAmBmB,EAA2BxB,IAAY,EAAI,EAEtH,SAAIwB,EAAA,QAAQ,WAAY,KAAK,EAAE,KAAK,CAAA,EALhCA,CAAA,CAOZ,CACL,CAAA,CAAA,CACJ,CAAA,CAAA,EACJ,EAECb,MAAA,MAAA,CAAI,UAAU,oBACX,gBAACe,EACG,CAAA,SAAA,CAACf,EAAA,IAAAgB,EAAA,CACG,gBAACC,EACG,CAAA,SAAA,CAACjB,EAAAA,IAAAkB,EAAA,CAAU,UAAU,WACjB,SAAAlB,EAAA,IAACmB,EAAA,CACG,QAAStC,EAAa,OAASI,EAAa,QAAUA,EAAa,OAAS,EAC5E,gBAAkBI,GAAuCD,EAAgBC,IAAY,EAAI,CAAA,CAAA,EAEjG,EACC,OAAO,KAAKf,EAAqB,CAAC,CAAC,EAAE,IAAKuC,GAAS9B,EAAe,IAAI8B,CAAyB,GAAKA,IAAQ,MACxGb,MAAAkB,EAAA,CAAoB,UAAU,aAC1B,SAAIL,EAAA,QAAQ,WAAY,KAAK,EAAE,MADpB,EAAAA,CAEhB,CACH,EACAb,EAAA,IAAAkB,EAAA,CAAU,UAAU,aAAa,SAAO,SAAA,CAAA,CAAA,CAAA,CAC7C,CACJ,CAAA,QACCE,EACI,CAAA,SAAAnC,EAAa,IAAKC,UACd+B,EACG,CAAA,SAAA,CAAAjB,MAACqB,EACG,CAAA,SAAArB,EAAA,IAACmB,EAAA,CACG,QAAStC,EAAa,IAAIK,EAAO,EAAE,EACnC,gBAAkBG,GAAuCE,EAAgBL,EAAO,GAAIG,IAAY,EAAI,CAAA,CAAA,EAE5G,EACC,OAAO,QAAQH,CAAM,EAAE,IAAI,CAAC,CAAC2B,EAAK1B,CAAK,IAAOJ,EAAe,IAAI8B,CAAyB,GAAKA,IAAQ,YACnGQ,EAAqB,CAAA,SAAAlC,GAAN0B,CAAY,CAC/B,EACAb,MAAAqB,EAAA,CAAU,UAAU,aACjB,gBAACb,EACG,CAAA,SAAA,CAACR,EAAAA,IAAAS,EAAA,CAAoB,QAAO,GACxB,SAAAJ,EAAAA,KAACK,GAAO,QAAQ,QAAQ,UAAU,cAC9B,SAAA,CAACV,EAAA,IAAA,OAAA,CAAK,UAAU,UAAU,SAAS,YAAA,EACnCA,EAAAA,IAACsB,EAAiB,CAAA,UAAU,SAAU,CAAA,CAAA,CAAA,CAC1C,CACJ,CAAA,EACAjB,EAAAA,KAACO,EAAoB,CAAA,MAAM,MACvB,SAAA,CAAAZ,EAAAA,IAACuB,GAAkB,SAAO,SAAA,CAAA,EAC1BvB,MAACwB,GAAiB,QAAS,IAAM3B,EAAkBX,EAAO,EAAE,EAAG,SAE/D,eAAA,EACAc,MAACwB,GAAiB,QAAS,IAAM1B,EAAmBZ,EAAO,EAAE,EAAG,SAEhE,eAAA,CAAA,CAAA,CACJ,CAAA,CAAA,CAAA,CACJ,CACJ,CAAA,CAAA,GA5BWA,EAAO,EA6BtB,CACH,CACL,CAAA,CAAA,CAAA,CACJ,CACJ,CAAA,EAEAmB,EAAAA,KAAC,MAAI,CAAA,UAAU,yCACX,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wBACV,SAAA,CAAaxB,EAAA,KAAK,OAAKI,EAAa,OAAO,mBAAA,EAChD,EACAoB,EAAAA,KAAC,MAAI,CAAA,UAAU,YACX,SAAA,CAAAL,MAACU,EAAO,CAAA,QAAQ,UAAU,KAAK,KAAK,SAAQ,WAAA,QAC3CA,EAAO,CAAA,QAAQ,UAAU,KAAK,KAAK,SAAI,MAAA,CAAA,CAAA,CAC5C,CAAA,CAAA,CACJ,CAAA,CAAA,CACJ,CAAA,CAAA,CACJ,CAAA,CACJ,CAAA,EACJ,CAER"}