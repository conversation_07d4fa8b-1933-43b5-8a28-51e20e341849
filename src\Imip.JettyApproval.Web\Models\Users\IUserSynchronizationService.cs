using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using Imip.JettyApproval.Web.Services.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Identity;

namespace Imip.JettyApproval.Web.Models.Users;

/// <summary>
/// Service interface for synchronizing users between Identity Server and internal database
/// </summary>
public interface IUserSynchronizationService : IApplicationService
{
    /// <summary>
    /// Synchronizes user from JWT token claims to internal database
    /// </summary>
    /// <param name="token">JWT token string</param>
    /// <returns>The synchronized IdentityUser</returns>
    Task<IdentityUser> SynchronizeUserFromTokenAsync(string token);

    /// <summary>
    /// Synchronizes user from claims principal to internal database
    /// </summary>
    /// <param name="claimsPrincipal">Claims principal containing user information</param>
    /// <returns>The synchronized IdentityUser</returns>
    Task<IdentityUser> SynchronizeUserFromClaimsAsync(ClaimsPrincipal claimsPrincipal);

    /// <summary>
    /// Synchronizes user from claims collection to internal database
    /// </summary>
    /// <param name="claims">Collection of claims containing user information</param>
    /// <returns>The synchronized IdentityUser</returns>
    Task<IdentityUser> SynchronizeUserFromClaimsAsync(IEnumerable<Claim> claims);

    /// <summary>
    /// Checks if user exists in internal database
    /// </summary>
    /// <param name="userId">User ID from Identity Server</param>
    /// <returns>True if user exists, false otherwise</returns>
    Task<bool> UserExistsAsync(Guid userId);

    /// <summary>
    /// Gets user from internal database by ID
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <returns>IdentityUser if found, null otherwise</returns>
    Task<IdentityUser?> GetUserAsync(Guid userId);

    /// <summary>
    /// Creates or updates user in internal database
    /// </summary>
    /// <param name="userInfo">User information extracted from token</param>
    /// <returns>The created or updated IdentityUser</returns>
    Task<IdentityUser> CreateOrUpdateUserAsync(UserSynchronizationInfo userInfo);

    /// <summary>
    /// Gets health check information for user synchronization
    /// </summary>
    /// <returns>Health check information</returns>
    Task<UserSynchronizationHealthDto> GetHealthAsync();
}

/// <summary>
/// User information extracted from JWT token for synchronization
/// </summary>
public class UserSynchronizationInfo
{
    /// <summary>
    /// User ID from Identity Server (must match exactly)
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Username
    /// </summary>
    public string UserName { get; set; } = string.Empty;

    /// <summary>
    /// Email address
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// First name
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Last name
    /// </summary>
    public string? Surname { get; set; }

    /// <summary>
    /// Phone number
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// Whether email is confirmed
    /// </summary>
    public bool EmailConfirmed { get; set; }

    /// <summary>
    /// Whether phone number is confirmed
    /// </summary>
    public bool PhoneNumberConfirmed { get; set; }

    /// <summary>
    /// Whether user is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// User roles
    /// </summary>
    public List<string> Roles { get; set; } = new();

    /// <summary>
    /// Additional claims
    /// </summary>
    public Dictionary<string, string> Claims { get; set; } = new();

    /// <summary>
    /// Tenant ID if multi-tenant
    /// </summary>
    public Guid? TenantId { get; set; }
}

