import{h as z,r as o,j as e,u as I,f as q}from"./vendor-CrSBzUoz.js";import{Q as U,ap as te,o as H,aq as ae,M as R,ar as re,as as ne,I as C,B as j,W as ie,at as le,a0 as P,a1 as oe,a2 as ce,u as W,T as de,au as ue,L as O,S as me,a as pe,b as he,c as xe,d as M,av as fe,aw as ge,D as je,e as ye,f as be,j as K,A as ve}from"./app-layout-CNB1Wtrx.js";import{L as Se,v as Ne,E as Ce}from"./Loader-DXXM89G3.js";import{A as we,a as Ue,b as Ae,c as Te,d as Ee,e as De,f as ke,g as Fe,T as Ie}from"./TableSkeleton-Bzdk6y-O.js";import{u as Q,h as Y,D as E}from"./DataTableColumnHeader-DJ80pmDz.js";import{u as J}from"./index.esm-CT1elm-0.js";import{C as k}from"./checkbox-CayrCcBd.js";import{D as L,b as G,c as _,d as V,e as F,a as X}from"./dialog-DAr_Mtxm.js";import{n as Re,L as Pe,T as qe,o as Le}from"./radix-DaY-mnHi.js";import{F as Z,a as N}from"./FormField-BFBouSal.js";import{u as Ge,P as _e,T as Ve}from"./TogglePermission-LBMxXwtr.js";import{a as Oe,f as Me,w as Ke,l as ze,p as He,_ as Qe}from"./popover-7NwOVASC.js";import{V as $e,D as Be}from"./DataTable-BL4SJdnU.js";import{S as We}from"./search-YAT3sv3T.js";import{$ as Ye}from"./App-CGHLK9xH.js";import"./card-BAJCNJxm.js";import"./table-CAbNlII1.js";import"./tiny-invariant-CopsF_GD.js";import"./index-CZZWNLgr.js";const Je=(t,s,l,c)=>z({queryKey:[U.GetUsers,t,s,l,c],queryFn:async()=>{let r=0;return t>0&&(r=t*s),(await te({query:{MaxResultCount:s,SkipCount:r,Filter:l,Sorting:c}})).data}}),Xe=({user:{userId:t,username:s},onDismiss:l})=>{const{toast:c}=H(),[r,x]=o.useState(!1),h=async()=>{try{await ae({path:{id:t}}),c({title:"Success",description:`User "${s}" has been deleted successfully.`}),l()}catch(f){f instanceof Error&&c({title:"Failed",description:`There was a problem when deleting the user "${s}". Kindly try again.`,variant:"destructive"})}};return o.useEffect(()=>{x(!0)},[]),e.jsx(we,{open:r,children:e.jsxs(Ue,{children:[e.jsxs(Ae,{children:[e.jsx(Te,{children:"Are you absolutely sure?"}),e.jsxs(Ee,{children:['This action cannot be undone. This will permanently delete your this user "',s,'"']})]}),e.jsxs(De,{children:[e.jsx(ke,{onClick:l,children:"Cancel"}),e.jsx(Fe,{onClick:h,children:"Yes"})]})]})})};function Ze({className:t,...s}){return e.jsx(Re,{"data-slot":"tabs",className:R("flex flex-col gap-2",t),...s})}function es({className:t,...s}){return e.jsx(Pe,{"data-slot":"tabs-list",className:R("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",t),...s})}function $({className:t,...s}){return e.jsx(qe,{"data-slot":"tabs-trigger",className:R("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...s})}function B({className:t,...s}){return e.jsx(Le,{"data-slot":"tabs-content",className:R("flex-1 outline-none",t),...s})}const ss=()=>z({queryKey:[U.GetAssignableRoles],queryFn:async()=>{const{data:t}=await re();return t}}),ee=({userId:t})=>z({queryKey:[U.GetUserRoles,t],queryFn:async()=>{const{data:s}=await ne({path:{id:t}});return s}}),D={USERS_EDIT:"user_edit",USERS_ROLE_ASSIGN:"user_role_assign"},ts=({userDto:t,userId:s,onDismiss:l})=>{const[c,r]=o.useState(!1),{toast:x}=Q(),h=I(),{handleSubmit:f,register:y}=J(),[d,b]=o.useState([]),u=ee({userId:s}),g=ss(),w=q({mutationFn:async a=>le({path:{id:s},body:{...t,...a}}),onSuccess:()=>{x({title:"Success",description:"Claim Type Created Successfully",variant:"success"}),h.invalidateQueries({queryKey:[U.GetUsers]}),r(!1)},onError:a=>{const n=Y(a);x({title:n.title,description:n.description,variant:"error"})}}),p=a=>{const n={...a};w.mutate(n)},v=()=>{r(!1),l()};o.useEffect(()=>{r(!0)},[]),o.useEffect(()=>{if(u.data?.items){const a=[];u.data.items.forEach(n=>{a.push({name:n.name,id:n.id})}),b(a)}},[u.data?.items]);const A=o.useCallback(a=>{const n=d.findIndex(i=>a.id===i.id);n!==-1?(d.splice(n,1),b([...d])):(d.push({name:a.name,id:a.id}),b([...d]))},[d]),m=a=>{a.preventDefault();const n={...t,roleNames:d?.map(i=>i.name)??[]};p(n)};return e.jsx(L,{open:c,onOpenChange:v,children:e.jsxs(G,{size:"2xl",className:"",children:[e.jsx(_,{children:e.jsxs(V,{children:["Update a User: ",t.userName]})}),e.jsxs(Ze,{defaultValue:D.USERS_EDIT,children:[e.jsxs(es,{className:"w-full",children:[e.jsx($,{value:D.USERS_EDIT,className:"w-full",children:"User Information"}),e.jsx($,{value:D.USERS_ROLE_ASSIGN,className:"w-full",children:"Roles"})]}),e.jsx(B,{value:D.USERS_EDIT,children:e.jsxs("form",{onSubmit:f(p),children:[e.jsx("section",{className:"flex flex-col space-y-2 mt-4",children:e.jsxs(Z,{children:[e.jsx(N,{label:"Name",description:"The name of the user",children:e.jsx(C,{required:!0,...y("name"),defaultValue:t.name??"",placeholder:"Name"})}),e.jsx(N,{label:"Surname",description:"The surname of the user",children:e.jsx(C,{required:!0,...y("surname"),defaultValue:t.surname??"",placeholder:"Surname"})}),e.jsx(N,{label:"Email",description:"The email of the user",children:e.jsx(C,{required:!0,...y("email"),defaultValue:t.email??"",placeholder:"Email"})}),e.jsx(N,{label:"Phone Number",description:"The phone number of the user",children:e.jsx(C,{required:!0,...y("phoneNumber"),defaultValue:t.phoneNumber??"",placeholder:"Phone Number"})})]})}),e.jsxs(F,{className:"mt-5",children:[e.jsx(j,{variant:"outline",onClick:a=>{a.preventDefault(),v()},children:"Cancel"}),e.jsx(j,{type:"submit",children:"Save"})]})]})}),e.jsxs(B,{className:"max-h-[70vh] overflow-hidden flex flex-col",value:D.USERS_ROLE_ASSIGN,children:[g?.isLoading&&e.jsx(Se,{}),g?.isError&&e.jsxs("div",{className:"bg-error p-10 text-3xl",children:["There was an error while fetching roles information for the ",t.userName]}),!g.isLoading&&!g.isError&&e.jsx("div",{className:"flex-1 overflow-y-auto",children:g?.data?.items?.map(a=>e.jsxs("div",{className:ie("flex items-center space-x-2 pb-5 mt-3"),children:[e.jsx(k,{id:a.id,name:a.name,checked:!!d?.find(n=>n.id===a.id),onCheckedChange:()=>{A(a)}}),e.jsx("label",{htmlFor:a.id,className:"text-sm font-medium leading-none",children:a.name})]},Ne()))}),e.jsxs(F,{className:"mt-5",children:[e.jsx(j,{variant:"outline",onClick:a=>{a.preventDefault(),v()},children:"Cancel"}),e.jsx(j,{onClick:m,children:"Save"})]})]})]})]})})},as=({userDto:t,userId:s,onDismiss:l})=>{const[c,r]=o.useState(!1),{toast:x}=H(),h=ee({userId:s}),[f,y]=o.useState(!1),{data:d}=Ge(P.U,s),b=I(),[u,g]=o.useState([]);o.useEffect(()=>(r(!0),()=>{b.invalidateQueries({queryKey:[P.U]})}),[]),o.useEffect(()=>{d?.groups&&Array.isArray(d.groups)&&g([...d.groups])},[d]),o.useEffect(()=>{if(d?.groups&&d.groups.length>0){const a=d.groups.map(n=>n.permissions?.every(i=>i.isGranted)).every(n=>n);y(a)}},[d]),o.useEffect(()=>{if(u.length>0){const a=u.map(n=>({...n,permissions:n.permissions?.map(i=>({...i,isGranted:f}))}));g(a)}},[f]);const w=o.useCallback(async a=>{a.preventDefault();const i={permissions:u?.map(S=>S.permissions.map(T=>({name:T.name,isGranted:T.isGranted}))).flat()};try{await oe({query:{providerName:P.U,providerKey:s},body:i}),x({title:"Success",description:"Permission Updated Successfully",variant:"default"}),b.invalidateQueries({queryKey:[P.U]}),p()}catch(S){S instanceof Error&&x({title:"Failed",description:"Permission update wasn't successful.",variant:"destructive"})}},[u]),p=o.useCallback(()=>{r(!1),l()},[]),v=o.useMemo(()=>h?.data?.items?h.data.items.filter(a=>a.name?.includes(ce.ADMIN)).length>0:!1,[h]),A=a=>a?(a.split(" ")[0]??"").toLowerCase():"identity",m=o.useMemo(()=>{const a=[];for(let n=0;n<u.length;n+=2)a.push(u.slice(n,n+2));return a},[u]);return e.jsx(L,{open:c,onOpenChange:p,children:e.jsxs(G,{size:"4xl",className:"max-w-4xl max-h-[90vh] overflow-hidden flex flex-col",children:[e.jsx(_,{children:e.jsxs(V,{children:["Permissions - ",t.userName]})}),e.jsx("form",{onSubmit:w,className:"flex-1 overflow-y-auto",children:e.jsxs("div",{className:"p-1",children:[e.jsx(_e,{name:"Grant All Permissions",isGranted:f,id:"all_granted",disabled:!v,onUpdate:()=>{y(a=>!a)},className:"ml-2 mb-4"}),e.jsx("div",{className:"space-y-6",children:m.map((a,n)=>e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:a.map((i,S)=>{const T=i.displayName??"",se=i.permissions??[];return e.jsxs("div",{className:"border rounded-lg p-4",children:[e.jsx("h3",{className:"text-lg font-medium mb-2",children:T}),e.jsx("div",{className:"border-t pt-3",children:e.jsx(Ve,{permissions:se,type:A(T),disabled:f&&v,hideSelectAll:f,hideSave:!0})})]},`${n}-${S}`)})},n))})]})}),e.jsxs(F,{className:"mt-4 border-t pt-4 bg-white dark:bg-gray-950",children:[e.jsx(j,{onClick:a=>{a.preventDefault(),p()},variant:"ghost",children:"Cancel"}),e.jsx(j,{onClick:w,children:"Save"})]})]})})},rs=({children:t})=>{const{can:s}=W(),[l,c]=o.useState(!1),[r,x]=o.useState(!0),[h,f]=o.useState(!0),{toast:y}=Q(),d=I(),{handleSubmit:b,register:u}=J(),g=q({mutationFn:async p=>ue({body:p}),onSuccess:()=>{y({title:"Success",description:"User Created Successfully",variant:"success"}),d.invalidateQueries({queryKey:[U.GetUsers]}),c(!1)},onError:p=>{const v=Y(p);y({title:v.title,description:v.description,variant:"error"})}}),w=p=>{const v={...p,isActive:r,lockoutEnabled:h};g.mutate(v)};return e.jsxs("section",{children:[e.jsx(de,{}),e.jsxs(L,{open:l,onOpenChange:c,children:[e.jsx(X,{asChild:!0,children:t}),e.jsx("section",{className:"flex items-center justify-between pb-5",children:s("AbpIdentity.Users.Create")&&e.jsxs(j,{size:"sm",className:"w-full sm:py-1 sm:mt-0 sm:w-fit",onClick:()=>c(!0),children:[e.jsx(Oe,{className:"-ml-1 size-4 shrink-0","aria-hidden":"true"}),e.jsx("span",{className:"hidden truncate sm:inline",children:"Create New User"})]})}),e.jsxs(G,{children:[e.jsx(_,{children:e.jsx(V,{children:"Create a New User"})}),e.jsxs("form",{onSubmit:b(w),className:"mt-2",children:[e.jsx("section",{className:"flex w-full flex-col space-y-2",children:e.jsxs(Z,{children:[e.jsx(N,{label:"Username",description:"The username of the user",children:e.jsx(C,{required:!0,...u("userName"),placeholder:"Username"})}),e.jsx(N,{label:"Password",description:"The password of the user",children:e.jsx(C,{required:!0,type:"password",...u("password"),placeholder:"Password"})}),e.jsx(N,{label:"Name",description:"The name of the user",children:e.jsx(C,{required:!0,...u("name"),placeholder:"Name"})}),e.jsx(N,{label:"Surname",description:"The surname of the user",children:e.jsx(C,{required:!0,...u("surname"),placeholder:"Surname"})}),e.jsx(N,{label:"Email",description:"The email of the user",children:e.jsx(C,{required:!0,...u("email"),placeholder:"Email"})}),e.jsx(N,{label:"Phone Number",description:"The phone number of the user",children:e.jsx(C,{required:!0,...u("phoneNumber"),placeholder:"Phone Number"})}),e.jsx(N,{label:"Is Active",description:"The active status of the user",children:e.jsx(k,{id:"isActive",name:"isActive",defaultChecked:!0,checked:r,onCheckedChange:p=>x(!!p.valueOf())})}),e.jsx(N,{label:"Lockout Enabled",description:"The lockout status of the user",children:e.jsx(k,{id:"lockoutEnabled",name:"lockoutEnabled",defaultChecked:!0,checked:h,onCheckedChange:p=>f(!!p.valueOf())})})]})}),e.jsxs(F,{className:"mt-5",children:[e.jsx(j,{variant:"ghost",onClick:p=>{p.preventDefault(),c(!1)},disabled:g.isPending,children:"Cancel"}),e.jsx(j,{type:"submit",disabled:g.isPending,children:g.isPending?"Saving...":"Save"})]})]})]})]})]})},ns=()=>{const{toast:t}=Q(),s=I(),[l,c]=o.useState(!1),[r,x]=o.useState(null),[h,f]=o.useState(!1),[y,d]=o.useState(!1),[b,u]=o.useState("users"),g=m=>{if(m.target.files&&m.target.files.length>0){const a=m.target.files[0];if(!a?.name.toLowerCase().endsWith(".csv")){t({title:"Invalid File Type",description:"Please select a CSV file",variant:"destructive"}),m.target.value="";return}x(a)}},w=q({mutationFn:async()=>{d(!0);try{const m=await fe({query:{type:b}}),a=new Blob([m.data],{type:"text/csv"}),n=window.URL.createObjectURL(a),i=document.createElement("a");return i.href=n,i.download="users_import_template.csv",document.body.appendChild(i),i.click(),window.URL.revokeObjectURL(n),document.body.removeChild(i),m.data}finally{d(!1)}},onError:m=>{t({title:"Download Failed",description:m.error?.message??"Failed to download template",variant:"destructive"})}}),p=q({mutationFn:async()=>{if(!r)throw new Error("No file selected");f(!0);try{const m=new FormData;return m.append("File",r),m.append("Type",b),m.append("Delimiter",";"),(await ge({body:{File:r,Type:b,Delimiter:";"}})).data}finally{f(!1)}},onSuccess:()=>{t({title:"Import Successful",description:"Imported successfully",variant:"success"}),s.invalidateQueries({queryKey:[U.GetUsers]}),c(!1),x(null)},onError:m=>{const a=m;let n="Upload Failed",i="An error occurred while uploading the file";a.details?.error?.message&&(n=a.details.error.message),a.details?.error?.details&&(i=a.details.error.details),t({title:n,description:i,variant:"destructive"})}}),v=()=>{w.mutate()},A=()=>{if(!r){t({title:"No File Selected",description:"Please select a CSV file to upload",variant:"warning"});return}p.mutate()};return e.jsxs(L,{open:l,onOpenChange:c,children:[e.jsx(X,{asChild:!0,children:e.jsxs(j,{size:"sm",className:"w-full sm:py-1 sm:mt-0 sm:w-fit",children:[e.jsx(Me,{className:"-ml-1 size-4 shrink-0","aria-hidden":"true"}),e.jsx("span",{className:"hidden truncate sm:inline",children:"Upload CSV"})]})}),e.jsxs(G,{className:"sm:max-w-md",children:[e.jsx(_,{children:e.jsx(V,{children:"Upload Users CSV"})}),e.jsxs("div",{className:"flex flex-col gap-4 py-4",children:[e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx(O,{htmlFor:"csvFile",className:"text-left",children:"Upload Type"}),e.jsxs(me,{value:b,onValueChange:m=>u(m),children:[e.jsx(pe,{className:"w-full",children:e.jsx(he,{placeholder:"Select upload type"})}),e.jsxs(xe,{children:[e.jsx(M,{value:"users",children:"Users"}),e.jsx(M,{value:"roles",children:"Roles"}),e.jsx(M,{value:"userroles",children:"User Roles"})]})]})]}),e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx(O,{htmlFor:"template",className:"text-left",children:"Download Template"}),e.jsx("div",{className:"flex items-center gap-2",children:e.jsxs(j,{variant:"outline",onClick:v,disabled:y,className:"w-full",children:[e.jsx(Ke,{className:"mr-2 h-4 w-4"}),y?"Downloading...":"Download Template"]})}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Download the template first, fill it with your data, and then upload it."})]}),e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx(O,{htmlFor:"csvFile",className:"text-left",children:"Upload CSV File"}),e.jsx(C,{id:"csvFile",type:"file",accept:".csv",onChange:g,disabled:h}),r&&e.jsxs("p",{className:"text-sm text-muted-foreground",children:["Selected file: ",r.name]})]})]}),e.jsxs(F,{children:[e.jsx(j,{variant:"ghost",onClick:()=>{c(!1),x(null)},disabled:h,children:"Cancel"}),e.jsx(j,{onClick:A,disabled:!r||h,children:h?"Uploading...":"Upload"})]})]})]})},is=({userId:t,userDto:s,onAction:l,variant:c="dropdown"})=>{const{can:r}=W();return c==="dropdown"?e.jsx("div",{className:"flex justify-end",children:e.jsxs(je,{children:[e.jsx(ye,{asChild:!0,children:e.jsxs(j,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:[e.jsx(ze,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"Open menu"})]})}),e.jsxs(be,{align:"end",className:"w-[160px]",children:[r("AbpIdentity.Users.ManagePermissions")&&e.jsx(K,{className:"cursor-pointer text-sm",onClick:()=>l(t,s,"permission"),children:"Permission"}),r("AbpIdentity.Users.Update")&&e.jsx(K,{className:"cursor-pointer text-sm",onClick:()=>l(t,s,"edit"),children:"Edit"}),r("AbpIdentity.Users.Delete")&&e.jsx(K,{className:"cursor-pointer text-sm text-red-500",onClick:()=>l(t,s,"delete"),children:"Delete"})]})]})}):e.jsxs("div",{className:"flex items-center justify-end gap-1",children:[r("AbpIdentity.Users.ManagePermissions")&&e.jsxs(j,{variant:"primary",size:"sm",className:"flex items-center gap-1 px-2 py-1",onClick:()=>l(t,s,"permission"),children:[e.jsx(He,{className:"h-4 w-4"}),e.jsx("span",{children:"Permission"})]}),r("AbpIdentity.Users.Update")&&e.jsxs(j,{variant:"primary",size:"sm",className:"flex items-center gap-1 px-2 py-1",onClick:()=>l(t,s,"edit"),children:[e.jsx(Qe,{className:"h-4 w-4"}),e.jsx("span",{children:"Edit"})]})]})},ls=({status:t})=>{let s="Inactive",l="bg-gray-100 text-gray-700 border-gray-200";return typeof t=="boolean"?t&&(s="Active",l="bg-green-50 text-green-700 border-green-200"):typeof t=="string"&&(s=t,t.toLowerCase()==="active"?l="bg-green-50 text-green-700 border-green-200":t.toLowerCase()==="archived"?l="bg-amber-50 text-amber-700 border-amber-200":t.toLowerCase()==="inactive"&&(l="bg-gray-100 text-gray-700 border-gray-200")),e.jsx("span",{className:R("inline-flex items-center rounded-md border px-2 py-1 text-xs font-medium",l),children:s})},os=t=>[{id:"select",header:({table:s})=>e.jsx(k,{checked:s.getIsAllPageRowsSelected()?!0:s.getIsSomeRowsSelected()?"indeterminate":!1,onCheckedChange:()=>s.toggleAllPageRowsSelected(),className:"translate-y-0.5","aria-label":"Select all"}),cell:({row:s})=>e.jsx(k,{checked:s.getIsSelected(),onCheckedChange:()=>s.toggleSelected(),className:"translate-y-0.5","aria-label":"Select row"}),enableSorting:!1,enableHiding:!1,meta:{displayName:"Select"}},{accessorKey:"userName",header:({column:s})=>e.jsx(E,{column:s,title:"Username"}),enableHiding:!0,enableSorting:!0,cell:s=>s.getValue(),meta:{className:"text-left",displayName:"Username"}},{accessorKey:"name",header:({column:s})=>e.jsx(E,{column:s,title:"Name"}),enableHiding:!0,enableSorting:!0,cell:s=>s.getValue(),meta:{className:"text-left",displayName:"Name"}},{accessorKey:"extraProperties.Company",header:({column:s})=>e.jsx(E,{column:s,title:"Company"}),enableHiding:!0,enableSorting:!0,cell:s=>s.getValue(),meta:{className:"text-left",displayName:"Company"}},{accessorKey:"email",header:({column:s})=>e.jsx(E,{column:s,title:"Email"}),enableSorting:!0,enableHiding:!0,cell:s=>s.getValue(),meta:{className:"text-left",displayName:"Email"}},{accessorKey:"isActive",header:({column:s})=>e.jsx(E,{column:s,title:"Status"}),enableHiding:!0,enableSorting:!0,cell:s=>e.jsx(ls,{status:s.getValue()}),meta:{className:"text-left",displayName:"Status"}},{id:"actions",header:"Actions",enableHiding:!0,cell:s=>e.jsx(is,{userId:s.row.original.id,userDto:s.row.original,onAction:t,variant:"dropdown"}),meta:{className:"text-right",displayName:"Actions"}}];function cs({table:t,onSearch:s,searchValue:l=""}){const c=t.getState().columnFilters.length>0;return e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-2",children:[s&&e.jsx("div",{className:"w-full sm:w-auto sm:max-w-[250px]",children:e.jsx(We,{onUpdate:s,value:l})}),e.jsxs("div",{className:"flex items-center gap-2 ml-auto",children:[c&&e.jsx(j,{variant:"ghost",onClick:()=>t.resetColumnFilters(),className:"border border-gray-200 px-2 font-semibold text-indigo-600 sm:border-none sm:py-1 dark:border-gray-800 dark:text-indigo-500",children:"Clear filters"}),e.jsx($e,{table:t})]})]})}const ds=()=>{const{toast:t}=H(),s=I(),[l,c]=o.useState(""),[r,x]=o.useState(),[h,f]=o.useState({pageIndex:0,pageSize:10}),[y,d]=o.useState([{id:"name",desc:!1}]),b=i=>{if(!i.length)return"name asc";const S=i[0]??{id:"name",desc:!1};return`${S.id} ${S.desc?"desc":"asc"}`},{isLoading:u,data:g,isError:w}=Je(h.pageIndex,h.pageSize,l,b(y)),v=os((i,S,T)=>{x({userId:i,userDto:S,dialogType:T})}),A=i=>{c(i),f(S=>({...S,pageIndex:0}))},m=i=>{f(i)},a=i=>{d(i),f(S=>({...S,pageIndex:0}))},n=()=>{s.invalidateQueries({queryKey:[U.GetUsers]}),setTimeout(()=>{t({title:"Data refreshed",description:"The user list has been refreshed.",variant:"default"})},800)};return u?e.jsx(Ie,{rowCount:h.pageSize,columnCount:4,hasTitle:!0,hasSearch:!0,hasFilters:!0,hasPagination:!0,hasActions:!0}):w?e.jsx(Ce,{}):e.jsxs(e.Fragment,{children:[r&&r.dialogType==="edit"&&e.jsx(ts,{userId:r.userId,userDto:r.userDto,onDismiss:()=>{s.invalidateQueries({queryKey:[U.GetUsers]}),x(null)}}),r&&r.dialogType==="permission"&&e.jsx(as,{userId:r.userId,userDto:r.userDto,onDismiss:()=>x(null)}),r&&r.dialogType==="delete"&&e.jsx(Xe,{user:{username:r.userDto.userName,userId:r.userId},onDismiss:()=>{s.invalidateQueries({queryKey:[U.GetUsers]}),x(null)}}),e.jsx("div",{className:"space-y-4",children:e.jsx(Be,{title:"Users Management",columns:v,data:g?.items??[],totalCount:g?.totalCount,isLoading:u,manualPagination:!0,manualSorting:!0,pageSize:h.pageSize,onPaginationChange:m,onSortingChange:a,sortingState:y,onSearch:A,searchValue:l,customFilterbar:cs,hideDefaultFilterbar:!0,onRefresh:n,enableRowSelection:!1,actionButton:{onClick:()=>{},content:e.jsxs("div",{className:"flex gap-2",children:[e.jsx(rs,{}),e.jsx(ns,{})]})}})})]})};function Ds(){return e.jsxs(ve,{children:[e.jsx(Ye,{title:"Dashboard"}),e.jsx(ds,{})]})}export{Ds as default};
//# sourceMappingURL=user-Bz9QvVXa.js.map
