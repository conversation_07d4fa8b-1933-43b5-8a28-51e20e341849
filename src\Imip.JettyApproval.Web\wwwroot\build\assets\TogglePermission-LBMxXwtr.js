import{Q as C,ax as G,W as N,ay as f,B as P}from"./app-layout-CNB1Wtrx.js";import{h as p,r as d,j as o}from"./vendor-CrSBzUoz.js";import{C as x}from"./checkbox-CayrCcBd.js";import{e as R}from"./dialog-DAr_Mtxm.js";const L=(n,e)=>p({queryKey:[C.GetPermissions,n,e],queryFn:async()=>{const{data:l}=await G({query:{providerName:n,providerKey:e}});return l}});function A({name:n,id:e,onUpdate:l,className:u,isGranted:t,disabled:s=!1}){const i=d.useCallback(()=>{l?.()},[l]);return o.jsxs("div",{className:N("flex items-center space-x-2 pb-2",u),children:[o.jsx(x,{id:e,onCheckedChange:i,checked:t,disabled:s}),o.jsx("label",{htmlFor:e,className:"text-sm font-medium leading-none",children:n})]})}const g=d.memo(A),m=(n,e,l,u)=>{const t=n.find(i=>!i.parentName&&i.name===l),s=n.filter(i=>i.parentName===l);if(e.parentName===l&&t)return e.isGranted?(e.isGranted=!1,t.isGranted=!1):e.isGranted=!0,t.isGranted||s.every(c=>c.isGranted)&&(t.isGranted=!0),u([...n]),!1;!e.parentName&&e.name===l&&(t?.isGranted?(t.isGranted=!1,s.forEach(i=>i.isGranted=!1)):t&&!t.isGranted&&(t.isGranted=!0,s.forEach(i=>i.isGranted=!0)),u([...n]))},_=n=>n.some(e=>e.name?.startsWith("IdentityServer."))?"identityServer":n.some(e=>e.name?.startsWith("AbpIdentity."))?"identity":n.some(e=>e.name?.startsWith("AbpTenantManagement."))?"tenant":n.some(e=>e.name?.startsWith("FeatureManagement."))?"feature":n.some(e=>e.name?.startsWith("SettingManagement."))?"setting":"",D=n=>{if(!n)return"";const e=n.toLowerCase().replace(/\s/g,"");return e.includes("identityserver")||e.includes("permission:identityserver")?"identityserver":e.includes("identity")&&!e.includes("identityserver")?"identity":e.includes("tenant")?"tenant":e.includes("feature")?"feature":e.includes("setting")?"setting":e},O=({permissions:n,type:e})=>{const[l,u]=d.useState(!1),[t,s]=d.useState(n),i=_(n),c=D(e||i),T=d.useCallback(h=>{const r=t[h];r&&(setTimeout(()=>{const E=t.every(a=>a.isGranted);u(E)},0),c==="identity"?(m(t,r,f.ROLES,s),m(t,r,f.USERS,s)):c==="tenant"?m(t,r,f.TENANTS,s):c==="feature"?m(t,r,f.MANAGE_HOST_FEATURES,s):c==="setting"?m(t,r,f.SETTINGS,s):c==="identityprovider"?(m(t,r,f.IDENTITY_PROVIDER_CLAIMS,s),m(t,r,f.IDENTITY_PROVIDER_CLAIM_TYPES,s),m(t,r,f.IDENTITY_PROVIDER_OPENIDDICT_APPLICATIONS,s),m(t,r,f.IDENTITY_PROVIDER_OPENIDDICT_SCOPES,s),m(t,r,f.IDENTITY_PROVIDER_OPENIDDICT_RESOURCES,s)):(r.isGranted=!r.isGranted,s([...t])))},[t,c]),S=d.useCallback(()=>{u(h=>{const r=!h,E=t.filter(a=>!a.parentName);return E.forEach(a=>{a.isGranted=r,t.filter(I=>I.parentName===a.name).forEach(I=>{I.isGranted=r})}),t.forEach(a=>{!a.parentName&&!E.includes(a)&&(a.isGranted=r)}),s([...t]),r})},[t]);return d.useEffect(()=>{s(n)},[n]),d.useEffect(()=>{u(t.every(h=>h.isGranted))},[t]),{hasAllSelected:l,data:t,onCurrentPermissionChanges:T,onHasAllSelectedUpdate:S}},M=d.memo(function({permissions:e,type:l,hideSelectAll:u,hideSave:t,onSelectedUpdate:s,disabled:i,onCancelEvent:c}){const T=d.useRef(e);d.useEffect(()=>{e.length!==T.current.length&&(T.current=e)},[e]);const{hasAllSelected:S,onCurrentPermissionChanges:h,data:r,onHasAllSelectedUpdate:E}=O({permissions:T.current,type:l});return o.jsxs(o.Fragment,{children:[!u&&o.jsx(g,{name:"Select All",isGranted:S,disabled:i??!1,id:"select_all",onUpdate:E}),r?.map((a,y)=>o.jsx("div",{children:o.jsx(g,{name:a.displayName,isGranted:a.isGranted,id:a.displayName.toLocaleLowerCase().concat(a.parentName),onUpdate:()=>{h(y),s&&s(a)},className:N("ml-5",{"pl-5":a.parentName}),disabled:i??!1})},y)),!t&&o.jsxs(R,{children:[c&&o.jsx(P,{onClick:a=>{a.preventDefault(),c()},children:"Cancel"}),o.jsx(P,{type:"submit",children:"Save"})]})]})});export{g as P,M as T,L as u};
//# sourceMappingURL=TogglePermission-LBMxXwtr.js.map
