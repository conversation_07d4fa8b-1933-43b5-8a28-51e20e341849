import{r as p,j as a}from"./vendor-CrSBzUoz.js";import{V as T,g as S}from"./popover-7NwOVASC.js";import{az as n}from"./app-layout-CNB1Wtrx.js";const m=1,g=1e6;let u=0;function x(){return u=(u+1)%Number.MAX_SAFE_INTEGER,u.toString()}const l=new Map,f=e=>{if(l.has(e))return;const t=setTimeout(()=>{l.delete(e),o({type:"REMOVE_TOAST",toastId:e})},g);l.set(e,t)},A=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,m)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(s=>s.id===t.toast.id?{...s,...t.toast}:s)};case"DISMISS_TOAST":{const{toastId:s}=t;return s?f(s):e.toasts.forEach(r=>{f(r.id)}),{...e,toasts:e.toasts.map(r=>r.id===s||s===void 0?{...r,open:!1}:r)}}case"REMOVE_TOAST":return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(s=>s.id!==t.toastId)}}},i=[];let d={toasts:[]};function o(e){d=A(d,e),i.forEach(t=>{t(d)})}function E({...e}){const t=x(),s=c=>o({type:"UPDATE_TOAST",toast:{...c,id:t}}),r=()=>o({type:"DISMISS_TOAST",toastId:t});return o({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:c=>{c||r()}}}),{id:t,dismiss:r,update:s}}function D(){const[e,t]=p.useState(d);return p.useEffect(()=>(i.push(t),()=>{const s=i.indexOf(t);s>-1&&i.splice(s,1)}),[e]),{...e,toast:E,dismiss:s=>o({type:"DISMISS_TOAST",toastId:s})}}const y=e=>{if(typeof e!="object"||e===null)return!1;const t=e;return!("details"in t)||typeof t.details!="object"||t.details===null?!1:"error"in t.details},I=e=>y(e)?{title:e.details.error.message,description:e.details.error.details,variant:"error"}:{title:"Error",description:"An unexpected error occurred",variant:"error"};function j({column:e,title:t,className:s}){return e.getCanSort()?a.jsxs("div",{onClick:e.getToggleSortingHandler(),className:n(e.columnDef.enableSorting===!0?"-mx-2 inline-flex cursor-pointer select-none items-center gap-2 rounded-md px-2 py-1 hover:bg-gray-50 dark:hover:bg-gray-900":""),children:[a.jsx("span",{children:t}),e.getCanSort()?a.jsxs("div",{className:"-space-y-2",children:[a.jsx(T,{className:n("size-3.5 text-gray-900 dark:text-gray-50",e.getIsSorted()==="desc"?"opacity-30":""),"aria-hidden":"true"}),a.jsx(S,{className:n("size-3.5 text-gray-900 dark:text-gray-50",e.getIsSorted()==="asc"?"opacity-30":""),"aria-hidden":"true"})]}):null]}):a.jsx("div",{className:n(s),children:t})}export{j as D,I as h,E as t,D as u};
//# sourceMappingURL=DataTableColumnHeader-DJ80pmDz.js.map
