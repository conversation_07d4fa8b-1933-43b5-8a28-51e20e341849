using System;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.JettyApproval.Approvals.ApprovalStages;

/// <summary>
/// Entity for storing approval stage information
/// </summary>
public class ApprovalStage : FullAuditedAggregateRoot<Guid>
{
    /// <summary>
    /// Foreign key to the approval template
    /// </summary>
    public Guid ApprovalTemplateId { get; set; }

    /// <summary>
    /// ID of the approver for this stage
    /// </summary>
    public Guid ApproverId { get; set; }

    /// <summary>
    /// Action date and time
    /// </summary>
    public DateTime? ActionDate { get; set; }

    /// <summary>
    /// Document ID for this stage
    /// </summary>
    public string? DocumentId { get; set; }

    /// <summary>
    /// ID of the requester
    /// </summary>
    public string? RequesterId { get; set; }

    /// <summary>
    /// Request date and time
    /// </summary>
    public DateTime? RequestDate { get; set; }

    /// <summary>
    /// Status of the approval stage
    /// </summary>
    public string? Status { get; set; }

    /// <summary>
    /// Additional notes for the stage
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// Navigation property to the approval template
    /// </summary>
    public virtual ApprovalTemplates.ApprovalTemplate ApprovalTemplate { get; set; } = null!;

    /// <summary>
    /// Default constructor for EF Core
    /// </summary>
    protected ApprovalStage()
    {
    }

    /// <summary>
    /// Creates a new ApprovalStage
    /// </summary>
    public ApprovalStage(
        Guid id,
        Guid approvalTemplateId,
        Guid approverId,
        DateTime? actionDate = null,
        string? documentId = null,
        string? requesterId = null,
        DateTime? requestDate = null,
        string? status = null,
        string? notes = null)
        : base(id)
    {
        ApprovalTemplateId = approvalTemplateId;
        ApproverId = approverId;
        ActionDate = actionDate;
        DocumentId = documentId;
        RequesterId = requesterId;
        RequestDate = requestDate;
        Status = status;
        Notes = notes;
    }

    /// <summary>
    /// Creates a new ApprovalStage without ID (for mapping from DTOs)
    /// </summary>
    public ApprovalStage(
        Guid approvalTemplateId,
        Guid approverId,
        DateTime? actionDate = null,
        string? documentId = null,
        string? requesterId = null,
        DateTime? requestDate = null,
        string? status = null,
        string? notes = null)
    {
        ApprovalTemplateId = approvalTemplateId;
        ApproverId = approverId;
        ActionDate = actionDate;
        DocumentId = documentId;
        RequesterId = requesterId;
        RequestDate = requestDate;
        Status = status;
        Notes = notes;
    }
}