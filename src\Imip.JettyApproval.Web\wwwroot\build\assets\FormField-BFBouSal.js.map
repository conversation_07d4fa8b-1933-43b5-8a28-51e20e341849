{"version": 3, "file": "FormField-BFBouSal.js", "sources": ["../../../../../frontend/src/components/ui/divider.tsx", "../../../../../frontend/src/components/ui/FormField.tsx"], "sourcesContent": ["// Tremor Divider [v0.0.2]\r\n\r\nimport React from 'react';\r\n\r\nimport { cx } from '@/lib/utils';\r\n\r\ntype DividerProps = React.ComponentPropsWithoutRef<'div'>;\r\n\r\nconst Divider = React.forwardRef<HTMLDivElement, DividerProps>(\r\n  ({ className, children, ...props }, forwardedRef) => (\r\n    <div\r\n      ref={forwardedRef}\r\n      className={cx(\r\n        // base\r\n        'mx-auto my-6 flex w-full items-center justify-between gap-3 text-sm',\r\n        // text color\r\n        'text-gray-500 dark:text-gray-500',\r\n        className,\r\n      )}\r\n      tremor-id=\"tremor-raw\"\r\n      {...props}\r\n    >\r\n      {children ? (\r\n        <>\r\n          <div\r\n            className={cx(\r\n              // base\r\n              'h-[1px] w-full',\r\n              // background color\r\n              'bg-gray-200 dark:bg-gray-800',\r\n            )}\r\n          />\r\n          <div className=\"whitespace-nowrap text-inherit\">{children}</div>\r\n          <div\r\n            className={cx(\r\n              // base\r\n              'h-[1px] w-full',\r\n              // background color\r\n              'bg-gray-200 dark:bg-gray-800',\r\n            )}\r\n          />\r\n        </>\r\n      ) : (\r\n        <div\r\n          className={cx(\r\n            // base\r\n            'h-[1px] w-full',\r\n            // background color\r\n            'bg-gray-200 dark:bg-gray-800',\r\n          )}\r\n        />\r\n      )}\r\n    </div>\r\n  ),\r\n);\r\n\r\nDivider.displayName = 'Divider';\r\n\r\nexport { Divider };\r\n", "'use client'\r\n\r\nimport React from 'react'\r\nimport { cn } from '@/lib/utils'\r\nimport { Divider } from '@/components/ui/divider'\r\n\r\ninterface FormFieldProps {\r\n  label: string\r\n  description?: string\r\n  className?: string\r\n  labelWidth?: string\r\n  children: React.ReactNode\r\n}\r\n\r\nexport function FormField({\r\n  label,\r\n  description,\r\n  className,\r\n  labelWidth = '200px',\r\n  children\r\n}: FormFieldProps) {\r\n  return (\r\n    <div className={cn(\"grid items-start gap-4\", className)}\r\n      style={{ gridTemplateColumns: `${labelWidth} 1fr` }}>\r\n      <div>\r\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n          {label}\r\n        </label>\r\n        {description && (\r\n          <p className=\"mt-1 text-xs text-gray-500 dark:text-gray-400\">\r\n            {description}\r\n          </p>\r\n        )}\r\n      </div>\r\n      <div>\r\n        {children}\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport function FormSection({ children, className }: { children: React.ReactNode, className?: string }) {\r\n  return (\r\n    <div className={cn(\"space-y-4\", className)}>\r\n      {children}\r\n      <Divider />\r\n    </div>\r\n  )\r\n}\r\n"], "names": ["Divider", "React", "className", "children", "props", "forwardedRef", "jsx", "cx", "jsxs", "Fragment", "FormField", "label", "description", "labelWidth", "cn", "FormSection"], "mappings": "qGAQA,MAAMA,EAAUC,EAAM,WACpB,CAAC,CAAE,UAAAC,EAAW,SAAAC,EAAU,GAAGC,GAASC,IAClCC,EAAA,IAAC,MAAA,CACC,IAAKD,EACL,UAAWE,EAET,sEAEA,mCACAL,CACF,EACA,YAAU,aACT,GAAGE,EAEH,WAEGI,EAAAA,KAAAC,EAAA,SAAA,CAAA,SAAA,CAAAH,EAAA,IAAC,MAAA,CACC,UAAWC,EAET,iBAEA,8BAAA,CACF,CACF,EACCD,EAAAA,IAAA,MAAA,CAAI,UAAU,iCAAkC,SAAAH,CAAS,CAAA,EAC1DG,EAAA,IAAC,MAAA,CACC,UAAWC,EAET,iBAEA,8BAAA,CACF,CAAA,CACF,CAAA,CACF,EAEAD,EAAA,IAAC,MAAA,CACC,UAAWC,EAET,iBAEA,8BAAA,CACF,CAAA,CACF,CAAA,CAIR,EAEAP,EAAQ,YAAc,UC1Cf,SAASU,EAAU,CACxB,MAAAC,EACA,YAAAC,EACA,UAAAV,EACA,WAAAW,EAAa,QACb,SAAAV,CACF,EAAmB,CAEf,OAAAK,EAAA,KAAC,MAAA,CAAI,UAAWM,EAAG,yBAA0BZ,CAAS,EACpD,MAAO,CAAE,oBAAqB,GAAGW,CAAU,MAAO,EAClD,SAAA,CAAAL,OAAC,MACC,CAAA,SAAA,CAACF,EAAA,IAAA,QAAA,CAAM,UAAU,6DACd,SACHK,EAAA,EACCC,GACCN,EAAA,IAAC,IAAE,CAAA,UAAU,gDACV,SACHM,CAAA,CAAA,CAAA,EAEJ,EACAN,MAAC,OACE,SAAAH,CACH,CAAA,CAAA,CAAA,CACF,CAEJ,CAEO,SAASY,EAAY,CAAE,SAAAZ,EAAU,UAAAD,GAAgE,CACtG,cACG,MAAI,CAAA,UAAWY,EAAG,YAAaZ,CAAS,EACtC,SAAA,CAAAC,QACAH,EAAQ,CAAA,CAAA,CAAA,EACX,CAEJ"}