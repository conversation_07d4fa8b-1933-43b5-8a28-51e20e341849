using Imip.JettyApproval.Web.Services.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Imip.JettyApproval.Web.Services;

public class BackgroundTokenRefreshService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<BackgroundTokenRefreshService> _logger;
    private readonly TimeSpan _refreshInterval = TimeSpan.FromMinutes(15); // Refresh every 15 minutes

    public BackgroundTokenRefreshService(
        IServiceProvider serviceProvider,
        ILogger<BackgroundTokenRefreshService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Background token refresh service started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                // Note: This service runs in the background but can only refresh tokens
                // for the current user context. For multi-user token refresh, you would need
                // a more sophisticated approach with user session management.

                _logger.LogDebug("Background token refresh service is running");
                await Task.Delay(_refreshInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                // Service is stopping
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in background token refresh service");
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken); // Wait 1 minute before retrying
            }
        }

        _logger.LogInformation("Background token refresh service stopped");
    }
}