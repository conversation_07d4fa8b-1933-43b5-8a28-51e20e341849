using Imip.JettyApproval.Localization;
using Imip.JettyApproval.Permissions.Apps;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Localization;
using Volo.Abp.MultiTenancy;

namespace Imip.JettyApproval.Permissions;

public class JettyApprovalPermissionDefinitionProvider : PermissionDefinitionProvider
{
    public override void Define(IPermissionDefinitionContext context)
    {
        // var myGroup = context.AddGroup(JettyApprovalPermissions.GroupName);

        var jettyApprovalGroup = context.AddGroup(JettyApprovalPermission.GroupName, L("JettyApprovalApp"));
        DefineJettyApprovalPermissions(jettyApprovalGroup);

        // Define your own permissions here. Example:
        //myGroup.AddPermission(JettyApprovalPermissions.MyPermission1, L("Permission:MyPermission1"));
    }

    private void DefineJettyApprovalPermissions(PermissionGroupDefinition group)
    {
        DefinePermission(group, "JettyRequest", JettyApprovalPermission.PolicyJettyRequest.Default);
        DefinePermission(group, "JettyRequestItem", JettyApprovalPermission.PolicyJettyRequestItem.Default);
        DefinePermission(group, "Attachment", JettyApprovalPermission.PolicyAttachment.Default);
        DefinePermission(group, "Report", JettyApprovalPermission.PolicyReport.Default);
        DefinePermission(group, "JettySchedule", JettyApprovalPermission.PolicyJettySchedule.Default);
        DefinePermission(group, "JettyDockedVessel", JettyApprovalPermission.PolicyJettyDockedVessel.Default);
        DefinePermission(group, "JettyManage", JettyApprovalPermission.PolicyJettyManage.Default);
        DefinePermission(group, "DocumentTemplate", JettyApprovalPermission.PolicyDocumentTemplate.Default);
        DefinePermission(group, "ApprovalRequest", JettyApprovalPermission.PolicyApprovalRequest.Default);
        DefinePermission(group, "ApprovalTemplate", JettyApprovalPermission.PolicyApprovalTemplate.Default);
        DefinePermission(group, "ApprovalApprover", JettyApprovalPermission.PolicyApprovalApprover.Default);
        DefinePermission(group, "ApprovalCriteria", JettyApprovalPermission.PolicyApprovalCriteria.Default);
        DefinePermission(group, "ApprovalStages", JettyApprovalPermission.PolicyApprovalStages.Default);
        DefinePermission(group, "ApprovalDelegation", JettyApprovalPermission.PolicyApprovalDelegation.Default);

    }

    private void DefinePermission(PermissionGroupDefinition group, string displayName, string defaultPermission)
    {
        var permission = group.AddPermission(defaultPermission, L(displayName));
        permission.AddChild(defaultPermission + ".View", L("View"));
        permission.AddChild(defaultPermission + ".Create", L("Create"));
        permission.AddChild(defaultPermission + ".Edit", L("Edit"));
        permission.AddChild(defaultPermission + ".Delete", L("Delete"));

        if (displayName == "Report")
        {
            permission.AddChild(defaultPermission + ".Export", L("Export"));
            permission.AddChild(defaultPermission + ".Execute", L("Execute"));
        }
    }

    private static LocalizableString L(string name)
    {
        return LocalizableString.Create<JettyApprovalResource>(name);
    }
}
