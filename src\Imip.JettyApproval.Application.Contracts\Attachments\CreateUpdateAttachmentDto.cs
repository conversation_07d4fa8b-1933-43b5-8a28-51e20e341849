using System;
using System.ComponentModel.DataAnnotations;

namespace Imip.JettyApproval.Attachments;

/// <summary>
/// DTO for creating and updating Attachment entity
/// </summary>
public class CreateUpdateAttachmentDto
{
    /// <summary>
    /// The name of the file
    /// </summary>
    [Required]
    [StringLength(255)]
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// The content type of the file
    /// </summary>
    [Required]
    [StringLength(100)]
    public string ContentType { get; set; } = string.Empty;

    /// <summary>
    /// The size of the file in bytes
    /// </summary>
    [Range(0, long.MaxValue)]
    public long Size { get; set; }

    /// <summary>
    /// The blob name used to store the file in the blob storage
    /// </summary>
    [Required]
    [StringLength(500)]
    public string BlobName { get; set; } = string.Empty;

    /// <summary>
    /// Optional description of the file
    /// </summary>
    [StringLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Optional reference ID that this file is associated with
    /// </summary>
    public Guid? ReferenceId { get; set; }

    /// <summary>
    /// Optional reference type that this file is associated with
    /// </summary>
    [StringLength(100)]
    public string? ReferenceType { get; set; }
}