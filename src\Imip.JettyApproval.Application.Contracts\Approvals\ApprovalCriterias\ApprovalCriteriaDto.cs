using System;
using Volo.Abp.Application.Dtos;

namespace Imip.JettyApproval.Approvals.ApprovalCriterias;

/// <summary>
/// DTO for ApprovalCriteria entity
/// </summary>
public class ApprovalCriteriaDto : FullAuditedEntityDto<Guid>
{
    /// <summary>
    /// Foreign key to the approval template
    /// </summary>
    public Guid ApprovalId { get; set; }

    /// <summary>
    /// Type of document for approval criteria
    /// </summary>
    public string? DocumentType { get; set; }
}