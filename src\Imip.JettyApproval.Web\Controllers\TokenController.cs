using Imip.JettyApproval.Web.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using Volo.Abp.AspNetCore.Mvc;

namespace Imip.JettyApproval.Web.Controllers;

[Authorize]
public class TokenController : AbpController
{
    private readonly ITokenService _tokenService;

    public TokenController(ITokenService tokenService)
    {
        _tokenService = tokenService;
    }

    [HttpGet("/api/token/access")]
    public async Task<IActionResult> GetAccessToken()
    {
        var token = await _tokenService.GetAccessTokenAsync();
        if (string.IsNullOrEmpty(token))
        {
            return NotFound("No access token found");
        }

        return Ok(new { access_token = token });
    }

    [HttpGet("/api/token/refresh")]
    public async Task<IActionResult> RefreshToken()
    {
        var newToken = await _tokenService.RefreshAccessTokenAsync();
        if (string.IsNullOrEmpty(newToken))
        {
            return BadRequest("Failed to refresh token");
        }

        return Ok(new { access_token = newToken });
    }

    [HttpGet("/api/token/status")]
    public async Task<IActionResult> GetTokenStatus()
    {
        var hasValidToken = await _tokenService.HasValidTokenAsync();
        var isRefreshTokenExpired = await _tokenService.IsRefreshTokenExpiredAsync();

        return Ok(new
        {
            has_valid_token = hasValidToken,
            refresh_token_expired = isRefreshTokenExpired
        });
    }

    [HttpGet("/api/token/valid")]
    public async Task<IActionResult> GetValidToken()
    {
        var token = await _tokenService.GetValidAccessTokenAsync();
        if (string.IsNullOrEmpty(token))
        {
            return BadRequest("Failed to get valid token");
        }

        return Ok(new { access_token = token });
    }

    [HttpGet("/api/token/expiration")]
    public async Task<IActionResult> GetTokenExpirationInfo()
    {
        var accessToken = await _tokenService.GetAccessTokenAsync();
        var refreshToken = await _tokenService.GetRefreshTokenAsync();

        var accessTokenValid = await _tokenService.HasValidTokenAsync();
        var refreshTokenExpired = await _tokenService.IsRefreshTokenExpiredAsync();

        return Ok(new
        {
            access_token_valid = accessTokenValid,
            refresh_token_expired = refreshTokenExpired,
            has_access_token = !string.IsNullOrEmpty(accessToken),
            has_refresh_token = !string.IsNullOrEmpty(refreshToken)
        });
    }
}