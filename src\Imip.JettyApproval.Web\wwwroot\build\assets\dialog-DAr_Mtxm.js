import{j as t}from"./vendor-CrSBzUoz.js";import{R as i,C as r,E as d,F as x,G as m,H as g,a as c,O as u}from"./radix-DaY-mnHi.js";import{M as e,X as f,aE as p}from"./app-layout-CNB1Wtrx.js";const j=p("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200",{variants:{size:{sm:"sm:max-w-sm",md:"sm:max-w-md",lg:"sm:max-w-lg",xl:"sm:max-w-xl","2xl":"sm:max-w-2xl","3xl":"sm:max-w-3xl","4xl":"sm:max-w-4xl","5xl":"sm:max-w-5xl","6xl":"sm:max-w-6xl","7xl":"sm:max-w-7xl",full:"sm:max-w-full"}},defaultVariants:{size:"lg"}});function y({...a}){return t.jsx(i,{"data-slot":"dialog",...a})}function z({...a}){return t.jsx(g,{"data-slot":"dialog-trigger",...a})}function w({...a}){return t.jsx(c,{"data-slot":"dialog-portal",...a})}function v({className:a,...s}){return t.jsx(u,{"data-slot":"dialog-overlay",className:e("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...s})}function N({className:a,children:s,showCloseButton:o=!0,size:l,...n}){return t.jsxs(w,{"data-slot":"dialog-portal",children:[t.jsx(v,{}),t.jsxs(r,{"data-slot":"dialog-content",className:e(j({size:l}),a),...n,children:[s,o&&t.jsxs(d,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[t.jsx(f,{}),t.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}function C({className:a,...s}){return t.jsx("div",{"data-slot":"dialog-header",className:e("flex flex-col gap-2 text-center sm:text-left",a),...s})}function k({className:a,...s}){return t.jsx("div",{"data-slot":"dialog-footer",className:e("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...s})}function T({className:a,...s}){return t.jsx(x,{"data-slot":"dialog-title",className:e("text-lg leading-none font-semibold",a),...s})}function E({className:a,...s}){return t.jsx(m,{"data-slot":"dialog-description",className:e("text-muted-foreground text-sm",a),...s})}export{y as D,z as a,N as b,C as c,T as d,k as e,E as f};
//# sourceMappingURL=dialog-DAr_Mtxm.js.map
