import { type IdentityRoleUpdateDto, putApiIdentityRolesById } from '@/client'
import { useToast } from '@/lib/useToast'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { FormField, FormSection } from '@/components/ui/FormField'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { QueryNames } from '@/lib/hooks/QueryConstants'
import { handleApiError } from '@/lib/handleApiError'

type UserEditProps = {
  dataEdit: IdentityRoleUpdateDto
  dataId: string
  onDismiss: () => void
}
export const Edit = ({ dataEdit, dataId, onDismiss }: UserEditProps) => {
  const [open, setOpen] = useState(false)
  const { toast } = useToast()
  const queryClient = useQueryClient()
  const { handleSubmit, register, setValue } = useForm<IdentityRoleUpdateDto>()
  const [isDefault, setIsDefault] = useState(dataEdit.isDefault ?? false)
  const [isPublic, setIsPublic] = useState(dataEdit.isPublic ?? false)

  const updateDataMutation = useMutation({
    mutationFn: async (formData: IdentityRoleUpdateDto) =>
      putApiIdentityRolesById({
        path: { id: dataId },
        body: {
          name: formData.name ?? dataEdit.name ?? '',
          isDefault: formData.isDefault ?? dataEdit.isDefault ?? false,
          isPublic: formData.isPublic ?? dataEdit.isPublic ?? false,
        },
      }),
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Role Updated Successfully',
        variant: 'success',
      })
      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetRoles] })
      onCloseEvent()
    },
    onError: (err: unknown) => {
      console.log('Error updating claim:', err);
      const error = handleApiError(err);
      toast({
        title: error.title,
        description: error.description,
        variant: 'error',
      })
    }
  })

  const onSubmit = (formData: IdentityRoleUpdateDto) => {
    // Merge form data with consent type and permissions
    const userData: IdentityRoleUpdateDto = {
      ...formData,
    }

    updateDataMutation.mutate(userData)
  }

  const onCloseEvent = () => {
    setOpen(false)
    onDismiss()
  }

  // Initialize form when opened
  useEffect(() => {
    if (open) {
      // Initialize form values from dataEdit
      setValue('name', dataEdit.name || '')
      setValue('isDefault', dataEdit.isDefault ?? false)
      setValue('isPublic', dataEdit.isPublic ?? false)
    }
  }, [open, dataEdit, setValue])

  useEffect(() => {
    setOpen(true)
  }, [])

  return (
    <Dialog open={open} onOpenChange={onCloseEvent}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Update a Role: {dataEdit.name}</DialogTitle>
        </DialogHeader>
        <form
          onSubmit={handleSubmit(onSubmit)}
          className='mt-2'
          onKeyDown={(e) => {
            if (e.key === 'Enter' && e.target instanceof HTMLInputElement) {
              e.preventDefault();
              void handleSubmit(onSubmit)();
            }
          }}
        >
          <section className="flex w-full flex-col space-y-2">
            <FormSection>
              <FormField
                label="Name"
                description="The name of the role"
              >
                <Input required {...register('name')} defaultValue={dataEdit.name ?? ''} placeholder="Role Name" />
              </FormField>

              <FormField
                label="Default"
                description="Whether the role is default"
              >
                <Checkbox
                  checked={isDefault}
                  onCheckedChange={(checked) => {
                    setIsDefault(!!checked)
                    setValue('isDefault', !!checked)
                  }}
                />
              </FormField>

              <FormField
                label="Public"
                description="Whether the role is public"
              >
                <Checkbox
                  checked={isPublic}
                  onCheckedChange={(checked) => {
                    setIsPublic(!!checked)
                    setValue('isPublic', !!checked)
                  }}
                />
              </FormField>
            </FormSection>
          </section>
          <DialogFooter className="mt-5">
            <Button
              variant="ghost"
              onClick={(e) => {
                e.preventDefault()
                setOpen(false)
              }}
              disabled={updateDataMutation.isPending}
              type="button"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={updateDataMutation.isPending}
            >
              {updateDataMutation.isPending ? 'Saving...' : 'Save'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
