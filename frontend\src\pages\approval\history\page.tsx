import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { DropdownMenu, DropdownMenuCheckboxItem, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { IconChevronDown, IconDotsVertical } from '@tabler/icons-react';
import { useState } from 'react';

interface ApprovalHistory {
  id: string;
  vesselName: string;
  arrivalDate: string;
  departureDate: string;
  itemName: string;
  requestBy: string;
  status: 'Approved' | 'Rejected';
}

const mockHistoryData: ApprovalHistory[] = Array(10).fill(null).map((_, i) => ({
  id: `hist_${i + 1}`,
  vesselName: `MV. GRAND LINE V. 00${i + 1}`,
  arrivalDate: '2025-04-15',
  departureDate: '2025-04-16',
  itemName: 'COAL 50MT',
  requestBy: 'USER2',
  status: i % 2 === 0 ? 'Approved' : 'Rejected',
}));

export default function ApprovalHistoryList() {
  const [filter, setFilter] = useState('');
  const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set());
  const [visibleColumns, setVisibleColumns] = useState<Set<keyof ApprovalHistory>>(
    new Set(Object.keys(mockHistoryData[0]) as (keyof ApprovalHistory)[])
  );

  const filteredData = mockHistoryData.filter(history =>
    Object.values(history).some(value =>
      value.toString().toLowerCase().includes(filter.toLowerCase())
    )
  );

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedRows(new Set(filteredData.map(row => row.id)));
    } else {
      setSelectedRows(new Set());
    }
  };

  const handleRowSelect = (id: string, checked: boolean) => {
    const newSelection = new Set(selectedRows);
    if (checked) {
      newSelection.add(id);
    } else {
      newSelection.delete(id);
    }
    setSelectedRows(newSelection);
  };

  const handleToggleColumn = (columnKey: keyof ApprovalHistory, checked: boolean) => {
    const newVisibleColumns = new Set(visibleColumns);
    if (checked) {
      newVisibleColumns.add(columnKey);
    } else {
      newVisibleColumns.delete(columnKey);
    }
    setVisibleColumns(newVisibleColumns);
  };

  const handleViewDetails = (id: string) => {
    console.log(`View details for history ID: ${id}`);
    // Implement navigation or dialog for viewing details
  };

  return (
    <AppLayout>
      <div className="container mx-auto p-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-bold">Approval History</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between mb-4">
              <Input
                placeholder="Filter lines..."
                value={filter}
                onChange={(e) => setFilter(e.target.value)}
                className="max-w-sm"
              />
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="ml-auto">
                    Columns <IconChevronDown className="ml-2 h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {Object.keys(mockHistoryData[0]).map((key) => (
                    <DropdownMenuCheckboxItem
                      key={key}
                      className="capitalize"
                      checked={visibleColumns.has(key as keyof ApprovalHistory)}
                      onCheckedChange={(checked) => handleToggleColumn(key as keyof ApprovalHistory, checked === true)}
                    >
                      {key.replace(/([A-Z])/g, ' $1').trim()}
                    </DropdownMenuCheckboxItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[30px]">
                      <Checkbox
                        checked={selectedRows.size === filteredData.length && filteredData.length > 0}
                        onCheckedChange={(checked) => handleSelectAll(checked === true)}
                      />
                    </TableHead>
                    {Object.keys(mockHistoryData[0]).map((key) => (visibleColumns.has(key as keyof ApprovalHistory) && key !== 'id' &&
                      <TableHead key={key} className="capitalize">
                        {key.replace(/([A-Z])/g, ' $1').trim()}
                      </TableHead>
                    ))}
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredData.map((historyItem) => (
                    <TableRow key={historyItem.id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedRows.has(historyItem.id)}
                          onCheckedChange={(checked) => handleRowSelect(historyItem.id, checked === true)}
                        />
                      </TableCell>
                      {Object.entries(historyItem).map(([key, value]) => (visibleColumns.has(key as keyof ApprovalHistory) && key !== 'id' &&
                        <TableCell key={key}>{value}</TableCell>
                      ))}
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <IconDotsVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => handleViewDetails(historyItem.id)}>
                              View Details
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-gray-500">
                {selectedRows.size} of {filteredData.length} row(s) selected.
              </div>
              <div className="space-x-2">
                <Button variant="outline" size="sm">Previous</Button>
                <Button variant="outline" size="sm">Next</Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
