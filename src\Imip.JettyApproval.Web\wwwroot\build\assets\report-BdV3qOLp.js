import{r,j as e}from"./vendor-CrSBzUoz.js";import{A as N,L as x,S as j,a as g,b,c as y,d as v,I as A,B as $}from"./app-layout-CNB1Wtrx.js";import{C as S,a as f,b as C,c as R}from"./card-BAJCNJxm.js";import{D as V,H as I,r as J}from"./ht-theme-main.min-D0JFAomv.js";import"./radix-DaY-mnHi.js";import"./App-CGHLK9xH.js";import"./chevron-left-BtkzUODq.js";J();const D=[{id:"jetty_schedule_report",name:"Jetty Schedule Report",parameters:[{name:"startDate",label:"Start Date",type:"date"},{name:"endDate",label:"End Date",type:"date"},{name:"jetty",label:"Jetty",type:"select",options:[{value:"all",label:"All Jetties"},{value:"jetty_a",label:"Jetty A"},{value:"jetty_b",label:"Jetty B"},{value:"jetty_c",label:"Jetty C"}]},{name:"status",label:"Status",type:"select",options:[{value:"all",label:"All Status"},{value:"scheduled",label:"Scheduled"},{value:"in_progress",label:"In Progress"},{value:"completed",label:"Completed"}]}],mockDataGenerator:s=>{const n=[];for(let a=0;a<10;a++)n.push([`Vessel_${a+1}`,`2024-03-${10+a}`,`2024-03-${12+a}`,s.jetty==="all"?`Jetty ${String.fromCharCode(65+a%3)}`:s.jetty,["scheduled","in_progress","completed"][a%3],`Cargo Type ${a%3+1}`,`${Math.floor(Math.random()*1e3)} tons`]);return n},columns:[{data:0,title:"Vessel Name"},{data:1,title:"Arrival Date"},{data:2,title:"Departure Date"},{data:3,title:"Jetty"},{data:4,title:"Status"},{data:5,title:"Cargo Type"},{data:6,title:"Cargo Volume"}]},{id:"bounded_zone_report",name:"Bounded Zone Report",parameters:[{name:"date",label:"Date",type:"date"},{name:"zone",label:"Zone",type:"select",options:[{value:"all",label:"All Zones"},{value:"zone_a",label:"Zone A"},{value:"zone_b",label:"Zone B"},{value:"zone_c",label:"Zone C"}]},{name:"activity",label:"Activity Type",type:"select",options:[{value:"all",label:"All Activities"},{value:"loading",label:"Loading"},{value:"unloading",label:"Unloading"},{value:"storage",label:"Storage"}]}],mockDataGenerator:s=>{const n=[];for(let a=0;a<10;a++)n.push([s.zone==="all"?`Zone ${String.fromCharCode(65+a%3)}`:s.zone,["loading","unloading","storage"][a%3],`Cargo_${a+1}`,`${Math.floor(Math.random()*500)} tons`,`Vessel_${a+1}`,`Operator_${a+1}`,s.date?s.date.toString():"2024-03-15"]);return n},columns:[{data:0,title:"Zone"},{data:1,title:"Activity Type"},{data:2,title:"Cargo ID"},{data:3,title:"Volume"},{data:4,title:"Vessel"},{data:5,title:"Operator"},{data:6,title:"Date"}]}],B=()=>{const[s,n]=r.useState(""),[a,h]=r.useState(null),[d,c]=r.useState({}),[p,i]=r.useState([]),u=r.useRef(null);r.useEffect(()=>{if(s){const t=D.find(o=>o.id===s);h(t||null);const l={};t?.parameters.forEach(o=>{o.type==="date"?l[o.name]=null:l[o.name]=""}),c(l),i([])}else h(null),c({}),i([])},[s]);const m=(t,l)=>{c(o=>({...o,[t]:l}))},_=()=>{if(a){const t=a.mockDataGenerator(d);i(t),u.current?.hotInstance&&u.current.hotInstance.loadData(t)}};return e.jsx(N,{children:e.jsxs("div",{className:"flex flex-col space-y-2 p-2",children:[e.jsxs(S,{children:[e.jsx(f,{children:e.jsx(C,{children:"Report Selection and Parameters"})}),e.jsxs(R,{className:"p-3",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx(x,{htmlFor:"report-select",className:"text-sm mb-1",children:"Select Report"}),e.jsxs(j,{onValueChange:n,value:s,children:[e.jsx(g,{className:"w-[240px]",children:e.jsx(b,{placeholder:"Select a report"})}),e.jsx(y,{children:D.map(t=>e.jsx(v,{value:t.id,children:t.name},t.id))})]})]}),a&&e.jsxs(e.Fragment,{children:[e.jsxs("h4",{className:"text-lg font-semibold mb-3",children:[a.name," Parameters"]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-x-2 gap-y-2",children:a.parameters.map(t=>e.jsxs("div",{children:[e.jsx(x,{htmlFor:t.name,className:"text-sm mb-1",children:t.label}),t.type==="text"&&e.jsx(A,{id:t.name,value:d[t.name]||"",onChange:l=>m(t.name,l.target.value),className:"w-[240px]"}),t.type==="date"&&e.jsx(V,{value:d[t.name]||null,onChange:l=>m(t.name,l)}),t.type==="select"&&e.jsxs(j,{value:d[t.name]||"",onValueChange:l=>m(t.name,l),children:[e.jsx(g,{className:"w-[240px]",children:e.jsx(b,{placeholder:`Select ${t.label}`})}),e.jsx(y,{children:t.options?.map(l=>e.jsx(v,{value:l.value,children:l.label},l.value))})]})]},t.name))}),e.jsx($,{onClick:_,className:"mt-4",children:"Run Report"})]})]})]}),p.length>0&&a&&e.jsxs(S,{children:[e.jsx(f,{children:e.jsxs(C,{children:[a.name," Results"]})}),e.jsx(R,{className:"p-3",children:e.jsx(I,{ref:u,themeName:"ht-theme-main",data:p,columns:a.columns,colHeaders:a.columns.map(t=>t.title),rowHeaders:!0,height:"auto",width:"auto",stretchH:"all",licenseKey:"non-commercial-and-evaluation"})})]})]})})};export{B as default};
//# sourceMappingURL=report-BdV3qOLp.js.map
