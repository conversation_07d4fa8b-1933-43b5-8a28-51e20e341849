using Imip.JettyApproval.Approvals;
using Imip.JettyApproval.Approvals.ApprovalTemplates;
using Imip.JettyApproval.Mapping.Mappers;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.JettyApproval.Approvals;

/// <summary>
/// Application service for ApprovalTemplate entity
/// </summary>
public class ApprovalTemplateAppService :
    CrudAppService<ApprovalTemplate, ApprovalTemplateDto, Guid, PagedAndSortedResultRequestDto, CreateUpdateApprovalTemplateDto, CreateUpdateApprovalTemplateDto>,
    IApprovalTemplateAppService
{
    private readonly IApprovalTemplateRepository _approvalTemplateRepository;
    private readonly ApprovalTemplateMapper _mapper;
    private readonly ILogger<ApprovalTemplateAppService> _logger;

    public ApprovalTemplateAppService(
        IApprovalTemplateRepository approvalTemplateRepository,
        ApprovalTemplateMapper mapper,
        ILogger<ApprovalTemplateAppService> logger)
        : base(approvalTemplateRepository)
    {
        _approvalTemplateRepository = approvalTemplateRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public override async Task<ApprovalTemplateDto> CreateAsync(CreateUpdateApprovalTemplateDto input)
    {
        await CheckCreatePolicyAsync();

        var entity = _mapper.CreateEntityWithId(input, Guid.NewGuid());

        await _approvalTemplateRepository.InsertAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task<ApprovalTemplateDto> UpdateAsync(Guid id, CreateUpdateApprovalTemplateDto input)
    {
        await CheckUpdatePolicyAsync();

        var entity = await _approvalTemplateRepository.GetAsync(id);

        _mapper.MapToEntity(input, entity);

        await _approvalTemplateRepository.UpdateAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task DeleteAsync(Guid id)
    {
        await CheckDeletePolicyAsync();

        var entity = await _approvalTemplateRepository.GetAsync(id);

        await _approvalTemplateRepository.DeleteAsync(entity, autoSave: true);
    }

    public override async Task<ApprovalTemplateDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var entity = await _approvalTemplateRepository.GetAsync(id);
        return _mapper.MapToDto(entity);
    }

    public override async Task<PagedResultDto<ApprovalTemplateDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        await CheckGetListPolicyAsync();

        var queryable = await _approvalTemplateRepository.GetQueryableAsync();

        // Apply sorting
        if (!string.IsNullOrWhiteSpace(input.Sorting))
        {
            queryable = queryable.OrderBy(input.Sorting);
        }
        else
        {
            queryable = queryable.OrderByDescending(x => x.CreationTime);
        }

        var totalCount = await AsyncExecuter.CountAsync(queryable);

        var entities = await AsyncExecuter.ToListAsync(
            queryable.PageBy(input.SkipCount, input.MaxResultCount)
        );

        var dtos = entities.Select(_mapper.MapToDto).ToList();

        return new PagedResultDto<ApprovalTemplateDto>(totalCount, dtos);
    }
}