using Imip.JettyApproval.Approvals.ApprovalDelegations;
using Imip.JettyApproval.Mapping.Mappers;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.JettyApproval.Approvals.ApprovalDelegations;

/// <summary>
/// Application service for ApprovalDelegation entity
/// </summary>
public class ApprovalDelegationAppService :
    CrudAppService<ApprovalDelegation, ApprovalDelegationDto, Guid, PagedAndSortedResultRequestDto, CreateUpdateApprovalDelegationDto, CreateUpdateApprovalDelegationDto>,
    IApprovalDelegationAppService
{
    private readonly IApprovalDelegationRepository _approvalDelegationRepository;
    private readonly ApprovalDelegationMapper _mapper;
    private readonly ILogger<ApprovalDelegationAppService> _logger;

    public ApprovalDelegationAppService(
        IApprovalDelegationRepository approvalDelegationRepository,
        ApprovalDelegationMapper mapper,
        ILogger<ApprovalDelegationAppService> logger)
        : base(approvalDelegationRepository)
    {
        _approvalDelegationRepository = approvalDelegationRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public override async Task<ApprovalDelegationDto> CreateAsync(CreateUpdateApprovalDelegationDto input)
    {
        await CheckCreatePolicyAsync();

        var entity = _mapper.CreateEntityWithId(input, Guid.NewGuid());

        await _approvalDelegationRepository.InsertAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task<ApprovalDelegationDto> UpdateAsync(Guid id, CreateUpdateApprovalDelegationDto input)
    {
        await CheckUpdatePolicyAsync();

        var entity = await _approvalDelegationRepository.GetAsync(id);

        _mapper.MapToEntity(input, entity);

        await _approvalDelegationRepository.UpdateAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task DeleteAsync(Guid id)
    {
        await CheckDeletePolicyAsync();

        var entity = await _approvalDelegationRepository.GetAsync(id);

        await _approvalDelegationRepository.DeleteAsync(entity, autoSave: true);
    }

    public override async Task<ApprovalDelegationDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var entity = await _approvalDelegationRepository.GetAsync(id);
        return _mapper.MapToDto(entity);
    }

    public override async Task<PagedResultDto<ApprovalDelegationDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        await CheckGetListPolicyAsync();

        var queryable = await _approvalDelegationRepository.GetQueryableAsync();

        // Apply sorting
        if (!string.IsNullOrWhiteSpace(input.Sorting))
        {
            queryable = queryable.OrderBy(input.Sorting);
        }
        else
        {
            queryable = queryable.OrderByDescending(x => x.CreationTime);
        }

        var totalCount = await AsyncExecuter.CountAsync(queryable);

        var entities = await AsyncExecuter.ToListAsync(
            queryable.PageBy(input.SkipCount, input.MaxResultCount)
        );

        var dtos = entities.Select(_mapper.MapToDto).ToList();

        return new PagedResultDto<ApprovalDelegationDto>(totalCount, dtos);
    }
}