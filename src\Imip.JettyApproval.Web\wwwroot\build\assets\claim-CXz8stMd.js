import{r as u,j as e,u as M,f as Q,h as X}from"./vendor-CrSBzUoz.js";import{k as Z,u as z,D as ee,e as te,B as j,f as se,j as O,T as ie,I as f,p as ae,Q as I,l as ne,m as re,A as le}from"./app-layout-CNB1Wtrx.js";import{u as q,h as H,D as P,t as oe}from"./DataTableColumnHeader-DJ80pmDz.js";import{A as ce,a as de,b as ue,c as pe,d as me,e as he,f as xe,g as ge,T as ye}from"./TableSkeleton-Bzdk6y-O.js";import{D as fe}from"./DataTable-BL4SJdnU.js";import{l as je,p as Se,_ as Ce,a as Te}from"./popover-7NwOVASC.js";import{C as w}from"./checkbox-CayrCcBd.js";import{c as K,M as U}from"./multi-select-BfYI64yx.js";import{D as B,a as ve,b as E,c as G,d as L,e as J}from"./dialog-DAr_Mtxm.js";import{u as W}from"./index.esm-CT1elm-0.js";import{F as Y,a as p}from"./FormField-BFBouSal.js";import{g as be,e as De,N as Ne}from"./NotionFilter-COtzX9y0.js";import{$ as Ie}from"./App-CGHLK9xH.js";import"./radix-DaY-mnHi.js";import"./card-BAJCNJxm.js";import"./table-CAbNlII1.js";import"./tiny-invariant-CopsF_GD.js";import"./search-YAT3sv3T.js";import"./index-CZZWNLgr.js";import"./badge-BtBZs1VC.js";import"./scroll-area-5IehhMpa.js";const we=({dataId:s,onDismiss:t})=>{const{toast:n}=q(),[l,i]=u.useState(!1),h=async()=>{try{await Z({path:{id:s}}),n({title:"Success",description:"Claim Type has been deleted successfully."}),t()}catch(d){const o=H(d);n({title:o.title,description:o.description,variant:"error"})}};return u.useEffect(()=>{i(!0)},[]),e.jsx(ce,{open:l,children:e.jsxs(de,{children:[e.jsxs(ue,{children:[e.jsx(pe,{children:"Are you absolutely sure?"}),e.jsx(me,{children:"This action cannot be undone. This will permanently delete your this claim type."})]}),e.jsxs(he,{children:[e.jsx(xe,{onClick:t,children:"Cancel"}),e.jsx(ge,{onClick:h,children:"Yes"})]})]})})},Ae=({userId:s,userDto:t,onAction:n,variant:l="dropdown"})=>{const{can:i}=z();return l==="dropdown"?e.jsx("div",{className:"flex justify-end",children:e.jsxs(ee,{children:[e.jsx(te,{asChild:!0,children:e.jsxs(j,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:[e.jsx(je,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"Open menu"})]})}),e.jsxs(se,{align:"end",className:"w-[160px]",children:[i("IdentityServer.ClaimTypes.Edit")&&e.jsx(O,{className:"cursor-pointer text-sm",onClick:()=>n(s,t,"edit"),children:"Edit"}),i("IdentityServer.ClaimTypes.Delete")&&e.jsx(O,{className:"cursor-pointer text-sm text-red-500",onClick:()=>n(s,t,"delete"),children:"Delete"})]})]})}):e.jsxs("div",{className:"flex items-center justify-end gap-1",children:[i("AbpIdentity.Users.ManagePermissions")&&e.jsxs(j,{variant:"primary",size:"sm",className:"flex items-center gap-1 px-2 py-1",onClick:()=>n(s,t,"permission"),children:[e.jsx(Se,{className:"h-4 w-4"}),e.jsx("span",{children:"Permission"})]}),i("AbpIdentity.Users.Update")&&e.jsxs(j,{variant:"primary",size:"sm",className:"flex items-center gap-1 px-2 py-1",onClick:()=>n(s,t,"edit"),children:[e.jsx(Ce,{className:"h-4 w-4"}),e.jsx("span",{children:"Edit"})]})]})},Fe=s=>[{id:"select",header:({table:t})=>e.jsx(w,{checked:t.getIsAllPageRowsSelected()?!0:t.getIsSomeRowsSelected()?"indeterminate":!1,onCheckedChange:()=>t.toggleAllPageRowsSelected(),className:"translate-y-0.5","aria-label":"Select all"}),cell:({row:t})=>e.jsx(w,{checked:t.getIsSelected(),onCheckedChange:()=>t.toggleSelected(),className:"translate-y-0.5","aria-label":"Select row"}),enableSorting:!1,enableHiding:!0,meta:{displayName:"Select"}},{accessorKey:"name",header:({column:t})=>e.jsx(P,{column:t,title:"Name"}),enableSorting:!0,enableHiding:!0,cell:t=>t.getValue(),filterFn:K,meta:{className:"text-left",displayName:"Name"}},{accessorKey:"description",header:({column:t})=>e.jsx(P,{column:t,title:"Description"}),enableSorting:!0,enableHiding:!0,filterFn:K,cell:t=>t.getValue(),meta:{className:"text-left",displayName:"Description"}},{accessorKey:"valueType",header:({column:t})=>e.jsx(P,{column:t,title:"Value Type"}),cell:({row:t})=>{const n=t.getValue("valueType");return{0:"String",1:"Int",2:"Boolean",3:"DateTime"}[n]||"Unknown"},enableSorting:!0,enableHiding:!0,meta:{className:"text-left",displayName:"Value Type"}},{id:"actions",header:"Actions",cell:t=>e.jsx(Ae,{userId:t.row.original.id,userDto:t.row.original,onAction:s,variant:"dropdown"}),enableSorting:!1,enableHiding:!0,meta:{className:"text-right",displayName:"Action"}}],_=[{label:"String",value:"0"},{label:"Int",value:"1"},{label:"Boolean",value:"2"},{label:"DateTime",value:"3"}],qe=({children:s})=>{const{can:t}=z(),[n,l]=u.useState(!1),{toast:i}=q(),h=M(),{handleSubmit:d,register:o,reset:m}=W(),[c,v]=u.useState([]);u.useEffect(()=>{n||(m({name:"",required:!1,isStatic:!1,regex:"",regexDescription:"",description:"",valueType:0}),v([]))},[n,m]);const y=Q({mutationFn:async r=>ae({body:r}),onSuccess:()=>{i({title:"Success",description:"Claim Type Created Successfully",variant:"success"}),h.invalidateQueries({queryKey:[I.GetIdentityClaimTypes]}),l(!1)},onError:r=>{const g=H(r);i({title:g.title,description:g.description,variant:"error"})}}),S=r=>{const g={...r};y.mutate(g)},b=r=>{l(r)};return e.jsxs("section",{children:[e.jsx(ie,{}),e.jsxs(B,{open:n,onOpenChange:b,children:[e.jsx(ve,{asChild:!0,children:s}),e.jsx("section",{className:"flex items-center justify-between pb-5",children:t("IdentityServer.ClaimTypes.Create")&&e.jsxs(j,{size:"sm",className:"w-full sm:py-1 sm:mt-0 sm:w-fit",onClick:()=>l(!0),children:[e.jsx(Te,{className:"-ml-1 size-4 shrink-0","aria-hidden":"true"}),e.jsx("span",{className:"hidden truncate sm:inline",children:"New Claim Type"})]})}),e.jsxs(E,{className:"max-w-2xl",children:[e.jsx(G,{children:e.jsx(L,{children:"Create a New Claim Type"})}),e.jsxs("form",{onSubmit:d(S),className:"mt-2",onKeyDown:r=>{r.key==="Enter"&&r.target instanceof HTMLInputElement&&(r.preventDefault(),d(S)())},children:[e.jsx("section",{className:"flex w-full flex-col space-y-2",children:e.jsxs(Y,{children:[e.jsx(p,{label:"Name",description:"The name of the claim",children:e.jsx(f,{required:!0,...o("name"),placeholder:"Claim Name"})}),e.jsx(p,{label:"Required",description:"Whether the claim is required",children:e.jsx(w,{...o("required",{setValueAs:r=>r===!0})})}),e.jsx(p,{label:"Is Static",description:"Whether the claim is static",children:e.jsx(w,{...o("isStatic",{setValueAs:r=>r===!0})})}),e.jsx(p,{label:"Regex",description:"The regex for the claim",children:e.jsx(f,{...o("regex"),placeholder:"Regex"})}),e.jsx(p,{label:"Regex Description",description:"The description for the regex",children:e.jsx(f,{...o("regexDescription"),placeholder:"Regex Description"})}),e.jsx(p,{label:"Description",description:"The description for the claim",children:e.jsx(f,{required:!0,...o("description"),placeholder:"Description"})}),e.jsx(p,{label:"Value Type",description:"The value type for the claim",children:e.jsx(U,{mode:"single",options:_,value:c,onChange:v,placeholder:"Select value type",maxHeight:300,name:"valueType",register:o,valueAsNumber:!0})})]})}),e.jsxs(J,{className:"mt-5",children:[e.jsx(j,{variant:"ghost",onClick:r=>{r.preventDefault(),l(!1)},disabled:y.isPending,children:"Cancel"}),e.jsx(j,{type:"submit",disabled:y.isPending,children:y.isPending?"Saving...":"Save"})]})]})]})]})]})},ke=({dataEdit:s,dataId:t,onDismiss:n})=>{const[l,i]=u.useState(!1),{toast:h}=q(),d=M(),{handleSubmit:o,register:m,setValue:c}=W(),[v,y]=u.useState(s.required??!1),[S,b]=u.useState(s.isStatic??!1),[r,g]=u.useState([]),D=Q({mutationFn:async a=>ne({path:{id:t},body:a}),onSuccess:()=>{h({title:"Success",description:"Claim Type Updated Successfully",variant:"success"}),d.invalidateQueries({queryKey:[I.GetIdentityClaimTypes]}),F()},onError:a=>{const N=H(a);h({title:N.title,description:N.description,variant:"error"})}}),A=a=>{const N={...a};D.mutate(N)},F=()=>{i(!1),n()};return u.useEffect(()=>{if(l){c("name",s.name??""),c("required",s.required??!1),c("isStatic",s.isStatic??!1),c("regex",s.regex??""),c("regexDescription",s.regexDescription??""),c("description",s.description??""),c("valueType",s.valueType??0);const a=s.valueType!==void 0&&s.valueType!==null?s.valueType.toString():"0";g([a])}},[l,s,c]),u.useEffect(()=>{i(!0)},[]),e.jsx(B,{open:l,onOpenChange:F,children:e.jsxs(E,{children:[e.jsx(G,{children:e.jsxs(L,{children:["Update a Claim Type: ",s.name]})}),e.jsxs("form",{onSubmit:o(A),className:"mt-2",onKeyDown:a=>{a.key==="Enter"&&a.target instanceof HTMLInputElement&&(a.preventDefault(),o(A)())},children:[e.jsx("section",{className:"flex w-full flex-col space-y-2",children:e.jsxs(Y,{children:[e.jsx(p,{label:"Name",description:"The name of the claim",children:e.jsx(f,{required:!0,...m("name"),defaultValue:s.name??"",placeholder:"Claim Name"})}),e.jsx(p,{label:"Required",description:"Whether the claim is required",children:e.jsx(w,{checked:v,onCheckedChange:a=>{y(!!a),c("required",!!a)}})}),e.jsx(p,{label:"Is Static",description:"Whether the claim is static",children:e.jsx(w,{checked:S,onCheckedChange:a=>{b(!!a),c("isStatic",!!a)}})}),e.jsx(p,{label:"Regex",description:"The regex for the claim",children:e.jsx(f,{...m("regex"),defaultValue:s.regex??"",placeholder:"Regex"})}),e.jsx(p,{label:"Regex Description",description:"The description for the regex",children:e.jsx(f,{...m("regexDescription"),defaultValue:s.regexDescription??"",placeholder:"Regex Description"})}),e.jsx(p,{label:"Description",description:"The description for the claim",children:e.jsx(f,{required:!0,...m("description"),defaultValue:s.description??"",placeholder:"Description"})}),e.jsx(p,{label:"Value Type",description:"The value type for the claim",children:e.jsx(U,{mode:"single",options:_,value:r,onChange:g,placeholder:"Select value type",maxHeight:300,name:"valueType",register:m,valueAsNumber:!0})})]})}),e.jsxs(J,{className:"mt-5",children:[e.jsx(j,{variant:"ghost",onClick:a=>{a.preventDefault(),i(!1)},disabled:D.isPending,type:"button",children:"Cancel"}),e.jsx(j,{type:"submit",disabled:D.isPending,children:D.isPending?"Saving...":"Save"})]})]})]})})},Ve=(s,t,n=[],l)=>X({queryKey:[I.GetIdentityClaimTypes,s,t,JSON.stringify(n),l],queryFn:async()=>{try{const i=be({pageIndex:s,pageSize:t,sorting:l,filterConditions:n});return(await re({body:i})).data?.data}catch(i){const{title:h,description:d}=De(i,"Error loading clients");return oe({title:h,description:d,variant:"destructive"}),{items:[],totalCount:0}}}}),Re=()=>{const{toast:s}=q(),t=M(),[n,l]=u.useState(""),[i,h]=u.useState([]),[d,o]=u.useState(),[m,c]=u.useState({pageIndex:0,pageSize:10}),[v,y]=u.useState([{id:"name",desc:!1}]),{isLoading:S,data:b}=Ve(m.pageIndex,m.pageSize,i),g=Fe((x,C,T)=>{o({dataId:x,dataEdit:C,dialogType:T})}),D=x=>{l(x);const T=[...i.filter(R=>R.fieldName!=="name")];x&&T.push({fieldName:"name",operator:"Contains",value:x});const k=JSON.stringify(i),V=JSON.stringify(T);k!==V&&(h(T),c(R=>({...R,pageIndex:0})))},A=x=>{c(x)},F=x=>{y(x),c(C=>({...C,pageIndex:0}))},a=()=>{t.invalidateQueries({queryKey:[I.GetIdentityClaimTypes]}),setTimeout(()=>{s({title:"Data refreshed",description:"The claims list has been refreshed.",variant:"success"})},100)};if(S)return e.jsx(ye,{rowCount:m.pageSize,columnCount:4,hasTitle:!0,hasSearch:!0,hasFilters:!0,hasPagination:!0,hasActions:!0});const N=b?.items??[],$=b?.totalCount??0;return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"space-y-4",children:e.jsx(fe,{title:"Claims Types",columns:g,data:N,totalCount:$,isLoading:S,manualPagination:!0,manualSorting:!0,pageSize:m.pageSize,onPaginationChange:A,onSortingChange:F,sortingState:v,onSearch:D,searchValue:n,customFilterbar:x=>e.jsx(Ne,{...x,activeFilters:i,onServerFilter:C=>{const T=JSON.stringify(i),k=JSON.stringify(C);T!==k&&(h(C),c(V=>({...V,pageIndex:0})))}}),hideDefaultFilterbar:!0,onRefresh:a,enableRowSelection:!1,actionButton:{onClick:()=>{},content:e.jsx(qe,{})}})}),d&&d.dialogType==="edit"&&e.jsx(ke,{dataId:d.dataId,dataEdit:d.dataEdit,onDismiss:()=>{t.invalidateQueries({queryKey:[I.GetIdentityClaimTypes]}),o(null)}}),d&&d.dialogType==="delete"&&e.jsx(we,{dataId:d.dataId,onDismiss:()=>{t.invalidateQueries({queryKey:[I.GetIdentityClaimTypes]}),o(null)}})]})};function st(){return e.jsxs(le,{children:[e.jsx(Ie,{title:"Claim"}),e.jsx(Re,{})]})}export{st as default};
//# sourceMappingURL=claim-CXz8stMd.js.map
