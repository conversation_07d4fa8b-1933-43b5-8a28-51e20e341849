using Imip.JettyApproval.Approvals.ApprovalStages;
using Imip.JettyApproval.Mapping.Mappers;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.JettyApproval.Approvals.ApprovalStages;

/// <summary>
/// Application service for ApprovalStage entity
/// </summary>
public class ApprovalStageAppService :
    CrudAppService<ApprovalStage, ApprovalStageDto, Guid, PagedAndSortedResultRequestDto, CreateUpdateApprovalStageDto, CreateUpdateApprovalStageDto>,
    IApprovalStageAppService
{
    private readonly IApprovalStageRepository _approvalStageRepository;
    private readonly ApprovalStageMapper _mapper;
    private readonly ILogger<ApprovalStageAppService> _logger;

    public ApprovalStageAppService(
        IApprovalStageRepository approvalStageRepository,
        ApprovalStageMapper mapper,
        ILogger<ApprovalStageAppService> logger)
        : base(approvalStageRepository)
    {
        _approvalStageRepository = approvalStageRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public override async Task<ApprovalStageDto> CreateAsync(CreateUpdateApprovalStageDto input)
    {
        await CheckCreatePolicyAsync();

        var entity = _mapper.CreateEntityWithId(input, Guid.NewGuid());

        await _approvalStageRepository.InsertAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task<ApprovalStageDto> UpdateAsync(Guid id, CreateUpdateApprovalStageDto input)
    {
        await CheckUpdatePolicyAsync();

        var entity = await _approvalStageRepository.GetAsync(id);

        _mapper.MapToEntity(input, entity);

        await _approvalStageRepository.UpdateAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task DeleteAsync(Guid id)
    {
        await CheckDeletePolicyAsync();

        var entity = await _approvalStageRepository.GetAsync(id);

        await _approvalStageRepository.DeleteAsync(entity, autoSave: true);
    }

    public override async Task<ApprovalStageDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var entity = await _approvalStageRepository.GetAsync(id);
        return _mapper.MapToDto(entity);
    }

    public override async Task<PagedResultDto<ApprovalStageDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        await CheckGetListPolicyAsync();

        var queryable = await _approvalStageRepository.GetQueryableAsync();

        // Apply sorting
        if (!string.IsNullOrWhiteSpace(input.Sorting))
        {
            queryable = queryable.OrderBy(input.Sorting);
        }
        else
        {
            queryable = queryable.OrderByDescending(x => x.CreationTime);
        }

        var totalCount = await AsyncExecuter.CountAsync(queryable);

        var entities = await AsyncExecuter.ToListAsync(
            queryable.PageBy(input.SkipCount, input.MaxResultCount)
        );

        var dtos = entities.Select(_mapper.MapToDto).ToList();

        return new PagedResultDto<ApprovalStageDto>(totalCount, dtos);
    }
}