using System;
using System.IO;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Caching.StackExchangeRedis;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using StackExchange.Redis;
using Volo.Abp.Caching;

namespace Imip.JettyApproval.Web.Services;

public static class DataProtectionConfigurationService
{
    /// <summary>
    /// Configures data protection services with environment-specific storage strategies
    /// </summary>
    public static void ConfigureDataProtection(IServiceCollection services, IConfiguration configuration, IWebHostEnvironment hostingEnvironment)
    {
        var appName = configuration["App:AppName"] ?? "Imip.JettyApproval";
        var forceRedisDataProtection = configuration.GetValue<bool>("DataProtection:ForceRedis", false);

        Console.WriteLine($"Configuring data protection for app: {appName}");
        Console.WriteLine($"Environment: {hostingEnvironment.EnvironmentName}");
        Console.WriteLine($"Force Redis: {forceRedisDataProtection}");

        var dataProtectionBuilder = services.AddDataProtection()
            .SetApplicationName(appName)
            .SetDefaultKeyLifetime(TimeSpan.FromDays(90));

        ConfigureProductionDataProtection(services, configuration, dataProtectionBuilder, appName);
    }

    private static void ConfigureProductionDataProtection(IServiceCollection services, IConfiguration configuration,
        IDataProtectionBuilder dataProtectionBuilder, string appName)
    {
        var redisIsEnabled = configuration.GetValue<bool>("Redis:IsEnabled", true);
        var redisConfiguration = configuration["Redis:Configuration"];

        // Strategy 1: Try Redis first (best for Kubernetes multi-pod scenarios)
        if (redisIsEnabled && !string.IsNullOrEmpty(redisConfiguration))
        {
            if (TryConfigureRedisDataProtection(services, redisConfiguration, appName))
            {
                return; // Redis success, we're done
            }
        }

        // Strategy 2: Kubernetes Persistent Volume (fallback)
        ConfigureKubernetesPersistentStorage(dataProtectionBuilder);
    }

    private static bool TryConfigureRedisDataProtection(IServiceCollection services, string redisConfiguration, string appName)
    {
        try
        {
            // Configure data protection to use Redis
            var redis = ConnectionMultiplexer.Connect(redisConfiguration);

            // Configure data protection with Redis
            services.AddDataProtection()
                .SetApplicationName(appName)
                .SetDefaultKeyLifetime(TimeSpan.FromDays(90))
                .PersistKeysToStackExchangeRedis(redis, $"{appName}:DataProtection-Keys");

            Console.WriteLine("Production: Data protection keys stored in Redis (Kubernetes-ready)");
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Redis data protection failed: {ex.Message}. Falling back to persistent volume.");
            return false;
        }
    }

    private static void ConfigureKubernetesPersistentStorage(IDataProtectionBuilder dataProtectionBuilder)
    {
        // Kubernetes persistent volume paths (must be configured in your deployment)
        var kubernetesPaths = new[]
        {
            "/app/data-protection-keys",  // Your current path
            "/data/keys",                 // Alternative common path
            "/persistent/data-protection-keys"  // Another alternative
        };

        foreach (var path in kubernetesPaths)
        {
            if (TryConfigurePersistentVolumePath(dataProtectionBuilder, path))
            {
                return; // Success
            }
        }

        // Strategy 3: Fallback to temporary storage (NOT recommended for production)
        ConfigureFallbackTempStorage(dataProtectionBuilder);
    }

    private static bool TryConfigurePersistentVolumePath(IDataProtectionBuilder dataProtectionBuilder, string path)
    {
        try
        {
            // Ensure directory exists
            Directory.CreateDirectory(path);

            // Test write permissions
            var testFile = Path.Combine(path, "test-write.tmp");
            File.WriteAllText(testFile, "test");
            File.Delete(testFile);

            dataProtectionBuilder.PersistKeysToFileSystem(new DirectoryInfo(path));
            Console.WriteLine($"Production: Data protection keys stored at: {path} (Kubernetes PV)");
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Failed to use path {path}: {ex.Message}");
            return false;
        }
    }

    private static void ConfigureFallbackTempStorage(IDataProtectionBuilder dataProtectionBuilder)
    {
        Console.WriteLine("WARNING: Using temporary storage for data protection keys. Users will be logged out on pod restarts!");
        var tempPath = Path.Combine(Path.GetTempPath(), "dataprotection-keys");
        Directory.CreateDirectory(tempPath);
        dataProtectionBuilder.PersistKeysToFileSystem(new DirectoryInfo(tempPath));
    }
}
