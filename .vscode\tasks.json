{"version": "2.0.0", "tasks": [{"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/Imip.JettyApproval.sln", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary;ForceNoAlign"], "problemMatcher": "$msCompile"}, {"label": "publish", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/Imip.JettyApproval.sln", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary;ForceNoAlign"], "problemMatcher": "$msCompile"}, {"label": "watch", "command": "dotnet", "type": "process", "args": ["watch", "run", "--project", "${workspaceFolder}/Imip.JettyApproval.sln"], "problemMatcher": "$msCompile"}, {"label": "dotrush: Build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/src/Imip.JettyApproval.Web/Imip.JettyApproval.Web.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": {"kind": "build", "isDefault": true}}]}