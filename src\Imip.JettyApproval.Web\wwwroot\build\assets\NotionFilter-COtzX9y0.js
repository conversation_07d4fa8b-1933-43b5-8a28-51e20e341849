import{n as q,B as u,L as N,S as w,a as I,b as S,c as F,d as A,I as V}from"./app-layout-CNB1Wtrx.js";import{r as p,j as e}from"./vendor-CrSBzUoz.js";import{P as z,b as D,t as P,c as G,e as R,a as W}from"./popover-7NwOVASC.js";import{V as U}from"./DataTable-BL4SJdnU.js";import{S as B}from"./search-YAT3sv3T.js";/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const M=[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]],_=q("loader-circle",M);function Y(t,i="Error loading data",c="An unexpected error occurred"){let r=i,n=c;try{const o=t;o?.details?.error&&(r=o.details.error.message??i,n=o.details.error.details??c)}catch{}return{title:r,description:n}}function Z(t){const{pageIndex:i,pageSize:c,sorting:r,filterConditions:n=[]}=t,o=r??"",h={operator:"And",conditions:n},l=[];if(r)try{JSON.parse(r).forEach(m=>{l.push({field:m.id,desc:m.desc})})}catch{}return{sorting:o,page:i+1,sort:l,filterGroup:h,maxResultCount:c}}const $=[{label:"Equals",value:"Equals"},{label:"Not Equals",value:"NotEquals"},{label:"Contains",value:"Contains"},{label:"Starts With",value:"StartsWith"},{label:"Ends With",value:"EndsWith"},{label:"Greater Than",value:"GreaterThan"},{label:"Greater Than or Equal",value:"GreaterThanOrEqual"},{label:"Less Than",value:"LessThan"},{label:"Less Than or Equal",value:"LessThanOrEqual"},{label:"Is Empty",value:"IsEmpty"},{label:"Is Not Empty",value:"IsNotEmpty"},{label:"Is Null",value:"IsNull"},{label:"Is Not Null",value:"IsNotNull"}];function ee({table:t,onSearch:i,searchValue:c="",onServerFilter:r,activeFilters:n=[]}){const[o,h]=p.useState(!1),[l,d]=p.useState([]);function m(){return`filter-${Date.now()}-${Math.random().toString(36).substring(2,9)}`}const v=p.useRef(!0);p.useEffect(()=>{if(v.current&&(v.current=!1,n&&n.length>0)){const s=n.map(a=>({id:m(),columnId:a.fieldName,operator:a.operator,value:a.value}));d(s)}},[n]);const x=t.getAllColumns().filter(s=>s.id!=="select"&&s.id!=="actions"&&s.id!=="drag"&&s.getCanFilter()),C=l.filter(s=>s.value).length,f=C>0||t.getState().columnFilters.length>0,[b,y]=p.useState(!1),L=()=>{if(x.length===0)return;const s={id:m(),columnId:x[0]?.id??"",operator:"Contains",value:""};d([...l,s])},T=s=>{d(l.filter(a=>a.id!==s))},g=(s,a)=>{d(l.map(j=>j.id===s?{...j,...a}:j))},O=()=>{if(y(!0),t.resetColumnFilters(),r){const s=l.filter(a=>a.value).map(a=>({fieldName:a.columnId,operator:a.operator,value:a.value}));s.length>0&&r(s)}else l.forEach(s=>{const a=t.getColumn(s.columnId);a&&s.value&&a.setFilterValue({operator:s.operator,value:s.value})});h(!1),setTimeout(()=>{y(!1)},500)},E=()=>{d([]),t.resetColumnFilters(),r&&r([])},k=s=>{const a=t.getColumn(s);return a?.columnDef?.meta?.displayName?a.columnDef.meta.displayName:a?.columnDef?.header?typeof a.columnDef.header=="function"?s.charAt(0).toUpperCase()+s.slice(1):a.columnDef.header.toString():s.charAt(0).toUpperCase()+s.slice(1)};return e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-2",children:[i&&e.jsx("div",{className:"w-full sm:w-auto sm:max-w-[250px]",children:e.jsx(B,{onUpdate:s=>{i&&i(s)},value:c||""})}),e.jsxs("div",{className:"flex items-center gap-2 ml-auto",children:[e.jsxs(z,{open:o,onOpenChange:h,children:[e.jsx(D,{asChild:!0,children:e.jsxs(u,{variant:f?"default":"outline",size:"sm",className:"h-8 gap-1",children:[e.jsx(P,{className:"h-3.5 w-3.5"}),e.jsx("span",{children:"Filter"}),f&&e.jsx("span",{className:"ml-1 rounded-full bg-primary/20 px-1 text-xs font-medium",children:C})]})}),e.jsx(G,{className:"w-[600px] p-3",align:"end",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("h4",{className:"font-medium leading-none",children:"Filter"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Filter records by specific conditions"})]}),l.length>0?e.jsx("div",{className:"space-y-2",children:l.map(s=>e.jsx("div",{className:"rounded-md border p-3",children:e.jsxs("div",{className:"grid grid-cols-3 gap-3",children:[e.jsxs("div",{className:"col-span-1",children:[e.jsx("div",{className:"flex items-center justify-between mb-1",children:e.jsx(N,{className:"text-xs font-medium",children:"Column"})}),e.jsxs(w,{value:s.columnId,onValueChange:a=>g(s.id,{columnId:a}),children:[e.jsx(I,{className:"w-full",children:e.jsx(S,{placeholder:"Select column"})}),e.jsx(F,{children:x.map(a=>e.jsx(A,{value:a.id,children:k(a.id)},a.id))})]})]}),e.jsxs("div",{className:"col-span-1",children:[e.jsx("div",{className:"flex items-center justify-between mb-1",children:e.jsx(N,{className:"text-xs font-medium",children:"Operator"})}),e.jsxs(w,{value:s.operator,onValueChange:a=>g(s.id,{operator:a}),children:[e.jsx(I,{className:"w-full",children:e.jsx(S,{placeholder:"Select operator"})}),e.jsx(F,{children:$.map(a=>e.jsx(A,{value:a.value,children:a.label},a.value))})]})]}),e.jsx("div",{className:"col-span-1",children:e.jsxs("div",{className:"flex",children:[e.jsxs("div",{children:[e.jsx("div",{className:"flex items-center justify-between mb-1",children:e.jsx(N,{className:"text-xs font-medium",children:"Value"})}),e.jsx(V,{placeholder:"Enter value",className:"w-full",value:s.value,onChange:a=>g(s.id,{value:a.target.value})})]}),e.jsx("div",{className:"col-span-1 flex items-end justify-end",children:e.jsx(u,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",onClick:()=>T(s.id),children:e.jsx(R,{className:"h-4 w-4"})})})]})})]})},s.id))}):e.jsx("div",{className:"flex h-20 items-center justify-center rounded-md border border-dashed",children:e.jsx("p",{className:"text-sm text-muted-foreground",children:"No filters added"})}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(u,{variant:"ghost",size:"sm",className:"h-8 gap-1",onClick:L,children:[e.jsx(W,{className:"h-3.5 w-3.5"}),e.jsx("span",{children:"Add filter"})]}),l.length>0&&e.jsx(u,{variant:"ghost",size:"sm",className:"h-8",onClick:E,children:"Clear all"})]}),e.jsx(u,{className:"w-full",onClick:O,disabled:l.length===0||b,children:b?e.jsxs(e.Fragment,{children:[e.jsx(_,{className:"mr-2 h-4 w-4 animate-spin"}),"Applying..."]}):"Apply filters"})]})})]}),f&&e.jsx(u,{variant:"ghost",onClick:E,className:"border border-gray-200 px-2 font-semibold text-indigo-600 sm:border-none sm:py-1 dark:border-gray-800 dark:text-indigo-500",children:"Clear filters"}),e.jsx(U,{table:t})]})]})}export{ee as N,Y as e,Z as g};
//# sourceMappingURL=NotionFilter-COtzX9y0.js.map
