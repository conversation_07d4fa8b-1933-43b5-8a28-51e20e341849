import{r as h,j as e,h as P,u as R,f as H}from"./vendor-CrSBzUoz.js";import{o as W,F as X,u as V,D as Z,e as ee,B as S,f as se,j as K,Q as N,G as te,H as ie,T as k,I as w,J as ae,K as re,A as ne}from"./app-layout-CNB1Wtrx.js";import{A as oe,a as le,b as ce,c as de,d as ue,e as pe,f as me,g as he,T as xe}from"./TableSkeleton-Bzdk6y-O.js";import{D as v,t as ge,u as E,h as L}from"./DataTableColumnHeader-DJ80pmDz.js";import{D as fe}from"./DataTable-BL4SJdnU.js";import{l as ye,_ as je,a as Se}from"./popover-7NwOVASC.js";import{B as Ne}from"./badge-BtBZs1VC.js";import{c as F,M as z}from"./multi-select-BfYI64yx.js";import{g as be,e as De,N as Ce}from"./NotionFilter-COtzX9y0.js";import{D as M,a as ve,b as Q,c as G,d as U,e as B}from"./dialog-DAr_Mtxm.js";import{u as J}from"./index.esm-CT1elm-0.js";import{F as $,a as f}from"./FormField-BFBouSal.js";import{T as Y}from"./textarea-P1Up2ORZ.js";import{$ as Fe}from"./App-CGHLK9xH.js";import"./radix-DaY-mnHi.js";import"./card-BAJCNJxm.js";import"./table-CAbNlII1.js";import"./checkbox-CayrCcBd.js";import"./tiny-invariant-CopsF_GD.js";import"./search-YAT3sv3T.js";import"./index-CZZWNLgr.js";import"./scroll-area-5IehhMpa.js";const we=({data:{dataId:a,dataEdit:s},onDismiss:n})=>{const{toast:r}=W(),[i,c]=h.useState(!1),l=async()=>{try{await X({path:{id:a}}),r({title:"Success",description:`User "${s.name}" has been deleted successfully.`}),n()}catch(d){d instanceof Error&&r({title:"Failed",description:`There was a problem when deleting the user "${s.name}". Kindly try again.`,variant:"destructive"})}};return h.useEffect(()=>{c(!0)},[]),e.jsx(oe,{open:i,children:e.jsxs(le,{children:[e.jsxs(ce,{children:[e.jsx(de,{children:"Are you absolutely sure?"}),e.jsxs(ue,{children:['This action cannot be undone. This will permanently delete your this user "',s.name,'"']})]}),e.jsxs(pe,{children:[e.jsx(me,{onClick:n,children:"Cancel"}),e.jsx(he,{onClick:l,children:"Yes"})]})]})})},Ae=({dataId:a,dataEdit:s,onAction:n,variant:r="dropdown"})=>{const{can:i}=V();return r==="dropdown"?e.jsx("div",{className:"flex justify-end",children:e.jsxs(Z,{children:[e.jsx(ee,{asChild:!0,children:e.jsxs(S,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:[e.jsx(ye,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"Open menu"})]})}),e.jsxs(se,{align:"end",className:"w-[160px]",children:[i("IdentityServer.OpenIddictScopes.Edit")&&e.jsx(K,{className:"cursor-pointer text-sm",onClick:()=>n(a,s,"edit"),children:"Edit"}),i("IdentityServer.OpenIddictScopes.Delete")&&e.jsx(K,{className:"cursor-pointer text-sm text-red-500",onClick:()=>n(a,s,"delete"),children:"Delete"})]})]})}):e.jsx("div",{className:"flex items-center justify-end gap-1",children:i("IdentityServer.OpenIddictScopes.Edit")&&e.jsxs(S,{variant:"primary",size:"sm",className:"flex items-center gap-1 px-2 py-1",onClick:()=>n(a,s,"edit"),children:[e.jsx(je,{className:"h-4 w-4"}),e.jsx("span",{children:"Edit"})]})})},Te=a=>[{accessorKey:"name",header:({column:s})=>e.jsx(v,{column:s,title:"Name"}),enableSorting:!0,enableHiding:!0,cell:s=>s.getValue(),meta:{className:"text-left",displayName:"Name"},filterFn:F},{accessorKey:"displayName",header:({column:s})=>e.jsx(v,{column:s,title:"Display Name"}),enableSorting:!0,enableHiding:!0,cell:s=>s.getValue(),meta:{className:"text-left",displayName:"Display Name"},filterFn:F},{accessorKey:"description",header:({column:s})=>e.jsx(v,{column:s,title:"Description"}),enableSorting:!0,enableHiding:!0,cell:s=>s.getValue(),meta:{className:"text-left",displayName:"Description"},filterFn:F},{accessorKey:"resources",header:({column:s})=>e.jsx(v,{column:s,title:"Resources"}),enableSorting:!0,enableHiding:!0,filterFn:F,cell:s=>{const n=s.getValue();return e.jsx("div",{className:"flex flex-wrap gap-1",children:n?.map(r=>e.jsx(Ne,{variant:"secondary",children:r},r))})},meta:{className:"text-left",displayName:"Resources"}},{id:"actions",header:({column:s})=>e.jsx(v,{column:s,title:"Actions"}),enableHiding:!0,cell:s=>e.jsx(Ae,{dataId:s.row.original.id,dataEdit:s.row.original,onAction:a,variant:"dropdown"}),meta:{className:"text-right",displayName:"Actions"}}],Oe=(a,s,n=[],r)=>P({queryKey:[N.GetOpeniddictScopes,a,s,JSON.stringify(n),r],queryFn:async()=>{try{const i=be({pageIndex:a,pageSize:s,sorting:r,filterConditions:n});return(await te({body:i})).data?.data}catch(i){const{title:c,description:l}=De(i,"Error loading scopes");return ge({title:c,description:l,variant:"destructive"}),{items:[],totalCount:0}}},retry:!1}),_=()=>P({queryKey:[N.GetOpeniddictResourcesAvailableResources],queryFn:async()=>(await ie()).data?.data}),qe=({children:a})=>{const{can:s}=V(),[n,r]=h.useState(!1),{toast:i}=E(),c=R(),{handleSubmit:l,register:d,setValue:p}=J(),[x,b]=h.useState([]),{data:y,isLoading:C}=_(),g=H({mutationFn:async o=>ae({body:o}),onSuccess:()=>{i({title:"Success",description:"Client Created Successfully",variant:"success"}),c.invalidateQueries({queryKey:[N.GetOpeniddictScopes]}),r(!1)},onError:o=>{const u=L(o);i({title:u.title,description:u.description,variant:"error"})}}),D=o=>{const u={...o,resources:x};g.mutate(u)};return e.jsxs("section",{children:[e.jsx(k,{}),e.jsxs(M,{open:n,onOpenChange:r,children:[e.jsx(ve,{asChild:!0,children:a}),e.jsx("section",{className:"flex items-center justify-between pb-5",children:s("AbpIdentity.Users.Create")&&e.jsxs(S,{size:"sm",className:"w-full sm:py-1 sm:mt-0 sm:w-fit",onClick:()=>r(!0),children:[e.jsx(Se,{className:"-ml-1 size-4 shrink-0","aria-hidden":"true"}),e.jsx("span",{className:"hidden truncate sm:inline",children:"Create Scope"})]})}),e.jsxs(Q,{size:"xl",children:[e.jsx(G,{children:e.jsx(U,{children:"Create a New Scope"})}),e.jsxs("form",{onSubmit:l(D),className:"mt-2",children:[e.jsx("section",{className:"flex w-full flex-col space-y-2",children:e.jsxs($,{children:[e.jsx(f,{label:"Name",description:"The unique identifier for this resource. Used in requests",children:e.jsx(w,{required:!0,...d("name"),placeholder:"Name"})}),e.jsx(f,{label:"Display Name",description:"The display name for this resource",children:e.jsx(w,{required:!0,...d("displayName"),placeholder:"Display Name"})}),e.jsx(f,{label:"Description",description:"The description for this resource",children:e.jsx(Y,{required:!0,...d("description"),placeholder:"Description"})}),e.jsx(f,{label:"Resources",description:"The resources of the scope",children:e.jsx(z,{options:y?.map(o=>({value:o.value??"",label:o.label??""}))??[],value:x,onChange:o=>{b(o),p("resources",o)},placeholder:C?"Loading resources...":"Select resources",disabled:C,maxHeight:300})})]})}),e.jsxs(B,{className:"mt-5",children:[e.jsx(S,{variant:"ghost",onClick:o=>{o.preventDefault(),r(!1)},disabled:g.isPending,children:"Cancel"}),e.jsx(S,{type:"submit",disabled:g.isPending,children:g.isPending?"Saving...":"Save"})]})]})]})]})]})},Ie=({dataEdit:a,dataId:s,onDismiss:n})=>{const[r,i]=h.useState(!0),{toast:c}=E(),l=R(),{handleSubmit:d,register:p,setValue:x,reset:b}=J(),[y,C]=h.useState(a.resources??[]),{data:g,isLoading:D}=_(),o=()=>{b({name:"",displayName:"",description:"",resources:[]})},u=H({mutationFn:async t=>re({path:{id:s},body:t}),onSuccess:()=>{c({title:"Success",description:"Scope Updated Successfully",variant:"success"}),l.invalidateQueries({queryKey:[N.GetOpeniddictScopes]}),o(),i(!1),n()},onError:t=>{const m=L(t);c({title:m.title,description:m.description,variant:"error"})}}),A=t=>{const m={...t,resources:y};u.mutate(m)},T=t=>{t||n(),i(t)};return e.jsxs("section",{children:[e.jsx(k,{}),e.jsx(M,{open:r,onOpenChange:T,children:e.jsxs(Q,{size:"xl",children:[e.jsx(G,{children:e.jsx(U,{children:"Edit Scope"})}),e.jsxs("form",{onSubmit:d(A),className:"mt-2",children:[e.jsx("section",{className:"flex w-full flex-col space-y-2",children:e.jsxs($,{children:[e.jsx(f,{label:"Name",description:"The unique identifier for this resource. Used in requests",children:e.jsx(w,{required:!0,...p("name"),placeholder:"Name",defaultValue:a.name??""})}),e.jsx(f,{label:"Display Name",description:"The display name for this resource",children:e.jsx(w,{required:!0,...p("displayName"),placeholder:"Display Name",defaultValue:a.displayName??""})}),e.jsx(f,{label:"Description",description:"The description for this resource",children:e.jsx(Y,{required:!0,...p("description"),placeholder:"Description",defaultValue:a.description??""})}),e.jsx(f,{label:"Resources",description:"The resources of the scope",children:e.jsx(z,{options:g?.map(t=>({value:t.value??"",label:t.label??""}))??[],value:y,onChange:t=>{C(t),x("resources",t)},placeholder:D?"Loading resources...":"Select resources",disabled:D,maxHeight:300})})]})}),e.jsxs(B,{className:"mt-5",children:[e.jsx(S,{variant:"ghost",onClick:t=>{t.preventDefault(),i(!1)},disabled:u.isPending,children:"Cancel"}),e.jsx(S,{type:"submit",disabled:u.isPending,children:u.isPending?"Updating...":"Update"})]})]})]})})]})},Re=()=>{const{toast:a}=E(),s=R(),[n,r]=h.useState(""),[i,c]=h.useState([]),[l,d]=h.useState(),[p,x]=h.useState({pageIndex:0,pageSize:10}),{isLoading:b,data:y}=Oe(p.pageIndex,p.pageSize,i),g=Te((t,m,j)=>{d({dataId:t,dataEdit:m,dialogType:j})}),D=t=>{r(t);const j=[...i.filter(I=>I.fieldName!=="name")];t&&j.push({fieldName:"name",operator:"Contains",value:t});const O=JSON.stringify(i),q=JSON.stringify(j);O!==q&&(c(j),x(I=>({...I,pageIndex:0})))},o=t=>{x(t)},u=()=>{s.invalidateQueries({queryKey:[N.GetOpeniddictScopes]}),setTimeout(()=>{a({title:"Data refreshed",description:"The client list has been refreshed.",variant:"success"})},800)};if(b)return e.jsx(xe,{rowCount:p.pageSize,columnCount:4,hasTitle:!0,hasSearch:!0,hasFilters:!0,hasPagination:!0,hasActions:!0});const A=y?.items??[],T=y?.totalCount??0;return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"space-y-4",children:e.jsx(fe,{title:"Scopes Management",columns:g,data:A,totalCount:T,isLoading:b,manualPagination:!0,pageSize:p.pageSize,onPaginationChange:o,onSearch:D,searchValue:n,customFilterbar:t=>e.jsx(Ce,{...t,activeFilters:i,onServerFilter:m=>{const j=JSON.stringify(i),O=JSON.stringify(m);j!==O&&(c(m),x(q=>({...q,pageIndex:0})))}}),hideDefaultFilterbar:!0,onRefresh:u,enableRowSelection:!1,actionButton:{onClick:()=>{},content:e.jsx(qe,{})}})}),l&&l.dialogType==="edit"&&e.jsx(Ie,{dataId:l.dataId,dataEdit:l.dataEdit,onDismiss:()=>{s.invalidateQueries({queryKey:[N.GetOpeniddictScopes]}),d(null)}}),l&&l.dialogType==="delete"&&e.jsx(we,{data:{dataId:l.dataId,dataEdit:l.dataEdit},onDismiss:()=>{s.invalidateQueries({queryKey:[N.GetOpeniddictScopes]}),d(null)}})]})};function ts(){return e.jsxs(ne,{children:[e.jsx(Fe,{title:"Dashboard"}),e.jsx(Re,{})]})}export{ts as default};
//# sourceMappingURL=scope-Du4YH1iU.js.map
