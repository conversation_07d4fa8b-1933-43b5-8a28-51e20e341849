'use client';

import ReactQueryProviders from "@/components/provider/query-client-provider";
import ThemeProvider from "@/components/provider/theme-provider";
import { Toaster } from "@/components/ui/toaster";
import type { IAppLayoutProps } from "@/lib/interfaces/IAppLayoutPros";
import { cn } from "@/lib/utils";
import Cookies from "js-cookie";
import { StrictMode } from "react";
import { ActiveThemeProvider, useThemeConfig } from "../components/active-theme";
import { PolicyGuard } from "../components/auth/policy-guard";
import AppSidebar from "../components/layout/app-sidebar";
import Header from "../components/layout/header";
import { SidebarInset, SidebarProvider } from "../components/ui/sidebar";

function AppLayoutContent({ children, policy, message }: IAppLayoutProps) {
  const defaultOpen = Cookies.get("sidebar_state") === "true"
  const { activeTheme } = useThemeConfig();
  const isScaled = activeTheme.endsWith('-scaled');

  return (
    <div
      className={cn(
        'bg-background font-sans antialiased',
        `theme-${activeTheme}`,
        isScaled ? 'theme-scaled' : ''
      )}
    >
      <Toaster />
      <SidebarProvider defaultOpen={defaultOpen}>
        <AppSidebar />
        <SidebarInset>
          <Header />
          <div className="flex flex-1 flex-col overflow-auto">
            <div className="@container/main flex flex-1 flex-col gap-2">
              <div className="flex flex-col gap-4 py-2 px-2 md:gap-4 md:py-2 md:px-2">
                {policy !== undefined ? (
                  <PolicyGuard policy={policy} message={message}>
                    {children}
                  </PolicyGuard>
                ) : children}
              </div>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    </div>
  );
}

export default function AppLayout(props: IAppLayoutProps) {
  return (
    <StrictMode>
      <ReactQueryProviders>
        <ThemeProvider defaultTheme="system" storageKey="vite-ui-theme">
          <ActiveThemeProvider>
            <AppLayoutContent {...props} />
          </ActiveThemeProvider>
        </ThemeProvider>
      </ReactQueryProviders>
    </StrictMode>
  );
}