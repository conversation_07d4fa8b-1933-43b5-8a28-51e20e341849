using System;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.Identity;
using System.ComponentModel.DataAnnotations.Schema;

namespace Imip.JettyApproval.Approvals.ApprovalDelegations;

/// <summary>
/// Entity for storing approval delegation information
/// </summary>
public class ApprovalDelegation : FullAuditedAggregateRoot<Guid>
{
    /// <summary>
    /// ID of the original approver
    /// </summary>
    public Guid ApproverId { get; set; }

    /// <summary>
    /// ID of the substitute approver
    /// </summary>
    public Guid SubstituteId { get; set; }

    /// <summary>
    /// Start date of the delegation
    /// </summary>
    public DateTime StartDate { get; set; }

    /// <summary>
    /// End date of the delegation
    /// </summary>
    public DateTime EndDate { get; set; }

    /// <summary>
    /// Whether the delegation is currently active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Additional notes for the delegation
    /// </summary>
    public string? Notes { get; set; }

    [ForeignKey(nameof(ApproverId))]
    public virtual IdentityUser? Approver { get; set; }

    [ForeignKey(nameof(SubstituteId))]
    public virtual IdentityUser? Substitute { get; set; }

    /// <summary>
    /// Default constructor for EF Core
    /// </summary>
    protected ApprovalDelegation()
    {
    }

    /// <summary>
    /// Creates a new ApprovalDelegation
    /// </summary>
    public ApprovalDelegation(
        Guid id,
        Guid approverId,
        Guid substituteId,
        DateTime startDate,
        DateTime endDate,
        bool isActive = true,
        string? notes = null)
        : base(id)
    {
        ApproverId = approverId;
        SubstituteId = substituteId;
        StartDate = startDate;
        EndDate = endDate;
        IsActive = isActive;
        Notes = notes;
    }

    /// <summary>
    /// Creates a new ApprovalDelegation without ID (for mapping from DTOs)
    /// </summary>
    public ApprovalDelegation(
        Guid approverId,
        Guid substituteId,
        DateTime startDate,
        DateTime endDate,
        bool isActive = true,
        string? notes = null)
    {
        ApproverId = approverId;
        SubstituteId = substituteId;
        StartDate = startDate;
        EndDate = endDate;
        IsActive = isActive;
        Notes = notes;
    }
}