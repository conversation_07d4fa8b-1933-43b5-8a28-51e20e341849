'use client'
import { type CreateUpdateClaimTypeDto, postApiIdentityClaimTypes } from '@/client'
import { Button } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { useToast } from '@/lib/useToast'
import { handleApiError } from '@/lib/handleApiError'
import { QueryNames } from '@/lib/hooks/QueryConstants'
import { useGrantedPolicies } from '@/lib/hooks/useGrantedPolicies'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { Toaster } from '@/components/ui/toaster'
import { RiAddLine } from '@remixicon/react'
import { FormField, FormSection } from '@/components/ui/FormField'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { MultiSelect } from '@/components/ui/multi-select'
import { itemValueType } from './variable'

export type AddClientProps = {
  children?: React.ReactNode
}

export const Add = ({ children }: AddClientProps) => {
  const { can } = useGrantedPolicies()
  const [open, setOpen] = useState(false)
  const { toast } = useToast()
  const queryClient = useQueryClient()
  const { handleSubmit, register, reset } = useForm<CreateUpdateClaimTypeDto>()
  const [selectedValueType, setSelectedValueType] = useState<string[]>([])

  // Reset form when dialog is closed
  useEffect(() => {
    if (!open) {
      reset({
        name: '',
        required: false,
        isStatic: false,
        regex: '',
        regexDescription: '',
        description: '',
        valueType: 0
      })
      setSelectedValueType([])
    }
  }, [open, reset])


  const createDataMutation = useMutation({
    mutationFn: async (dataMutation: CreateUpdateClaimTypeDto) =>
      postApiIdentityClaimTypes({
        body: dataMutation
      }),
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Claim Type Created Successfully',
        variant: 'success',
      })
      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetIdentityClaimTypes] })
      setOpen(false)
    },
    onError: (err: unknown) => {
      // Use the global helper to handle API errors
      const error = handleApiError(err);
      toast({
        title: error.title,
        description: error.description,
        variant: 'error',
      })
    }
  })

  const onSubmit = (formData: CreateUpdateClaimTypeDto) => {
    // Merge form data with consent type and permissions
    const userData: CreateUpdateClaimTypeDto = {
      ...formData,
    }

    // Explicitly mark the promise as handled
    void createDataMutation.mutate(userData)
  }

  const handleOpenChange = (newOpenState: boolean) => {
    setOpen(newOpenState)
  }

  return (
    <section>
      <Toaster />
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogTrigger asChild>{children}</DialogTrigger>
        <section className="flex items-center justify-between pb-5">
          {can('IdentityServer.ClaimTypes.Create') && (
            <Button size='sm' className="w-full sm:py-1 sm:mt-0 sm:w-fit" onClick={() => setOpen(true)}>
              <RiAddLine className="-ml-1 size-4 shrink-0" aria-hidden="true" />
              <span className="hidden truncate sm:inline">New Claim Type</span>
            </Button>
          )}
        </section>
        <DialogContent className='max-w-2xl'>
          <DialogHeader>
            <DialogTitle>Create a New Claim Type</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSubmit(onSubmit)} className='mt-2' onKeyDown={(e) => {
            if (e.key === 'Enter' && e.target instanceof HTMLInputElement) {
              e.preventDefault();
              void handleSubmit(onSubmit)();
            }
          }}>
            <section className="flex w-full flex-col space-y-2">
              <FormSection>
                <FormField
                  label="Name"
                  description="The name of the claim"
                >
                  <Input required {...register('name')} placeholder="Claim Name" />
                </FormField>

                <FormField
                  label="Required"
                  description="Whether the claim is required"
                >
                  <Checkbox {...register('required', {
                    setValueAs: (value) => value === true
                  })} />
                </FormField>

                <FormField
                  label="Is Static"
                  description="Whether the claim is static"
                >
                  <Checkbox {...register('isStatic', {
                    setValueAs: (value) => value === true
                  })} />
                </FormField>

                <FormField
                  label="Regex"
                  description="The regex for the claim"
                >
                  <Input {...register('regex')} placeholder="Regex" />
                </FormField>

                <FormField
                  label="Regex Description"
                  description="The description for the regex"
                >
                  <Input {...register('regexDescription')} placeholder="Regex Description" />
                </FormField>

                <FormField
                  label="Description"
                  description="The description for the claim"
                >
                  <Input required {...register('description')} placeholder="Description" />
                </FormField>

                <FormField
                  label="Value Type"
                  description="The value type for the claim"
                >
                  <MultiSelect
                    mode='single'
                    options={itemValueType}
                    value={selectedValueType}
                    onChange={setSelectedValueType}
                    placeholder="Select value type"
                    maxHeight={300}
                    name="valueType"
                    register={register}
                    valueAsNumber={true}
                  />
                </FormField>
              </FormSection>
            </section>
            <DialogFooter className="mt-5">
              <Button
                variant="ghost"
                onClick={(e) => {
                  e.preventDefault()
                  setOpen(false)
                }}
                disabled={createDataMutation.isPending}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={createDataMutation.isPending}>
                {createDataMutation.isPending ? 'Saving...' : 'Save'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </section>
  )
}
