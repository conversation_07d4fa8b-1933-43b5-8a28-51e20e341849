using System;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.JettyApproval.Approvals.ApprovalApprovers;

/// <summary>
/// Application service interface for ApprovalApprover entity
/// </summary>
public interface IApprovalApproverAppService :
    ICrudAppService<
        ApprovalApproverDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateApprovalApproverDto,
        CreateUpdateApprovalApproverDto>
{
}