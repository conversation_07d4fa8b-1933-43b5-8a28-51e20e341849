{"version": 3, "file": "role-VZNP3dwx.js", "sources": ["../../../../../frontend/src/components/app/role/Delete.tsx", "../../../../../frontend/src/components/app/role/Actions.tsx", "../../../../../frontend/src/components/ui/YesNoBadge.tsx", "../../../../../frontend/src/components/app/role/Columns.tsx", "../../../../../frontend/src/components/app/role/Filter.tsx", "../../../../../frontend/src/components/app/role/Add.tsx", "../../../../../frontend/src/components/app/role/Edit.tsx", "../../../../../frontend/src/lib/hooks/useRoles.ts", "../../../../../frontend/src/components/app/role/RolePermission.tsx", "../../../../../frontend/src/components/app/role/List.tsx", "../../../../../frontend/src/pages/role.tsx"], "sourcesContent": ["import { deleteApiIdentityRolesById } from '@/client'\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@/components/ui/alert-dialog'\r\nimport { useToast } from '@/hooks/use-toast'\r\nimport { useEffect, useState } from 'react'\r\n\r\ntype DeleteUserProps = {\r\n  dataId: string\r\n  onDismiss: () => void\r\n}\r\nexport const Delete = ({ dataId, onDismiss }: DeleteUserProps) => {\r\n  const { toast } = useToast()\r\n  const [open, setOpen] = useState<boolean>(false)\r\n  const onYesEvent = async () => {\r\n    try {\r\n      await deleteApiIdentityRolesById({\r\n        path: { id: dataId },\r\n      })\r\n      toast({\r\n        title: 'Success',\r\n        description: `Role has been deleted successfully.`,\r\n      })\r\n      onDismiss()\r\n    } catch (err: unknown) {\r\n      if (err instanceof Error) {\r\n        toast({\r\n          title: 'Failed',\r\n          description: `There was a problem when deleting the role. Kindly try again.`,\r\n          variant: 'destructive',\r\n        })\r\n      }\r\n    }\r\n  }\r\n\r\n  useEffect(() => {\r\n    setOpen(true)\r\n  }, [])\r\n\r\n  return (\r\n    <AlertDialog open={open}>\r\n      <AlertDialogContent>\r\n        <AlertDialogHeader>\r\n          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>\r\n          <AlertDialogDescription>\r\n            This action cannot be undone. This will permanently delete your this role.\r\n          </AlertDialogDescription>\r\n        </AlertDialogHeader>\r\n        <AlertDialogFooter>\r\n          <AlertDialogCancel onClick={onDismiss}>Cancel</AlertDialogCancel>\r\n          <AlertDialogAction onClick={onYesEvent}>Yes</AlertDialogAction>\r\n        </AlertDialogFooter>\r\n      </AlertDialogContent>\r\n    </AlertDialog>\r\n  )\r\n}\r\n", "'use client'\r\n\r\nimport { type IdentityUserUpdateDto } from '@/client'\r\nimport { useGrantedPolicies } from '@/lib/hooks/useGrantedPolicies'\r\nimport { RiMoreLine, RiPencilLine, RiShieldKeyholeLine } from '@remixicon/react'\r\nimport { Button } from '@/components/ui/button'\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu'\r\n\r\ntype UserActionProps = {\r\n  userId: string\r\n  userDto: IdentityUserUpdateDto\r\n  onAction: (userId: string, userDto: IdentityUserUpdateDto, dialogType: 'edit' | 'permission' | 'delete') => void\r\n  variant?: 'dropdown' | 'buttons'\r\n}\r\n\r\nexport const Actions = ({ userId, userDto, onAction, variant = 'dropdown' }: UserActionProps) => {\r\n  const { can } = useGrantedPolicies()\r\n\r\n  // For dropdown menu style (first image)\r\n  if (variant === 'dropdown') {\r\n    return (\r\n      <div className=\"flex justify-end\">\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"h-8 w-8 p-0\"\r\n            >\r\n              <RiMoreLine className=\"h-4 w-4\" />\r\n              <span className=\"sr-only\">Open menu</span>\r\n            </Button>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent align=\"end\" className=\"w-[160px]\">\r\n            {can('AbpIdentity.Roles.Update') && (\r\n              <DropdownMenuItem\r\n                className=\"cursor-pointer text-sm\"\r\n                onClick={() => onAction(userId, userDto, 'edit')}\r\n              >\r\n                Edit\r\n              </DropdownMenuItem>\r\n            )}\r\n            {can('AbpIdentity.Roles.ManagePermissions') && (\r\n              <DropdownMenuItem\r\n                className=\"cursor-pointer text-sm\"\r\n                onClick={() => onAction(userId, userDto, 'permission')}\r\n              >\r\n                Permission\r\n              </DropdownMenuItem>\r\n            )}\r\n            {can('AbpIdentity.Roles.Delete') && (\r\n              <DropdownMenuItem\r\n                className=\"cursor-pointer text-sm text-red-500\"\r\n                onClick={() => onAction(userId, userDto, 'delete')}\r\n              >\r\n                Delete\r\n              </DropdownMenuItem>\r\n            )}\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // For button group style (second image)\r\n  return (\r\n    <div className=\"flex items-center justify-end gap-1\">\r\n      {can('AbpIdentity.Users.ManagePermissions') && (\r\n        <Button\r\n          variant=\"primary\"\r\n          size=\"sm\"\r\n          className=\"flex items-center gap-1 px-2 py-1\"\r\n          onClick={() => onAction(userId, userDto, 'permission')}\r\n        >\r\n          <RiShieldKeyholeLine className=\"h-4 w-4\" />\r\n          <span>Permission</span>\r\n        </Button>\r\n      )}\r\n      {can('AbpIdentity.Users.Update') && (\r\n        <Button\r\n          variant=\"primary\"\r\n          size=\"sm\"\r\n          className=\"flex items-center gap-1 px-2 py-1\"\r\n          onClick={() => onAction(userId, userDto, 'edit')}\r\n        >\r\n          <RiPencilLine className=\"h-4 w-4\" />\r\n          <span>Edit</span>\r\n        </Button>\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n", "import React from 'react';\r\nimport { cn } from '@/lib/utils';\r\n\r\ninterface YesNoBadgeProps {\r\n  value: boolean;\r\n  className?: string;\r\n}\r\n\r\nexport const YesNoBadge: React.FC<YesNoBadgeProps> = ({ value, className }) => {\r\n  return (\r\n    <span\r\n      className={cn(\r\n        \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\",\r\n        value\r\n          ? \"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300\"\r\n          : \"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300\",\r\n        className\r\n      )}\r\n    >\r\n      {value ? 'Yes' : 'No'}\r\n    </span>\r\n  );\r\n};\r\n\r\nexport default YesNoBadge; ", "'use client'\r\n\r\nimport { type IdentityRoleDto, type IdentityUserUpdateDto } from '@/client'\r\nimport { type ColumnDef } from '@tanstack/react-table'\r\nimport { Actions } from './Actions'\r\nimport { Checkbox } from '@/components/ui/checkbox'\r\nimport { DataTableColumnHeader } from '@/components/data-table/DataTableColumnHeader'\r\nimport { YesNoBadge } from '@/components/ui/YesNoBadge'\r\n\r\n// Type for the callback function to handle user actions\r\ntype UserActionCallback = (userId: string, userDto: IdentityUserUpdateDto, dialogType: 'edit' | 'permission' | 'delete') => void\r\n\r\n// Function to create columns with the action callback\r\nexport const getColumns = (\r\n  handleUserAction: UserActionCallback\r\n): ColumnDef<IdentityRoleDto>[] => {\r\n  return [\r\n    {\r\n      id: \"select\",\r\n      header: ({ table }) => (\r\n        <Checkbox\r\n          checked={\r\n            table.getIsAllPageRowsSelected()\r\n              ? true\r\n              : table.getIsSomeRowsSelected()\r\n                ? \"indeterminate\"\r\n                : false\r\n          }\r\n          onCheckedChange={() => table.toggleAllPageRowsSelected()}\r\n          className=\"translate-y-0.5\"\r\n          aria-label=\"Select all\"\r\n        />\r\n      ),\r\n      cell: ({ row }) => (\r\n        <Checkbox\r\n          checked={row.getIsSelected()}\r\n          onCheckedChange={() => row.toggleSelected()}\r\n          className=\"translate-y-0.5\"\r\n          aria-label=\"Select row\"\r\n        />\r\n      ),\r\n      enableSorting: false,\r\n      enableHiding: true,\r\n      meta: {\r\n        displayName: \"Select\",\r\n      },\r\n    },\r\n    {\r\n      accessorKey: \"name\",\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title=\"Name\" />\r\n      ),\r\n      enableSorting: true,\r\n      enableHiding: true,\r\n      cell: (info) => info.getValue(),\r\n      meta: {\r\n        className: \"text-left\",\r\n        displayName: \"Name\",\r\n      },\r\n    },\r\n    {\r\n      accessorKey: \"isDefault\",\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title=\"Is Default\" />\r\n      ),\r\n      enableSorting: true,\r\n      enableHiding: true,\r\n      cell: (info) => <YesNoBadge value={info.getValue() as boolean} />,\r\n      meta: {\r\n        className: \"text-left\",\r\n        displayName: \"Is Default\",\r\n      },\r\n    },\r\n    {\r\n      accessorKey: \"isPublic\",\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title=\"Is Public\" />\r\n      ),\r\n      enableSorting: true,\r\n      enableHiding: true,\r\n      cell: (info) => <YesNoBadge value={info.getValue() as boolean} />,\r\n      meta: {\r\n        className: \"text-left\",\r\n        displayName: \"Is Public\",\r\n      },\r\n    },\r\n    {\r\n      id: \"actions\",\r\n      header: \"Actions\",\r\n      cell: (info) => (\r\n        <Actions\r\n          userId={info.row.original.id!}\r\n          userDto={info.row.original as unknown as IdentityUserUpdateDto}\r\n          onAction={handleUserAction}\r\n          variant=\"dropdown\"\r\n        />\r\n      ),\r\n      enableSorting: false,\r\n      enableHiding: true,\r\n      meta: {\r\n        className: \"text-right\",\r\n        displayName: \"Action\",\r\n      },\r\n    }\r\n  ];\r\n}\r\n", "\"use client\"\r\n\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\nimport { type Table } from \"@tanstack/react-table\"\r\nimport { ViewOptions } from \"@/components/data-table/DataTableViewOptions\"\r\nimport { Search } from \"@/components/ui/search\"\r\n\r\ninterface FilterProps<TData> {\r\n  table: Table<TData>\r\n  onSearch?: (value: string) => void\r\n  searchValue?: string\r\n}\r\n\r\nexport function Filter<TData>({\r\n  table,\r\n  onSearch,\r\n  searchValue = \"\",\r\n}: FilterProps<TData>) {\r\n  const isFiltered = table.getState().columnFilters.length > 0\r\n\r\n  return (\r\n    <div className=\"flex flex-wrap items-center justify-between gap-2\">\r\n      {onSearch && (\r\n        <div className=\"w-full sm:w-auto sm:max-w-[250px]\">\r\n          <Search onUpdate={onSearch} value={searchValue} />\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"flex items-center gap-2 ml-auto\">\r\n        {isFiltered && (\r\n          <Button\r\n            variant=\"ghost\"\r\n            onClick={() => table.resetColumnFilters()}\r\n            className=\"border border-gray-200 px-2 font-semibold text-indigo-600 sm:border-none sm:py-1 dark:border-gray-800 dark:text-indigo-500\"\r\n          >\r\n            Clear filters\r\n          </Button>\r\n        )}\r\n        {/* <Button\r\n          variant=\"secondary\"\r\n          className=\"gap-x-2 px-2 py-1.5 text-sm sm:text-xs\"\r\n        >\r\n          <RiDownloadLine className=\"size-4 shrink-0\" aria-hidden=\"true\" />\r\n          <span className=\"hidden sm:inline\">Export</span>\r\n        </Button> */}\r\n        <ViewOptions table={table} />\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n", "'use client'\r\nimport { type IdentityRoleCreateDto, postApiIdentityRoles } from '@/client'\r\nimport { Button } from '@/components/ui/button'\r\nimport {\r\n  <PERSON><PERSON>,\r\n  DialogContent,\r\n  DialogFooter,\r\n  <PERSON><PERSON>Header,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n} from '@/components/ui/dialog'\r\nimport { useToast } from '@/lib/useToast'\r\nimport { QueryNames } from '@/lib/hooks/QueryConstants'\r\nimport { useGrantedPolicies } from '@/lib/hooks/useGrantedPolicies'\r\nimport { useMutation, useQueryClient } from '@tanstack/react-query'\r\nimport { useState, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { Toaster } from '@/components/ui/toaster'\r\nimport { RiAddLine } from '@remixicon/react'\r\nimport { FormField, FormSection } from '@/components/ui/FormField'\r\nimport { Checkbox } from '@/components/ui/checkbox'\r\nimport { Input } from '@/components/ui/input'\r\nimport { handleApiError } from '@/lib/handleApiError'\r\n\r\nexport type AddClientProps = {\r\n  children?: React.ReactNode\r\n}\r\n\r\nexport const Add = ({ children }: AddClientProps) => {\r\n  const { can } = useGrantedPolicies()\r\n  const [open, setOpen] = useState(false)\r\n  const { toast } = useToast()\r\n  const queryClient = useQueryClient()\r\n  const { handleSubmit, register, reset } = useForm<IdentityRoleCreateDto>()\r\n  const [, setSelectedValueType] = useState<string[]>([])\r\n\r\n  // Reset form when dialog is closed\r\n  useEffect(() => {\r\n    if (!open) {\r\n      reset({\r\n        name: '',\r\n        isDefault: false,\r\n        isPublic: false,\r\n      })\r\n      setSelectedValueType([])\r\n    }\r\n  }, [open, reset])\r\n\r\n  const createDataMutation = useMutation({\r\n    mutationFn: async (formData: IdentityRoleCreateDto) =>\r\n      postApiIdentityRoles({\r\n        body: {\r\n          name: formData.name ?? '',\r\n          isDefault: formData.isDefault ?? false,\r\n          isPublic: formData.isPublic ?? false,\r\n        }\r\n      }),\r\n    onSuccess: () => {\r\n      toast({\r\n        title: 'Success',\r\n        description: 'Role Created Successfully',\r\n        variant: 'success',\r\n      })\r\n      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetRoles] })\r\n      setOpen(false)\r\n    },\r\n    onError: (err: unknown) => {\r\n      console.log('Error creating role:', err);\r\n      const error = handleApiError(err);\r\n      toast({\r\n        title: error.title,\r\n        description: error.description,\r\n        variant: 'error',\r\n      })\r\n    }\r\n  })\r\n\r\n  const onSubmit = (formData: IdentityRoleCreateDto) => {\r\n    // Merge form data with consent type and permissions\r\n    const userData: IdentityRoleCreateDto = {\r\n      ...formData,\r\n    }\r\n\r\n    createDataMutation.mutate(userData)\r\n  }\r\n\r\n  const handleOpenChange = (newOpenState: boolean) => {\r\n    setOpen(newOpenState)\r\n  }\r\n\r\n  return (\r\n    <section>\r\n      <Toaster />\r\n      <Dialog open={open} onOpenChange={handleOpenChange}>\r\n        <DialogTrigger asChild>{children}</DialogTrigger>\r\n        <section className=\"flex items-center justify-between pb-5\">\r\n          {can('IdentityServer.ClaimTypes.Create') && (\r\n            <Button size='sm' className=\"w-full sm:py-1 sm:mt-0 sm:w-fit\" onClick={() => setOpen(true)}>\r\n              <RiAddLine className=\"-ml-1 size-4 shrink-0\" aria-hidden=\"true\" />\r\n              <span className=\"hidden truncate sm:inline\">New Role</span>\r\n            </Button>\r\n          )}\r\n        </section>\r\n        <DialogContent className='max-w-2xl'>\r\n          <DialogHeader>\r\n            <DialogTitle>Create a New Role</DialogTitle>\r\n          </DialogHeader>\r\n          <form onSubmit={handleSubmit(onSubmit)} className='mt-2' onKeyDown={(e) => {\r\n            if (e.key === 'Enter' && e.target instanceof HTMLInputElement) {\r\n              e.preventDefault();\r\n              void handleSubmit(onSubmit)();\r\n            }\r\n          }}>\r\n            <section className=\"flex w-full flex-col space-y-2\">\r\n              <FormSection>\r\n                <FormField\r\n                  label=\"Name\"\r\n                  description=\"The name of the claim\"\r\n                >\r\n                  <Input required {...register('name')} placeholder=\"Claim Name\" />\r\n                </FormField>\r\n\r\n                <FormField\r\n                  label=\"Default\"\r\n                  description=\"Whether the role is default\"\r\n                >\r\n                  <Checkbox {...register('isDefault', {\r\n                    setValueAs: (value) => value === true\r\n                  })} />\r\n                </FormField>\r\n\r\n                <FormField\r\n                  label=\"Public\"\r\n                  description=\"Whether the role is public\"\r\n                >\r\n                  <Checkbox {...register('isPublic', {\r\n                    setValueAs: (value) => value === true\r\n                  })} />\r\n                </FormField>\r\n              </FormSection>\r\n            </section>\r\n            <DialogFooter className=\"mt-5\">\r\n              <Button\r\n                variant=\"ghost\"\r\n                onClick={(e) => {\r\n                  e.preventDefault()\r\n                  setOpen(false)\r\n                }}\r\n                disabled={createDataMutation.isPending}\r\n              >\r\n                Cancel\r\n              </Button>\r\n              <Button type=\"submit\" disabled={createDataMutation.isPending}>\r\n                {createDataMutation.isPending ? 'Saving...' : 'Save'}\r\n              </Button>\r\n            </DialogFooter>\r\n          </form>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </section>\r\n  )\r\n}\r\n", "import { type IdentityRoleUpdateDto, putApiIdentityRolesById } from '@/client'\r\nimport { useToast } from '@/lib/useToast'\r\nimport { useEffect, useState } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { Button } from '@/components/ui/button'\r\nimport { Checkbox } from '@/components/ui/checkbox'\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog'\r\nimport { Input } from '@/components/ui/input'\r\nimport { FormField, FormSection } from '@/components/ui/FormField'\r\nimport { useMutation, useQueryClient } from '@tanstack/react-query'\r\nimport { QueryNames } from '@/lib/hooks/QueryConstants'\r\nimport { handleApiError } from '@/lib/handleApiError'\r\n\r\ntype UserEditProps = {\r\n  dataEdit: IdentityRoleUpdateDto\r\n  dataId: string\r\n  onDismiss: () => void\r\n}\r\nexport const Edit = ({ dataEdit, dataId, onDismiss }: UserEditProps) => {\r\n  const [open, setOpen] = useState(false)\r\n  const { toast } = useToast()\r\n  const queryClient = useQueryClient()\r\n  const { handleSubmit, register, setValue } = useForm<IdentityRoleUpdateDto>()\r\n  const [isDefault, setIsDefault] = useState(dataEdit.isDefault ?? false)\r\n  const [isPublic, setIsPublic] = useState(dataEdit.isPublic ?? false)\r\n\r\n  const updateDataMutation = useMutation({\r\n    mutationFn: async (formData: IdentityRoleUpdateDto) =>\r\n      putApiIdentityRolesById({\r\n        path: { id: dataId },\r\n        body: {\r\n          name: formData.name ?? dataEdit.name ?? '',\r\n          isDefault: formData.isDefault ?? dataEdit.isDefault ?? false,\r\n          isPublic: formData.isPublic ?? dataEdit.isPublic ?? false,\r\n        },\r\n      }),\r\n    onSuccess: () => {\r\n      toast({\r\n        title: 'Success',\r\n        description: 'Role Updated Successfully',\r\n        variant: 'success',\r\n      })\r\n      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetRoles] })\r\n      onCloseEvent()\r\n    },\r\n    onError: (err: unknown) => {\r\n      console.log('Error updating claim:', err);\r\n      const error = handleApiError(err);\r\n      toast({\r\n        title: error.title,\r\n        description: error.description,\r\n        variant: 'error',\r\n      })\r\n    }\r\n  })\r\n\r\n  const onSubmit = (formData: IdentityRoleUpdateDto) => {\r\n    // Merge form data with consent type and permissions\r\n    const userData: IdentityRoleUpdateDto = {\r\n      ...formData,\r\n    }\r\n\r\n    updateDataMutation.mutate(userData)\r\n  }\r\n\r\n  const onCloseEvent = () => {\r\n    setOpen(false)\r\n    onDismiss()\r\n  }\r\n\r\n  // Initialize form when opened\r\n  useEffect(() => {\r\n    if (open) {\r\n      // Initialize form values from dataEdit\r\n      setValue('name', dataEdit.name || '')\r\n      setValue('isDefault', dataEdit.isDefault ?? false)\r\n      setValue('isPublic', dataEdit.isPublic ?? false)\r\n    }\r\n  }, [open, dataEdit, setValue])\r\n\r\n  useEffect(() => {\r\n    setOpen(true)\r\n  }, [])\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={onCloseEvent}>\r\n      <DialogContent>\r\n        <DialogHeader>\r\n          <DialogTitle>Update a Role: {dataEdit.name}</DialogTitle>\r\n        </DialogHeader>\r\n        <form\r\n          onSubmit={handleSubmit(onSubmit)}\r\n          className='mt-2'\r\n          onKeyDown={(e) => {\r\n            if (e.key === 'Enter' && e.target instanceof HTMLInputElement) {\r\n              e.preventDefault();\r\n              void handleSubmit(onSubmit)();\r\n            }\r\n          }}\r\n        >\r\n          <section className=\"flex w-full flex-col space-y-2\">\r\n            <FormSection>\r\n              <FormField\r\n                label=\"Name\"\r\n                description=\"The name of the role\"\r\n              >\r\n                <Input required {...register('name')} defaultValue={dataEdit.name ?? ''} placeholder=\"Role Name\" />\r\n              </FormField>\r\n\r\n              <FormField\r\n                label=\"Default\"\r\n                description=\"Whether the role is default\"\r\n              >\r\n                <Checkbox\r\n                  checked={isDefault}\r\n                  onCheckedChange={(checked) => {\r\n                    setIsDefault(!!checked)\r\n                    setValue('isDefault', !!checked)\r\n                  }}\r\n                />\r\n              </FormField>\r\n\r\n              <FormField\r\n                label=\"Public\"\r\n                description=\"Whether the role is public\"\r\n              >\r\n                <Checkbox\r\n                  checked={isPublic}\r\n                  onCheckedChange={(checked) => {\r\n                    setIsPublic(!!checked)\r\n                    setValue('isPublic', !!checked)\r\n                  }}\r\n                />\r\n              </FormField>\r\n            </FormSection>\r\n          </section>\r\n          <DialogFooter className=\"mt-5\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              onClick={(e) => {\r\n                e.preventDefault()\r\n                setOpen(false)\r\n              }}\r\n              disabled={updateDataMutation.isPending}\r\n              type=\"button\"\r\n            >\r\n              Cancel\r\n            </Button>\r\n            <Button\r\n              type=\"submit\"\r\n              disabled={updateDataMutation.isPending}\r\n            >\r\n              {updateDataMutation.isPending ? 'Saving...' : 'Save'}\r\n            </Button>\r\n          </DialogFooter>\r\n        </form>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n", "import { getApiIdentityRoles } from '@/client'\r\nimport { useQuery } from '@tanstack/react-query'\r\nimport { QueryNames } from './QueryConstants'\r\n\r\n/**\r\n * Custom hook to fetch a list of roles.\r\n *\r\n * This hook uses the `useQuery` hook from `react-query` to fetch the role data\r\n * asynchronously. The query key used is `QueryNames.GetRoles` along with pagination,\r\n * filter, and sorting parameters.\r\n *\r\n * @param {number} pageIndex - The current page index.\r\n * @param {number} pageSize - The number of items per page.\r\n * @param {string} [filter] - Optional filter string.\r\n * @param {string} [sorting] - Optional sorting string.\r\n * @returns {UseQueryResult} The result of the query, which includes the role data and query status.\r\n */\r\nexport const useRoles = (\r\n  pageIndex: number,\r\n  pageSize: number,\r\n  filter?: string  ,\r\n  sorting?: string  \r\n) => {\r\n  return useQuery({\r\n    queryKey: [QueryNames.GetRoles, pageIndex, pageSize, filter, sorting],\r\n    queryFn: async () => {\r\n      let skip = 0\r\n      if (pageIndex > 0) {\r\n        skip = pageIndex * pageSize\r\n      }\r\n      const { data } = await getApiIdentityRoles({\r\n        query: {\r\n          MaxResultCount: pageSize,\r\n          SkipCount: skip,\r\n          Filter: filter,\r\n          Sorting: sorting,\r\n        }\r\n      })\r\n      return data\r\n    },\r\n  })\r\n}\r\n", "import { type FormEvent, useCallback, useEffect, useMemo, useState, useRef } from 'react'\r\n\r\nimport {\r\n  type IdentityRoleDto,\r\n  type PermissionGroupDto,\r\n  type UpdatePermissionsDto,\r\n  putApiPermissionManagementPermissions,\r\n} from '@/client'\r\nimport { usePermissions } from '@/lib/hooks/usePermissions'\r\nimport { PermissionProvider, USER_ROLE } from '@/lib/utils'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { TogglePermission } from '@/components/app/permission/TogglePermission'\r\nimport { Button } from '@/components/ui/button'\r\nimport { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'\r\nimport { useToast } from '@/hooks/use-toast'\r\nimport { type Management, Permission } from '@/components/app/permission/PermissionToggle'\r\n\r\ntype RolePermissionProps = {\r\n  roleDto: IdentityRoleDto\r\n  onDismiss: () => void\r\n}\r\n\r\nexport const RolePermission = ({ roleDto, onDismiss }: RolePermissionProps) => {\r\n  const [open, setOpen] = useState(false)\r\n  const { toast } = useToast()\r\n\r\n  // flag determine to enable/disable all the permissions to a role.\r\n  const [hasAllGranted, setHasAllGranted] = useState(false)\r\n  const { data } = usePermissions(PermissionProvider.R, roleDto.name ?? undefined)\r\n  const queryClient = useQueryClient()\r\n\r\n  const [permissionGroups, setPermissionGroups] = useState<PermissionGroupDto[]>([])\r\n\r\n  useEffect(() => {\r\n    setOpen(true)\r\n    return () => {\r\n      void queryClient.invalidateQueries({ queryKey: [PermissionProvider.R] })\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [])\r\n\r\n  // Update the local state with the remote data\r\n  useEffect(() => {\r\n    if (data?.groups) {\r\n      setPermissionGroups([...data?.groups])\r\n    }\r\n  }, [data])\r\n\r\n  // Check if all permissions are granted already - only run when data changes\r\n  useEffect(() => {\r\n    if (data?.groups && data.groups.length > 0) {\r\n      const hasAllPermissionGranted = data.groups\r\n        .map((g) => g.permissions?.every((p) => p.isGranted))\r\n        .every((e) => e)\r\n      setHasAllGranted(hasAllPermissionGranted)\r\n    }\r\n  }, [data])\r\n\r\n  // Apply hasAllGranted to all permissions - only when hasAllGranted changes\r\n  useEffect(() => {\r\n    if (permissionGroups.length > 0) {\r\n      // Create a new array with updated permissions to avoid direct mutation\r\n      const updatedGroups = permissionGroups.map(group => ({\r\n        ...group,\r\n        permissions: group.permissions?.map(permission => ({\r\n          ...permission,\r\n          isGranted: hasAllGranted\r\n        })) ?? null\r\n      }));\r\n\r\n      // Update state with the new array\r\n      setPermissionGroups(updatedGroups);\r\n    }\r\n  }, [hasAllGranted]); // Remove permissionGroups from dependency array\r\n\r\n  const onCloseEvent = useCallback(() => {\r\n    setOpen(false)\r\n    onDismiss()\r\n  }, [onDismiss])\r\n\r\n  const onSubmit = useCallback(\r\n    async (e: FormEvent) => {\r\n      e.preventDefault()\r\n\r\n      if (!permissionGroups || permissionGroups.length === 0) return;\r\n\r\n      // Create a stable copy of the permissions to prevent reference issues\r\n      const payload = permissionGroups\r\n        .map((p) =>\r\n          (p.permissions ?? []).map((grant) => ({\r\n            name: grant.name ?? null,\r\n            isGranted: grant.isGranted ?? false,\r\n          }))\r\n        )\r\n        .flat()\r\n\r\n      const requestPayload: UpdatePermissionsDto = {\r\n        permissions: payload,\r\n      }\r\n\r\n      try {\r\n        await putApiPermissionManagementPermissions({\r\n          query: {\r\n            providerKey: roleDto.name ?? '',\r\n            providerName: PermissionProvider.R\r\n          },\r\n          body: requestPayload,\r\n        })\r\n\r\n        toast({\r\n          title: 'Success',\r\n          description: 'Permission Updated Successfully',\r\n          variant: 'default',\r\n        })\r\n\r\n        void queryClient.invalidateQueries({\r\n          queryKey: [PermissionProvider.R],\r\n        })\r\n\r\n        onCloseEvent()\r\n      } catch (err: unknown) {\r\n        if (err instanceof Error) {\r\n          toast({\r\n            title: 'Failed',\r\n            description: \"Permission update wasn't successful.\",\r\n            variant: 'destructive',\r\n          })\r\n        }\r\n      }\r\n    },\r\n    [permissionGroups, roleDto.name, toast, queryClient, onCloseEvent]\r\n  )\r\n\r\n  const hasAdmin = useMemo(() => roleDto.name?.includes(USER_ROLE.ADMIN) ?? false, [roleDto])\r\n\r\n  // Use stable keys to prevent remounting issues\r\n  const dialogKey = useRef(`dialog-${Math.random().toString(36).substring(2, 9)}`).current;\r\n\r\n  const formatDisplayName = (str: string): Management => {\r\n    const parts = (str || '').split(' ');\r\n    return (parts[0] ?? '').toLowerCase() as Management;\r\n  }\r\n\r\n  // Group permissions into pairs for the grid layout\r\n  const groupedPermissions = useMemo(() => {\r\n    const result = [];\r\n    for (let i = 0; i < permissionGroups.length; i += 2) {\r\n      result.push(permissionGroups.slice(i, i + 2));\r\n    }\r\n    return result;\r\n  }, [permissionGroups]);\r\n\r\n  return (\r\n    <Dialog key={dialogKey} open={open} onOpenChange={onCloseEvent}>\r\n      <DialogContent className=\"max-h-[90vh] overflow-hidden flex flex-col\" style={{ maxWidth: \"900px\", width: \"90vw\" }}>\r\n        <DialogHeader>\r\n          <DialogTitle>Permissions - {roleDto.name}</DialogTitle>\r\n        </DialogHeader>\r\n        <form onSubmit={onSubmit} className=\"flex-1 overflow-y-auto\">\r\n          <div className=\"p-1\">\r\n            <Permission\r\n              name=\"Grant All Permissions\"\r\n              isGranted={hasAllGranted}\r\n              id=\"all_granted\"\r\n              disabled={!hasAdmin}\r\n              onUpdate={() => {\r\n                setHasAllGranted((prev) => !prev)\r\n              }}\r\n              className=\"ml-2 mb-4\"\r\n            />\r\n\r\n            <div className=\"space-y-6\">\r\n              {groupedPermissions.map((row, rowIndex) => (\r\n                <div key={rowIndex} className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                  {row.map((group, colIndex) => (\r\n                    <div key={`${rowIndex}-${colIndex}`} className=\"border rounded-lg p-4\">\r\n                      <h3 className=\"text-lg font-medium mb-2\">{group.displayName}</h3>\r\n                      <div className=\"border-t pt-3\">\r\n                        <TogglePermission\r\n                          key={`group-${group.displayName}-${rowIndex}-${colIndex}`}\r\n                          permissions={group.permissions ?? []}\r\n                          type={formatDisplayName(group.displayName ?? '')}\r\n                          disabled={hasAllGranted && hasAdmin}\r\n                          hideSelectAll={hasAllGranted}\r\n                          hideSave\r\n                        />\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </form>\r\n        <DialogFooter className=\"mt-4 border-t pt-4 bg-white dark:bg-gray-950\">\r\n          <Button\r\n            onClick={(e) => {\r\n              e.preventDefault()\r\n              onCloseEvent()\r\n            }}\r\n            variant=\"ghost\"\r\n          >\r\n            Cancel\r\n          </Button>\r\n          <Button onClick={onSubmit}>Save</Button>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n", "'use client'\r\nimport { QueryNames } from '@/lib/hooks/QueryConstants'\r\nimport { useState } from 'react'\r\n\r\nimport { type IdentityRoleDto, type IdentityRoleUpdateDto } from '@/client'\r\nimport { type PaginationState, type SortingState } from '@tanstack/react-table'\r\n\r\nimport { useToast } from '@/lib/useToast'\r\nimport { Delete } from './Delete'\r\n\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { DataTable } from '@/components/data-table/DataTable'\r\nimport { getColumns } from './Columns'\r\nimport { Filter } from './Filter'\r\nimport { Add } from './Add'\r\nimport { Edit } from './Edit'\r\nimport { useRoles } from '@/lib/hooks/useRoles'\r\nimport { RolePermission } from './RolePermission'\r\nimport { TableSkeleton } from '@/components/ui/TableSkeleton'\r\n\r\nexport const RoleList = () => {\r\n  const { toast } = useToast()\r\n  const queryClient = useQueryClient()\r\n\r\n  const [searchStr, setSearchStr] = useState<string>('')\r\n  const [userActionDialog, setUserActionDialog] = useState<{\r\n    dataId: string\r\n    dataEdit: IdentityRoleDto\r\n    dialogType?: 'edit' | 'permission' | 'delete'\r\n  } | null>()\r\n\r\n  const [pagination, setPagination] = useState<PaginationState>({\r\n    pageIndex: 0,\r\n    pageSize: 10,\r\n  })\r\n\r\n  // Initialize sorting state\r\n  const [sorting, setSorting] = useState<SortingState>([\r\n    { id: 'name', desc: false }\r\n  ])\r\n\r\n  // Convert SortingState to API sorting string\r\n  const getSortingString = (sortState: SortingState): string => {\r\n    if (!sortState.length) return 'name asc';\r\n\r\n    const sort = sortState[0]; // Just use the first sort for now\r\n    return `${sort?.id} ${sort?.desc ? 'desc' : 'asc'}`;\r\n  }\r\n\r\n  const { isLoading, data } = useRoles(\r\n    pagination.pageIndex,\r\n    pagination.pageSize,\r\n    searchStr,\r\n    getSortingString(sorting)\r\n  )\r\n\r\n  // Handler for user actions (edit, permission, delete)\r\n  const handleUserAction = (dataId: string, dataEdit: IdentityRoleDto, dialogType: 'edit' | 'permission' | 'delete') => {\r\n    setUserActionDialog({\r\n      dataId,\r\n      dataEdit,\r\n      dialogType,\r\n    })\r\n  }\r\n\r\n  // Get columns with the action handler\r\n  const columns = getColumns(handleUserAction)\r\n\r\n  const handleSearch = (value: string) => {\r\n    setSearchStr(value)\r\n    setPagination(prev => ({ ...prev, pageIndex: 0 })) // Reset to first page on search\r\n  }\r\n\r\n  const handlePaginationChange = (newPagination: PaginationState) => {\r\n    setPagination(newPagination)\r\n  }\r\n\r\n  // Handler for sorting change\r\n  const handleSortingChange = (newSorting: SortingState) => {\r\n    setSorting(newSorting)\r\n    setPagination(prev => ({ ...prev, pageIndex: 0 })) // Reset to first page on sort change\r\n  }\r\n\r\n  // Handler for refreshing the data\r\n  const handleRefresh = () => {\r\n    // Invalidate the query to fetch fresh data\r\n    void queryClient.invalidateQueries({ queryKey: [QueryNames.GetRoles] })\r\n    // Show toast notification after a short delay to match the animation\r\n    setTimeout(() => {\r\n      toast({\r\n        title: \"Data refreshed\",\r\n        description: \"The claims list has been refreshed.\",\r\n        variant: \"success\",\r\n      })\r\n    }, 100)\r\n  }\r\n\r\n  if (isLoading) return (\r\n    <TableSkeleton\r\n      rowCount={pagination.pageSize}\r\n      columnCount={4}\r\n      hasTitle={true}\r\n      hasSearch={true}\r\n      hasFilters={true}\r\n      hasPagination={true}\r\n      hasActions={true}\r\n    />\r\n  )\r\n\r\n  // Ensure we have valid data to render\r\n  const items = data?.items ?? [];\r\n  const totalCount = data?.totalCount ?? 0;\r\n\r\n  return (\r\n    <>\r\n      <div className=\"space-y-4\">\r\n        <DataTable\r\n          title=\"Roles\"\r\n          columns={columns}\r\n          data={items}\r\n          totalCount={totalCount}\r\n          isLoading={isLoading}\r\n          manualPagination={true}\r\n          manualSorting={true}\r\n          pageSize={pagination.pageSize}\r\n          onPaginationChange={handlePaginationChange}\r\n          onSortingChange={handleSortingChange}\r\n          sortingState={sorting}\r\n          onSearch={handleSearch}\r\n          searchValue={searchStr}\r\n          customFilterbar={Filter}\r\n          hideDefaultFilterbar={true}\r\n          onRefresh={handleRefresh}\r\n          enableRowSelection={false}\r\n          actionButton={{\r\n            // label: \"Create New User\",\r\n            onClick: () => { /* Required but not used */ },\r\n            content: <Add />\r\n          }}\r\n        />\r\n      </div>\r\n\r\n      {userActionDialog && userActionDialog.dialogType === 'edit' && (\r\n        <Edit\r\n          dataId={userActionDialog.dataId}\r\n          dataEdit={userActionDialog.dataEdit as IdentityRoleUpdateDto}\r\n          onDismiss={() => {\r\n            void queryClient.invalidateQueries({ queryKey: [QueryNames.GetRoles] })\r\n            setUserActionDialog(null)\r\n          }}\r\n        />\r\n      )}\r\n      {userActionDialog && userActionDialog.dialogType === 'delete' && (\r\n        <Delete\r\n          dataId={userActionDialog.dataId}\r\n          onDismiss={() => {\r\n            void queryClient.invalidateQueries({ queryKey: [QueryNames.GetRoles] })\r\n            setUserActionDialog(null)\r\n          }}\r\n        />\r\n      )}\r\n\r\n      {userActionDialog && userActionDialog.dialogType === 'permission' && (\r\n        <RolePermission\r\n          roleDto={userActionDialog.dataEdit}\r\n          onDismiss={() => setUserActionDialog(null)}\r\n        />\r\n      )}\r\n    </>\r\n  )\r\n}\r\n", "'use client';\r\n\r\nimport { RoleList } from '@/components/app/role/List';\r\nimport AppLayout from '@/layouts/app-layout';\r\nimport { Head } from '@inertiajs/react';\r\nexport default function OverViewLayout() {\r\n  return (\r\n    <AppLayout>\r\n      <Head title=\"Dashboard\" />\r\n\r\n      <RoleList />\r\n    </AppLayout>\r\n  );\r\n}\r\n"], "names": ["Delete", "dataId", "on<PERSON><PERSON><PERSON>", "toast", "useToast", "open", "<PERSON><PERSON><PERSON>", "useState", "onYesEvent", "deleteApiIdentityRolesById", "err", "useEffect", "jsx", "AlertDialog", "jsxs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Alert<PERSON><PERSON><PERSON>H<PERSON>er", "AlertDialogTitle", "AlertDialogDescription", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AlertDialogCancel", "AlertDialogAction", "Actions", "userId", "userDto", "onAction", "variant", "can", "useGrantedPolicies", "DropdownMenu", "DropdownMenuTrigger", "<PERSON><PERSON>", "RiMoreLine", "DropdownMenuContent", "DropdownMenuItem", "RiShieldKeyholeLine", "RiPencilLine", "YesNoBadge", "value", "className", "cn", "getColumns", "handleUserAction", "table", "Checkbox", "row", "column", "DataTableColumnHeader", "info", "Filter", "onSearch", "searchValue", "isFiltered", "Search", "ViewOptions", "Add", "children", "queryClient", "useQueryClient", "handleSubmit", "register", "reset", "useForm", "setSelectedValueType", "createDataMutation", "useMutation", "formData", "postApiIdentityRoles", "QueryNames", "error", "handleApiError", "onSubmit", "userData", "handleOpenChange", "newOpenState", "Toaster", "Dialog", "DialogTrigger", "RiAddLine", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "DialogTitle", "e", "FormSection", "FormField", "Input", "<PERSON><PERSON><PERSON><PERSON>er", "Edit", "dataEdit", "setValue", "isDefault", "setIsDefault", "isPublic", "setIsPublic", "updateDataMutation", "putApiIdentityRolesById", "onCloseEvent", "checked", "useRoles", "pageIndex", "pageSize", "filter", "sorting", "useQuery", "skip", "data", "getApiIdentityRoles", "RolePermission", "roleDto", "hasAllGranted", "setHasAllGranted", "usePermissions", "PermissionProvider", "permissionGroups", "setPermissionGroups", "hasAllPermissionGranted", "g", "p", "updatedGroups", "group", "permission", "useCallback", "requestPayload", "grant", "putApiPermissionManagementPermissions", "has<PERSON>dmin", "useMemo", "USER_ROLE", "dialogKey", "useRef", "formatDisplayName", "str", "groupedPermissions", "result", "i", "Permission", "prev", "rowIndex", "colIndex", "TogglePermission", "RoleList", "searchStr", "setSearchStr", "userActionDialog", "setUserActionDialog", "pagination", "setPagination", "setSorting", "getSortingString", "sortState", "sort", "isLoading", "columns", "dialogType", "handleSearch", "handlePaginationChange", "newPagination", "handleSortingChange", "newSorting", "handleRefresh", "TableSkeleton", "items", "totalCount", "Fragment", "DataTable", "OverViewLayout", "AppLayout", "Head"], "mappings": "oiCAkBO,MAAMA,GAAS,CAAC,CAAE,OAAAC,EAAQ,UAAAC,KAAiC,CAC1D,KAAA,CAAE,MAAAC,CAAM,EAAIC,EAAS,EACrB,CAACC,EAAMC,CAAO,EAAIC,EAAAA,SAAkB,EAAK,EACzCC,EAAa,SAAY,CACzB,GAAA,CACF,MAAMC,EAA2B,CAC/B,KAAM,CAAE,GAAIR,CAAO,CAAA,CACpB,EACKE,EAAA,CACJ,MAAO,UACP,YAAa,qCAAA,CACd,EACSD,EAAA,QACHQ,EAAc,CACjBA,aAAe,OACXP,EAAA,CACJ,MAAO,SACP,YAAa,gEACb,QAAS,aAAA,CACV,CACH,CAEJ,EAEAQ,OAAAA,EAAAA,UAAU,IAAM,CACdL,EAAQ,EAAI,CACd,EAAG,EAAE,EAGFM,EAAA,IAAAC,GAAA,CAAY,KAAAR,EACX,SAAAS,OAACC,GACC,CAAA,SAAA,CAAAD,OAACE,GACC,CAAA,SAAA,CAAAJ,EAAAA,IAACK,IAAiB,SAAwB,0BAAA,CAAA,EAC1CL,EAAAA,IAACM,IAAuB,SAExB,4EAAA,CAAA,CAAA,EACF,SACCC,GACC,CAAA,SAAA,CAACP,EAAA,IAAAQ,GAAA,CAAkB,QAASlB,EAAW,SAAM,SAAA,EAC5CU,EAAA,IAAAS,GAAA,CAAkB,QAASb,EAAY,SAAG,KAAA,CAAA,CAAA,CAC7C,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,EC1Cac,GAAU,CAAC,CAAE,OAAAC,EAAQ,QAAAC,EAAS,SAAAC,EAAU,QAAAC,EAAU,cAAkC,CACzF,KAAA,CAAE,IAAAC,CAAI,EAAIC,EAAmB,EAGnC,OAAIF,IAAY,WAEXd,EAAA,IAAA,MAAA,CAAI,UAAU,mBACb,gBAACiB,EACC,CAAA,SAAA,CAACjB,EAAAA,IAAAkB,EAAA,CAAoB,QAAO,GAC1B,SAAAhB,EAAA,KAACiB,EAAA,CACC,QAAQ,QACR,KAAK,KACL,UAAU,cAEV,SAAA,CAACnB,EAAAA,IAAAoB,GAAA,CAAW,UAAU,SAAU,CAAA,EAC/BpB,EAAA,IAAA,OAAA,CAAK,UAAU,UAAU,SAAS,WAAA,CAAA,CAAA,CAAA,CAAA,EAEvC,EACCE,EAAA,KAAAmB,EAAA,CAAoB,MAAM,MAAM,UAAU,YACxC,SAAA,CAAAN,EAAI,0BAA0B,GAC7Bf,EAAA,IAACsB,EAAA,CACC,UAAU,yBACV,QAAS,IAAMT,EAASF,EAAQC,EAAS,MAAM,EAChD,SAAA,MAAA,CAED,EAEDG,EAAI,qCAAqC,GACxCf,EAAA,IAACsB,EAAA,CACC,UAAU,yBACV,QAAS,IAAMT,EAASF,EAAQC,EAAS,YAAY,EACtD,SAAA,YAAA,CAED,EAEDG,EAAI,0BAA0B,GAC7Bf,EAAA,IAACsB,EAAA,CACC,UAAU,sCACV,QAAS,IAAMT,EAASF,EAAQC,EAAS,QAAQ,EAClD,SAAA,QAAA,CAAA,CAED,CAEJ,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAMFV,EAAA,KAAC,MAAI,CAAA,UAAU,sCACZ,SAAA,CAAAa,EAAI,qCAAqC,GACxCb,EAAA,KAACiB,EAAA,CACC,QAAQ,UACR,KAAK,KACL,UAAU,oCACV,QAAS,IAAMN,EAASF,EAAQC,EAAS,YAAY,EAErD,SAAA,CAACZ,EAAAA,IAAAuB,GAAA,CAAoB,UAAU,SAAU,CAAA,EACzCvB,EAAAA,IAAC,QAAK,SAAU,YAAA,CAAA,CAAA,CAAA,CAClB,EAEDe,EAAI,0BAA0B,GAC7Bb,EAAA,KAACiB,EAAA,CACC,QAAQ,UACR,KAAK,KACL,UAAU,oCACV,QAAS,IAAMN,EAASF,EAAQC,EAAS,MAAM,EAE/C,SAAA,CAACZ,EAAAA,IAAAwB,GAAA,CAAa,UAAU,SAAU,CAAA,EAClCxB,EAAAA,IAAC,QAAK,SAAI,MAAA,CAAA,CAAA,CAAA,CAAA,CACZ,EAEJ,CAEJ,ECxFayB,EAAwC,CAAC,CAAE,MAAAC,EAAO,UAAAC,KAE3D3B,EAAA,IAAC,OAAA,CACC,UAAW4B,EACT,0EACAF,EACI,oEACA,4DACJC,CACF,EAEC,WAAQ,MAAQ,IAAA,CACnB,ECPSE,GACXC,GAEO,CACL,CACE,GAAI,SACJ,OAAQ,CAAC,CAAE,MAAAC,CAAA,IACT/B,EAAA,IAACgC,EAAA,CACC,QACED,EAAM,2BACF,GACAA,EAAM,sBAAA,EACJ,gBACA,GAER,gBAAiB,IAAMA,EAAM,0BAA0B,EACvD,UAAU,kBACV,aAAW,YAAA,CACb,EAEF,KAAM,CAAC,CAAE,IAAAE,CAAA,IACPjC,EAAA,IAACgC,EAAA,CACC,QAASC,EAAI,cAAc,EAC3B,gBAAiB,IAAMA,EAAI,eAAe,EAC1C,UAAU,kBACV,aAAW,YAAA,CACb,EAEF,cAAe,GACf,aAAc,GACd,KAAM,CACJ,YAAa,QAAA,CAEjB,EACA,CACE,YAAa,OACb,OAAQ,CAAC,CAAE,OAAAC,CAAA,IACRlC,EAAA,IAAAmC,EAAA,CAAsB,OAAAD,EAAgB,MAAM,OAAO,EAEtD,cAAe,GACf,aAAc,GACd,KAAOE,GAASA,EAAK,SAAS,EAC9B,KAAM,CACJ,UAAW,YACX,YAAa,MAAA,CAEjB,EACA,CACE,YAAa,YACb,OAAQ,CAAC,CAAE,OAAAF,CAAA,IACRlC,EAAA,IAAAmC,EAAA,CAAsB,OAAAD,EAAgB,MAAM,aAAa,EAE5D,cAAe,GACf,aAAc,GACd,KAAOE,GAASpC,MAACyB,GAAW,MAAOW,EAAK,WAAuB,EAC/D,KAAM,CACJ,UAAW,YACX,YAAa,YAAA,CAEjB,EACA,CACE,YAAa,WACb,OAAQ,CAAC,CAAE,OAAAF,CAAA,IACRlC,EAAA,IAAAmC,EAAA,CAAsB,OAAAD,EAAgB,MAAM,YAAY,EAE3D,cAAe,GACf,aAAc,GACd,KAAOE,GAASpC,MAACyB,GAAW,MAAOW,EAAK,WAAuB,EAC/D,KAAM,CACJ,UAAW,YACX,YAAa,WAAA,CAEjB,EACA,CACE,GAAI,UACJ,OAAQ,UACR,KAAOA,GACLpC,EAAA,IAACU,GAAA,CACC,OAAQ0B,EAAK,IAAI,SAAS,GAC1B,QAASA,EAAK,IAAI,SAClB,SAAUN,EACV,QAAQ,UAAA,CACV,EAEF,cAAe,GACf,aAAc,GACd,KAAM,CACJ,UAAW,aACX,YAAa,QAAA,CACf,CAEJ,EC3FK,SAASO,GAAc,CAC5B,MAAAN,EACA,SAAAO,EACA,YAAAC,EAAc,EAChB,EAAuB,CACrB,MAAMC,EAAaT,EAAM,SAAS,EAAE,cAAc,OAAS,EAGzD,OAAA7B,EAAA,KAAC,MAAI,CAAA,UAAU,oDACZ,SAAA,CACCoC,GAAAtC,EAAA,IAAC,MAAI,CAAA,UAAU,oCACb,SAAAA,EAAAA,IAACyC,IAAO,SAAUH,EAAU,MAAOC,CAAA,CAAa,CAClD,CAAA,EAGFrC,EAAAA,KAAC,MAAI,CAAA,UAAU,kCACZ,SAAA,CACCsC,GAAAxC,EAAA,IAACmB,EAAA,CACC,QAAQ,QACR,QAAS,IAAMY,EAAM,mBAAmB,EACxC,UAAU,6HACX,SAAA,eAAA,CAED,EASF/B,MAAC0C,IAAY,MAAAX,CAAc,CAAA,CAAA,CAC7B,CAAA,CAAA,EACF,CAEJ,CCrBO,MAAMY,GAAM,CAAC,CAAE,SAAAC,KAA+B,CAC7C,KAAA,CAAE,IAAA7B,CAAI,EAAIC,EAAmB,EAC7B,CAACvB,EAAMC,CAAO,EAAIC,EAAAA,SAAS,EAAK,EAChC,CAAE,MAAAJ,CAAM,EAAIC,EAAS,EACrBqD,EAAcC,EAAe,EAC7B,CAAE,aAAAC,EAAc,SAAAC,EAAU,MAAAC,CAAA,EAAUC,EAA+B,EACnE,EAAGC,CAAoB,EAAIxD,EAAA,SAAmB,EAAE,EAGtDI,EAAAA,UAAU,IAAM,CACTN,IACGwD,EAAA,CACJ,KAAM,GACN,UAAW,GACX,SAAU,EAAA,CACX,EACDE,EAAqB,CAAA,CAAE,EACzB,EACC,CAAC1D,EAAMwD,CAAK,CAAC,EAEhB,MAAMG,EAAqBC,EAAY,CACrC,WAAY,MAAOC,GACjBC,GAAqB,CACnB,KAAM,CACJ,KAAMD,EAAS,MAAQ,GACvB,UAAWA,EAAS,WAAa,GACjC,SAAUA,EAAS,UAAY,EAAA,CACjC,CACD,EACH,UAAW,IAAM,CACT/D,EAAA,CACJ,MAAO,UACP,YAAa,4BACb,QAAS,SAAA,CACV,EACIsD,EAAY,kBAAkB,CAAE,SAAU,CAACW,EAAW,QAAQ,EAAG,EACtE9D,EAAQ,EAAK,CACf,EACA,QAAUI,GAAiB,CAEnB,MAAA2D,EAAQC,EAAe5D,CAAG,EAC1BP,EAAA,CACJ,MAAOkE,EAAM,MACb,YAAaA,EAAM,YACnB,QAAS,OAAA,CACV,CAAA,CACH,CACD,EAEKE,EAAYL,GAAoC,CAEpD,MAAMM,EAAkC,CACtC,GAAGN,CACL,EAEAF,EAAmB,OAAOQ,CAAQ,CACpC,EAEMC,EAAoBC,GAA0B,CAClDpE,EAAQoE,CAAY,CACtB,EAEA,cACG,UACC,CAAA,SAAA,CAAA9D,EAAA,IAAC+D,GAAQ,EAAA,EACR7D,EAAA,KAAA8D,EAAA,CAAO,KAAAvE,EAAY,aAAcoE,EAChC,SAAA,CAAC7D,EAAAA,IAAAiE,GAAA,CAAc,QAAO,GAAE,SAAArB,CAAS,CAAA,QAChC,UAAQ,CAAA,UAAU,yCAChB,SAAA7B,EAAI,kCAAkC,GACrCb,OAACiB,EAAO,CAAA,KAAK,KAAK,UAAU,kCAAkC,QAAS,IAAMzB,EAAQ,EAAI,EACvF,SAAA,CAAAM,EAAA,IAACkE,GAAU,CAAA,UAAU,wBAAwB,cAAY,OAAO,EAC/DlE,EAAA,IAAA,OAAA,CAAK,UAAU,4BAA4B,SAAQ,UAAA,CAAA,CAAA,CAAA,CACtD,CAEJ,CAAA,EACAE,EAAAA,KAACiE,EAAc,CAAA,UAAU,YACvB,SAAA,CAAAnE,MAACoE,EACC,CAAA,SAAApE,EAAA,IAACqE,EAAY,CAAA,SAAA,mBAAiB,CAAA,EAChC,EACAnE,EAAAA,KAAC,OAAK,CAAA,SAAU6C,EAAaY,CAAQ,EAAG,UAAU,OAAO,UAAYW,GAAM,CACrEA,EAAE,MAAQ,SAAWA,EAAE,kBAAkB,mBAC3CA,EAAE,eAAe,EACZvB,EAAaY,CAAQ,EAAE,EAG9B,EAAA,SAAA,CAAA3D,MAAC,UAAQ,CAAA,UAAU,iCACjB,SAAAE,EAAA,KAACqE,EACC,CAAA,SAAA,CAAAvE,EAAA,IAACwE,EAAA,CACC,MAAM,OACN,YAAY,wBAEZ,SAAAxE,EAAA,IAACyE,GAAM,SAAQ,GAAE,GAAGzB,EAAS,MAAM,EAAG,YAAY,YAAa,CAAA,CAAA,CACjE,EAEAhD,EAAA,IAACwE,EAAA,CACC,MAAM,UACN,YAAY,8BAEZ,SAACxE,EAAAA,IAAAgC,EAAA,CAAU,GAAGgB,EAAS,YAAa,CAClC,WAAatB,GAAUA,IAAU,EAAA,CAClC,CAAG,CAAA,CAAA,CACN,EAEA1B,EAAA,IAACwE,EAAA,CACC,MAAM,SACN,YAAY,6BAEZ,SAACxE,EAAAA,IAAAgC,EAAA,CAAU,GAAGgB,EAAS,WAAY,CACjC,WAAatB,GAAUA,IAAU,EAAA,CAClC,CAAG,CAAA,CAAA,CAAA,CACN,CAAA,CACF,CACF,CAAA,EACAxB,EAAAA,KAACwE,EAAa,CAAA,UAAU,OACtB,SAAA,CAAA1E,EAAA,IAACmB,EAAA,CACC,QAAQ,QACR,QAAUmD,GAAM,CACdA,EAAE,eAAe,EACjB5E,EAAQ,EAAK,CACf,EACA,SAAU0D,EAAmB,UAC9B,SAAA,QAAA,CAED,EACApD,EAAAA,IAACmB,EAAO,CAAA,KAAK,SAAS,SAAUiC,EAAmB,UAChD,SAAAA,EAAmB,UAAY,YAAc,MAChD,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,CAEJ,ECzIauB,GAAO,CAAC,CAAE,SAAAC,EAAU,OAAAvF,EAAQ,UAAAC,KAA+B,CACtE,KAAM,CAACG,EAAMC,CAAO,EAAIC,EAAAA,SAAS,EAAK,EAChC,CAAE,MAAAJ,CAAM,EAAIC,EAAS,EACrBqD,EAAcC,EAAe,EAC7B,CAAE,aAAAC,EAAc,SAAAC,EAAU,SAAA6B,CAAA,EAAa3B,EAA+B,EACtE,CAAC4B,EAAWC,CAAY,EAAIpF,EAAS,SAAAiF,EAAS,WAAa,EAAK,EAChE,CAACI,EAAUC,CAAW,EAAItF,EAAS,SAAAiF,EAAS,UAAY,EAAK,EAE7DM,EAAqB7B,EAAY,CACrC,WAAY,MAAOC,GACjB6B,GAAwB,CACtB,KAAM,CAAE,GAAI9F,CAAO,EACnB,KAAM,CACJ,KAAMiE,EAAS,MAAQsB,EAAS,MAAQ,GACxC,UAAWtB,EAAS,WAAasB,EAAS,WAAa,GACvD,SAAUtB,EAAS,UAAYsB,EAAS,UAAY,EAAA,CACtD,CACD,EACH,UAAW,IAAM,CACTrF,EAAA,CACJ,MAAO,UACP,YAAa,4BACb,QAAS,SAAA,CACV,EACIsD,EAAY,kBAAkB,CAAE,SAAU,CAACW,EAAW,QAAQ,EAAG,EACzD4B,EAAA,CACf,EACA,QAAUtF,GAAiB,CAEnB,MAAA2D,EAAQC,EAAe5D,CAAG,EAC1BP,EAAA,CACJ,MAAOkE,EAAM,MACb,YAAaA,EAAM,YACnB,QAAS,OAAA,CACV,CAAA,CACH,CACD,EAEKE,EAAYL,GAAoC,CAEpD,MAAMM,EAAkC,CACtC,GAAGN,CACL,EAEA4B,EAAmB,OAAOtB,CAAQ,CACpC,EAEMwB,EAAe,IAAM,CACzB1F,EAAQ,EAAK,EACHJ,EAAA,CACZ,EAGAS,OAAAA,EAAAA,UAAU,IAAM,CACVN,IAEOoF,EAAA,OAAQD,EAAS,MAAQ,EAAE,EAC3BC,EAAA,YAAaD,EAAS,WAAa,EAAK,EACxCC,EAAA,WAAYD,EAAS,UAAY,EAAK,EAEhD,EAAA,CAACnF,EAAMmF,EAAUC,CAAQ,CAAC,EAE7B9E,EAAAA,UAAU,IAAM,CACdL,EAAQ,EAAI,CACd,EAAG,EAAE,QAGFsE,EAAO,CAAA,KAAAvE,EAAY,aAAc2F,EAChC,gBAACjB,EACC,CAAA,SAAA,CAACnE,EAAA,IAAAoE,EAAA,CACC,gBAACC,EAAY,CAAA,SAAA,CAAA,kBAAgBO,EAAS,IAAA,CAAA,CAAK,CAC7C,CAAA,EACA1E,EAAA,KAAC,OAAA,CACC,SAAU6C,EAAaY,CAAQ,EAC/B,UAAU,OACV,UAAYW,GAAM,CACZA,EAAE,MAAQ,SAAWA,EAAE,kBAAkB,mBAC3CA,EAAE,eAAe,EACZvB,EAAaY,CAAQ,EAAE,EAEhC,EAEA,SAAA,CAAA3D,MAAC,UAAQ,CAAA,UAAU,iCACjB,SAAAE,EAAA,KAACqE,EACC,CAAA,SAAA,CAAAvE,EAAA,IAACwE,EAAA,CACC,MAAM,OACN,YAAY,uBAEZ,SAACxE,EAAA,IAAAyE,EAAA,CAAM,SAAQ,GAAE,GAAGzB,EAAS,MAAM,EAAG,aAAc4B,EAAS,MAAQ,GAAI,YAAY,WAAY,CAAA,CAAA,CACnG,EAEA5E,EAAA,IAACwE,EAAA,CACC,MAAM,UACN,YAAY,8BAEZ,SAAAxE,EAAA,IAACgC,EAAA,CACC,QAAS8C,EACT,gBAAkBO,GAAY,CACfN,EAAA,CAAC,CAACM,CAAO,EACbR,EAAA,YAAa,CAAC,CAACQ,CAAO,CAAA,CACjC,CAAA,CACF,CACF,EAEArF,EAAA,IAACwE,EAAA,CACC,MAAM,SACN,YAAY,6BAEZ,SAAAxE,EAAA,IAACgC,EAAA,CACC,QAASgD,EACT,gBAAkBK,GAAY,CAChBJ,EAAA,CAAC,CAACI,CAAO,EACZR,EAAA,WAAY,CAAC,CAACQ,CAAO,CAAA,CAChC,CAAA,CACF,CAAA,CACF,CAAA,CACF,CACF,CAAA,EACAnF,EAAAA,KAACwE,EAAa,CAAA,UAAU,OACtB,SAAA,CAAA1E,EAAA,IAACmB,EAAA,CACC,QAAQ,QACR,QAAUmD,GAAM,CACdA,EAAE,eAAe,EACjB5E,EAAQ,EAAK,CACf,EACA,SAAUwF,EAAmB,UAC7B,KAAK,SACN,SAAA,QAAA,CAED,EACAlF,EAAA,IAACmB,EAAA,CACC,KAAK,SACL,SAAU+D,EAAmB,UAE5B,SAAAA,EAAmB,UAAY,YAAc,MAAA,CAAA,CAChD,CACF,CAAA,CAAA,CAAA,CAAA,CACF,CAAA,CACF,CACF,CAAA,CAEJ,ECpJaI,GAAW,CACtBC,EACAC,EACAC,EACAC,IAEOC,EAAS,CACd,SAAU,CAACnC,EAAW,SAAU+B,EAAWC,EAAUC,EAAQC,CAAO,EACpE,QAAS,SAAY,CACnB,IAAIE,EAAO,EACPL,EAAY,IACdK,EAAOL,EAAYC,GAErB,KAAM,CAAE,KAAAK,GAAS,MAAMC,GAAoB,CACzC,MAAO,CACL,eAAgBN,EAChB,UAAWI,EACX,OAAQH,EACR,QAASC,CAAA,CACX,CACD,EACM,OAAAG,CAAA,CACT,CACD,EClBUE,GAAiB,CAAC,CAAE,QAAAC,EAAS,UAAA1G,KAAqC,CAC7E,KAAM,CAACG,EAAMC,CAAO,EAAIC,EAAAA,SAAS,EAAK,EAChC,CAAE,MAAAJ,CAAM,EAAIC,EAAS,EAGrB,CAACyG,EAAeC,CAAgB,EAAIvG,EAAAA,SAAS,EAAK,EAClD,CAAE,KAAAkG,CAAS,EAAAM,GAAeC,EAAmB,EAAGJ,EAAQ,MAAQ,MAAS,EACzEnD,EAAcC,EAAe,EAE7B,CAACuD,EAAkBC,CAAmB,EAAI3G,EAAAA,SAA+B,CAAA,CAAE,EAEjFI,EAAAA,UAAU,KACRL,EAAQ,EAAI,EACL,IAAM,CACNmD,EAAY,kBAAkB,CAAE,SAAU,CAACuD,EAAmB,CAAC,EAAG,CACzE,GAEC,EAAE,EAGLrG,EAAAA,UAAU,IAAM,CACV8F,GAAM,QACRS,EAAoB,CAAC,GAAGT,GAAM,MAAM,CAAC,CACvC,EACC,CAACA,CAAI,CAAC,EAGT9F,EAAAA,UAAU,IAAM,CACd,GAAI8F,GAAM,QAAUA,EAAK,OAAO,OAAS,EAAG,CAC1C,MAAMU,EAA0BV,EAAK,OAClC,IAAKW,GAAMA,EAAE,aAAa,MAAOC,GAAMA,EAAE,SAAS,CAAC,EACnD,MAAOnC,GAAMA,CAAC,EACjB4B,EAAiBK,CAAuB,CAAA,CAC1C,EACC,CAACV,CAAI,CAAC,EAGT9F,EAAAA,UAAU,IAAM,CACV,GAAAsG,EAAiB,OAAS,EAAG,CAEzB,MAAAK,EAAgBL,EAAiB,IAAcM,IAAA,CACnD,GAAGA,EACH,YAAaA,EAAM,aAAa,IAAmBC,IAAA,CACjD,GAAGA,EACH,UAAWX,GACX,GAAK,IAAA,EACP,EAGFK,EAAoBI,CAAa,CAAA,CACnC,EACC,CAACT,CAAa,CAAC,EAEZ,MAAAb,EAAeyB,EAAAA,YAAY,IAAM,CACrCnH,EAAQ,EAAK,EACHJ,EAAA,CAAA,EACT,CAACA,CAAS,CAAC,EAERqE,EAAWkD,EAAA,YACf,MAAOvC,GAAiB,CAGtB,GAFAA,EAAE,eAAe,EAEb,CAAC+B,GAAoBA,EAAiB,SAAW,EAAG,OAYxD,MAAMS,EAAuC,CAC3C,YAVcT,EACb,IAAKI,IACHA,EAAE,aAAe,CAAA,GAAI,IAAKM,IAAW,CACpC,KAAMA,EAAM,MAAQ,KACpB,UAAWA,EAAM,WAAa,EAAA,EAC9B,GAEH,KAAK,CAIR,EAEI,GAAA,CACF,MAAMC,GAAsC,CAC1C,MAAO,CACL,YAAahB,EAAQ,MAAQ,GAC7B,aAAcI,EAAmB,CACnC,EACA,KAAMU,CAAA,CACP,EAEKvH,EAAA,CACJ,MAAO,UACP,YAAa,kCACb,QAAS,SAAA,CACV,EAEIsD,EAAY,kBAAkB,CACjC,SAAU,CAACuD,EAAmB,CAAC,CAAA,CAChC,EAEYhB,EAAA,QACNtF,EAAc,CACjBA,aAAe,OACXP,EAAA,CACJ,MAAO,SACP,YAAa,uCACb,QAAS,aAAA,CACV,CACH,CAEJ,EACA,CAAC8G,EAAkBL,EAAQ,KAAMzG,EAAOsD,EAAauC,CAAY,CACnE,EAEM6B,EAAWC,EAAAA,QAAQ,IAAMlB,EAAQ,MAAM,SAASmB,GAAU,KAAK,GAAK,GAAO,CAACnB,CAAO,CAAC,EAGpFoB,EAAYC,EAAAA,OAAO,UAAU,KAAK,SAAS,SAAS,EAAE,EAAE,UAAU,EAAG,CAAC,CAAC,EAAE,EAAE,QAE3EC,EAAqBC,KACVA,GAAO,IAAI,MAAM,GAAG,EACrB,CAAC,GAAK,IAAI,YAAY,EAIhCC,EAAqBN,EAAAA,QAAQ,IAAM,CACvC,MAAMO,EAAS,CAAC,EAChB,QAASC,EAAI,EAAGA,EAAIrB,EAAiB,OAAQqB,GAAK,EAChDD,EAAO,KAAKpB,EAAiB,MAAMqB,EAAGA,EAAI,CAAC,CAAC,EAEvC,OAAAD,CAAA,EACN,CAACpB,CAAgB,CAAC,EAErB,OACGrG,EAAA,IAAAgE,EAAA,CAAuB,KAAAvE,EAAY,aAAc2F,EAChD,SAAClF,EAAAA,KAAAiE,EAAA,CAAc,UAAU,6CAA6C,MAAO,CAAE,SAAU,QAAS,MAAO,MACvG,EAAA,SAAA,CAACnE,EAAA,IAAAoE,EAAA,CACC,gBAACC,EAAY,CAAA,SAAA,CAAA,iBAAe2B,EAAQ,IAAA,CAAA,CAAK,CAC3C,CAAA,EACAhG,EAAAA,IAAC,QAAK,SAAA2D,EAAoB,UAAU,yBAClC,SAACzD,EAAAA,KAAA,MAAA,CAAI,UAAU,MACb,SAAA,CAAAF,EAAA,IAAC2H,GAAA,CACC,KAAK,wBACL,UAAW1B,EACX,GAAG,cACH,SAAU,CAACgB,EACX,SAAU,IAAM,CACGf,EAAC0B,GAAS,CAACA,CAAI,CAClC,EACA,UAAU,WAAA,CACZ,EAEA5H,EAAAA,IAAC,OAAI,UAAU,YACZ,WAAmB,IAAI,CAACiC,EAAK4F,IAC5B7H,EAAAA,IAAC,OAAmB,UAAU,wCAC3B,WAAI,IAAI,CAAC2G,EAAOmB,IACf5H,EAAAA,KAAC,MAAoC,CAAA,UAAU,wBAC7C,SAAA,CAAAF,EAAA,IAAC,KAAG,CAAA,UAAU,2BAA4B,SAAA2G,EAAM,YAAY,EAC5D3G,EAAAA,IAAC,MAAI,CAAA,UAAU,gBACb,SAAAA,EAAA,IAAC+H,GAAA,CAEC,YAAapB,EAAM,aAAe,CAAC,EACnC,KAAMW,EAAkBX,EAAM,aAAe,EAAE,EAC/C,SAAUV,GAAiBgB,EAC3B,cAAehB,EACf,SAAQ,EAAA,EALH,SAASU,EAAM,WAAW,IAAIkB,CAAQ,IAAIC,CAAQ,EAAA,CAO3D,CAAA,CAAA,GAXQ,GAAGD,CAAQ,IAAIC,CAAQ,EAYjC,CACD,CAAA,EAfOD,CAgBV,CACD,CACH,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EACA3H,EAAAA,KAACwE,EAAa,CAAA,UAAU,+CACtB,SAAA,CAAA1E,EAAA,IAACmB,EAAA,CACC,QAAUmD,GAAM,CACdA,EAAE,eAAe,EACJc,EAAA,CACf,EACA,QAAQ,QACT,SAAA,QAAA,CAED,EACCpF,EAAA,IAAAmB,EAAA,CAAO,QAASwC,EAAU,SAAI,MAAA,CAAA,CAAA,CACjC,CAAA,CAAA,CACF,CAAA,GArDWyD,CAsDb,CAEJ,EC7LaY,GAAW,IAAM,CACtB,KAAA,CAAE,MAAAzI,CAAM,EAAIC,EAAS,EACrBqD,EAAcC,EAAe,EAE7B,CAACmF,EAAWC,CAAY,EAAIvI,EAAAA,SAAiB,EAAE,EAC/C,CAACwI,EAAkBC,CAAmB,EAAIzI,WAItC,EAEJ,CAAC0I,EAAYC,CAAa,EAAI3I,WAA0B,CAC5D,UAAW,EACX,SAAU,EAAA,CACX,EAGK,CAAC+F,EAAS6C,CAAU,EAAI5I,WAAuB,CACnD,CAAE,GAAI,OAAQ,KAAM,EAAM,CAAA,CAC3B,EAGK6I,EAAoBC,GAAoC,CACxD,GAAA,CAACA,EAAU,OAAe,MAAA,WAExB,MAAAC,EAAOD,EAAU,CAAC,EACxB,MAAO,GAAGC,GAAM,EAAE,IAAIA,GAAM,KAAO,OAAS,KAAK,EACnD,EAEM,CAAE,UAAAC,EAAW,KAAA9C,CAAA,EAASP,GAC1B+C,EAAW,UACXA,EAAW,SACXJ,EACAO,EAAiB9C,CAAO,CAC1B,EAYMkD,EAAU/G,GATS,CAACxC,EAAgBuF,EAA2BiE,IAAiD,CAChGT,EAAA,CAClB,OAAA/I,EACA,SAAAuF,EACA,WAAAiE,CAAA,CACD,CACH,CAG2C,EAErCC,EAAgBpH,GAAkB,CACtCwG,EAAaxG,CAAK,EAClB4G,MAAuB,CAAE,GAAGV,EAAM,UAAW,GAAI,CACnD,EAEMmB,EAA0BC,GAAmC,CACjEV,EAAcU,CAAa,CAC7B,EAGMC,EAAuBC,GAA6B,CACxDX,EAAWW,CAAU,EACrBZ,MAAuB,CAAE,GAAGV,EAAM,UAAW,GAAI,CACnD,EAGMuB,EAAgB,IAAM,CAErBtG,EAAY,kBAAkB,CAAE,SAAU,CAACW,EAAW,QAAQ,EAAG,EAEtE,WAAW,IAAM,CACTjE,EAAA,CACJ,MAAO,iBACP,YAAa,sCACb,QAAS,SAAA,CACV,GACA,GAAG,CACR,EAEA,GAAIoJ,EACF,OAAA3I,EAAA,IAACoJ,GAAA,CACC,SAAUf,EAAW,SACrB,YAAa,EACb,SAAU,GACV,UAAW,GACX,WAAY,GACZ,cAAe,GACf,WAAY,EAAA,CACd,EAII,MAAAgB,EAAQxD,GAAM,OAAS,CAAC,EACxByD,EAAazD,GAAM,YAAc,EAEvC,OAEI3F,EAAA,KAAAqJ,WAAA,CAAA,SAAA,CAACvJ,EAAAA,IAAA,MAAA,CAAI,UAAU,YACb,SAAAA,EAAA,IAACwJ,GAAA,CACC,MAAM,QACN,QAAAZ,EACA,KAAMS,EACN,WAAAC,EACA,UAAAX,EACA,iBAAkB,GAClB,cAAe,GACf,SAAUN,EAAW,SACrB,mBAAoBU,EACpB,gBAAiBE,EACjB,aAAcvD,EACd,SAAUoD,EACV,YAAab,EACb,gBAAiB5F,GACjB,qBAAsB,GACtB,UAAW8G,EACX,mBAAoB,GACpB,aAAc,CAEZ,QAAS,IAAM,CAA8B,EAC7C,cAAUxG,GAAI,CAAA,CAAA,CAAA,CAChB,CAAA,EAEJ,EAECwF,GAAoBA,EAAiB,aAAe,QACnDnI,EAAA,IAAC2E,GAAA,CACC,OAAQwD,EAAiB,OACzB,SAAUA,EAAiB,SAC3B,UAAW,IAAM,CACVtF,EAAY,kBAAkB,CAAE,SAAU,CAACW,EAAW,QAAQ,EAAG,EACtE4E,EAAoB,IAAI,CAAA,CAC1B,CACF,EAEDD,GAAoBA,EAAiB,aAAe,UACnDnI,EAAA,IAACZ,GAAA,CACC,OAAQ+I,EAAiB,OACzB,UAAW,IAAM,CACVtF,EAAY,kBAAkB,CAAE,SAAU,CAACW,EAAW,QAAQ,EAAG,EACtE4E,EAAoB,IAAI,CAAA,CAC1B,CACF,EAGDD,GAAoBA,EAAiB,aAAe,cACnDnI,EAAA,IAAC+F,GAAA,CACC,QAASoC,EAAiB,SAC1B,UAAW,IAAMC,EAAoB,IAAI,CAAA,CAAA,CAC3C,EAEJ,CAEJ,ECrKA,SAAwBqB,IAAiB,CACvC,cACGC,GACC,CAAA,SAAA,CAAC1J,EAAAA,IAAA2J,GAAA,CAAK,MAAM,WAAY,CAAA,QAEvB3B,GAAS,CAAA,CAAA,CAAA,EACZ,CAEJ"}