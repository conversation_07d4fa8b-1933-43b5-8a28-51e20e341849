using System;
using System.Collections.Generic;
using Volo.Abp.Application.Dtos;
using Imip.JettyApproval.Approvals.ApprovalApprovers;
using Imip.JettyApproval.Approvals.ApprovalCriterias;
using Imip.JettyApproval.Approvals.ApprovalStages;

namespace Imip.JettyApproval.Approvals.ApprovalTemplates;

/// <summary>
/// DTO for ApprovalTemplate entity
/// </summary>
public class ApprovalTemplateDto : FullAuditedEntityDto<Guid>
{
    /// <summary>
    /// Name of the approval template
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Description of the approval template
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Code for the approval template
    /// </summary>
    public string? Code { get; set; }

    /// <summary>
    /// Collection of approval approvers
    /// </summary>
    public List<ApprovalApproverDto> Approvers { get; set; } = new List<ApprovalApproverDto>();

    /// <summary>
    /// Collection of approval criterias
    /// </summary>
    public List<ApprovalCriteriaDto> Criterias { get; set; } = new List<ApprovalCriteriaDto>();

    /// <summary>
    /// Collection of approval stages
    /// </summary>
    public List<ApprovalStageDto> Stages { get; set; } = new List<ApprovalStageDto>();
}