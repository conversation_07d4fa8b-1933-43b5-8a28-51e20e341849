using System;
using Volo.Abp.BlobStoring;

namespace Imip.JettyApproval.BlobStorage;

public static class SftpBlobProviderConfigurationExtensions
{
    /// <summary>
    /// Configures the container to use the SFTP blob provider.
    /// </summary>
    /// <param name="containerConfiguration">The container configuration.</param>
    /// <param name="configureAction">The configuration action.</param>
    /// <returns>The container configuration.</returns>
    public static BlobContainerConfiguration UseSftp(
        this BlobContainerConfiguration containerConfiguration,
        Action<SftpBlobProviderConfiguration>? configureAction = null)
    {
        containerConfiguration.ProviderType = typeof(SftpBlobProvider);

        configureAction?.Invoke(
            new SftpBlobProviderConfiguration(containerConfiguration)
        );

        return containerConfiguration;
    }

    /// <summary>
    /// Gets the SFTP blob provider configuration.
    /// </summary>
    /// <param name="containerConfiguration">The container configuration.</param>
    /// <returns>The SFTP blob provider configuration.</returns>
    public static SftpBlobProviderConfiguration GetSftpConfiguration(
        this BlobContainerConfiguration containerConfiguration)
    {
        return new SftpBlobProviderConfiguration(containerConfiguration);
    }
}