{"version": 3, "file": "setting-BMimcGQa.js", "sources": ["../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/eye-off.js", "../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/eye.js", "../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/mail.js", "../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/server.js", "../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/toggle-right.js", "../../../../../frontend/src/lib/hooks/useEmailing.ts", "../../../../../frontend/src/components/app/settings/TestEmail.tsx", "../../../../../frontend/src/components/app/settings/Emailing.tsx", "../../../../../frontend/src/components/app/settings/Features.tsx", "../../../../../frontend/src/components/app/settings/FeatureManagement.tsx", "../../../../../frontend/src/lib/hooks/useActiveDirectory.ts", "../../../../../frontend/src/components/app/settings/ActiveDirectory.tsx", "../../../../../frontend/src/components/app/settings/SettingSidebar.tsx", "../../../../../frontend/src/pages/setting.tsx"], "sourcesContent": ["/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\n    \"path\",\n    {\n      d: \"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49\",\n      key: \"ct8e1f\"\n    }\n  ],\n  [\"path\", { d: \"M14.084 14.158a3 3 0 0 1-4.242-4.242\", key: \"151rxh\" }],\n  [\n    \"path\",\n    {\n      d: \"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143\",\n      key: \"13bj9a\"\n    }\n  ],\n  [\"path\", { d: \"m2 2 20 20\", key: \"1ooewy\" }]\n];\nconst EyeOff = createLucideIcon(\"eye-off\", __iconNode);\n\nexport { __iconNode, EyeOff as default };\n//# sourceMappingURL=eye-off.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\n    \"path\",\n    {\n      d: \"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0\",\n      key: \"1nclc0\"\n    }\n  ],\n  [\"circle\", { cx: \"12\", cy: \"12\", r: \"3\", key: \"1v7zrd\" }]\n];\nconst Eye = createLucideIcon(\"eye\", __iconNode);\n\nexport { __iconNode, Eye as default };\n//# sourceMappingURL=eye.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7\", key: \"132q7q\" }],\n  [\"rect\", { x: \"2\", y: \"4\", width: \"20\", height: \"16\", rx: \"2\", key: \"izxlao\" }]\n];\nconst Mail = createLucideIcon(\"mail\", __iconNode);\n\nexport { __iconNode, Mail as default };\n//# sourceMappingURL=mail.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"rect\", { width: \"20\", height: \"8\", x: \"2\", y: \"2\", rx: \"2\", ry: \"2\", key: \"ngkwjq\" }],\n  [\"rect\", { width: \"20\", height: \"8\", x: \"2\", y: \"14\", rx: \"2\", ry: \"2\", key: \"iecqi9\" }],\n  [\"line\", { x1: \"6\", x2: \"6.01\", y1: \"6\", y2: \"6\", key: \"16zg32\" }],\n  [\"line\", { x1: \"6\", x2: \"6.01\", y1: \"18\", y2: \"18\", key: \"nzw8ys\" }]\n];\nconst Server = createLucideIcon(\"server\", __iconNode);\n\nexport { __iconNode, Server as default };\n//# sourceMappingURL=server.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"circle\", { cx: \"15\", cy: \"12\", r: \"3\", key: \"1afu0r\" }],\n  [\"rect\", { width: \"20\", height: \"14\", x: \"2\", y: \"5\", rx: \"7\", key: \"g7kal2\" }]\n];\nconst ToggleRight = createLucideIcon(\"toggle-right\", __iconNode);\n\nexport { __iconNode, ToggleRight as default };\n//# sourceMappingURL=toggle-right.js.map\n", "import { type EmailSettingsDto, getApiSettingManagementEmailing } from '@/client'\r\nimport { type UseQueryResult, useQuery } from '@tanstack/react-query'\r\nimport { QueryNames } from './QueryConstants'\r\n\r\n/**\r\n * Custom hook to fetch email settings using React Query.\r\n *\r\n * This hook uses the `useQuery` hook from React Query to fetch email settings data.\r\n * It returns a `UseQueryResult` containing the email settings data or an error.\r\n *\r\n * @returns {UseQueryResult<EmailSettingsDto, unknown>} The result of the email settings query.\r\n */\r\nexport const useEmailing = (): UseQueryResult<EmailSettingsDto, unknown> => {\r\n  return useQuery({\r\n    queryKey: [QueryNames.GetEmailing],\r\n    queryFn: async () => {\r\n      const { data } = await getApiSettingManagementEmailing()\r\n      return data!\r\n    },\r\n  })\r\n}\r\n", "import { type SendTestEmailInput, postApiSettingManagementEmailingSendTestEmail } from '@/client'\r\nimport { QueryNames } from '@/lib/hooks/QueryConstants'\r\n// import { useGrantedPolicies } from '@/lib/hooks/useGrantedPolicies'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { useEffect, useState } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { Button } from '@/components/ui/button'\r\nimport { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'\r\nimport { Input } from '@/components/ui/input'\r\nimport { Textarea } from '@/components/ui/textarea'\r\nimport { useToast } from \"@/hooks/use-toast\"\r\n\r\nexport type TestEmailProps = {\r\n  onDismiss: () => void\r\n}\r\n\r\nexport const TestEmail = ({ onDismiss }: TestEmailProps) => {\r\n  // const { can } = useGrantedPolicies()\r\n  const [open, setOpen] = useState(false)\r\n\r\n  const { toast } = useToast()\r\n  const { handleSubmit, register } = useForm()\r\n  const queryClient = useQueryClient()\r\n\r\n  useEffect(() => {\r\n    setOpen(true)\r\n  }, [])\r\n\r\n  const onSubmit = async (data: unknown) => {\r\n    try {\r\n      const payload = data as SendTestEmailInput\r\n      await postApiSettingManagementEmailingSendTestEmail({ body: payload })\r\n      toast({\r\n        title: 'Success',\r\n        description: 'Test email has been sent Successfully',\r\n        variant: 'default',\r\n      })\r\n      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetRoles] })\r\n      onCloseEvent()\r\n    } catch (err: unknown) {\r\n      if (err instanceof Error) {\r\n        toast({\r\n          title: 'Failed',\r\n          description: \"Test email wasn't successful.\",\r\n          variant: 'destructive',\r\n        })\r\n      }\r\n    }\r\n  }\r\n\r\n  const onCloseEvent = () => {\r\n    setOpen(false)\r\n    onDismiss()\r\n  }\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={onCloseEvent}>\r\n      <DialogContent>\r\n        <DialogHeader>\r\n          <DialogTitle>Send Test Email</DialogTitle>\r\n        </DialogHeader>\r\n        <form onSubmit={handleSubmit(onSubmit)}>\r\n          <section className=\"flex w-full flex-col space-y-5\">\r\n            <Input\r\n              required\r\n              {...register('senderEmailAddress')}\r\n              placeholder=\"Sender Email Address\"\r\n            />\r\n            <Input\r\n              required\r\n              {...register('targetEmailAddress')}\r\n              placeholder=\"Target Email Address\"\r\n            />\r\n            <Input required {...register('subject')} placeholder=\"Subject\" />\r\n            <Textarea placeholder=\"Body\" {...register('body')} />\r\n          </section>\r\n          <DialogFooter className=\"mt-5\">\r\n            <Button type=\"submit\">Send</Button>\r\n            <Button\r\n              onClick={(e) => {\r\n                e.preventDefault()\r\n                onCloseEvent()\r\n              }}\r\n            >\r\n              Close\r\n            </Button>\r\n          </DialogFooter>\r\n        </form>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n", "'use client'\r\n\r\nimport { type EmailSettingsDto, type PostApiSettingManagementEmailingData, postApiSettingManagementEmailing } from '@/client'\r\nimport { QueryNames } from '@/lib/hooks/QueryConstants'\r\nimport { useEmailing } from '@/lib/hooks/useEmailing'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport clsx from 'clsx'\r\nimport { type SyntheticEvent, useCallback, useEffect, useState } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { Button } from '@/components/ui/button'\r\nimport { Checkbox } from '@/components/ui/checkbox'\r\nimport { Input } from '@/components/ui/input'\r\nimport { useToast } from \"@/hooks/use-toast\"\r\nimport { TestEmail } from './TestEmail'\r\n\r\nexport const Emailing = () => {\r\n  const { toast } = useToast()\r\n  const { data } = useEmailing()\r\n  const queryClient = useQueryClient()\r\n  const [openTestEmail, setOpenTestEmail] = useState<boolean>(false)\r\n  const { handleSubmit, register } = useForm()\r\n\r\n  const [emailSettingDto, setEmailSettingDto] = useState<EmailSettingsDto | undefined>(data)\r\n\r\n  useEffect(() => {\r\n    if (data) {\r\n      setEmailSettingDto({ ...data })\r\n    }\r\n  }, [data])\r\n\r\n  const onChangeEvent = useCallback(\r\n    (e: SyntheticEvent) => {\r\n      const { value, name } = e.target as HTMLInputElement\r\n\r\n      setEmailSettingDto({ ...emailSettingDto, [name]: value })\r\n    },\r\n    [emailSettingDto]\r\n  )\r\n\r\n  const onSubmitEvent = async () => {\r\n    try {\r\n      await postApiSettingManagementEmailing({\r\n        body: emailSettingDto,\r\n      } as PostApiSettingManagementEmailingData)\r\n      toast({\r\n        title: 'Success',\r\n        description: 'Email settings updated successfully',\r\n        variant: 'default',\r\n      })\r\n      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetEmailing] })\r\n    } catch (err: unknown) {\r\n      if (err instanceof Error) {\r\n        toast({\r\n          title: 'Failed',\r\n          description: \"Email settings wasn't successfull.\",\r\n          variant: 'destructive',\r\n        })\r\n      }\r\n    }\r\n  }\r\n\r\n  return (\r\n    <section className=\"emailing p-5 xl:p-10\">\r\n      {openTestEmail && <TestEmail onDismiss={() => setOpenTestEmail(false)} />}\r\n      <h3 className=\"text-xl font-medium\">Emailing</h3>\r\n      <hr className=\"mt-2 border\" />\r\n      <div className=\"pt-5\">\r\n        <form onSubmit={handleSubmit(onSubmitEvent)}>\r\n          <div className=\"mb-5 space-y-5\">\r\n            <Input\r\n              type=\"text\"\r\n              {...register('defaultFromDisplayName')}\r\n              required\r\n              placeholder=\"Default from display name\"\r\n              value={emailSettingDto?.defaultFromDisplayName ?? ''}\r\n              onChange={onChangeEvent}\r\n            />\r\n            <Input\r\n              type=\"email\"\r\n              {...register('defaultFromAddress')}\r\n              required\r\n              placeholder=\"Default from address\"\r\n              value={emailSettingDto?.defaultFromAddress ?? ''}\r\n              onChange={onChangeEvent}\r\n            />\r\n            <Input\r\n              type=\"text\"\r\n              {...register('smtpHost')}\r\n              placeholder=\"Host\"\r\n              value={emailSettingDto?.smtpHost ?? ''}\r\n              onChange={onChangeEvent}\r\n            />\r\n\r\n            <Input\r\n              type=\"number\"\r\n              {...register('smtpPort')}\r\n              required\r\n              placeholder=\"Port\"\r\n              value={emailSettingDto?.smtpPort ?? 0}\r\n              onChange={onChangeEvent}\r\n            />\r\n\r\n            <div className={clsx('flex items-center space-x-2')}>\r\n              <Checkbox\r\n                id=\"ssl\"\r\n                {...register('smtpEnableSsl')}\r\n                checked={emailSettingDto?.smtpEnableSsl}\r\n                onCheckedChange={(checked) => {\r\n                  setEmailSettingDto({\r\n                    ...emailSettingDto,\r\n                    smtpEnableSsl: !!checked.valueOf(),\r\n                  })\r\n                }}\r\n              />\r\n              <label htmlFor=\"ssl\" className=\"text-sm font-medium leading-none\">\r\n                Enable ssl\r\n              </label>\r\n            </div>\r\n\r\n            <div className={clsx('flex items-center space-x-2')}>\r\n              <Checkbox\r\n                id=\"credentials\"\r\n                name=\"smtpUseDefaultCredentials\"\r\n                checked={emailSettingDto?.smtpUseDefaultCredentials}\r\n                onCheckedChange={(checked) =>\r\n                  setEmailSettingDto({\r\n                    ...emailSettingDto,\r\n                    smtpUseDefaultCredentials: !!checked.valueOf(),\r\n                  })\r\n                }\r\n              />\r\n              <label htmlFor=\"credentials\" className=\"text-sm font-medium leading-none\">\r\n                Use default credentials\r\n              </label>\r\n            </div>\r\n            {!emailSettingDto?.smtpUseDefaultCredentials && (\r\n              <>\r\n                <Input\r\n                  type=\"text\"\r\n                  {...register('smtpDomain')}\r\n                  placeholder=\"Domain\"\r\n                  value={emailSettingDto?.smtpDomain ?? ''}\r\n                  onChange={onChangeEvent}\r\n                />\r\n\r\n                <Input\r\n                  type=\"text\"\r\n                  {...register('smtpUserName')}\r\n                  placeholder=\"User name\"\r\n                  value={emailSettingDto?.smtpUserName ?? ''}\r\n                  onChange={onChangeEvent}\r\n                />\r\n\r\n                <Input\r\n                  type=\"password\"\r\n                  {...register('smtpPassword')}\r\n                  placeholder=\"Password\"\r\n                  value={emailSettingDto?.smtpPassword ?? ''}\r\n                  onChange={onChangeEvent}\r\n                />\r\n              </>\r\n            )}\r\n          </div>\r\n          <div className=\"w-full space-x-5 space-y-5\">\r\n            <Button type=\"submit\">Save</Button>\r\n            <Button\r\n              variant=\"default\"\r\n              onClick={(e: { preventDefault: () => void }) => {\r\n                e.preventDefault()\r\n                setOpenTestEmail(true)\r\n              }}\r\n            >\r\n              Send Test Email\r\n            </Button>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </section>\r\n  )\r\n}\r\n", "import { useEffect, useState } from 'react'\r\nimport { But<PERSON> } from '@/components/ui/button'\r\nimport { <PERSON><PERSON>, DialogContent, DialogFooter, DialogTitle } from '@/components/ui/dialog'\r\n\r\ntype FeaturesProps = {\r\n  onDismiss: () => void\r\n}\r\nexport const Features = ({ onDismiss }: FeaturesProps) => {\r\n  const [open, setOpen] = useState(false)\r\n  const onCloseEvent = () => {\r\n    setOpen(false)\r\n    onDismiss()\r\n  }\r\n\r\n  useEffect(() => {\r\n    setOpen(true)\r\n  }, [])\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={onCloseEvent}>\r\n      <DialogContent>\r\n        <DialogTitle>Features</DialogTitle>\r\n        <article>\r\n          <p>There isn&apos;t any available feature.</p>\r\n        </article>\r\n        <DialogFooter className=\"mt-5\">\r\n          <Button\r\n            onClick={(e) => {\r\n              e.preventDefault()\r\n              onCloseEvent()\r\n            }}\r\n          >\r\n            Cancel\r\n          </Button>\r\n          <Button type=\"submit\">Save</Button>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n", "'use client'\r\nimport { useState } from 'react'\r\nimport { Button } from '@/components/ui/button'\r\nimport { Features } from './Features'\r\n\r\nexport const FeatureManagement = () => {\r\n  const [showFeatureDialog, setShowFeatureDialog] = useState(false)\r\n\r\n  return (\r\n    <section className=\"feature-management p-5 xl:p-10\">\r\n      {showFeatureDialog && <Features onDismiss={() => setShowFeatureDialog(false)} />}\r\n      <h1 className=\"text-xl font-medium\">Feature Management</h1>\r\n      <hr className=\"mt-2 border\" />\r\n      <div className=\"pt-5\">\r\n        <article className=\"text-base-content mb-5\">\r\n          <p>You can manage the host side features by clicking the following button.</p>\r\n        </article>\r\n        <Button onClick={() => setShowFeatureDialog(true)}>Manage Host Features</Button>\r\n      </div>\r\n    </section>\r\n  )\r\n}\r\n", "import { type ActiveDirectorySettingsDto, getApiActiveDirectorySettings } from '@/client'\r\nimport { type UseQueryResult, useQuery } from '@tanstack/react-query'\r\nimport { QueryNames } from './QueryConstants'\r\n\r\n/**\r\n * Custom hook to fetch email settings using React Query.\r\n *\r\n * This hook uses the `useQuery` hook from React Query to fetch email settings data.\r\n * It returns a `UseQueryResult` containing the email settings data or an error.\r\n *\r\n * @returns {UseQueryResult<ActiveDirectorySettingsDto, unknown>} The result of the email settings query.\r\n */\r\nexport const useActiveDirectory = (): UseQueryResult<ActiveDirectorySettingsDto, unknown> => {\r\n  return useQuery({\r\n    queryKey: [QueryNames.GetActiveDirectory],\r\n    queryFn: async () => {\r\n      const { data } = await getApiActiveDirectorySettings()\r\n      return data!\r\n    },\r\n  })\r\n}\r\n", "'use client'\r\n\r\nimport { putApiActiveDirectorySettings, type PutApiActiveDirectorySettingsData, type ActiveDirectorySettingsDto } from \"@/client\"\r\nimport { useActiveDirectory } from \"@/lib/hooks/useActiveDirectory\"\r\nimport { useToast } from \"@/hooks/use-toast\"\r\nimport { useQueryClient } from \"@tanstack/react-query\"\r\nimport { type SyntheticEvent, useCallback, useEffect, useId, useState } from \"react\"\r\nimport { useForm } from \"react-hook-form\"\r\nimport { QueryNames } from \"@/lib/hooks/QueryConstants\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport { Checkbox } from \"@/components/ui/checkbox\"\r\nimport { Button } from \"@/components/ui/button\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { postApiActiveDirectorySettingsTestConnection } from \"@/client\"\r\nimport { FormField } from \"@/components/ui/FormField\"\r\nimport { EyeIcon, EyeOffIcon } from \"lucide-react\"\r\n\r\nexport const ActiveDirectory = () => {\r\n  const { toast } = useToast()\r\n  const { data } = useActiveDirectory()\r\n  const queryClient = useQueryClient()\r\n  const { handleSubmit, register } = useForm()\r\n\r\n  const [adSettingDto, setAdSettingDto] = useState<ActiveDirectorySettingsDto | undefined>(data)\r\n  const id = useId()\r\n  const [isVisible, setIsVisible] = useState<boolean>(false)\r\n\r\n  const toggleVisibility = () => setIsVisible((prevState) => !prevState)\r\n\r\n  useEffect(() => {\r\n    if (data) {\r\n      setAdSettingDto({ ...data })\r\n    }\r\n  }, [data])\r\n\r\n  const onChangeEvent = useCallback(\r\n    (e: SyntheticEvent) => {\r\n      const { value, name } = e.target as HTMLInputElement\r\n\r\n      if (adSettingDto) {\r\n        setAdSettingDto({ ...adSettingDto, [name]: value })\r\n      }\r\n    },\r\n    [adSettingDto]\r\n  )\r\n\r\n  const onCheckboxChange = useCallback(\r\n    (name: string, checked: boolean) => {\r\n      if (adSettingDto) {\r\n        setAdSettingDto({\r\n          ...adSettingDto,\r\n          [name]: checked,\r\n        })\r\n      }\r\n    },\r\n    [adSettingDto]\r\n  )\r\n\r\n  const onSubmitEvent = async () => {\r\n    try {\r\n      await putApiActiveDirectorySettings({\r\n        body: adSettingDto,\r\n      } as PutApiActiveDirectorySettingsData)\r\n      toast({\r\n        title: 'Success',\r\n        description: 'Active Directory settings updated successfully',\r\n        variant: 'default',\r\n      })\r\n      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetActiveDirectory] })\r\n    } catch (err: unknown) {\r\n      if (err instanceof Error) {\r\n        toast({\r\n          title: 'Failed',\r\n          description: \"Active Directory settings update wasn't successful.\",\r\n          variant: 'destructive',\r\n        })\r\n      }\r\n    }\r\n  }\r\n\r\n  const testConnection = async () => {\r\n    try {\r\n      await postApiActiveDirectorySettingsTestConnection({\r\n        body: adSettingDto,\r\n      })\r\n      toast({\r\n        title: 'Success',\r\n        description: 'Connection to Active Directory successful',\r\n        variant: 'default',\r\n      })\r\n    } catch (err: unknown) {\r\n      if (err instanceof Error) {\r\n        toast({\r\n          title: 'Failed',\r\n          description: \"Connection to Active Directory failed\",\r\n          variant: 'destructive',\r\n        })\r\n      }\r\n    }\r\n  }\r\n\r\n  return (\r\n    <section className=\"p-5 xl:p-10\">\r\n      <h3 className=\"font-medium text-xl\">Active Directory</h3>\r\n      <hr className=\"mt-2 border\" />\r\n      <div className=\"pt-5\">\r\n        <form onSubmit={handleSubmit(onSubmitEvent)}>\r\n          <div className=\"mb-5 space-y-5\">\r\n            <FormField\r\n              label=\"Enable Active Directory\"\r\n              description=\"\"\r\n            >\r\n              <div className={cn('flex items-center space-x-2')}>\r\n                <Checkbox\r\n                  id=\"enabled\"\r\n                  checked={adSettingDto?.enabled}\r\n                  onCheckedChange={(checked) => onCheckboxChange('enabled', !!checked)}\r\n                />\r\n                <label htmlFor=\"enabled\" className=\"font-medium leading-none text-sm\">\r\n                  Enable Active Directory\r\n                </label>\r\n              </div>\r\n            </FormField>\r\n\r\n            <FormField\r\n              label=\"Domain\"\r\n              description=\"\"\r\n            >\r\n              <Input\r\n                type=\"text\"\r\n                {...register('domain')}\r\n                required\r\n                placeholder=\"Domain\"\r\n                value={adSettingDto?.domain ?? ''}\r\n                onChange={onChangeEvent}\r\n              />\r\n            </FormField>\r\n\r\n            <FormField\r\n              label=\"LDAP Server\"\r\n              description=\"\"\r\n            >\r\n              <Input\r\n                type=\"text\"\r\n                {...register('ldapServer')}\r\n                required\r\n                placeholder=\"LDAP Server\"\r\n                value={adSettingDto?.ldapServer ?? ''}\r\n                onChange={onChangeEvent}\r\n              />\r\n            </FormField>\r\n\r\n            <FormField\r\n              label=\"Base DN\"\r\n              description=\"\"\r\n            >\r\n              <Input\r\n                type=\"text\"\r\n                {...register('baseDn')}\r\n                placeholder=\"Base DN\"\r\n                value={adSettingDto?.baseDn ?? ''}\r\n                onChange={onChangeEvent}\r\n              />\r\n            </FormField>\r\n\r\n\r\n            <FormField\r\n              label=\"Username\"\r\n              description=\"\"\r\n            >\r\n              <Input\r\n                type=\"text\"\r\n                {...register('username')}\r\n                required\r\n                placeholder=\"Username\"\r\n                value={adSettingDto?.username ?? ''}\r\n                onChange={onChangeEvent}\r\n              />\r\n            </FormField>\r\n\r\n            <FormField\r\n              label=\"Password\"\r\n              description=\"\"\r\n            >\r\n              <div className=\"relative\">\r\n                <Input\r\n                  id={id}\r\n                  type={isVisible ? \"text\" : \"password\"}\r\n                  {...register('password')}\r\n                  required\r\n                  placeholder=\"Password\"\r\n                  className=\"pe-9\"\r\n                  value={adSettingDto?.password ?? ''}\r\n                  onChange={onChangeEvent}\r\n                />\r\n                <button\r\n                  className=\"text-muted-foreground/80 hover:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 absolute inset-y-0 end-0 flex h-full w-9 items-center justify-center rounded-e-md transition-[color,box-shadow] outline-none focus:z-10 focus-visible:ring-[3px] disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50\"\r\n                  type=\"button\"\r\n                  onClick={toggleVisibility}\r\n                  aria-label={isVisible ? \"Hide password\" : \"Show password\"}\r\n                  aria-pressed={isVisible}\r\n                  aria-controls=\"password\"\r\n                >\r\n                  {isVisible ? (\r\n                    <EyeOffIcon size={16} aria-hidden=\"true\" />\r\n                  ) : (\r\n                    <EyeIcon size={16} aria-hidden=\"true\" />\r\n                  )}\r\n                </button>\r\n              </div>\r\n            </FormField>\r\n\r\n\r\n            <FormField\r\n              label=\"port\"\r\n              description=\"\"\r\n            >\r\n              <Input\r\n                type=\"text\"\r\n                {...register('port')}\r\n                required\r\n                placeholder=\"Port\"\r\n                value={adSettingDto?.port ?? ''}\r\n                onChange={onChangeEvent}\r\n              />\r\n            </FormField>\r\n\r\n            <FormField\r\n              label=\"useSsl\"\r\n              description=\"\"\r\n            >\r\n              <div className={cn('flex items-center space-x-2')}>\r\n                <Checkbox\r\n                  id=\"useSsl\"\r\n                  checked={adSettingDto?.useSsl}\r\n                  onCheckedChange={(checked) => onCheckboxChange('useSsl', !!checked)}\r\n                />\r\n                <label htmlFor=\"useSsl\" className=\"font-medium leading-none text-sm\">\r\n                  Use SSL\r\n                </label>\r\n              </div>\r\n            </FormField>\r\n\r\n            <FormField\r\n              label=\"autoLogin\"\r\n              description=\"\"\r\n            >\r\n              <div className={cn('flex items-center space-x-2')}>\r\n                <Checkbox\r\n                  id=\"autoLogin\"\r\n                  checked={adSettingDto?.autoLogin}\r\n                  onCheckedChange={(checked) => onCheckboxChange('autoLogin', !!checked)}\r\n                />\r\n                <label htmlFor=\"autoLogin\" className=\"font-medium leading-none text-sm\">\r\n                  Auto Login\r\n                </label>\r\n              </div>\r\n            </FormField>\r\n\r\n            <FormField\r\n              label=\"Token Secret\"\r\n              description=\"\"\r\n            >\r\n              <Input\r\n                type=\"text\"\r\n                {...register('tokenSecret')}\r\n                placeholder=\"Token Secret\"\r\n                value={adSettingDto?.tokenSecret ?? ''}\r\n                onChange={onChangeEvent}\r\n              />\r\n            </FormField>\r\n\r\n            <FormField\r\n              label=\"Default Username\"\r\n              description=\"\"\r\n            >\r\n              <Input\r\n                type=\"text\"\r\n                {...register('defaultUsername')}\r\n                placeholder=\"Default Username\"\r\n                value={adSettingDto?.defaultUsername ?? ''}\r\n                onChange={onChangeEvent}\r\n              />\r\n            </FormField>\r\n\r\n            <FormField\r\n              label=\" Enable Windows Authentication\"\r\n              description=\"\"\r\n            >\r\n              <div className={cn('flex items-center space-x-2')}>\r\n                <Checkbox\r\n                  id=\"windowsAuthEnabled\"\r\n                  checked={adSettingDto?.windowsAuthEnabled}\r\n                  onCheckedChange={(checked) => onCheckboxChange('windowsAuthEnabled', !!checked)}\r\n                />\r\n                <label htmlFor=\"windowsAuthEnabled\" className=\"text-sm font-medium leading-none\">\r\n                  Enable Windows Authentication\r\n                </label>\r\n              </div>\r\n            </FormField>\r\n          </div>\r\n          <div className=\"w-full space-x-5\">\r\n            <Button type=\"submit\">Save</Button>\r\n            <Button\r\n              type=\"button\"\r\n              variant=\"outline\"\r\n              onClick={(e) => {\r\n                e.preventDefault()\r\n                void testConnection()\r\n              }}\r\n            >\r\n              Test Connection\r\n            </Button>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </section>\r\n  )\r\n}\r\n", "\"use client\"\r\n\r\nimport * as React from \"react\";\r\nimport { useState } from \"react\";\r\nimport { Sidebar, SidebarContent, SidebarGroup, SidebarGroupContent, SidebarMenu, SidebarMenuButton, SidebarMenuItem, SidebarProvider } from \"@/components/ui/sidebar\";\r\nimport { Lock, Mail, ToggleRight, Server } from \"lucide-react\";\r\nimport { Emailing } from \"./Emailing\";\r\nimport { FeatureManagement } from \"./FeatureManagement\";\r\nimport { ActiveDirectory } from \"./ActiveDirectory\";\r\nimport { PolicyGuard } from \"@/components/auth/policy-guard\";\r\nimport type { Policy } from \"@/lib/hooks/useGrantedPolicies\";\r\nimport { useGrantedPolicies } from \"@/lib/hooks/useGrantedPolicies\";\r\n\r\n// Define the navigation items with their components\r\nconst data = {\r\n  nav: [\r\n    {\r\n      name: \"Emailing\",\r\n      icon: Mail,\r\n      content: Emailing,\r\n      policy: \"SettingManagement.Emailing\" as Policy\r\n    },\r\n    {\r\n      name: \"Feature management\",\r\n      icon: ToggleRight,\r\n      content: FeatureManagement,\r\n      policy: \"FeatureManagement.ManageHostFeatures\" as Policy\r\n    },\r\n    {\r\n      name: \"Active Directory\",\r\n      icon: Server,\r\n      content: ActiveDirectory,\r\n      policy: \"IdentityServer.ActiveDirectorySettings\" as Policy\r\n    },\r\n    {\r\n      name: \"Privacy & visibility\",\r\n      icon: Lock,\r\n      policy: \"AbpIdentity.Users\" as Policy,\r\n      content: () => (\r\n        <section className=\"privacy p-5 xl:p-10\">\r\n          <h1 className=\"font-medium text-xl\">Privacy & Visibility</h1>\r\n          <hr className=\"mt-2 border\" />\r\n          <div className=\"pt-5\">\r\n            <article className=\"mb-5 text-base-content\">\r\n              <p>Configure privacy and visibility settings here.</p>\r\n            </article>\r\n          </div>\r\n        </section>\r\n      )\r\n    },\r\n  ],\r\n}\r\n\r\nexport default function SettingSidebar() {\r\n  const { can } = useGrantedPolicies();\r\n\r\n  // Filter navigation items based on user permissions\r\n  const authorizedNavItems = data.nav.filter(item => can(item.policy));\r\n\r\n  // State to track the selected menu item\r\n  const [selectedItem, setSelectedItem] = useState<string>(\r\n    authorizedNavItems.length > 0 && authorizedNavItems[0]?.name ? authorizedNavItems[0].name : \"\"\r\n  );\r\n\r\n  // Find the selected component to render\r\n  const selectedNavItem = authorizedNavItems.length > 0\r\n    ? (authorizedNavItems.find(item => item.name === selectedItem) || authorizedNavItems[0])\r\n    : undefined;\r\n\r\n  // If no authorized items, show a message\r\n  if (authorizedNavItems.length === 0) {\r\n    return (\r\n      <div className=\"flex items-center justify-center\">\r\n        <p className=\"text-muted-foreground\">You don't have permission to access any settings.</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <SidebarProvider className=\"items-start\">\r\n      <Sidebar collapsible=\"none\" className=\"hidden md:flex\">\r\n        <SidebarContent>\r\n          <SidebarGroup>\r\n            <SidebarGroupContent>\r\n              <SidebarMenu>\r\n                {authorizedNavItems.map((item) => (\r\n                  <SidebarMenuItem key={item.name}>\r\n                    <SidebarMenuButton\r\n                      asChild\r\n                      isActive={item.name === selectedItem}\r\n                      onClick={() => setSelectedItem(item.name)}\r\n                    >\r\n                      <button type=\"button\">\r\n                        <item.icon />\r\n                        <span>{item.name}</span>\r\n                      </button>\r\n                    </SidebarMenuButton>\r\n                  </SidebarMenuItem>\r\n                ))}\r\n              </SidebarMenu>\r\n            </SidebarGroupContent>\r\n          </SidebarGroup>\r\n        </SidebarContent>\r\n      </Sidebar>\r\n\r\n      <main className=\"flex flex-1 flex-col overflow-hidden\">\r\n        <div className=\"flex flex-1 flex-col overflow-y-auto\">\r\n          {/* Render the selected component with policy guard */}\r\n          {selectedNavItem && (\r\n            <PolicyGuard\r\n              policy={selectedNavItem.policy}\r\n              message={`You don't have permission to access ${selectedNavItem.name} settings.`}\r\n            >\r\n              {typeof selectedNavItem.content === 'function'\r\n                ? <selectedNavItem.content />\r\n                : React.createElement(selectedNavItem.content)}\r\n            </PolicyGuard>\r\n          )}\r\n        </div>\r\n      </main>\r\n    </SidebarProvider>\r\n  )\r\n}", "'use client';\r\n\r\nimport SettingSidebar from '@/components/app/settings/SettingSidebar';\r\nimport AppLayout from '@/layouts/app-layout';\r\nimport { Head } from '@inertiajs/react';\r\nexport default function OverViewLayout() {\r\n  return (\r\n    <AppLayout>\r\n      <Head title=\"Dashboard\" />\r\n\r\n      <SettingSidebar />\r\n    </AppLayout>\r\n  );\r\n}\r\n"], "names": ["__iconNode", "Eye<PERSON>ff", "createLucideIcon", "Eye", "Mail", "Server", "ToggleRight", "useEmailing", "useQuery", "QueryNames", "data", "getApiSettingManagementEmailing", "TestEmail", "on<PERSON><PERSON><PERSON>", "open", "<PERSON><PERSON><PERSON>", "useState", "toast", "useToast", "handleSubmit", "register", "useForm", "queryClient", "useQueryClient", "useEffect", "onSubmit", "postApiSettingManagementEmailingSendTestEmail", "onCloseEvent", "err", "Dialog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsx", "DialogHeader", "DialogTitle", "jsxs", "Input", "Textarea", "<PERSON><PERSON><PERSON><PERSON>er", "<PERSON><PERSON>", "e", "Emailing", "openTestEmail", "setOpenTestEmail", "emailSettingDto", "setEmailSettingDto", "onChangeEvent", "useCallback", "value", "name", "onSubmitEvent", "postApiSettingManagementEmailing", "clsx", "Checkbox", "checked", "Fragment", "Features", "FeatureManagement", "showFeatureDialog", "setShowFeatureDialog", "useActiveDirectory", "getApiActiveDirectorySettings", "ActiveDirectory", "adSettingDto", "setAdSettingDto", "id", "useId", "isVisible", "setIsVisible", "toggleVisibility", "prevState", "onCheckboxChange", "putApiActiveDirectorySettings", "testConnection", "postApiActiveDirectorySettingsTestConnection", "FormField", "cn", "EyeOffIcon", "EyeIcon", "Lock", "SettingSidebar", "can", "useGrantedPolicies", "authorizedNavItems", "item", "selectedItem", "setSelectedItem", "selectedNavItem", "SidebarProvider", "Sidebar", "<PERSON>bar<PERSON><PERSON>nt", "SidebarGroup", "SidebarGroupContent", "SidebarMenu", "SidebarMenuItem", "SidebarMenuButton", "PolicyGuard", "React.createElement", "OverViewLayout", "AppLayout", "Head"], "mappings": "0nBAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CACE,OACA,CACE,EAAG,iGACH,IAAK,QACX,CACG,EACD,CAAC,OAAQ,CAAE,EAAG,uCAAwC,IAAK,QAAQ,CAAE,EACrE,CACE,OACA,CACE,EAAG,+FACH,IAAK,QACX,CACG,EACD,CAAC,OAAQ,CAAE,EAAG,aAAc,IAAK,QAAU,CAAA,CAC7C,EACMC,GAASC,EAAiB,UAAWF,EAAU,EC3BrD;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CACE,OACA,CACE,EAAG,wGACH,IAAK,QACX,CACG,EACD,CAAC,SAAU,CAAE,GAAI,KAAM,GAAI,KAAM,EAAG,IAAK,IAAK,QAAU,CAAA,CAC1D,EACMG,GAAMD,EAAiB,MAAOF,EAAU,ECnB9C;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,0CAA2C,IAAK,QAAQ,CAAE,EACxE,CAAC,OAAQ,CAAE,EAAG,IAAK,EAAG,IAAK,MAAO,KAAM,OAAQ,KAAM,GAAI,IAAK,IAAK,QAAU,CAAA,CAChF,EACMI,GAAOF,EAAiB,OAAQF,EAAU,ECbhD;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,MAAO,KAAM,OAAQ,IAAK,EAAG,IAAK,EAAG,IAAK,GAAI,IAAK,GAAI,IAAK,IAAK,SAAU,EACtF,CAAC,OAAQ,CAAE,MAAO,KAAM,OAAQ,IAAK,EAAG,IAAK,EAAG,KAAM,GAAI,IAAK,GAAI,IAAK,IAAK,SAAU,EACvF,CAAC,OAAQ,CAAE,GAAI,IAAK,GAAI,OAAQ,GAAI,IAAK,GAAI,IAAK,IAAK,QAAQ,CAAE,EACjE,CAAC,OAAQ,CAAE,GAAI,IAAK,GAAI,OAAQ,GAAI,KAAM,GAAI,KAAM,IAAK,QAAU,CAAA,CACrE,EACMK,GAASH,EAAiB,SAAUF,EAAU,ECfpD;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,SAAU,CAAE,GAAI,KAAM,GAAI,KAAM,EAAG,IAAK,IAAK,SAAU,EACxD,CAAC,OAAQ,CAAE,MAAO,KAAM,OAAQ,KAAM,EAAG,IAAK,EAAG,IAAK,GAAI,IAAK,IAAK,QAAU,CAAA,CAChF,EACMM,GAAcJ,EAAiB,eAAgBF,EAAU,ECDlDO,GAAc,IAClBC,EAAS,CACd,SAAU,CAACC,EAAW,WAAW,EACjC,QAAS,SAAY,CACnB,KAAM,CAAE,KAAAC,GAAS,MAAMC,EAAgC,EAChD,OAAAD,CAAA,CACT,CACD,ECHUE,GAAY,CAAC,CAAE,UAAAC,KAAgC,CAE1D,KAAM,CAACC,EAAMC,CAAO,EAAIC,EAAAA,SAAS,EAAK,EAEhC,CAAE,MAAAC,CAAM,EAAIC,EAAS,EACrB,CAAE,aAAAC,EAAc,SAAAC,CAAS,EAAIC,EAAQ,EACrCC,EAAcC,EAAe,EAEnCC,EAAAA,UAAU,IAAM,CACdT,EAAQ,EAAI,CACd,EAAG,EAAE,EAEC,MAAAU,EAAW,MAAOf,GAAkB,CACpC,GAAA,CAEF,MAAMgB,EAA8C,CAAE,KADtChB,EACqD,EAC/DO,EAAA,CACJ,MAAO,UACP,YAAa,wCACb,QAAS,SAAA,CACV,EACIK,EAAY,kBAAkB,CAAE,SAAU,CAACb,EAAW,QAAQ,EAAG,EACzDkB,EAAA,QACNC,EAAc,CACjBA,aAAe,OACXX,EAAA,CACJ,MAAO,SACP,YAAa,gCACb,QAAS,aAAA,CACV,CACH,CAEJ,EAEMU,EAAe,IAAM,CACzBZ,EAAQ,EAAK,EACHF,EAAA,CACZ,EAEA,aACGgB,EAAO,CAAA,KAAAf,EAAY,aAAca,EAChC,gBAACG,EACC,CAAA,SAAA,CAAAC,MAACC,EACC,CAAA,SAAAD,EAAA,IAACE,EAAY,CAAA,SAAA,iBAAe,CAAA,EAC9B,EACCC,EAAA,KAAA,OAAA,CAAK,SAAUf,EAAaM,CAAQ,EACnC,SAAA,CAACS,EAAAA,KAAA,UAAA,CAAQ,UAAU,iCACjB,SAAA,CAAAH,EAAA,IAACI,EAAA,CACC,SAAQ,GACP,GAAGf,EAAS,oBAAoB,EACjC,YAAY,sBAAA,CACd,EACAW,EAAA,IAACI,EAAA,CACC,SAAQ,GACP,GAAGf,EAAS,oBAAoB,EACjC,YAAY,sBAAA,CACd,EACAW,MAACI,GAAM,SAAQ,GAAE,GAAGf,EAAS,SAAS,EAAG,YAAY,UAAU,QAC9DgB,GAAS,CAAA,YAAY,OAAQ,GAAGhB,EAAS,MAAM,CAAG,CAAA,CAAA,EACrD,EACAc,EAAAA,KAACG,EAAa,CAAA,UAAU,OACtB,SAAA,CAACN,EAAA,IAAAO,EAAA,CAAO,KAAK,SAAS,SAAI,OAAA,EAC1BP,EAAA,IAACO,EAAA,CACC,QAAUC,GAAM,CACdA,EAAE,eAAe,EACJZ,EAAA,CACf,EACD,SAAA,OAAA,CAAA,CAED,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,EC5Eaa,GAAW,IAAM,CACtB,KAAA,CAAE,MAAAvB,CAAM,EAAIC,EAAS,EACrB,CAAE,KAAAR,CAAK,EAAIH,GAAY,EACvBe,EAAcC,EAAe,EAC7B,CAACkB,EAAeC,CAAgB,EAAI1B,EAAAA,SAAkB,EAAK,EAC3D,CAAE,aAAAG,EAAc,SAAAC,CAAS,EAAIC,EAAQ,EAErC,CAACsB,EAAiBC,CAAkB,EAAI5B,EAAAA,SAAuCN,CAAI,EAEzFc,EAAAA,UAAU,IAAM,CACVd,GACiBkC,EAAA,CAAE,GAAGlC,EAAM,CAChC,EACC,CAACA,CAAI,CAAC,EAET,MAAMmC,EAAgBC,EAAA,YACnBP,GAAsB,CACrB,KAAM,CAAE,MAAAQ,EAAO,KAAAC,CAAK,EAAIT,EAAE,OAE1BK,EAAmB,CAAE,GAAGD,EAAiB,CAACK,CAAI,EAAGD,EAAO,CAC1D,EACA,CAACJ,CAAe,CAClB,EAEMM,EAAgB,SAAY,CAC5B,GAAA,CACF,MAAMC,EAAiC,CACrC,KAAMP,CAAA,CACiC,EACnC1B,EAAA,CACJ,MAAO,UACP,YAAa,sCACb,QAAS,SAAA,CACV,EACIK,EAAY,kBAAkB,CAAE,SAAU,CAACb,EAAW,WAAW,EAAG,QAClEmB,EAAc,CACjBA,aAAe,OACXX,EAAA,CACJ,MAAO,SACP,YAAa,qCACb,QAAS,aAAA,CACV,CACH,CAEJ,EAGE,OAAAiB,EAAA,KAAC,UAAQ,CAAA,UAAU,uBAChB,SAAA,CAAAO,SAAkB7B,GAAU,CAAA,UAAW,IAAM8B,EAAiB,EAAK,EAAG,EACtEX,EAAA,IAAA,KAAA,CAAG,UAAU,sBAAsB,SAAQ,WAAA,EAC5CA,EAAAA,IAAC,KAAG,CAAA,UAAU,aAAc,CAAA,EAC5BA,EAAAA,IAAC,OAAI,UAAU,OACb,gBAAC,OAAK,CAAA,SAAUZ,EAAa8B,CAAa,EACxC,SAAA,CAACf,EAAAA,KAAA,MAAA,CAAI,UAAU,iBACb,SAAA,CAAAH,EAAA,IAACI,EAAA,CACC,KAAK,OACJ,GAAGf,EAAS,wBAAwB,EACrC,SAAQ,GACR,YAAY,4BACZ,MAAOuB,GAAiB,wBAA0B,GAClD,SAAUE,CAAA,CACZ,EACAd,EAAA,IAACI,EAAA,CACC,KAAK,QACJ,GAAGf,EAAS,oBAAoB,EACjC,SAAQ,GACR,YAAY,uBACZ,MAAOuB,GAAiB,oBAAsB,GAC9C,SAAUE,CAAA,CACZ,EACAd,EAAA,IAACI,EAAA,CACC,KAAK,OACJ,GAAGf,EAAS,UAAU,EACvB,YAAY,OACZ,MAAOuB,GAAiB,UAAY,GACpC,SAAUE,CAAA,CACZ,EAEAd,EAAA,IAACI,EAAA,CACC,KAAK,SACJ,GAAGf,EAAS,UAAU,EACvB,SAAQ,GACR,YAAY,OACZ,MAAOuB,GAAiB,UAAY,EACpC,SAAUE,CAAA,CACZ,EAECX,EAAA,KAAA,MAAA,CAAI,UAAWiB,EAAK,6BAA6B,EAChD,SAAA,CAAApB,EAAA,IAACqB,EAAA,CACC,GAAG,MACF,GAAGhC,EAAS,eAAe,EAC5B,QAASuB,GAAiB,cAC1B,gBAAkBU,GAAY,CACTT,EAAA,CACjB,GAAGD,EACH,cAAe,CAAC,CAACU,EAAQ,QAAQ,CAAA,CAClC,CAAA,CACH,CACF,QACC,QAAM,CAAA,QAAQ,MAAM,UAAU,mCAAmC,SAElE,YAAA,CAAA,CAAA,EACF,EAECnB,EAAA,KAAA,MAAA,CAAI,UAAWiB,EAAK,6BAA6B,EAChD,SAAA,CAAApB,EAAA,IAACqB,EAAA,CACC,GAAG,cACH,KAAK,4BACL,QAAST,GAAiB,0BAC1B,gBAAkBU,GAChBT,EAAmB,CACjB,GAAGD,EACH,0BAA2B,CAAC,CAACU,EAAQ,QAAQ,CAC9C,CAAA,CAAA,CAEL,QACC,QAAM,CAAA,QAAQ,cAAc,UAAU,mCAAmC,SAE1E,yBAAA,CAAA,CAAA,EACF,EACC,CAACV,GAAiB,2BAEfT,EAAA,KAAAoB,EAAA,SAAA,CAAA,SAAA,CAAAvB,EAAA,IAACI,EAAA,CACC,KAAK,OACJ,GAAGf,EAAS,YAAY,EACzB,YAAY,SACZ,MAAOuB,GAAiB,YAAc,GACtC,SAAUE,CAAA,CACZ,EAEAd,EAAA,IAACI,EAAA,CACC,KAAK,OACJ,GAAGf,EAAS,cAAc,EAC3B,YAAY,YACZ,MAAOuB,GAAiB,cAAgB,GACxC,SAAUE,CAAA,CACZ,EAEAd,EAAA,IAACI,EAAA,CACC,KAAK,WACJ,GAAGf,EAAS,cAAc,EAC3B,YAAY,WACZ,MAAOuB,GAAiB,cAAgB,GACxC,SAAUE,CAAA,CAAA,CACZ,CACF,CAAA,CAAA,EAEJ,EACAX,EAAAA,KAAC,MAAI,CAAA,UAAU,6BACb,SAAA,CAACH,EAAA,IAAAO,EAAA,CAAO,KAAK,SAAS,SAAI,OAAA,EAC1BP,EAAA,IAACO,EAAA,CACC,QAAQ,UACR,QAAUC,GAAsC,CAC9CA,EAAE,eAAe,EACjBG,EAAiB,EAAI,CACvB,EACD,SAAA,iBAAA,CAAA,CAED,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,EACF,CAEJ,EC5Kaa,GAAW,CAAC,CAAE,UAAA1C,KAA+B,CACxD,KAAM,CAACC,EAAMC,CAAO,EAAIC,EAAAA,SAAS,EAAK,EAChCW,EAAe,IAAM,CACzBZ,EAAQ,EAAK,EACHF,EAAA,CACZ,EAEAW,OAAAA,EAAAA,UAAU,IAAM,CACdT,EAAQ,EAAI,CACd,EAAG,EAAE,QAGFc,EAAO,CAAA,KAAAf,EAAY,aAAca,EAChC,gBAACG,EACC,CAAA,SAAA,CAAAC,EAAAA,IAACE,GAAY,SAAQ,UAAA,CAAA,EACpBF,MAAA,UAAA,CACC,SAACA,EAAA,IAAA,IAAA,CAAE,6CAAuC,CAAA,EAC5C,EACAG,EAAAA,KAACG,EAAa,CAAA,UAAU,OACtB,SAAA,CAAAN,EAAA,IAACO,EAAA,CACC,QAAUC,GAAM,CACdA,EAAE,eAAe,EACJZ,EAAA,CACf,EACD,SAAA,QAAA,CAED,EACCI,EAAA,IAAAO,EAAA,CAAO,KAAK,SAAS,SAAI,MAAA,CAAA,CAAA,CAC5B,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,EClCakB,GAAoB,IAAM,CACrC,KAAM,CAACC,EAAmBC,CAAoB,EAAI1C,EAAAA,SAAS,EAAK,EAG9D,OAAAkB,EAAA,KAAC,UAAQ,CAAA,UAAU,iCAChB,SAAA,CAAAuB,SAAsBF,GAAS,CAAA,UAAW,IAAMG,EAAqB,EAAK,EAAG,EAC7E3B,EAAA,IAAA,KAAA,CAAG,UAAU,sBAAsB,SAAkB,qBAAA,EACtDA,EAAAA,IAAC,KAAG,CAAA,UAAU,aAAc,CAAA,EAC5BG,EAAAA,KAAC,MAAI,CAAA,UAAU,OACb,SAAA,CAAAH,EAAAA,IAAC,WAAQ,UAAU,yBACjB,SAACA,MAAA,IAAA,CAAE,mFAAuE,CAC5E,CAAA,QACCO,EAAO,CAAA,QAAS,IAAMoB,EAAqB,EAAI,EAAG,SAAoB,sBAAA,CAAA,CAAA,CACzE,CAAA,CAAA,EACF,CAEJ,ECTaC,GAAqB,IACzBnD,EAAS,CACd,SAAU,CAACC,EAAW,kBAAkB,EACxC,QAAS,SAAY,CACnB,KAAM,CAAE,KAAAC,GAAS,MAAMkD,EAA8B,EAC9C,OAAAlD,CAAA,CACT,CACD,ECFUmD,GAAkB,IAAM,CAC7B,KAAA,CAAE,MAAA5C,CAAM,EAAIC,EAAS,EACrB,CAAE,KAAAR,CAAK,EAAIiD,GAAmB,EAC9BrC,EAAcC,EAAe,EAC7B,CAAE,aAAAJ,EAAc,SAAAC,CAAS,EAAIC,EAAQ,EAErC,CAACyC,EAAcC,CAAe,EAAI/C,EAAAA,SAAiDN,CAAI,EACvFsD,EAAKC,EAAAA,MAAM,EACX,CAACC,EAAWC,CAAY,EAAInD,EAAAA,SAAkB,EAAK,EAEnDoD,EAAmB,IAAMD,EAAcE,GAAc,CAACA,CAAS,EAErE7C,EAAAA,UAAU,IAAM,CACVd,GACcqD,EAAA,CAAE,GAAGrD,EAAM,CAC7B,EACC,CAACA,CAAI,CAAC,EAET,MAAMmC,EAAgBC,EAAA,YACnBP,GAAsB,CACrB,KAAM,CAAE,MAAAQ,EAAO,KAAAC,CAAK,EAAIT,EAAE,OAEtBuB,GACFC,EAAgB,CAAE,GAAGD,EAAc,CAACd,CAAI,EAAGD,EAAO,CAEtD,EACA,CAACe,CAAY,CACf,EAEMQ,EAAmBxB,EAAA,YACvB,CAACE,EAAcK,IAAqB,CAC9BS,GACcC,EAAA,CACd,GAAGD,EACH,CAACd,CAAI,EAAGK,CAAA,CACT,CAEL,EACA,CAACS,CAAY,CACf,EAEMb,EAAgB,SAAY,CAC5B,GAAA,CACF,MAAMsB,EAA8B,CAClC,KAAMT,CAAA,CAC8B,EAChC7C,EAAA,CACJ,MAAO,UACP,YAAa,iDACb,QAAS,SAAA,CACV,EACIK,EAAY,kBAAkB,CAAE,SAAU,CAACb,EAAW,kBAAkB,EAAG,QACzEmB,EAAc,CACjBA,aAAe,OACXX,EAAA,CACJ,MAAO,SACP,YAAa,sDACb,QAAS,aAAA,CACV,CACH,CAEJ,EAEMuD,EAAiB,SAAY,CAC7B,GAAA,CACF,MAAMC,EAA6C,CACjD,KAAMX,CAAA,CACP,EACK7C,EAAA,CACJ,MAAO,UACP,YAAa,4CACb,QAAS,SAAA,CACV,QACMW,EAAc,CACjBA,aAAe,OACXX,EAAA,CACJ,MAAO,SACP,YAAa,wCACb,QAAS,aAAA,CACV,CACH,CAEJ,EAGE,OAAAiB,EAAA,KAAC,UAAQ,CAAA,UAAU,cACjB,SAAA,CAACH,EAAA,IAAA,KAAA,CAAG,UAAU,sBAAsB,SAAgB,mBAAA,EACpDA,EAAAA,IAAC,KAAG,CAAA,UAAU,aAAc,CAAA,EAC5BA,EAAAA,IAAC,OAAI,UAAU,OACb,gBAAC,OAAK,CAAA,SAAUZ,EAAa8B,CAAa,EACxC,SAAA,CAACf,EAAAA,KAAA,MAAA,CAAI,UAAU,iBACb,SAAA,CAAAH,EAAA,IAAC2C,EAAA,CACC,MAAM,0BACN,YAAY,GAEZ,SAACxC,EAAA,KAAA,MAAA,CAAI,UAAWyC,EAAG,6BAA6B,EAC9C,SAAA,CAAA5C,EAAA,IAACqB,EAAA,CACC,GAAG,UACH,QAASU,GAAc,QACvB,gBAAkBT,GAAYiB,EAAiB,UAAW,CAAC,CAACjB,CAAO,CAAA,CACrE,QACC,QAAM,CAAA,QAAQ,UAAU,UAAU,mCAAmC,SAEtE,yBAAA,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EAEAtB,EAAA,IAAC2C,EAAA,CACC,MAAM,SACN,YAAY,GAEZ,SAAA3C,EAAA,IAACI,EAAA,CACC,KAAK,OACJ,GAAGf,EAAS,QAAQ,EACrB,SAAQ,GACR,YAAY,SACZ,MAAO0C,GAAc,QAAU,GAC/B,SAAUjB,CAAA,CAAA,CACZ,CACF,EAEAd,EAAA,IAAC2C,EAAA,CACC,MAAM,cACN,YAAY,GAEZ,SAAA3C,EAAA,IAACI,EAAA,CACC,KAAK,OACJ,GAAGf,EAAS,YAAY,EACzB,SAAQ,GACR,YAAY,cACZ,MAAO0C,GAAc,YAAc,GACnC,SAAUjB,CAAA,CAAA,CACZ,CACF,EAEAd,EAAA,IAAC2C,EAAA,CACC,MAAM,UACN,YAAY,GAEZ,SAAA3C,EAAA,IAACI,EAAA,CACC,KAAK,OACJ,GAAGf,EAAS,QAAQ,EACrB,YAAY,UACZ,MAAO0C,GAAc,QAAU,GAC/B,SAAUjB,CAAA,CAAA,CACZ,CACF,EAGAd,EAAA,IAAC2C,EAAA,CACC,MAAM,WACN,YAAY,GAEZ,SAAA3C,EAAA,IAACI,EAAA,CACC,KAAK,OACJ,GAAGf,EAAS,UAAU,EACvB,SAAQ,GACR,YAAY,WACZ,MAAO0C,GAAc,UAAY,GACjC,SAAUjB,CAAA,CAAA,CACZ,CACF,EAEAd,EAAA,IAAC2C,EAAA,CACC,MAAM,WACN,YAAY,GAEZ,SAAAxC,EAAA,KAAC,MAAI,CAAA,UAAU,WACb,SAAA,CAAAH,EAAA,IAACI,EAAA,CACC,GAAA6B,EACA,KAAME,EAAY,OAAS,WAC1B,GAAG9C,EAAS,UAAU,EACvB,SAAQ,GACR,YAAY,WACZ,UAAU,OACV,MAAO0C,GAAc,UAAY,GACjC,SAAUjB,CAAA,CACZ,EACAd,EAAA,IAAC,SAAA,CACC,UAAU,oVACV,KAAK,SACL,QAASqC,EACT,aAAYF,EAAY,gBAAkB,gBAC1C,eAAcA,EACd,gBAAc,WAEb,SACCA,EAAAnC,EAAA,IAAC6C,GAAW,CAAA,KAAM,GAAI,cAAY,MAAO,CAAA,EAExC7C,EAAA,IAAA8C,GAAA,CAAQ,KAAM,GAAI,cAAY,MAAO,CAAA,CAAA,CAAA,CAE1C,CACF,CAAA,CAAA,CACF,EAGA9C,EAAA,IAAC2C,EAAA,CACC,MAAM,OACN,YAAY,GAEZ,SAAA3C,EAAA,IAACI,EAAA,CACC,KAAK,OACJ,GAAGf,EAAS,MAAM,EACnB,SAAQ,GACR,YAAY,OACZ,MAAO0C,GAAc,MAAQ,GAC7B,SAAUjB,CAAA,CAAA,CACZ,CACF,EAEAd,EAAA,IAAC2C,EAAA,CACC,MAAM,SACN,YAAY,GAEZ,SAACxC,EAAA,KAAA,MAAA,CAAI,UAAWyC,EAAG,6BAA6B,EAC9C,SAAA,CAAA5C,EAAA,IAACqB,EAAA,CACC,GAAG,SACH,QAASU,GAAc,OACvB,gBAAkBT,GAAYiB,EAAiB,SAAU,CAAC,CAACjB,CAAO,CAAA,CACpE,QACC,QAAM,CAAA,QAAQ,SAAS,UAAU,mCAAmC,SAErE,SAAA,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EAEAtB,EAAA,IAAC2C,EAAA,CACC,MAAM,YACN,YAAY,GAEZ,SAACxC,EAAA,KAAA,MAAA,CAAI,UAAWyC,EAAG,6BAA6B,EAC9C,SAAA,CAAA5C,EAAA,IAACqB,EAAA,CACC,GAAG,YACH,QAASU,GAAc,UACvB,gBAAkBT,GAAYiB,EAAiB,YAAa,CAAC,CAACjB,CAAO,CAAA,CACvE,QACC,QAAM,CAAA,QAAQ,YAAY,UAAU,mCAAmC,SAExE,YAAA,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EAEAtB,EAAA,IAAC2C,EAAA,CACC,MAAM,eACN,YAAY,GAEZ,SAAA3C,EAAA,IAACI,EAAA,CACC,KAAK,OACJ,GAAGf,EAAS,aAAa,EAC1B,YAAY,eACZ,MAAO0C,GAAc,aAAe,GACpC,SAAUjB,CAAA,CAAA,CACZ,CACF,EAEAd,EAAA,IAAC2C,EAAA,CACC,MAAM,mBACN,YAAY,GAEZ,SAAA3C,EAAA,IAACI,EAAA,CACC,KAAK,OACJ,GAAGf,EAAS,iBAAiB,EAC9B,YAAY,mBACZ,MAAO0C,GAAc,iBAAmB,GACxC,SAAUjB,CAAA,CAAA,CACZ,CACF,EAEAd,EAAA,IAAC2C,EAAA,CACC,MAAM,iCACN,YAAY,GAEZ,SAACxC,EAAA,KAAA,MAAA,CAAI,UAAWyC,EAAG,6BAA6B,EAC9C,SAAA,CAAA5C,EAAA,IAACqB,EAAA,CACC,GAAG,qBACH,QAASU,GAAc,mBACvB,gBAAkBT,GAAYiB,EAAiB,qBAAsB,CAAC,CAACjB,CAAO,CAAA,CAChF,QACC,QAAM,CAAA,QAAQ,qBAAqB,UAAU,mCAAmC,SAEjF,+BAAA,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,EACF,EACAnB,EAAAA,KAAC,MAAI,CAAA,UAAU,mBACb,SAAA,CAACH,EAAA,IAAAO,EAAA,CAAO,KAAK,SAAS,SAAI,OAAA,EAC1BP,EAAA,IAACO,EAAA,CACC,KAAK,SACL,QAAQ,UACR,QAAUC,GAAM,CACdA,EAAE,eAAe,EACZiC,EAAe,CACtB,EACD,SAAA,iBAAA,CAAA,CAED,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,EACF,CAEJ,EChTM9D,GAAO,CACX,IAAK,CACH,CACE,KAAM,WACN,KAAMN,GACN,QAASoC,GACT,OAAQ,4BACV,EACA,CACE,KAAM,qBACN,KAAMlC,GACN,QAASkD,GACT,OAAQ,sCACV,EACA,CACE,KAAM,mBACN,KAAMnD,GACN,QAASwD,GACT,OAAQ,wCACV,EACA,CACE,KAAM,uBACN,KAAMiB,GACN,OAAQ,oBACR,QAAS,IACN5C,EAAAA,KAAA,UAAA,CAAQ,UAAU,sBACjB,SAAA,CAACH,EAAA,IAAA,KAAA,CAAG,UAAU,sBAAsB,SAAoB,uBAAA,EACxDA,EAAAA,IAAC,KAAG,CAAA,UAAU,aAAc,CAAA,EAC3BA,EAAA,IAAA,MAAA,CAAI,UAAU,OACb,SAACA,EAAAA,IAAA,UAAA,CAAQ,UAAU,yBACjB,SAACA,EAAAA,IAAA,IAAA,CAAE,SAA+C,iDAAA,CAAA,CAAA,CACpD,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAEJ,CAEJ,EAEA,SAAwBgD,IAAiB,CACjC,KAAA,CAAE,IAAAC,CAAI,EAAIC,EAAmB,EAG7BC,EAAqBxE,GAAK,IAAI,UAAesE,EAAIG,EAAK,MAAM,CAAC,EAG7D,CAACC,EAAcC,CAAe,EAAIrE,EAAA,SACtCkE,EAAmB,OAAS,GAAKA,EAAmB,CAAC,GAAG,KAAOA,EAAmB,CAAC,EAAE,KAAO,EAC9F,EAGMI,EAAkBJ,EAAmB,OAAS,EAC/CA,EAAmB,KAAaC,GAAAA,EAAK,OAASC,CAAY,GAAKF,EAAmB,CAAC,EACpF,OAGA,OAAAA,EAAmB,SAAW,EAE9BnD,EAAA,IAAC,OAAI,UAAU,mCACb,eAAC,IAAE,CAAA,UAAU,wBAAwB,SAAA,mDAAA,CAAiD,CACxF,CAAA,EAKFG,EAAA,KAACqD,EAAgB,CAAA,UAAU,cACzB,SAAA,CAAAxD,EAAAA,IAACyD,GAAQ,YAAY,OAAO,UAAU,iBACpC,SAAAzD,EAAAA,IAAC0D,GACC,SAAC1D,EAAA,IAAA2D,EAAA,CACC,SAAC3D,EAAA,IAAA4D,EAAA,CACC,eAACC,EACE,CAAA,SAAAV,EAAmB,IAAKC,SACtBU,EACC,CAAA,SAAA9D,EAAA,IAAC+D,EAAA,CACC,QAAO,GACP,SAAUX,EAAK,OAASC,EACxB,QAAS,IAAMC,EAAgBF,EAAK,IAAI,EAExC,SAAAjD,EAAA,KAAC,SAAO,CAAA,KAAK,SACX,SAAA,CAACH,MAAAoD,EAAK,KAAL,EAAU,EACXpD,EAAAA,IAAC,OAAM,CAAA,SAAAoD,EAAK,IAAK,CAAA,CAAA,CACnB,CAAA,CAAA,CAAA,GATkBA,EAAK,IAW3B,CACD,EACH,CACF,CAAA,CACF,CAAA,CACF,CAAA,EACF,EAEApD,EAAAA,IAAC,QAAK,UAAU,uCACd,eAAC,MAAI,CAAA,UAAU,uCAEZ,SACCuD,GAAAvD,EAAA,IAACgE,EAAA,CACC,OAAQT,EAAgB,OACxB,QAAS,uCAAuCA,EAAgB,IAAI,aAEnE,SAAO,OAAAA,EAAgB,SAAY,WAC/BvD,EAAA,IAAAuD,EAAgB,QAAhB,CAAwB,CAAA,EACzBU,EAAoB,cAAAV,EAAgB,OAAO,CAAA,GAGrD,CACF,CAAA,CAAA,EACF,CAEJ,CCrHA,SAAwBW,IAAiB,CACvC,cACGC,EACC,CAAA,SAAA,CAACnE,EAAAA,IAAAoE,GAAA,CAAK,MAAM,WAAY,CAAA,QAEvBpB,GAAe,CAAA,CAAA,CAAA,EAClB,CAEJ", "x_google_ignoreList": [0, 1, 2, 3, 4]}