import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuCheckboxItem, DropdownMenuContent, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { IconChevronDown } from '@tabler/icons-react';
import { useCallback, useRef, useState } from 'react';

import DatePickerReact from '@/components/ui/date-picker';
import { DocumentPreviewDialog } from '@/components/ui/DocumentPreviewDialog';
import { FormField, FormSection } from '@/components/ui/FormField';
import AppLayout from '@/layouts/app-layout';
import { HotTable } from '@handsontable/react-wrapper';
import Handsontable from 'handsontable';
import { registerAllModules } from 'handsontable/registry';
import 'handsontable/styles/handsontable.min.css';
import 'handsontable/styles/ht-theme-main.min.css';

registerAllModules();

const initialData = Array(10).fill(null).map((_, i) => ({
  tenant: i % 2 === 0 ? 'ITSS' : 'IRNC',
  itemName: 'STEEL PRODUCT',
  quantity: '36.399',
  uom: 'MT',
  remark: 'Remark 1',
  status: i % 3 === 0 ? 'Data' : 'Draft',
  action: '',
}));

const columns = [
  { data: 'tenant', type: 'text', title: 'Tenant' },
  { data: 'itemName', type: 'text', title: 'Item Name' },
  { data: 'quantity', type: 'numeric', title: 'Quantity' },
  { data: 'uom', type: 'text', title: 'UoM' },
  { data: 'remark', type: 'text', title: 'Remark' },
  { data: 'status', type: 'text', title: 'Status', readOnly: true },
  { data: 'action', title: 'Action', readOnly: true },
];

interface Vessel {
  docNum: string;
  vesselName: string;
  voyage: string;
  arrival: string;
  departure: string;
}

const vesselData: Vessel[] = Array(10).fill(null).map((_, i) => ({
  docNum: `2505000${i + 1}`,
  vesselName: 'MV. ORIENTAL LUNA',
  voyage: `00${i + 1}`,
  arrival: '2025-05-01',
  departure: '2025-05-02',
}));

export default function CreateApplication() {
  const [docNum, setDocNum] = useState('25060001');
  const [vesselType, setVesselType] = useState('');
  const [vessel, setVessel] = useState('');
  const [voyage, setVoyage] = useState('001');
  const [jetty, setJetty] = useState('');
  const [tableData] = useState(initialData);

  const [isVesselDialogOpen, setIsVesselDialogOpen] = useState(false);
  const [selectedVesselRows, setSelectedVesselRows] = useState<Set<string>>(new Set());
  const [vesselFilter, setVesselFilter] = useState('');
  const [visibleColumns, setVisibleColumns] = useState<Set<keyof Vessel>>(new Set(Object.keys(vesselData[0]) as (keyof Vessel)[]));

  const [isPreviewDialogOpen, setIsPreviewDialogOpen] = useState(false);
  const [previewDocumentSrc, setPreviewDocumentSrc] = useState('');

  const hotTableComponent = useRef(null);

  const handleSelectAllVessels = (checked: boolean) => {
    if (checked) {
      setSelectedVesselRows(new Set(vesselData.map(v => v.docNum)));
    } else {
      setSelectedVesselRows(new Set());
    }
  };

  const handleSelectVesselRow = (docNum: string, checked: boolean) => {
    const newSelection = new Set(selectedVesselRows);
    if (checked) {
      newSelection.add(docNum);
    } else {
      newSelection.delete(docNum);
    }
    setSelectedVesselRows(newSelection);
  };

  const handleVesselSelectConfirm = () => {
    if (selectedVesselRows.size > 0) {
      const selectedDocNum = Array.from(selectedVesselRows)[0];
      const vessel = vesselData.find(v => v.docNum === selectedDocNum);
      if (vessel) {
        setVessel(vessel.vesselName + ' ' + vessel.voyage);
      }
    } else {
      setVessel('');
    }
    setIsVesselDialogOpen(false);
  };

  const handleToggleColumn = (columnKey: keyof Vessel, checked: boolean) => {
    const newVisibleColumns = new Set(visibleColumns);
    if (checked) {
      newVisibleColumns.add(columnKey);
    } else {
      newVisibleColumns.delete(columnKey);
    }
    setVisibleColumns(newVisibleColumns);
  };

  const filteredVesselData = vesselData.filter(vessel =>
    vessel.docNum.toLowerCase().includes(vesselFilter.toLowerCase()) ||
    vessel.vesselName.toLowerCase().includes(vesselFilter.toLowerCase())
  );

  const handleSave = () => {
    console.log('Save button clicked');
    console.log('Table Data:', tableData);
  };

  const renderActionButtons = useCallback((instance: Handsontable.Core | undefined, td: HTMLTableCellElement, _row: number, _col: number, _prop: string | number, _value: unknown, _cellProperties: Handsontable.CellProperties) => {
    void _col;
    void _prop;
    void _value;
    void _cellProperties;
    const previewButton = `<button class="px-2 py-1 bg-blue-500 text-white rounded-md text-sm hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 mr-2" data-row="${_row}" data-action="preview">Preview</button>`;
    const submitButton = `<button class="px-2 py-1 bg-blue-500 text-white rounded-md text-sm hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">Submit</button>`;
    td.innerHTML = previewButton + submitButton;

    console.log("instance", instance);

    const previewBtn = td.querySelector('[data-action="preview"]');
    if (previewBtn) {
      previewBtn.addEventListener('click', () => {
        // For now, display the provided image.
        setPreviewDocumentSrc('/pdf/surat1.pdf');
        setIsPreviewDialogOpen(true);
      });
    }
  }, []);

  const columnConfig = columns.map(col => {
    if (col.data === 'action') {
      return { ...col, renderer: renderActionButtons };
    }
    return col;
  });

  return (
    <AppLayout>
      <div className="container mx-auto">
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-bold">New Application</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-2">
              <FormSection>
                <FormField label="DocNum">
                  <Input id="docNum" value={docNum} onChange={(e) => setDocNum(e.target.value)} />
                </FormField>
                <FormField label="Vessel Type">
                  <Select value={vesselType} onValueChange={setVesselType}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select Vessel Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="typeA">Type A</SelectItem>
                      <SelectItem value="typeB">Type B</SelectItem>
                    </SelectContent>
                  </Select>
                </FormField>
                <FormField label="Vessel">
                  <Dialog open={isVesselDialogOpen} onOpenChange={setIsVesselDialogOpen}>
                    <DialogTrigger asChild>
                      <Button variant="outline" className="w-full justify-start font-normal">
                        {vessel ? vessel : "Select Vessel"}
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="min-w-[900px] w-auto max-h-[70vh] flex flex-col">
                      <DialogHeader>
                        <DialogTitle>Select Vessel</DialogTitle>
                      </DialogHeader>
                      <div className="flex items-center justify-between mb-4">
                        <Input
                          placeholder="Filter lines..."
                          value={vesselFilter}
                          onChange={(e) => setVesselFilter(e.target.value)}
                          className="max-w-sm"
                        />
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="outline" className="ml-auto">
                              Columns <IconChevronDown className="ml-2 h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            {Object.keys(vesselData[0]).map((key) => (
                              <DropdownMenuCheckboxItem
                                key={key}
                                className="capitalize"
                                checked={visibleColumns.has(key as keyof Vessel)}
                                onCheckedChange={(checked) => handleToggleColumn(key as keyof Vessel, checked === true)}
                              >
                                {key.replace(/([A-Z])/g, ' $1').trim()}
                              </DropdownMenuCheckboxItem>
                            ))}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                      <div className="flex-grow overflow-auto border rounded-md">
                        <Table>
                          <TableHeader className="sticky top-0 bg-white">
                            <TableRow>
                              <TableHead className="w-[30px]">
                                <Checkbox
                                  checked={selectedVesselRows.size === filteredVesselData.length && filteredVesselData.length > 0}
                                  onCheckedChange={(checked) => handleSelectAllVessels(checked === true)}
                                />
                              </TableHead>
                              {Object.keys(vesselData[0]).map((key) => (visibleColumns.has(key as keyof Vessel) &&
                                <TableHead key={key} className="capitalize">
                                  {key.replace(/([A-Z])/g, ' $1').trim()}
                                </TableHead>
                              ))}
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {filteredVesselData.map((vesselItem) => (
                              <TableRow key={vesselItem.docNum} className="cursor-pointer hover:bg-gray-100">
                                <TableCell>
                                  <Checkbox
                                    checked={selectedVesselRows.has(vesselItem.docNum)}
                                    onCheckedChange={(checked) => handleSelectVesselRow(vesselItem.docNum, checked === true)}
                                  />
                                </TableCell>
                                {Object.entries(vesselItem).map(([key, value]) => (visibleColumns.has(key as keyof Vessel) &&
                                  <TableCell key={key}>{value}</TableCell>
                                ))}
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                      <div className="flex items-center justify-between mt-4">
                        <div className="text-sm text-gray-500">
                          {selectedVesselRows.size} of {filteredVesselData.length} row(s) selected.
                        </div>
                        <div className="space-x-2">
                          <Button variant="outline" size="sm">Previous</Button>
                          <Button variant="outline" size="sm">Next</Button>
                        </div>
                        <Button onClick={handleVesselSelectConfirm}>Select Vessel</Button>
                      </div>
                    </DialogContent>
                  </Dialog>
                </FormField>
                <FormField label="Voyage">
                  <Input id="voyage" value={voyage} onChange={(e) => setVoyage(e.target.value)} />
                </FormField>
                <FormField label="Jetty">
                  <Select value={jetty} onValueChange={setJetty}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select Jetty" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="jettyA">Jetty A</SelectItem>
                      <SelectItem value="jettyB">Jetty B</SelectItem>
                    </SelectContent>
                  </Select>
                </FormField>
              </FormSection>

              <FormSection>
                <FormField label="Arrival Date & Time">
                  <div className="flex gap-2">
                    <DatePickerReact />
                  </div>
                </FormField>
                <FormField label="Departure Date & Time">
                  <div className="flex gap-2">
                    <DatePickerReact />
                  </div>
                </FormField>
                <FormField label="A/Side Date & Time">
                  <div className="flex gap-2">
                    <DatePickerReact />
                  </div>
                </FormField>
                <FormField label="Cast Of Date & Time">
                  <div className="flex gap-2">
                    <DatePickerReact />
                  </div>
                </FormField>
                <FormField label="Posting Date & Time">
                  <div className="flex gap-2">
                    <DatePickerReact />
                  </div>
                </FormField>
              </FormSection>
            </div>

            <div className="mb-8">
              <HotTable
                ref={hotTableComponent}
                themeName="ht-theme-main"
                data={tableData}
                columns={columnConfig}
                colHeaders={columns.map(col => col.title)}
                rowHeaders={true}
                height="auto"
                autoWrapRow={true}
                licenseKey="non-commercial-and-evaluation"
                stretchH="all"
                contextMenu={true}
              />
            </div>

            <div className="flex justify-end">
              <Button onClick={handleSave} className="px-6 py-2 bg-black text-white rounded-md hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50">Save</Button>
            </div>
          </CardContent>
        </Card>
      </div>
      <DocumentPreviewDialog
        isOpen={isPreviewDialogOpen}
        onOpenChange={setIsPreviewDialogOpen}
        documentSrc={previewDocumentSrc}
      />
    </AppLayout>
  );
}
