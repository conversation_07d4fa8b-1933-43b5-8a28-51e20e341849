using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.Security.Claims;
using Imip.JettyApproval.Web.Services;

namespace Imip.JettyApproval.Web.Controllers;

[Route("api/token-validation")]
[ApiController]
public class TokenValidationController : AbpController
{
    private readonly ITokenValidationService _tokenValidationService;
    private readonly ILogger<TokenValidationController> _logger;

    public TokenValidationController(
        ITokenValidationService tokenValidationService,
        ILogger<TokenValidationController> logger)
    {
        _tokenValidationService = tokenValidationService;
        _logger = logger;
    }

    /// <summary>
    /// Validates a JWT token from another app and returns user information
    /// </summary>
    [HttpPost("validate")]
    [AllowAnonymous] // This endpoint needs to be accessible without authentication
    public async Task<IActionResult> ValidateToken([FromBody] TokenValidationRequest request)
    {
        try
        {
            if (string.IsNullOrEmpty(request.Token))
            {
                return BadRequest(new { error = "Token is required" });
            }

            var principal = await _tokenValidationService.ValidateTokenAsync(request.Token);

            if (principal == null)
            {
                return Unauthorized(new { error = "Invalid token" });
            }

            // Extract user information
            var userId = principal.FindFirst(AbpClaimTypes.UserId)?.Value;
            var userName = principal.FindFirst(AbpClaimTypes.UserName)?.Value;
            var email = principal.FindFirst(AbpClaimTypes.Email)?.Value;
            var sub = principal.FindFirst("sub")?.Value;

            var response = new TokenValidationResponse
            {
                IsValid = true,
                UserId = userId,
                UserName = userName,
                Email = email,
                Sub = sub,
                Claims = principal.Claims.Select(c => new ClaimInfo { Type = c.Type, Value = c.Value }).ToList()
            };

            _logger.LogInformation("Token validation successful for user: {Email}", email);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating token");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Simple endpoint to check if a token is valid
    /// </summary>
    [HttpPost("is-valid")]
    [AllowAnonymous]
    public async Task<IActionResult> IsTokenValid([FromBody] TokenValidationRequest request)
    {
        try
        {
            if (string.IsNullOrEmpty(request.Token))
            {
                return BadRequest(new { error = "Token is required" });
            }

            var isValid = await _tokenValidationService.IsValidTokenAsync(request.Token);

            return Ok(new { isValid });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking token validity");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Protected endpoint that requires a valid token
    /// This demonstrates how to use the validated token in your API
    /// </summary>
    [HttpGet("protected")]
    [Authorize(AuthenticationSchemes = "Bearer")]
    public IActionResult ProtectedEndpoint()
    {
        var userId = User.FindFirst(AbpClaimTypes.UserId)?.Value;
        var userName = User.FindFirst(AbpClaimTypes.UserName)?.Value;
        var email = User.FindFirst(AbpClaimTypes.Email)?.Value;

        return Ok(new
        {
            message = "Access granted to protected endpoint",
            userId,
            userName,
            email,
            claims = User.Claims.Select(c => new { Type = c.Type, Value = c.Value }).ToList()
        });
    }
}

public class TokenValidationRequest
{
    public string Token { get; set; } = string.Empty;
}

public class TokenValidationResponse
{
    public bool IsValid { get; set; }
    public string? UserId { get; set; }
    public string? UserName { get; set; }
    public string? Email { get; set; }
    public string? Sub { get; set; }
    public List<ClaimInfo> Claims { get; set; } = new List<ClaimInfo>();
}

public class ClaimInfo
{
    public string Type { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
}