using Imip.JettyApproval.JettyRequests;
using Imip.JettyApproval.Mapping.Mappers;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.JettyApproval.JettyRequests;

/// <summary>
/// Application service for JettyRequest entity
/// </summary>
public class JettyRequestAppService :
    CrudAppService<JettyRequest, JettyRequestDto, Guid, PagedAndSortedResultRequestDto, CreateUpdateJettyRequestDto, CreateUpdateJettyRequestDto>,
    IJettyRequestAppService
{
    private readonly IJettyRequestRepository _jettyRequestRepository;
    private readonly JettyRequestMapper _mapper;
    private readonly ILogger<JettyRequestAppService> _logger;

    public JettyRequestAppService(
        IJettyRequestRepository jettyRequestRepository,
        JettyRequestMapper mapper,
        ILogger<JettyRequestAppService> logger)
        : base(jettyRequestRepository)
    {
        _jettyRequestRepository = jettyRequestRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public override async Task<JettyRequestDto> CreateAsync(CreateUpdateJettyRequestDto input)
    {
        await CheckCreatePolicyAsync();

        var entity = _mapper.CreateEntityWithId(input, Guid.NewGuid());

        await _jettyRequestRepository.InsertAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task<JettyRequestDto> UpdateAsync(Guid id, CreateUpdateJettyRequestDto input)
    {
        await CheckUpdatePolicyAsync();

        var entity = await _jettyRequestRepository.GetAsync(id);

        _mapper.MapToEntity(input, entity);

        await _jettyRequestRepository.UpdateAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task DeleteAsync(Guid id)
    {
        await CheckDeletePolicyAsync();

        var entity = await _jettyRequestRepository.GetAsync(id);

        await _jettyRequestRepository.DeleteAsync(entity, autoSave: true);
    }

    public override async Task<JettyRequestDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var entity = await _jettyRequestRepository.GetWithItemsAsync(id);
        return _mapper.MapToDto(entity);
    }

    public override async Task<PagedResultDto<JettyRequestDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        await CheckGetListPolicyAsync();

        var queryable = await _jettyRequestRepository.GetQueryableAsync();

        // Apply sorting
        if (!string.IsNullOrWhiteSpace(input.Sorting))
        {
            queryable = queryable.OrderBy(input.Sorting);
        }
        else
        {
            queryable = queryable.OrderByDescending(x => x.CreationTime);
        }

        var totalCount = await AsyncExecuter.CountAsync(queryable);

        var entities = await AsyncExecuter.ToListAsync(
            queryable.PageBy(input.SkipCount, input.MaxResultCount)
        );

        var dtos = entities.Select(_mapper.MapToDto).ToList();

        return new PagedResultDto<JettyRequestDto>(totalCount, dtos);
    }

    public virtual async Task<List<JettyRequestDto>> GetByVesselNameAsync(string vesselName)
    {
        var entities = await _jettyRequestRepository.GetByVesselNameAsync(vesselName);
        return _mapper.MapToDtoList(entities);
    }

    public virtual async Task<List<JettyRequestDto>> GetByJettyAsync(string jetty)
    {
        var entities = await _jettyRequestRepository.GetByJettyAsync(jetty);
        return _mapper.MapToDtoList(entities);
    }

    public virtual async Task<List<JettyRequestDto>> GetByDateRangeAsync(DateTime startDate, DateTime endDate)
    {
        var entities = await _jettyRequestRepository.GetByDateRangeAsync(startDate, endDate);
        return _mapper.MapToDtoList(entities);
    }
}