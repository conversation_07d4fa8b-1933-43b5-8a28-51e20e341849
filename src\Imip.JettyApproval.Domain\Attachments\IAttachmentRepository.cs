using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace Imip.JettyApproval.Attachments;

/// <summary>
/// Repository interface for Attachment entity
/// </summary>
public interface IAttachmentRepository : IRepository<Attachment, Guid>
{
    /// <summary>
    /// Gets attachments by reference ID and type
    /// </summary>
    /// <param name="referenceId">The reference ID</param>
    /// <param name="referenceType">The reference type</param>
    /// <returns>List of attachments</returns>
    Task<List<Attachment>> GetByReferenceAsync(Guid referenceId, string referenceType);
}

