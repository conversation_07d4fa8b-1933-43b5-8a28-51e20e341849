import{r as f,j as e,h as $,u as I,f as P}from"./vendor-CrSBzUoz.js";import{y as Y,u as k,D as _,e as X,B as y,f as Z,j as q,Q as N,z as ee,T as z,I as C,C as se,E as te,A as re}from"./app-layout-CNB1Wtrx.js";import{A as ie,a as ae,b as ne,c as oe,d as le,e as ce,f as de,g as ue,T as pe}from"./TableSkeleton-Bzdk6y-O.js";import{u as v,h as E,D as A,t as me}from"./DataTableColumnHeader-DJ80pmDz.js";import{D as he}from"./DataTable-BL4SJdnU.js";import{l as xe,p as fe,_ as ye,a as ge}from"./popover-7NwOVASC.js";import{g as je,e as Ne,N as be}from"./NotionFilter-COtzX9y0.js";import{D as L,a as Se,b as U,c as V,d as Q,e as G}from"./dialog-DAr_Mtxm.js";import{u as K}from"./index.esm-CT1elm-0.js";import{F as M,a as b}from"./FormField-BFBouSal.js";import{T as H}from"./textarea-P1Up2ORZ.js";import{$ as De}from"./App-CGHLK9xH.js";import"./radix-DaY-mnHi.js";import"./card-BAJCNJxm.js";import"./table-CAbNlII1.js";import"./checkbox-CayrCcBd.js";import"./tiny-invariant-CopsF_GD.js";import"./search-YAT3sv3T.js";import"./index-CZZWNLgr.js";const Ce=({user:{userId:n,username:t},onDismiss:i})=>{const{toast:d}=v(),[s,l]=f.useState(!1),r=async()=>{try{await Y({path:{id:n}}),d({title:"Success",description:`User "${t}" has been deleted successfully.`}),i()}catch(a){const o=E(a);d({title:o.title,description:o.description,variant:"error"})}};return f.useEffect(()=>{l(!0)},[]),e.jsx(ie,{open:s,children:e.jsxs(ae,{children:[e.jsxs(ne,{children:[e.jsx(oe,{children:"Are you absolutely sure?"}),e.jsxs(le,{children:['This action cannot be undone. This will permanently delete your this user "',t,'"']})]}),e.jsxs(ce,{children:[e.jsx(de,{onClick:i,children:"Cancel"}),e.jsx(ue,{onClick:r,children:"Yes"})]})]})})},ve=({dataId:n,dataEdit:t,onAction:i,variant:d="dropdown"})=>{const{can:s}=k();return d==="dropdown"?e.jsx("div",{className:"flex justify-end",children:e.jsxs(_,{children:[e.jsx(X,{asChild:!0,children:e.jsxs(y,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:[e.jsx(xe,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"Open menu"})]})}),e.jsxs(Z,{align:"end",className:"w-[160px]",children:[s("IdentityServer.OpenIddictResources.Edit")&&e.jsx(q,{className:"cursor-pointer text-sm",onClick:()=>i(n,t,"edit"),children:"Edit"}),s("IdentityServer.OpenIddictResources.Delete")&&e.jsx(q,{className:"cursor-pointer text-sm text-red-500",onClick:()=>i(n,t,"delete"),children:"Delete"})]})]})}):e.jsxs("div",{className:"flex items-center justify-end gap-1",children:[s("AbpIdentity.Users.ManagePermissions")&&e.jsxs(y,{variant:"primary",size:"sm",className:"flex items-center gap-1 px-2 py-1",onClick:()=>i(n,t,"permission"),children:[e.jsx(fe,{className:"h-4 w-4"}),e.jsx("span",{children:"Permission"})]}),s("AbpIdentity.Users.Update")&&e.jsxs(y,{variant:"primary",size:"sm",className:"flex items-center gap-1 px-2 py-1",onClick:()=>i(n,t,"edit"),children:[e.jsx(ye,{className:"h-4 w-4"}),e.jsx("span",{children:"Edit"})]})]})};function O(n,t,i){if(!i||typeof i!="object"||!("operator"in i)||!(p=>typeof p=="object"&&p!==null&&"operator"in p&&"value"in p)(i))return!0;const s=n.getValue(t),{operator:l,value:r}=i;if(s==null)return l==="IsNull"?!0:l==="IsNotNull"?!1:l==="IsEmpty";let a="";if(typeof s=="object"&&s!==null)try{a=JSON.stringify(s).toLowerCase()}catch{a=`[${Object.prototype.toString.call(s)}]`}else typeof s=="number"||typeof s=="boolean"||typeof s=="string"?a=String(s).toLowerCase():a=`[${typeof s}]`;const o=r?String(r).toLowerCase():"";switch(l){case"Equals":return a===o;case"NotEquals":return a!==o;case"Contains":return a.includes(o);case"StartsWith":return a.startsWith(o);case"EndsWith":return a.endsWith(o);case"GreaterThan":return Number(s)>Number(r);case"GreaterThanOrEqual":return Number(s)>=Number(r);case"LessThan":return Number(s)<Number(r);case"LessThanOrEqual":return Number(s)<=Number(r);case"IsEmpty":return a==="";case"IsNotEmpty":return a!=="";case"IsNull":return s==null;case"IsNotNull":return s!=null;default:return!0}}const Fe=n=>[{accessorKey:"name",header:({column:t})=>e.jsx(A,{column:t,title:"Resource Name"}),enableSorting:!0,enableHiding:!0,cell:t=>t.getValue(),meta:{className:"text-left",displayName:"Name"},filterFn:O},{accessorKey:"displayName",header:({column:t})=>e.jsx(A,{column:t,title:"Display Name"}),enableSorting:!0,enableHiding:!0,cell:t=>t.getValue(),meta:{className:"text-left",displayName:"Display Name"},filterFn:O},{accessorKey:"description",header:({column:t})=>e.jsx(A,{column:t,title:"Description"}),enableSorting:!0,enableHiding:!0,cell:t=>t.getValue(),meta:{className:"text-left",displayName:"Description"},filterFn:O},{id:"actions",header:"Actions",enableHiding:!0,cell:t=>e.jsx(ve,{dataId:t.row.original.id,dataEdit:t.row.original,onAction:n,variant:"dropdown"}),meta:{className:"text-right",displayName:"Actions"}}],we=(n,t,i=[],d)=>$({queryKey:[N.GetOpeniddictResources,n,t,JSON.stringify(i),d],queryFn:async()=>{try{const s=je({pageIndex:n,pageSize:t,sorting:d,filterConditions:i});return(await ee({body:s})).data?.data}catch(s){const{title:l,description:r}=Ne(s,"Error loading resources");return me({title:l,description:r,variant:"destructive"}),{items:[],totalCount:0}}},retry:!1}),Te=({children:n})=>{const{can:t}=k(),[i,d]=f.useState(!1),{toast:s}=v(),l=I(),{handleSubmit:r,register:a,reset:o}=K(),p=()=>{o({name:"",displayName:"",description:""})},m=P({mutationFn:async u=>se({body:u}),onSuccess:()=>{s({title:"Success",description:"Resource Created Successfully",variant:"success"}),l.invalidateQueries({queryKey:[N.GetOpeniddictResources]}),p(),d(!1)},onError:u=>{const c=E(u);s({title:c.title,description:c.description,variant:"error"})}}),h=u=>{const c={...u};m.mutate(c)},S=u=>{u&&p(),d(u)};return e.jsxs("section",{children:[e.jsx(z,{}),e.jsxs(L,{open:i,onOpenChange:S,children:[e.jsx(Se,{asChild:!0,children:n}),e.jsx("section",{className:"flex items-center justify-between pb-5",children:t("AbpIdentity.Users.Create")&&e.jsxs(y,{size:"sm",className:"w-full sm:py-1 sm:mt-0 sm:w-fit",onClick:()=>S(!0),children:[e.jsx(ge,{className:"-ml-1 size-4 shrink-0","aria-hidden":"true"}),e.jsx("span",{className:"hidden truncate sm:inline",children:"Create New Resources"})]})}),e.jsxs(U,{size:"xl",children:[e.jsx(V,{children:e.jsx(Q,{children:"Create a New Resource"})}),e.jsxs("form",{onSubmit:r(h),className:"mt-2",children:[e.jsx("section",{className:"flex w-full flex-col space-y-2",children:e.jsxs(M,{children:[e.jsx(b,{label:"Name",description:"The unique identifier for this resource. Used in requests",children:e.jsx(C,{required:!0,...a("name"),placeholder:"Name"})}),e.jsx(b,{label:"Display Name",description:"The display name for this resource",children:e.jsx(C,{required:!0,...a("displayName"),placeholder:"Display Name"})}),e.jsx(b,{label:"Description",description:"The description for this resource",children:e.jsx(H,{required:!0,...a("description"),placeholder:"Description"})})]})}),e.jsxs(G,{className:"mt-5",children:[e.jsx(y,{variant:"ghost",onClick:u=>{u.preventDefault(),d(!1)},disabled:m.isPending,children:"Cancel"}),e.jsx(y,{type:"submit",disabled:m.isPending,children:m.isPending?"Saving...":"Save"})]})]})]})]})]})},R=({dataEdit:n,dataId:t,onDismiss:i})=>{const[d,s]=f.useState(!0),{toast:l}=v(),r=I(),{handleSubmit:a,register:o,reset:p}=K(),m=()=>{p({name:"",displayName:"",description:""})},h=P({mutationFn:async c=>te({path:{id:t||""},body:c}),onSuccess:()=>{l({title:"Success",description:"Resource Updated Successfully",variant:"success"}),r.invalidateQueries({queryKey:[N.GetOpeniddictResources]}),m(),s(!1),i()},onError:c=>{const j=E(c);l({title:j.title,description:j.description,variant:"error"})}}),S=c=>{const j={...c};h.mutate(j)},u=c=>{c||i(),s(c)};return e.jsxs("section",{children:[e.jsx(z,{}),e.jsx(L,{open:d,onOpenChange:u,children:e.jsxs(U,{size:"xl",children:[e.jsx(V,{children:e.jsx(Q,{children:"Edit Resource"})}),e.jsxs("form",{onSubmit:a(S),className:"mt-2",children:[e.jsx("section",{className:"flex w-full flex-col space-y-2",children:e.jsxs(M,{children:[e.jsx(b,{label:"Name",description:"The unique identifier for this resource. Used in requests",children:e.jsx(C,{required:!0,...o("name"),defaultValue:n.name??"",placeholder:"Name"})}),e.jsx(b,{label:"Display Name",description:"The display name for this resource",children:e.jsx(C,{required:!0,...o("displayName"),defaultValue:n.displayName??"",placeholder:"Display Name"})}),e.jsx(b,{label:"Description",description:"The description for this resource",children:e.jsx(H,{required:!0,...o("description"),defaultValue:n.description??"",placeholder:"Description"})})]})}),e.jsxs(G,{className:"mt-5",children:[e.jsx(y,{variant:"ghost",onClick:c=>{c.preventDefault(),s(!1),i()},disabled:h.isPending,children:"Cancel"}),e.jsx(y,{type:"submit",disabled:h.isPending,children:h.isPending?"Saving...":"Save"})]})]})]})})]})},Ae=()=>{const{toast:n}=v(),t=I(),[i,d]=f.useState(""),[s,l]=f.useState([]),[r,a]=f.useState(null),[o,p]=f.useState({pageIndex:0,pageSize:10}),{isLoading:m,data:h}=we(o.pageIndex,o.pageSize,s),u=Fe((x,D,g)=>{a({dataId:x,dataEdit:D,dialogType:g})}),c=x=>{d(x);const g=[...s.filter(T=>T.fieldName!=="name")];x&&g.push({fieldName:"name",operator:"Contains",value:x});const F=JSON.stringify(s),w=JSON.stringify(g);F!==w&&(l(g),p(T=>({...T,pageIndex:0})))},j=x=>{p(x)},J=()=>{t.invalidateQueries({queryKey:[N.GetOpeniddictResources]}),setTimeout(()=>{n({title:"Data refreshed",description:"The client list has been refreshed.",variant:"success"})},800)};if(m)return e.jsx(pe,{rowCount:o.pageSize,columnCount:4,hasTitle:!0,hasSearch:!0,hasFilters:!0,hasPagination:!0,hasActions:!0});const B=h?.items??[],W=h?.totalCount??0;return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"space-y-4",children:e.jsx(he,{title:"Resources Management",columns:u,data:B,totalCount:W,isLoading:m,manualPagination:!0,pageSize:o.pageSize,onPaginationChange:j,onSearch:c,searchValue:i,customFilterbar:x=>e.jsx(be,{...x,activeFilters:s,onServerFilter:D=>{const g=JSON.stringify(s),F=JSON.stringify(D);g!==F&&(l(D),p(w=>({...w,pageIndex:0})))}}),hideDefaultFilterbar:!0,onRefresh:J,enableRowSelection:!1,actionButton:{onClick:()=>{},content:e.jsx(Te,{})}})}),r&&r.dialogType==="edit"&&e.jsx(R,{dataId:r.dataId,dataEdit:r.dataEdit,onDismiss:()=>{t.invalidateQueries({queryKey:[N.GetOpeniddictResources]}),a(null)}}),r&&r.dialogType==="permission"&&e.jsx(R,{dataId:r.dataId,dataEdit:r.dataEdit,onDismiss:()=>a(null)}),r&&r.dialogType==="delete"&&e.jsx(Ce,{user:{username:r.dataEdit.name,userId:r.dataId},onDismiss:()=>{t.invalidateQueries({queryKey:[N.GetOpeniddictApplications]}),a(null)}})]})};function $e(){return e.jsxs(re,{children:[e.jsx(De,{title:"Dashboard"}),e.jsx(Ae,{})]})}export{$e as default};
//# sourceMappingURL=resource-DblyzjJZ.js.map
