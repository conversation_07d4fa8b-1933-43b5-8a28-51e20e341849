{"version": 3, "file": "claim-CXz8stMd.js", "sources": ["../../../../../frontend/src/components/app/claims/Delete.tsx", "../../../../../frontend/src/components/app/claims/Actions.tsx", "../../../../../frontend/src/components/app/claims/Columns.tsx", "../../../../../frontend/src/components/app/claims/variable.ts", "../../../../../frontend/src/components/app/claims/Add.tsx", "../../../../../frontend/src/components/app/claims/Edit.tsx", "../../../../../frontend/src/lib/hooks/useIdentityClaimTypes.ts", "../../../../../frontend/src/components/app/claims/List.tsx", "../../../../../frontend/src/pages/claim.tsx"], "sourcesContent": ["import { deleteApiIdentityClaimTypesById } from '@/client'\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@/components/ui/alert-dialog'\r\nimport { useToast } from '@/lib/useToast'\r\nimport { handleApiError } from '@/lib/handleApiError'\r\nimport { useEffect, useState } from 'react'\r\n\r\ntype DeleteUserProps = {\r\n  dataId: string\r\n  onDismiss: () => void\r\n}\r\nexport const Delete = ({ dataId, onDismiss }: DeleteUserProps) => {\r\n  const { toast } = useToast()\r\n  const [open, setOpen] = useState<boolean>(false)\r\n  const onYesEvent = async () => {\r\n    try {\r\n      await deleteApiIdentityClaimTypesById({\r\n        path: { id: dataId },\r\n      })\r\n      toast({\r\n        title: 'Success',\r\n        description: `Claim Type has been deleted successfully.`,\r\n      })\r\n      onDismiss()\r\n    } catch (err: unknown) {\r\n      const error = handleApiError(err);\r\n      toast({\r\n        title: error.title,\r\n        description: error.description,\r\n        variant: 'error',\r\n      })\r\n    }\r\n  }\r\n\r\n  useEffect(() => {\r\n    setOpen(true)\r\n  }, [])\r\n\r\n  return (\r\n    <AlertDialog open={open}>\r\n      <AlertDialogContent>\r\n        <AlertDialogHeader>\r\n          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>\r\n          <AlertDialogDescription>\r\n            This action cannot be undone. This will permanently delete your this claim type.\r\n          </AlertDialogDescription>\r\n        </AlertDialogHeader>\r\n        <AlertDialogFooter>\r\n          <AlertDialogCancel onClick={onDismiss}>Cancel</AlertDialogCancel>\r\n          <AlertDialogAction onClick={onYesEvent}>Yes</AlertDialogAction>\r\n        </AlertDialogFooter>\r\n      </AlertDialogContent>\r\n    </AlertDialog>\r\n  )\r\n}\r\n", "'use client'\r\n\r\nimport { type IdentityClaimTypeDto } from '@/client'\r\nimport { useGrantedPolicies } from '@/lib/hooks/useGrantedPolicies'\r\nimport { RiMoreLine, RiPencilLine, RiShieldKeyholeLine } from '@remixicon/react'\r\nimport { Button } from '@/components/ui/button'\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu'\r\n\r\ntype ClaimActionProps = {\r\n  userId: string\r\n  userDto: IdentityClaimTypeDto\r\n  onAction: (dataId: string, dataEdit: IdentityClaimTypeDto, dialogType: 'edit' | 'permission' | 'delete') => void\r\n  variant?: 'dropdown' | 'buttons'\r\n}\r\n\r\nexport const Actions = ({ userId, userDto, onAction, variant = 'dropdown' }: ClaimActionProps) => {\r\n  const { can } = useGrantedPolicies()\r\n\r\n  // For dropdown menu style (first image)\r\n  if (variant === 'dropdown') {\r\n    return (\r\n      <div className=\"flex justify-end\">\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"h-8 w-8 p-0\"\r\n            >\r\n              <RiMoreLine className=\"h-4 w-4\" />\r\n              <span className=\"sr-only\">Open menu</span>\r\n            </Button>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent align=\"end\" className=\"w-[160px]\">\r\n            {can('IdentityServer.ClaimTypes.Edit') && (\r\n              <DropdownMenuItem\r\n                className=\"cursor-pointer text-sm\"\r\n                onClick={() => onAction(userId, userDto, 'edit')}\r\n              >\r\n                Edit\r\n              </DropdownMenuItem>\r\n            )}\r\n            {can('IdentityServer.ClaimTypes.Delete') && (\r\n              <DropdownMenuItem\r\n                className=\"cursor-pointer text-sm text-red-500\"\r\n                onClick={() => onAction(userId, userDto, 'delete')}\r\n              >\r\n                Delete\r\n              </DropdownMenuItem>\r\n            )}\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // For button group style (second image)\r\n  return (\r\n    <div className=\"flex items-center justify-end gap-1\">\r\n      {can('AbpIdentity.Users.ManagePermissions') && (\r\n        <Button\r\n          variant=\"primary\"\r\n          size=\"sm\"\r\n          className=\"flex items-center gap-1 px-2 py-1\"\r\n          onClick={() => onAction(userId, userDto, 'permission')}\r\n        >\r\n          <RiShieldKeyholeLine className=\"h-4 w-4\" />\r\n          <span>Permission</span>\r\n        </Button>\r\n      )}\r\n      {can('AbpIdentity.Users.Update') && (\r\n        <Button\r\n          variant=\"primary\"\r\n          size=\"sm\"\r\n          className=\"flex items-center gap-1 px-2 py-1\"\r\n          onClick={() => onAction(userId, userDto, 'edit')}\r\n        >\r\n          <RiPencilLine className=\"h-4 w-4\" />\r\n          <span>Edit</span>\r\n        </Button>\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n", "'use client'\r\n\r\nimport { type IdentityClaimTypeDto } from '@/client'\r\nimport { type ColumnDef } from '@tanstack/react-table'\r\nimport { Actions } from './Actions'\r\nimport { Checkbox } from '@/components/ui/checkbox'\r\nimport { DataTableColumnHeader } from '@/components/data-table/DataTableColumnHeader'\r\nimport { customFilterFunction } from '@/components/data-table/filterFunctions'\r\n\r\n// Type for the callback function to handle claim type actions\r\ntype ClaimActionCallback = (dataId: string, dataEdit: IdentityClaimTypeDto, dialogType: 'edit' | 'permission' | 'delete') => void\r\n\r\n// Function to create columns with the action callback\r\nexport const getColumns = (\r\n  handleUserAction: ClaimActionCallback\r\n): ColumnDef<IdentityClaimTypeDto>[] => {\r\n  return [\r\n    {\r\n      id: \"select\",\r\n      header: ({ table }) => (\r\n        <Checkbox\r\n          checked={\r\n            table.getIsAllPageRowsSelected()\r\n              ? true\r\n              : table.getIsSomeRowsSelected()\r\n                ? \"indeterminate\"\r\n                : false\r\n          }\r\n          onCheckedChange={() => table.toggleAllPageRowsSelected()}\r\n          className=\"translate-y-0.5\"\r\n          aria-label=\"Select all\"\r\n        />\r\n      ),\r\n      cell: ({ row }) => (\r\n        <Checkbox\r\n          checked={row.getIsSelected()}\r\n          onCheckedChange={() => row.toggleSelected()}\r\n          className=\"translate-y-0.5\"\r\n          aria-label=\"Select row\"\r\n        />\r\n      ),\r\n      enableSorting: false,\r\n      enableHiding: true,\r\n      meta: {\r\n        displayName: \"Select\",\r\n      },\r\n    },\r\n    {\r\n      accessorKey: \"name\",\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title=\"Name\" />\r\n      ),\r\n      enableSorting: true,\r\n      enableHiding: true,\r\n      cell: (info) => info.getValue(),\r\n      filterFn: customFilterFunction,\r\n      meta: {\r\n        className: \"text-left\",\r\n        displayName: \"Name\",\r\n      },\r\n    },\r\n    {\r\n      accessorKey: \"description\",\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title=\"Description\" />\r\n      ),\r\n      enableSorting: true,\r\n      enableHiding: true,\r\n      filterFn: customFilterFunction,\r\n      cell: (info) => info.getValue(),\r\n      meta: {\r\n        className: \"text-left\",\r\n        displayName: \"Description\",\r\n      },\r\n    },\r\n    {\r\n      accessorKey: \"valueType\",\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title=\"Value Type\" />\r\n      ),\r\n      cell: ({ row }) => {\r\n        const valueType = row.getValue(\"valueType\");\r\n        const valueTypeMap = {\r\n          0: \"String\",\r\n          1: \"Int\",\r\n          2: \"Boolean\",\r\n          3: \"DateTime\"\r\n        };\r\n        return valueTypeMap[valueType as keyof typeof valueTypeMap] || \"Unknown\";\r\n      },\r\n      enableSorting: true,\r\n      enableHiding: true,\r\n      meta: {\r\n        className: \"text-left\",\r\n        displayName: \"Value Type\",\r\n      },\r\n    },\r\n    {\r\n      id: \"actions\",\r\n      header: \"Actions\",\r\n      cell: (info) => (\r\n        <Actions\r\n          userId={info.row.original.id!}\r\n          userDto={info.row.original}\r\n          onAction={handleUserAction}\r\n          variant=\"dropdown\"\r\n        />\r\n      ),\r\n      enableSorting: false,\r\n      enableHiding: true,\r\n      meta: {\r\n        className: \"text-right\",\r\n        displayName: \"Action\",\r\n      },\r\n    }\r\n  ];\r\n}\r\n", "export const itemValueType = [\r\n  {\r\n    label: 'String',\r\n    value: '0',\r\n  },\r\n  {\r\n    label: 'Int',\r\n    value: '1',\r\n  },\r\n  {\r\n    label: 'Boolean',\r\n    value: '2',\r\n  },\r\n  {\r\n    label: 'DateTime',\r\n    value: '3',\r\n  },\r\n]\r\n", "'use client'\r\nimport { type CreateUpdateClaimTypeDto, postApiIdentityClaimTypes } from '@/client'\r\nimport { Button } from '@/components/ui/button'\r\nimport {\r\n  <PERSON><PERSON>,\r\n  DialogContent,\r\n  Di<PERSON>Footer,\r\n  <PERSON><PERSON>Header,\r\n  <PERSON>alogTitle,\r\n  DialogTrigger,\r\n} from '@/components/ui/dialog'\r\nimport { useToast } from '@/lib/useToast'\r\nimport { handleApiError } from '@/lib/handleApiError'\r\nimport { QueryNames } from '@/lib/hooks/QueryConstants'\r\nimport { useGrantedPolicies } from '@/lib/hooks/useGrantedPolicies'\r\nimport { useMutation, useQueryClient } from '@tanstack/react-query'\r\nimport { useState, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { Toaster } from '@/components/ui/toaster'\r\nimport { RiAddLine } from '@remixicon/react'\r\nimport { FormField, FormSection } from '@/components/ui/FormField'\r\nimport { Checkbox } from '@/components/ui/checkbox'\r\nimport { Input } from '@/components/ui/input'\r\nimport { MultiSelect } from '@/components/ui/multi-select'\r\nimport { itemValueType } from './variable'\r\n\r\nexport type AddClientProps = {\r\n  children?: React.ReactNode\r\n}\r\n\r\nexport const Add = ({ children }: AddClientProps) => {\r\n  const { can } = useGrantedPolicies()\r\n  const [open, setOpen] = useState(false)\r\n  const { toast } = useToast()\r\n  const queryClient = useQueryClient()\r\n  const { handleSubmit, register, reset } = useForm<CreateUpdateClaimTypeDto>()\r\n  const [selectedValueType, setSelectedValueType] = useState<string[]>([])\r\n\r\n  // Reset form when dialog is closed\r\n  useEffect(() => {\r\n    if (!open) {\r\n      reset({\r\n        name: '',\r\n        required: false,\r\n        isStatic: false,\r\n        regex: '',\r\n        regexDescription: '',\r\n        description: '',\r\n        valueType: 0\r\n      })\r\n      setSelectedValueType([])\r\n    }\r\n  }, [open, reset])\r\n\r\n\r\n  const createDataMutation = useMutation({\r\n    mutationFn: async (dataMutation: CreateUpdateClaimTypeDto) =>\r\n      postApiIdentityClaimTypes({\r\n        body: dataMutation\r\n      }),\r\n    onSuccess: () => {\r\n      toast({\r\n        title: 'Success',\r\n        description: 'Claim Type Created Successfully',\r\n        variant: 'success',\r\n      })\r\n      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetIdentityClaimTypes] })\r\n      setOpen(false)\r\n    },\r\n    onError: (err: unknown) => {\r\n      // Use the global helper to handle API errors\r\n      const error = handleApiError(err);\r\n      toast({\r\n        title: error.title,\r\n        description: error.description,\r\n        variant: 'error',\r\n      })\r\n    }\r\n  })\r\n\r\n  const onSubmit = (formData: CreateUpdateClaimTypeDto) => {\r\n    // Merge form data with consent type and permissions\r\n    const userData: CreateUpdateClaimTypeDto = {\r\n      ...formData,\r\n    }\r\n\r\n    // Explicitly mark the promise as handled\r\n    void createDataMutation.mutate(userData)\r\n  }\r\n\r\n  const handleOpenChange = (newOpenState: boolean) => {\r\n    setOpen(newOpenState)\r\n  }\r\n\r\n  return (\r\n    <section>\r\n      <Toaster />\r\n      <Dialog open={open} onOpenChange={handleOpenChange}>\r\n        <DialogTrigger asChild>{children}</DialogTrigger>\r\n        <section className=\"flex items-center justify-between pb-5\">\r\n          {can('IdentityServer.ClaimTypes.Create') && (\r\n            <Button size='sm' className=\"w-full sm:py-1 sm:mt-0 sm:w-fit\" onClick={() => setOpen(true)}>\r\n              <RiAddLine className=\"-ml-1 size-4 shrink-0\" aria-hidden=\"true\" />\r\n              <span className=\"hidden truncate sm:inline\">New Claim Type</span>\r\n            </Button>\r\n          )}\r\n        </section>\r\n        <DialogContent className='max-w-2xl'>\r\n          <DialogHeader>\r\n            <DialogTitle>Create a New Claim Type</DialogTitle>\r\n          </DialogHeader>\r\n          <form onSubmit={handleSubmit(onSubmit)} className='mt-2' onKeyDown={(e) => {\r\n            if (e.key === 'Enter' && e.target instanceof HTMLInputElement) {\r\n              e.preventDefault();\r\n              void handleSubmit(onSubmit)();\r\n            }\r\n          }}>\r\n            <section className=\"flex w-full flex-col space-y-2\">\r\n              <FormSection>\r\n                <FormField\r\n                  label=\"Name\"\r\n                  description=\"The name of the claim\"\r\n                >\r\n                  <Input required {...register('name')} placeholder=\"Claim Name\" />\r\n                </FormField>\r\n\r\n                <FormField\r\n                  label=\"Required\"\r\n                  description=\"Whether the claim is required\"\r\n                >\r\n                  <Checkbox {...register('required', {\r\n                    setValueAs: (value) => value === true\r\n                  })} />\r\n                </FormField>\r\n\r\n                <FormField\r\n                  label=\"Is Static\"\r\n                  description=\"Whether the claim is static\"\r\n                >\r\n                  <Checkbox {...register('isStatic', {\r\n                    setValueAs: (value) => value === true\r\n                  })} />\r\n                </FormField>\r\n\r\n                <FormField\r\n                  label=\"Regex\"\r\n                  description=\"The regex for the claim\"\r\n                >\r\n                  <Input {...register('regex')} placeholder=\"Regex\" />\r\n                </FormField>\r\n\r\n                <FormField\r\n                  label=\"Regex Description\"\r\n                  description=\"The description for the regex\"\r\n                >\r\n                  <Input {...register('regexDescription')} placeholder=\"Regex Description\" />\r\n                </FormField>\r\n\r\n                <FormField\r\n                  label=\"Description\"\r\n                  description=\"The description for the claim\"\r\n                >\r\n                  <Input required {...register('description')} placeholder=\"Description\" />\r\n                </FormField>\r\n\r\n                <FormField\r\n                  label=\"Value Type\"\r\n                  description=\"The value type for the claim\"\r\n                >\r\n                  <MultiSelect\r\n                    mode='single'\r\n                    options={itemValueType}\r\n                    value={selectedValueType}\r\n                    onChange={setSelectedValueType}\r\n                    placeholder=\"Select value type\"\r\n                    maxHeight={300}\r\n                    name=\"valueType\"\r\n                    register={register}\r\n                    valueAsNumber={true}\r\n                  />\r\n                </FormField>\r\n              </FormSection>\r\n            </section>\r\n            <DialogFooter className=\"mt-5\">\r\n              <Button\r\n                variant=\"ghost\"\r\n                onClick={(e) => {\r\n                  e.preventDefault()\r\n                  setOpen(false)\r\n                }}\r\n                disabled={createDataMutation.isPending}\r\n              >\r\n                Cancel\r\n              </Button>\r\n              <Button type=\"submit\" disabled={createDataMutation.isPending}>\r\n                {createDataMutation.isPending ? 'Saving...' : 'Save'}\r\n              </Button>\r\n            </DialogFooter>\r\n          </form>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </section>\r\n  )\r\n}\r\n", "import { type CreateUpdateClaimTypeDto, type IdentityClaimTypeDto, putApiIdentityClaimTypesById } from '@/client'\r\nimport { useToast } from '@/lib/useToast'\r\nimport { handleApiError } from '@/lib/handleApiError'\r\nimport { useEffect, useState } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { Button } from '@/components/ui/button'\r\nimport { Checkbox } from '@/components/ui/checkbox'\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog'\r\nimport { Input } from '@/components/ui/input'\r\nimport { FormField, FormSection } from '@/components/ui/FormField'\r\nimport { MultiSelect } from '@/components/ui/multi-select'\r\nimport { useMutation, useQueryClient } from '@tanstack/react-query'\r\nimport { QueryNames } from '@/lib/hooks/QueryConstants'\r\nimport { itemValueType } from './variable'\r\n\r\ntype UserEditProps = {\r\n  dataEdit: IdentityClaimTypeDto\r\n  dataId: string\r\n  onDismiss: () => void\r\n}\r\nexport const Edit = ({ dataEdit, dataId, onDismiss }: UserEditProps) => {\r\n  const [open, setOpen] = useState(false)\r\n  const { toast } = useToast()\r\n  const queryClient = useQueryClient()\r\n  const { handleSubmit, register, setValue } = useForm<CreateUpdateClaimTypeDto>()\r\n  const [isRequired, setIsRequired] = useState(dataEdit.required ?? false)\r\n  const [isStatic, setIsStatic] = useState(dataEdit.isStatic ?? false)\r\n  const [selectedValueType, setSelectedValueType] = useState<string[]>([])\r\n\r\n  const updateDataMutation = useMutation({\r\n    mutationFn: async (formData: CreateUpdateClaimTypeDto) =>\r\n      putApiIdentityClaimTypesById({\r\n        path: { id: dataId },\r\n        body: formData,\r\n      }),\r\n    onSuccess: () => {\r\n      toast({\r\n        title: 'Success',\r\n        description: 'Claim Type Updated Successfully',\r\n        variant: 'success',\r\n      })\r\n      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetIdentityClaimTypes] })\r\n      onCloseEvent()\r\n    },\r\n    onError: (err: unknown) => {\r\n      // Use the global helper to handle API errors\r\n      const error = handleApiError(err);\r\n      toast({\r\n        title: error.title,\r\n        description: error.description,\r\n        variant: 'error',\r\n      })\r\n    }\r\n  })\r\n\r\n  const onSubmit = (formData: CreateUpdateClaimTypeDto) => {\r\n    // Merge form data with consent type and permissions\r\n    const userData: CreateUpdateClaimTypeDto = {\r\n      ...formData,\r\n    }\r\n\r\n    // Explicitly mark the promise as handled\r\n    void updateDataMutation.mutate(userData)\r\n  }\r\n\r\n  const onCloseEvent = () => {\r\n    setOpen(false)\r\n    onDismiss()\r\n  }\r\n\r\n  // Initialize form when opened\r\n  useEffect(() => {\r\n    if (open) {\r\n      // Initialize form values from dataEdit\r\n      setValue('name', dataEdit.name ?? '')\r\n      setValue('required', dataEdit.required ?? false)\r\n      setValue('isStatic', dataEdit.isStatic ?? false)\r\n      setValue('regex', dataEdit.regex ?? '')\r\n      setValue('regexDescription', dataEdit.regexDescription ?? '')\r\n      setValue('description', dataEdit.description ?? '')\r\n      setValue('valueType', dataEdit.valueType ?? 0)\r\n\r\n      // Initialize MultiSelect value\r\n      const valueTypeStr = (dataEdit.valueType !== undefined && dataEdit.valueType !== null)\r\n        ? dataEdit.valueType.toString()\r\n        : '0'\r\n      setSelectedValueType([valueTypeStr])\r\n    }\r\n  }, [open, dataEdit, setValue])\r\n\r\n  useEffect(() => {\r\n    setOpen(true)\r\n  }, [])\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={onCloseEvent}>\r\n      <DialogContent>\r\n        <DialogHeader>\r\n          <DialogTitle>Update a Claim Type: {dataEdit.name}</DialogTitle>\r\n        </DialogHeader>\r\n        <form\r\n          onSubmit={handleSubmit(onSubmit)}\r\n          className='mt-2'\r\n          onKeyDown={(e) => {\r\n            if (e.key === 'Enter' && e.target instanceof HTMLInputElement) {\r\n              e.preventDefault();\r\n              void handleSubmit(onSubmit)();\r\n            }\r\n          }}\r\n        >\r\n          <section className=\"flex w-full flex-col space-y-2\">\r\n            <FormSection>\r\n              <FormField\r\n                label=\"Name\"\r\n                description=\"The name of the claim\"\r\n              >\r\n                <Input required {...register('name')} defaultValue={dataEdit.name ?? ''} placeholder=\"Claim Name\" />\r\n              </FormField>\r\n\r\n              <FormField\r\n                label=\"Required\"\r\n                description=\"Whether the claim is required\"\r\n              >\r\n                <Checkbox\r\n                  checked={isRequired}\r\n                  onCheckedChange={(checked) => {\r\n                    setIsRequired(!!checked)\r\n                    setValue('required', !!checked)\r\n                  }}\r\n                />\r\n              </FormField>\r\n\r\n              <FormField\r\n                label=\"Is Static\"\r\n                description=\"Whether the claim is static\"\r\n              >\r\n                <Checkbox\r\n                  checked={isStatic}\r\n                  onCheckedChange={(checked) => {\r\n                    setIsStatic(!!checked)\r\n                    setValue('isStatic', !!checked)\r\n                  }}\r\n                />\r\n              </FormField>\r\n\r\n              <FormField\r\n                label=\"Regex\"\r\n                description=\"The regex for the claim\"\r\n              >\r\n                <Input {...register('regex')} defaultValue={dataEdit.regex ?? ''} placeholder=\"Regex\" />\r\n              </FormField>\r\n\r\n              <FormField\r\n                label=\"Regex Description\"\r\n                description=\"The description for the regex\"\r\n              >\r\n                <Input {...register('regexDescription')} defaultValue={dataEdit.regexDescription ?? ''} placeholder=\"Regex Description\" />\r\n              </FormField>\r\n\r\n              <FormField\r\n                label=\"Description\"\r\n                description=\"The description for the claim\"\r\n              >\r\n                <Input required {...register('description')} defaultValue={dataEdit.description ?? ''} placeholder=\"Description\" />\r\n              </FormField>\r\n\r\n              <FormField\r\n                label=\"Value Type\"\r\n                description=\"The value type for the claim\"\r\n              >\r\n                <MultiSelect\r\n                  mode='single'\r\n                  options={itemValueType}\r\n                  value={selectedValueType}\r\n                  onChange={setSelectedValueType}\r\n                  placeholder=\"Select value type\"\r\n                  maxHeight={300}\r\n                  name=\"valueType\"\r\n                  register={register}\r\n                  valueAsNumber={true}\r\n                />\r\n              </FormField>\r\n            </FormSection>\r\n          </section>\r\n          <DialogFooter className=\"mt-5\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              onClick={(e) => {\r\n                e.preventDefault()\r\n                setOpen(false)\r\n              }}\r\n              disabled={updateDataMutation.isPending}\r\n              type=\"button\"\r\n            >\r\n              Cancel\r\n            </Button>\r\n            <Button\r\n              type=\"submit\"\r\n              disabled={updateDataMutation.isPending}\r\n            >\r\n              {updateDataMutation.isPending ? 'Saving...' : 'Save'}\r\n            </Button>\r\n          </DialogFooter>\r\n        </form>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n", "import { postApiIdentityClaimTypesList } from '@/client'\r\nimport { useQuery } from '@tanstack/react-query'\r\nimport { extractApiError } from '@/lib/query-utils'\r\nimport { QueryNames } from './QueryConstants'\r\nimport { generateExtendedQueryParameters } from '@/lib/query-utils-extended'\r\nimport { type FilterCondition } from '@/lib/interfaces/IFilterCondition'\r\nimport { toast } from '@/lib/useToast'\r\n\r\nexport const useIdentityClaimTypes = (\r\n  pageIndex: number,\r\n  pageSize: number,\r\n  filterConditions: FilterCondition[] = [],\r\n  sorting?: string  \r\n) => {\r\n  return useQuery({\r\n    queryKey: [QueryNames.GetIdentityClaimTypes,  pageIndex, pageSize, JSON.stringify(filterConditions), sorting],\r\n    queryFn: async () => {\r\n      try {\r\n        // Generate query parameters using the extended utility function\r\n        const body = generateExtendedQueryParameters({\r\n          pageIndex,\r\n          pageSize,\r\n          sorting,\r\n          filterConditions,\r\n        })\r\n\r\n        const response = await postApiIdentityClaimTypesList({\r\n          body\r\n        })\r\n\r\n        return response.data?.data\r\n      } catch (error) {\r\n       // Use the error extraction utility\r\n       const { title, description } = extractApiError(error, 'Error loading clients')\r\n\r\n       // Show toast notification\r\n       toast({\r\n         title,\r\n         description,\r\n         variant: 'destructive',\r\n       })\r\n        // Return empty data to prevent UI crashes\r\n        return { items: [], totalCount: 0 }\r\n      }\r\n    },\r\n  })\r\n}\r\n", "'use client'\r\nimport { QueryNames } from '@/lib/hooks/QueryConstants'\r\nimport { useState } from 'react'\r\n\r\nimport { type IdentityClaimTypeDto } from '@/client'\r\nimport { type PaginationState, type SortingState } from '@tanstack/react-table'\r\n\r\nimport { useToast } from '@/lib/useToast'\r\nimport { Delete } from './Delete'\r\n\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { DataTable } from '@/components/data-table/DataTable'\r\nimport { getColumns } from './Columns'\r\nimport { Add } from './Add'\r\nimport { Edit } from './Edit'\r\nimport { useIdentityClaimTypes } from '@/lib/hooks/useIdentityClaimTypes'\r\nimport { TableSkeleton } from '@/components/ui/TableSkeleton'\r\nimport { type FilterCondition } from '@/lib/interfaces/IFilterCondition'\r\nimport { NotionFilter } from '@/components/data-table/NotionFilter'\r\n\r\nexport const ClaimList = () => {\r\n  const { toast } = useToast()\r\n  const queryClient = useQueryClient()\r\n\r\n  const [searchStr, setSearchStr] = useState<string>('')\r\n  const [filterConditions, setFilterConditions] = useState<FilterCondition[]>([])\r\n  const [userActionDialog, setUserActionDialog] = useState<{\r\n    dataId: string\r\n    dataEdit: IdentityClaimTypeDto\r\n    dialogType?: 'edit' | 'permission' | 'delete'\r\n  } | null>()\r\n\r\n  const [pagination, setPagination] = useState<PaginationState>({\r\n    pageIndex: 0,\r\n    pageSize: 10,\r\n  })\r\n\r\n  // Initialize sorting state\r\n  const [sorting, setSorting] = useState<SortingState>([\r\n    { id: 'name', desc: false }\r\n  ])\r\n\r\n  const { isLoading, data } = useIdentityClaimTypes(\r\n    pagination.pageIndex,\r\n    pagination.pageSize,\r\n    filterConditions\r\n  )\r\n\r\n  // Handler for user actions (edit, permission, delete)\r\n  const handleUserAction = (dataId: string, dataEdit: IdentityClaimTypeDto, dialogType: 'edit' | 'permission' | 'delete') => {\r\n    setUserActionDialog({\r\n      dataId,\r\n      dataEdit,\r\n      dialogType,\r\n    })\r\n  }\r\n\r\n  // Get columns with the action handler\r\n  const columns = getColumns(handleUserAction)\r\n\r\n  const handleSearch = (value: string) => {\r\n    // Always update the search string for UI consistency\r\n    setSearchStr(value)\r\n\r\n    // Create a search filter condition if there's a search value\r\n    // First, remove any existing name filter\r\n    const existingFilters = filterConditions.filter(fc => fc.fieldName !== 'name')\r\n    const newFilterConditions = [...existingFilters]\r\n\r\n    // Only add the search filter if there's a value\r\n    if (value) {\r\n      newFilterConditions.push({\r\n        fieldName: 'name',\r\n        operator: 'Contains',\r\n        value: value\r\n      })\r\n    }\r\n\r\n    // Only update state if filters have changed\r\n    const currentFiltersStr = JSON.stringify(filterConditions)\r\n    const newFiltersStr = JSON.stringify(newFilterConditions)\r\n\r\n    if (currentFiltersStr !== newFiltersStr) {\r\n      setFilterConditions(newFilterConditions)\r\n      setPagination(prev => ({ ...prev, pageIndex: 0 })) // Reset to first page on search\r\n    }\r\n  }\r\n\r\n  const handlePaginationChange = (newPagination: PaginationState) => {\r\n    setPagination(newPagination)\r\n  }\r\n\r\n  // Handler for sorting change\r\n  const handleSortingChange = (newSorting: SortingState) => {\r\n    setSorting(newSorting)\r\n    setPagination(prev => ({ ...prev, pageIndex: 0 })) // Reset to first page on sort change\r\n  }\r\n\r\n  // Handler for refreshing the data\r\n  const handleRefresh = () => {\r\n    // Invalidate the query to fetch fresh data\r\n    void queryClient.invalidateQueries({ queryKey: [QueryNames.GetIdentityClaimTypes] })\r\n    // Show toast notification after a short delay to match the animation\r\n    setTimeout(() => {\r\n      toast({\r\n        title: \"Data refreshed\",\r\n        description: \"The claims list has been refreshed.\",\r\n        variant: \"success\",\r\n      })\r\n    }, 100)\r\n  }\r\n\r\n  if (isLoading) return (\r\n    <TableSkeleton\r\n      rowCount={pagination.pageSize}\r\n      columnCount={4}\r\n      hasTitle={true}\r\n      hasSearch={true}\r\n      hasFilters={true}\r\n      hasPagination={true}\r\n      hasActions={true}\r\n    />\r\n  )\r\n\r\n  // Ensure we have valid data to render\r\n  const items = data?.items ?? [];\r\n  const totalCount = data?.totalCount ?? 0;\r\n\r\n  return (\r\n    <>\r\n      <div className=\"space-y-4\">\r\n        <DataTable\r\n          title=\"Claims Types\"\r\n          columns={columns}\r\n          data={items}\r\n          totalCount={totalCount}\r\n          isLoading={isLoading}\r\n          manualPagination={true}\r\n          manualSorting={true}\r\n          pageSize={pagination.pageSize}\r\n          onPaginationChange={handlePaginationChange}\r\n          onSortingChange={handleSortingChange}\r\n          sortingState={sorting}\r\n          onSearch={handleSearch}\r\n          searchValue={searchStr}\r\n          customFilterbar={(props) => (\r\n            <NotionFilter\r\n              {...props}\r\n              activeFilters={filterConditions}\r\n              onServerFilter={(conditions) => {\r\n                // Only update if the conditions have actually changed\r\n                const currentStr = JSON.stringify(filterConditions);\r\n                const newStr = JSON.stringify(conditions);\r\n\r\n                if (currentStr !== newStr) {\r\n                  setFilterConditions(conditions)\r\n                  setPagination(prev => ({ ...prev, pageIndex: 0 })) // Reset to first page on filter change\r\n                }\r\n              }}\r\n            />\r\n          )}\r\n          hideDefaultFilterbar={true}\r\n          onRefresh={handleRefresh}\r\n          enableRowSelection={false}\r\n          actionButton={{\r\n            // label: \"Create New User\",\r\n            // We need to provide an onClick handler even though we're using a custom component\r\n            onClick: () => { /* Required but not used */ },\r\n            content: <Add />\r\n          }}\r\n        />\r\n      </div>\r\n\r\n      {userActionDialog && userActionDialog.dialogType === 'edit' && (\r\n        <Edit\r\n          dataId={userActionDialog.dataId}\r\n          dataEdit={userActionDialog.dataEdit}\r\n          onDismiss={() => {\r\n            void queryClient.invalidateQueries({ queryKey: [QueryNames.GetIdentityClaimTypes] })\r\n            setUserActionDialog(null)\r\n          }}\r\n        />\r\n      )}\r\n      {userActionDialog && userActionDialog.dialogType === 'delete' && (\r\n        <Delete\r\n          dataId={userActionDialog.dataId}\r\n          onDismiss={() => {\r\n            void queryClient.invalidateQueries({ queryKey: [QueryNames.GetIdentityClaimTypes] })\r\n            setUserActionDialog(null)\r\n          }}\r\n        />\r\n      )}\r\n    </>\r\n  )\r\n}\r\n", "'use client';\r\n\r\nimport { ClaimList } from '@/components/app/claims/List';\r\nimport AppLayout from '../layouts/app-layout';\r\nimport { Head } from '@inertiajs/react';\r\nexport default function OverViewLayout() {\r\n  return (\r\n    <AppLayout>\r\n      <Head title=\"Claim\" />\r\n\r\n      <ClaimList />\r\n    </AppLayout>\r\n  );\r\n}\r\n"], "names": ["Delete", "dataId", "on<PERSON><PERSON><PERSON>", "toast", "useToast", "open", "<PERSON><PERSON><PERSON>", "useState", "onYesEvent", "deleteApiIdentityClaimTypesById", "err", "error", "handleApiError", "useEffect", "jsx", "AlertDialog", "jsxs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Alert<PERSON><PERSON><PERSON>H<PERSON>er", "AlertDialogTitle", "AlertDialogDescription", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AlertDialogCancel", "AlertDialogAction", "Actions", "userId", "userDto", "onAction", "variant", "can", "useGrantedPolicies", "DropdownMenu", "DropdownMenuTrigger", "<PERSON><PERSON>", "RiMoreLine", "DropdownMenuContent", "DropdownMenuItem", "RiShieldKeyholeLine", "RiPencilLine", "getColumns", "handleUserAction", "table", "Checkbox", "row", "column", "DataTableColumnHeader", "info", "customFilterFunction", "valueType", "itemValueType", "Add", "children", "queryClient", "useQueryClient", "handleSubmit", "register", "reset", "useForm", "selectedValueType", "setSelectedValueType", "createDataMutation", "useMutation", "dataMutation", "postApiIdentityClaimTypes", "QueryNames", "onSubmit", "formData", "userData", "handleOpenChange", "newOpenState", "Toaster", "Dialog", "DialogTrigger", "RiAddLine", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "DialogTitle", "e", "FormSection", "FormField", "Input", "value", "MultiSelect", "<PERSON><PERSON><PERSON><PERSON>er", "Edit", "dataEdit", "setValue", "isRequired", "setIsRequired", "isStatic", "setIsStatic", "updateDataMutation", "putApiIdentityClaimTypesById", "onCloseEvent", "valueTypeStr", "checked", "useIdentityClaimTypes", "pageIndex", "pageSize", "filterConditions", "sorting", "useQuery", "body", "generateExtendedQueryParameters", "postApiIdentityClaimTypesList", "title", "description", "extractApiError", "ClaimList", "searchStr", "setSearchStr", "setFilterConditions", "userActionDialog", "setUserActionDialog", "pagination", "setPagination", "setSorting", "isLoading", "data", "columns", "dialogType", "handleSearch", "newFilterConditions", "fc", "currentFiltersStr", "newFiltersStr", "prev", "handlePaginationChange", "newPagination", "handleSortingChange", "newSorting", "handleRefresh", "TableSkeleton", "items", "totalCount", "Fragment", "DataTable", "props", "NotionFilter", "conditions", "currentStr", "newStr", "OverViewLayout", "AppLayout", "Head"], "mappings": "umCAmBO,MAAMA,GAAS,CAAC,CAAE,OAAAC,EAAQ,UAAAC,KAAiC,CAC1D,KAAA,CAAE,MAAAC,CAAM,EAAIC,EAAS,EACrB,CAACC,EAAMC,CAAO,EAAIC,EAAAA,SAAkB,EAAK,EACzCC,EAAa,SAAY,CACzB,GAAA,CACF,MAAMC,EAAgC,CACpC,KAAM,CAAE,GAAIR,CAAO,CAAA,CACpB,EACKE,EAAA,CACJ,MAAO,UACP,YAAa,2CAAA,CACd,EACSD,EAAA,QACHQ,EAAc,CACf,MAAAC,EAAQC,EAAeF,CAAG,EAC1BP,EAAA,CACJ,MAAOQ,EAAM,MACb,YAAaA,EAAM,YACnB,QAAS,OAAA,CACV,CAAA,CAEL,EAEAE,OAAAA,EAAAA,UAAU,IAAM,CACdP,EAAQ,EAAI,CACd,EAAG,EAAE,EAGFQ,EAAA,IAAAC,GAAA,CAAY,KAAAV,EACX,SAAAW,OAACC,GACC,CAAA,SAAA,CAAAD,OAACE,GACC,CAAA,SAAA,CAAAJ,EAAAA,IAACK,IAAiB,SAAwB,0BAAA,CAAA,EAC1CL,EAAAA,IAACM,IAAuB,SAExB,kFAAA,CAAA,CAAA,EACF,SACCC,GACC,CAAA,SAAA,CAACP,EAAA,IAAAQ,GAAA,CAAkB,QAASpB,EAAW,SAAM,SAAA,EAC5CY,EAAA,IAAAS,GAAA,CAAkB,QAASf,EAAY,SAAG,KAAA,CAAA,CAAA,CAC7C,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,EC1CagB,GAAU,CAAC,CAAE,OAAAC,EAAQ,QAAAC,EAAS,SAAAC,EAAU,QAAAC,EAAU,cAAmC,CAC1F,KAAA,CAAE,IAAAC,CAAI,EAAIC,EAAmB,EAGnC,OAAIF,IAAY,WAEXd,EAAA,IAAA,MAAA,CAAI,UAAU,mBACb,gBAACiB,GACC,CAAA,SAAA,CAACjB,EAAAA,IAAAkB,GAAA,CAAoB,QAAO,GAC1B,SAAAhB,EAAA,KAACiB,EAAA,CACC,QAAQ,QACR,KAAK,KACL,UAAU,cAEV,SAAA,CAACnB,EAAAA,IAAAoB,GAAA,CAAW,UAAU,SAAU,CAAA,EAC/BpB,EAAA,IAAA,OAAA,CAAK,UAAU,UAAU,SAAS,WAAA,CAAA,CAAA,CAAA,CAAA,EAEvC,EACCE,EAAA,KAAAmB,GAAA,CAAoB,MAAM,MAAM,UAAU,YACxC,SAAA,CAAAN,EAAI,gCAAgC,GACnCf,EAAA,IAACsB,EAAA,CACC,UAAU,yBACV,QAAS,IAAMT,EAASF,EAAQC,EAAS,MAAM,EAChD,SAAA,MAAA,CAED,EAEDG,EAAI,kCAAkC,GACrCf,EAAA,IAACsB,EAAA,CACC,UAAU,sCACV,QAAS,IAAMT,EAASF,EAAQC,EAAS,QAAQ,EAClD,SAAA,QAAA,CAAA,CAED,CAEJ,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAMFV,EAAA,KAAC,MAAI,CAAA,UAAU,sCACZ,SAAA,CAAAa,EAAI,qCAAqC,GACxCb,EAAA,KAACiB,EAAA,CACC,QAAQ,UACR,KAAK,KACL,UAAU,oCACV,QAAS,IAAMN,EAASF,EAAQC,EAAS,YAAY,EAErD,SAAA,CAACZ,EAAAA,IAAAuB,GAAA,CAAoB,UAAU,SAAU,CAAA,EACzCvB,EAAAA,IAAC,QAAK,SAAU,YAAA,CAAA,CAAA,CAAA,CAClB,EAEDe,EAAI,0BAA0B,GAC7Bb,EAAA,KAACiB,EAAA,CACC,QAAQ,UACR,KAAK,KACL,UAAU,oCACV,QAAS,IAAMN,EAASF,EAAQC,EAAS,MAAM,EAE/C,SAAA,CAACZ,EAAAA,IAAAwB,GAAA,CAAa,UAAU,SAAU,CAAA,EAClCxB,EAAAA,IAAC,QAAK,SAAI,MAAA,CAAA,CAAA,CAAA,CAAA,CACZ,EAEJ,CAEJ,EC3EayB,GACXC,GAEO,CACL,CACE,GAAI,SACJ,OAAQ,CAAC,CAAE,MAAAC,CAAA,IACT3B,EAAA,IAAC4B,EAAA,CACC,QACED,EAAM,2BACF,GACAA,EAAM,sBAAA,EACJ,gBACA,GAER,gBAAiB,IAAMA,EAAM,0BAA0B,EACvD,UAAU,kBACV,aAAW,YAAA,CACb,EAEF,KAAM,CAAC,CAAE,IAAAE,CAAA,IACP7B,EAAA,IAAC4B,EAAA,CACC,QAASC,EAAI,cAAc,EAC3B,gBAAiB,IAAMA,EAAI,eAAe,EAC1C,UAAU,kBACV,aAAW,YAAA,CACb,EAEF,cAAe,GACf,aAAc,GACd,KAAM,CACJ,YAAa,QAAA,CAEjB,EACA,CACE,YAAa,OACb,OAAQ,CAAC,CAAE,OAAAC,CAAA,IACR9B,EAAA,IAAA+B,EAAA,CAAsB,OAAAD,EAAgB,MAAM,OAAO,EAEtD,cAAe,GACf,aAAc,GACd,KAAOE,GAASA,EAAK,SAAS,EAC9B,SAAUC,EACV,KAAM,CACJ,UAAW,YACX,YAAa,MAAA,CAEjB,EACA,CACE,YAAa,cACb,OAAQ,CAAC,CAAE,OAAAH,CAAA,IACR9B,EAAA,IAAA+B,EAAA,CAAsB,OAAAD,EAAgB,MAAM,cAAc,EAE7D,cAAe,GACf,aAAc,GACd,SAAUG,EACV,KAAOD,GAASA,EAAK,SAAS,EAC9B,KAAM,CACJ,UAAW,YACX,YAAa,aAAA,CAEjB,EACA,CACE,YAAa,YACb,OAAQ,CAAC,CAAE,OAAAF,CAAA,IACR9B,EAAA,IAAA+B,EAAA,CAAsB,OAAAD,EAAgB,MAAM,aAAa,EAE5D,KAAM,CAAC,CAAE,IAAAD,KAAU,CACX,MAAAK,EAAYL,EAAI,SAAS,WAAW,EAOnC,MANc,CACnB,EAAG,SACH,EAAG,MACH,EAAG,UACH,EAAG,UACL,EACoBK,CAAsC,GAAK,SACjE,EACA,cAAe,GACf,aAAc,GACd,KAAM,CACJ,UAAW,YACX,YAAa,YAAA,CAEjB,EACA,CACE,GAAI,UACJ,OAAQ,UACR,KAAOF,GACLhC,EAAA,IAACU,GAAA,CACC,OAAQsB,EAAK,IAAI,SAAS,GAC1B,QAASA,EAAK,IAAI,SAClB,SAAUN,EACV,QAAQ,UAAA,CACV,EAEF,cAAe,GACf,aAAc,GACd,KAAM,CACJ,UAAW,aACX,YAAa,QAAA,CACf,CAEJ,ECnHWS,EAAgB,CAC3B,CACE,MAAO,SACP,MAAO,GACT,EACA,CACE,MAAO,MACP,MAAO,GACT,EACA,CACE,MAAO,UACP,MAAO,GACT,EACA,CACE,MAAO,WACP,MAAO,GAAA,CAEX,ECaaC,GAAM,CAAC,CAAE,SAAAC,KAA+B,CAC7C,KAAA,CAAE,IAAAtB,CAAI,EAAIC,EAAmB,EAC7B,CAACzB,EAAMC,CAAO,EAAIC,EAAAA,SAAS,EAAK,EAChC,CAAE,MAAAJ,CAAM,EAAIC,EAAS,EACrBgD,EAAcC,EAAe,EAC7B,CAAE,aAAAC,EAAc,SAAAC,EAAU,MAAAC,CAAA,EAAUC,EAAkC,EACtE,CAACC,EAAmBC,CAAoB,EAAIpD,EAAAA,SAAmB,CAAA,CAAE,EAGvEM,EAAAA,UAAU,IAAM,CACTR,IACGmD,EAAA,CACJ,KAAM,GACN,SAAU,GACV,SAAU,GACV,MAAO,GACP,iBAAkB,GAClB,YAAa,GACb,UAAW,CAAA,CACZ,EACDG,EAAqB,CAAA,CAAE,EACzB,EACC,CAACtD,EAAMmD,CAAK,CAAC,EAGhB,MAAMI,EAAqBC,EAAY,CACrC,WAAY,MAAOC,GACjBC,GAA0B,CACxB,KAAMD,CAAA,CACP,EACH,UAAW,IAAM,CACT3D,EAAA,CACJ,MAAO,UACP,YAAa,kCACb,QAAS,SAAA,CACV,EACIiD,EAAY,kBAAkB,CAAE,SAAU,CAACY,EAAW,qBAAqB,EAAG,EACnF1D,EAAQ,EAAK,CACf,EACA,QAAUI,GAAiB,CAEnB,MAAAC,EAAQC,EAAeF,CAAG,EAC1BP,EAAA,CACJ,MAAOQ,EAAM,MACb,YAAaA,EAAM,YACnB,QAAS,OAAA,CACV,CAAA,CACH,CACD,EAEKsD,EAAYC,GAAuC,CAEvD,MAAMC,EAAqC,CACzC,GAAGD,CACL,EAGKN,EAAmB,OAAOO,CAAQ,CACzC,EAEMC,EAAoBC,GAA0B,CAClD/D,EAAQ+D,CAAY,CACtB,EAEA,cACG,UACC,CAAA,SAAA,CAAAvD,EAAA,IAACwD,GAAQ,EAAA,EACRtD,EAAA,KAAAuD,EAAA,CAAO,KAAAlE,EAAY,aAAc+D,EAChC,SAAA,CAACtD,EAAAA,IAAA0D,GAAA,CAAc,QAAO,GAAE,SAAArB,CAAS,CAAA,QAChC,UAAQ,CAAA,UAAU,yCAChB,SAAAtB,EAAI,kCAAkC,GACrCb,OAACiB,EAAO,CAAA,KAAK,KAAK,UAAU,kCAAkC,QAAS,IAAM3B,EAAQ,EAAI,EACvF,SAAA,CAAAQ,EAAA,IAAC2D,GAAU,CAAA,UAAU,wBAAwB,cAAY,OAAO,EAC/D3D,EAAA,IAAA,OAAA,CAAK,UAAU,4BAA4B,SAAc,gBAAA,CAAA,CAAA,CAAA,CAC5D,CAEJ,CAAA,EACAE,EAAAA,KAAC0D,EAAc,CAAA,UAAU,YACvB,SAAA,CAAA5D,MAAC6D,EACC,CAAA,SAAA7D,EAAA,IAAC8D,EAAY,CAAA,SAAA,yBAAuB,CAAA,EACtC,EACA5D,EAAAA,KAAC,OAAK,CAAA,SAAUsC,EAAaW,CAAQ,EAAG,UAAU,OAAO,UAAYY,GAAM,CACrEA,EAAE,MAAQ,SAAWA,EAAE,kBAAkB,mBAC3CA,EAAE,eAAe,EACZvB,EAAaW,CAAQ,EAAE,EAG9B,EAAA,SAAA,CAAAnD,MAAC,UAAQ,CAAA,UAAU,iCACjB,SAAAE,EAAA,KAAC8D,EACC,CAAA,SAAA,CAAAhE,EAAA,IAACiE,EAAA,CACC,MAAM,OACN,YAAY,wBAEZ,SAAAjE,EAAA,IAACkE,GAAM,SAAQ,GAAE,GAAGzB,EAAS,MAAM,EAAG,YAAY,YAAa,CAAA,CAAA,CACjE,EAEAzC,EAAA,IAACiE,EAAA,CACC,MAAM,WACN,YAAY,gCAEZ,SAACjE,EAAAA,IAAA4B,EAAA,CAAU,GAAGa,EAAS,WAAY,CACjC,WAAa0B,GAAUA,IAAU,EAAA,CAClC,CAAG,CAAA,CAAA,CACN,EAEAnE,EAAA,IAACiE,EAAA,CACC,MAAM,YACN,YAAY,8BAEZ,SAACjE,EAAAA,IAAA4B,EAAA,CAAU,GAAGa,EAAS,WAAY,CACjC,WAAa0B,GAAUA,IAAU,EAAA,CAClC,CAAG,CAAA,CAAA,CACN,EAEAnE,EAAA,IAACiE,EAAA,CACC,MAAM,QACN,YAAY,0BAEZ,eAACC,EAAO,CAAA,GAAGzB,EAAS,OAAO,EAAG,YAAY,OAAQ,CAAA,CAAA,CACpD,EAEAzC,EAAA,IAACiE,EAAA,CACC,MAAM,oBACN,YAAY,gCAEZ,eAACC,EAAO,CAAA,GAAGzB,EAAS,kBAAkB,EAAG,YAAY,mBAAoB,CAAA,CAAA,CAC3E,EAEAzC,EAAA,IAACiE,EAAA,CACC,MAAM,cACN,YAAY,gCAEZ,SAAAjE,EAAA,IAACkE,GAAM,SAAQ,GAAE,GAAGzB,EAAS,aAAa,EAAG,YAAY,aAAc,CAAA,CAAA,CACzE,EAEAzC,EAAA,IAACiE,EAAA,CACC,MAAM,aACN,YAAY,+BAEZ,SAAAjE,EAAA,IAACoE,EAAA,CACC,KAAK,SACL,QAASjC,EACT,MAAOS,EACP,SAAUC,EACV,YAAY,oBACZ,UAAW,IACX,KAAK,YACL,SAAAJ,EACA,cAAe,EAAA,CAAA,CACjB,CAAA,CACF,CAAA,CACF,CACF,CAAA,EACAvC,EAAAA,KAACmE,EAAa,CAAA,UAAU,OACtB,SAAA,CAAArE,EAAA,IAACmB,EAAA,CACC,QAAQ,QACR,QAAU4C,GAAM,CACdA,EAAE,eAAe,EACjBvE,EAAQ,EAAK,CACf,EACA,SAAUsD,EAAmB,UAC9B,SAAA,QAAA,CAED,EACA9C,EAAAA,IAACmB,EAAO,CAAA,KAAK,SAAS,SAAU2B,EAAmB,UAChD,SAAAA,EAAmB,UAAY,YAAc,MAChD,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,CAEJ,ECjLawB,GAAO,CAAC,CAAE,SAAAC,EAAU,OAAApF,EAAQ,UAAAC,KAA+B,CACtE,KAAM,CAACG,EAAMC,CAAO,EAAIC,EAAAA,SAAS,EAAK,EAChC,CAAE,MAAAJ,CAAM,EAAIC,EAAS,EACrBgD,EAAcC,EAAe,EAC7B,CAAE,aAAAC,EAAc,SAAAC,EAAU,SAAA+B,CAAA,EAAa7B,EAAkC,EACzE,CAAC8B,EAAYC,CAAa,EAAIjF,EAAS,SAAA8E,EAAS,UAAY,EAAK,EACjE,CAACI,EAAUC,CAAW,EAAInF,EAAS,SAAA8E,EAAS,UAAY,EAAK,EAC7D,CAAC3B,EAAmBC,CAAoB,EAAIpD,EAAAA,SAAmB,CAAA,CAAE,EAEjEoF,EAAqB9B,EAAY,CACrC,WAAY,MAAOK,GACjB0B,GAA6B,CAC3B,KAAM,CAAE,GAAI3F,CAAO,EACnB,KAAMiE,CAAA,CACP,EACH,UAAW,IAAM,CACT/D,EAAA,CACJ,MAAO,UACP,YAAa,kCACb,QAAS,SAAA,CACV,EACIiD,EAAY,kBAAkB,CAAE,SAAU,CAACY,EAAW,qBAAqB,EAAG,EACtE6B,EAAA,CACf,EACA,QAAUnF,GAAiB,CAEnB,MAAAC,EAAQC,EAAeF,CAAG,EAC1BP,EAAA,CACJ,MAAOQ,EAAM,MACb,YAAaA,EAAM,YACnB,QAAS,OAAA,CACV,CAAA,CACH,CACD,EAEKsD,EAAYC,GAAuC,CAEvD,MAAMC,EAAqC,CACzC,GAAGD,CACL,EAGKyB,EAAmB,OAAOxB,CAAQ,CACzC,EAEM0B,EAAe,IAAM,CACzBvF,EAAQ,EAAK,EACHJ,EAAA,CACZ,EAGAW,OAAAA,EAAAA,UAAU,IAAM,CACd,GAAIR,EAAM,CAECiF,EAAA,OAAQD,EAAS,MAAQ,EAAE,EAC3BC,EAAA,WAAYD,EAAS,UAAY,EAAK,EACtCC,EAAA,WAAYD,EAAS,UAAY,EAAK,EACtCC,EAAA,QAASD,EAAS,OAAS,EAAE,EAC7BC,EAAA,mBAAoBD,EAAS,kBAAoB,EAAE,EACnDC,EAAA,cAAeD,EAAS,aAAe,EAAE,EACzCC,EAAA,YAAaD,EAAS,WAAa,CAAC,EAGvC,MAAAS,EAAgBT,EAAS,YAAc,QAAaA,EAAS,YAAc,KAC7EA,EAAS,UAAU,SACnB,EAAA,IACiB1B,EAAA,CAACmC,CAAY,CAAC,CAAA,CAEpC,EAAA,CAACzF,EAAMgF,EAAUC,CAAQ,CAAC,EAE7BzE,EAAAA,UAAU,IAAM,CACdP,EAAQ,EAAI,CACd,EAAG,EAAE,QAGFiE,EAAO,CAAA,KAAAlE,EAAY,aAAcwF,EAChC,gBAACnB,EACC,CAAA,SAAA,CAAC5D,EAAA,IAAA6D,EAAA,CACC,gBAACC,EAAY,CAAA,SAAA,CAAA,wBAAsBS,EAAS,IAAA,CAAA,CAAK,CACnD,CAAA,EACArE,EAAA,KAAC,OAAA,CACC,SAAUsC,EAAaW,CAAQ,EAC/B,UAAU,OACV,UAAYY,GAAM,CACZA,EAAE,MAAQ,SAAWA,EAAE,kBAAkB,mBAC3CA,EAAE,eAAe,EACZvB,EAAaW,CAAQ,EAAE,EAEhC,EAEA,SAAA,CAAAnD,MAAC,UAAQ,CAAA,UAAU,iCACjB,SAAAE,EAAA,KAAC8D,EACC,CAAA,SAAA,CAAAhE,EAAA,IAACiE,EAAA,CACC,MAAM,OACN,YAAY,wBAEZ,SAACjE,EAAA,IAAAkE,EAAA,CAAM,SAAQ,GAAE,GAAGzB,EAAS,MAAM,EAAG,aAAc8B,EAAS,MAAQ,GAAI,YAAY,YAAa,CAAA,CAAA,CACpG,EAEAvE,EAAA,IAACiE,EAAA,CACC,MAAM,WACN,YAAY,gCAEZ,SAAAjE,EAAA,IAAC4B,EAAA,CACC,QAAS6C,EACT,gBAAkBQ,GAAY,CACdP,EAAA,CAAC,CAACO,CAAO,EACdT,EAAA,WAAY,CAAC,CAACS,CAAO,CAAA,CAChC,CAAA,CACF,CACF,EAEAjF,EAAA,IAACiE,EAAA,CACC,MAAM,YACN,YAAY,8BAEZ,SAAAjE,EAAA,IAAC4B,EAAA,CACC,QAAS+C,EACT,gBAAkBM,GAAY,CAChBL,EAAA,CAAC,CAACK,CAAO,EACZT,EAAA,WAAY,CAAC,CAACS,CAAO,CAAA,CAChC,CAAA,CACF,CACF,EAEAjF,EAAA,IAACiE,EAAA,CACC,MAAM,QACN,YAAY,0BAEZ,SAAAjE,EAAAA,IAACkE,EAAO,CAAA,GAAGzB,EAAS,OAAO,EAAG,aAAc8B,EAAS,OAAS,GAAI,YAAY,OAAQ,CAAA,CAAA,CACxF,EAEAvE,EAAA,IAACiE,EAAA,CACC,MAAM,oBACN,YAAY,gCAEZ,SAAAjE,EAAAA,IAACkE,EAAO,CAAA,GAAGzB,EAAS,kBAAkB,EAAG,aAAc8B,EAAS,kBAAoB,GAAI,YAAY,mBAAoB,CAAA,CAAA,CAC1H,EAEAvE,EAAA,IAACiE,EAAA,CACC,MAAM,cACN,YAAY,gCAEZ,SAACjE,EAAA,IAAAkE,EAAA,CAAM,SAAQ,GAAE,GAAGzB,EAAS,aAAa,EAAG,aAAc8B,EAAS,aAAe,GAAI,YAAY,aAAc,CAAA,CAAA,CACnH,EAEAvE,EAAA,IAACiE,EAAA,CACC,MAAM,aACN,YAAY,+BAEZ,SAAAjE,EAAA,IAACoE,EAAA,CACC,KAAK,SACL,QAASjC,EACT,MAAOS,EACP,SAAUC,EACV,YAAY,oBACZ,UAAW,IACX,KAAK,YACL,SAAAJ,EACA,cAAe,EAAA,CAAA,CACjB,CAAA,CACF,CAAA,CACF,CACF,CAAA,EACAvC,EAAAA,KAACmE,EAAa,CAAA,UAAU,OACtB,SAAA,CAAArE,EAAA,IAACmB,EAAA,CACC,QAAQ,QACR,QAAU4C,GAAM,CACdA,EAAE,eAAe,EACjBvE,EAAQ,EAAK,CACf,EACA,SAAUqF,EAAmB,UAC7B,KAAK,SACN,SAAA,QAAA,CAED,EACA7E,EAAA,IAACmB,EAAA,CACC,KAAK,SACL,SAAU0D,EAAmB,UAE5B,SAAAA,EAAmB,UAAY,YAAc,MAAA,CAAA,CAChD,CACF,CAAA,CAAA,CAAA,CAAA,CACF,CAAA,CACF,CACF,CAAA,CAEJ,EC7MaK,GAAwB,CACnCC,EACAC,EACAC,EAAsC,CAAA,EACtCC,IAEOC,EAAS,CACd,SAAU,CAACrC,EAAW,sBAAwBiC,EAAWC,EAAU,KAAK,UAAUC,CAAgB,EAAGC,CAAO,EAC5G,QAAS,SAAY,CACf,GAAA,CAEF,MAAME,EAAOC,GAAgC,CAC3C,UAAAN,EACA,SAAAC,EACA,QAAAE,EACA,iBAAAD,CAAA,CACD,EAMD,OAJiB,MAAMK,GAA8B,CACnD,KAAAF,CAAA,CACD,GAEe,MAAM,WACf3F,EAAO,CAEf,KAAM,CAAE,MAAA8F,EAAO,YAAAC,CAAA,EAAgBC,GAAgBhG,EAAO,uBAAuB,EAGvE,OAAAR,GAAA,CACJ,MAAAsG,EACA,YAAAC,EACA,QAAS,aAAA,CACV,EAEO,CAAE,MAAO,GAAI,WAAY,CAAE,CAAA,CACpC,CACF,CACD,ECzBUE,GAAY,IAAM,CACvB,KAAA,CAAE,MAAAzG,CAAM,EAAIC,EAAS,EACrBgD,EAAcC,EAAe,EAE7B,CAACwD,EAAWC,CAAY,EAAIvG,EAAAA,SAAiB,EAAE,EAC/C,CAAC4F,EAAkBY,CAAmB,EAAIxG,EAAAA,SAA4B,CAAA,CAAE,EACxE,CAACyG,EAAkBC,CAAmB,EAAI1G,WAItC,EAEJ,CAAC2G,EAAYC,CAAa,EAAI5G,WAA0B,CAC5D,UAAW,EACX,SAAU,EAAA,CACX,EAGK,CAAC6F,EAASgB,CAAU,EAAI7G,WAAuB,CACnD,CAAE,GAAI,OAAQ,KAAM,EAAM,CAAA,CAC3B,EAEK,CAAE,UAAA8G,EAAW,KAAAC,CAAA,EAAStB,GAC1BkB,EAAW,UACXA,EAAW,SACXf,CACF,EAYMoB,EAAUhF,GATS,CAACtC,EAAgBoF,EAAgCmC,IAAiD,CACrGP,EAAA,CAClB,OAAAhH,EACA,SAAAoF,EACA,WAAAmC,CAAA,CACD,CACH,CAG2C,EAErCC,EAAgBxC,GAAkB,CAEtC6B,EAAa7B,CAAK,EAKZ,MAAAyC,EAAsB,CAAC,GADLvB,EAAiB,OAAawB,GAAAA,EAAG,YAAc,MAAM,CAC9B,EAG3C1C,GACFyC,EAAoB,KAAK,CACvB,UAAW,OACX,SAAU,WACV,MAAAzC,CAAA,CACD,EAIG,MAAA2C,EAAoB,KAAK,UAAUzB,CAAgB,EACnD0B,EAAgB,KAAK,UAAUH,CAAmB,EAEpDE,IAAsBC,IACxBd,EAAoBW,CAAmB,EACvCP,MAAuB,CAAE,GAAGW,EAAM,UAAW,GAAI,EAErD,EAEMC,EAA0BC,GAAmC,CACjEb,EAAca,CAAa,CAC7B,EAGMC,EAAuBC,GAA6B,CACxDd,EAAWc,CAAU,EACrBf,MAAuB,CAAE,GAAGW,EAAM,UAAW,GAAI,CACnD,EAGMK,EAAgB,IAAM,CAErB/E,EAAY,kBAAkB,CAAE,SAAU,CAACY,EAAW,qBAAqB,EAAG,EAEnF,WAAW,IAAM,CACT7D,EAAA,CACJ,MAAO,iBACP,YAAa,sCACb,QAAS,SAAA,CACV,GACA,GAAG,CACR,EAEA,GAAIkH,EACF,OAAAvG,EAAA,IAACsH,GAAA,CACC,SAAUlB,EAAW,SACrB,YAAa,EACb,SAAU,GACV,UAAW,GACX,WAAY,GACZ,cAAe,GACf,WAAY,EAAA,CACd,EAII,MAAAmB,EAAQf,GAAM,OAAS,CAAC,EACxBgB,EAAahB,GAAM,YAAc,EAEvC,OAEItG,EAAA,KAAAuH,WAAA,CAAA,SAAA,CAACzH,EAAAA,IAAA,MAAA,CAAI,UAAU,YACb,SAAAA,EAAA,IAAC0H,GAAA,CACC,MAAM,eACN,QAAAjB,EACA,KAAMc,EACN,WAAAC,EACA,UAAAjB,EACA,iBAAkB,GAClB,cAAe,GACf,SAAUH,EAAW,SACrB,mBAAoBa,EACpB,gBAAiBE,EACjB,aAAc7B,EACd,SAAUqB,EACV,YAAaZ,EACb,gBAAkB4B,GAChB3H,EAAA,IAAC4H,GAAA,CACE,GAAGD,EACJ,cAAetC,EACf,eAAiBwC,GAAe,CAExB,MAAAC,EAAa,KAAK,UAAUzC,CAAgB,EAC5C0C,EAAS,KAAK,UAAUF,CAAU,EAEpCC,IAAeC,IACjB9B,EAAoB4B,CAAU,EAC9BxB,MAAuB,CAAE,GAAGW,EAAM,UAAW,GAAI,EACnD,CACF,CACF,EAEF,qBAAsB,GACtB,UAAWK,EACX,mBAAoB,GACpB,aAAc,CAGZ,QAAS,IAAM,CAA8B,EAC7C,cAAUjF,GAAI,CAAA,CAAA,CAAA,CAChB,CAAA,EAEJ,EAEC8D,GAAoBA,EAAiB,aAAe,QACnDlG,EAAA,IAACsE,GAAA,CACC,OAAQ4B,EAAiB,OACzB,SAAUA,EAAiB,SAC3B,UAAW,IAAM,CACV5D,EAAY,kBAAkB,CAAE,SAAU,CAACY,EAAW,qBAAqB,EAAG,EACnFiD,EAAoB,IAAI,CAAA,CAC1B,CACF,EAEDD,GAAoBA,EAAiB,aAAe,UACnDlG,EAAA,IAACd,GAAA,CACC,OAAQgH,EAAiB,OACzB,UAAW,IAAM,CACV5D,EAAY,kBAAkB,CAAE,SAAU,CAACY,EAAW,qBAAqB,EAAG,EACnFiD,EAAoB,IAAI,CAAA,CAC1B,CAAA,CACF,EAEJ,CAEJ,EC7LA,SAAwB6B,IAAiB,CACvC,cACGC,GACC,CAAA,SAAA,CAACjI,EAAAA,IAAAkI,GAAA,CAAK,MAAM,OAAQ,CAAA,QAEnBpC,GAAU,CAAA,CAAA,CAAA,EACb,CAEJ"}