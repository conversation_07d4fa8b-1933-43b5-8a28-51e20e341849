{"version": 3, "file": "tiny-invariant-CopsF_GD.js", "sources": ["../../../../../frontend/node_modules/.pnpm/tiny-invariant@1.3.3/node_modules/tiny-invariant/dist/esm/tiny-invariant.js"], "sourcesContent": ["var isProduction = process.env.NODE_ENV === 'production';\nvar prefix = 'Invariant failed';\nfunction invariant(condition, message) {\n    if (condition) {\n        return;\n    }\n    if (isProduction) {\n        throw new Error(prefix);\n    }\n    var provided = typeof message === 'function' ? message() : message;\n    var value = provided ? \"\".concat(prefix, \": \").concat(provided) : prefix;\n    throw new Error(value);\n}\n\nexport { invariant as default };\n"], "names": ["prefix", "invariant", "condition", "message"], "mappings": "AACA,IAAIA,EAAS,mBACb,SAASC,EAAUC,EAAWC,EAAS,CACnC,GAAI,CAAAD,EAIM,MAAA,IAAI,MAAMF,CAAM,CAK9B", "x_google_ignoreList": [0]}