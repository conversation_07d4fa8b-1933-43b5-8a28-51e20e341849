using System;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.JettyApproval.Attachments;

/// <summary>
/// Application service interface for Attachment entity
/// </summary>
public interface IAttachmentAppService :
    ICrudAppService<
        AttachmentDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateAttachmentDto,
        CreateUpdateAttachmentDto>
{
}