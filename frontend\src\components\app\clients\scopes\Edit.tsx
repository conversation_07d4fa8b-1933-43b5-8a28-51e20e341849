'use client'
import { type CreateUpdateOpenIddictScopeDto, type OpenIddictScopeDto, putApiOpeniddictScopesById } from '@/client'
import { Button } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { useToast } from '@/lib/useToast'
import { QueryNames } from '@/lib/hooks/QueryConstants'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { Toaster } from '@/components/ui/toaster'
import { MultiSelect } from '@/components/ui/multi-select'
import { FormField, FormSection } from '@/components/ui/FormField'
import { Textarea } from '@/components/ui/textarea'
import { useOpeniddictResourceSelect } from '@/lib/hooks/useOpeniddictResourceSelect'
import { handleApiError } from '@/lib/handleApiError'

export type EditDataProps = {
  dataEdit: OpenIddictScopeDto
  dataId: string
  onDismiss: () => void
}

export const Edit = ({ dataEdit, dataId, onDismiss }: EditDataProps) => {
  const [open, setOpen] = useState(true)
  const { toast } = useToast()
  const queryClient = useQueryClient()
  const { handleSubmit, register, setValue, reset } = useForm<CreateUpdateOpenIddictScopeDto>()

  const [selectedResources, setSelectedResources] = useState<string[]>(dataEdit.resources ?? [])
  // Fetch available permissions from the API
  const { data: resourcesData, isLoading: resourcesLoading } = useOpeniddictResourceSelect()

  const resetForm = () => {
    reset({
      name: '',
      displayName: '',
      description: '',
      resources: []
    })
  }

  const createMutation = useMutation({
    mutationFn: async (dataMutation: CreateUpdateOpenIddictScopeDto) =>
      putApiOpeniddictScopesById({
        path: { id: dataId },
        body: dataMutation
      }),
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Scope Updated Successfully',
        variant: 'success',
      })
      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetOpeniddictScopes] })
      resetForm()
      setOpen(false)
      onDismiss()
    },
    onError: (err: unknown) => {
      console.log('Error creating client:', err);
      // Use the global helper to handle API errors
      const error = handleApiError(err);
      toast({
        title: error.title,
        description: error.description,
        variant: 'error',
      })
    }
  })

  const onSubmit = (formData: CreateUpdateOpenIddictScopeDto) => {
    // Merge form data with consent type and permissions
    const scopeData: CreateUpdateOpenIddictScopeDto = {
      ...formData,
      resources: selectedResources
    }

    createMutation.mutate(scopeData)
  }

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      onDismiss()
    }
    setOpen(newOpen)
  }

  return (
    <section>
      <Toaster />
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogContent size="xl">
          <DialogHeader>
            <DialogTitle>Edit Scope</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSubmit(onSubmit)} className='mt-2'>
            <section className="flex w-full flex-col space-y-2">
              <FormSection>
                <FormField
                  label="Name"
                  description="The unique identifier for this resource. Used in requests"
                >
                  <Input required {...register('name')} placeholder="Name" defaultValue={dataEdit.name ?? ''} />
                </FormField>
                <FormField
                  label="Display Name"
                  description="The display name for this resource"
                >
                  <Input required {...register('displayName')} placeholder="Display Name" defaultValue={dataEdit.displayName ?? ''} />
                </FormField>
                <FormField
                  label="Description"
                  description="The description for this resource"
                >
                  <Textarea required {...register('description')} placeholder="Description" defaultValue={dataEdit.description ?? ''} />
                </FormField>

                <FormField
                  label="Resources"
                  description="The resources of the scope"
                >
                  <MultiSelect
                    options={resourcesData?.map(resource => ({
                      value: resource.value ?? '',
                      label: resource.label ?? ''
                    })) ?? []}
                    value={selectedResources}
                    onChange={(values) => {
                      setSelectedResources(values);
                      setValue('resources', values);
                    }}
                    placeholder={resourcesLoading ? "Loading resources..." : "Select resources"}
                    disabled={resourcesLoading}
                    maxHeight={300}
                  />
                </FormField>
              </FormSection>
            </section>
            <DialogFooter className="mt-5">
              <Button
                variant="ghost"
                onClick={(e) => {
                  e.preventDefault()
                  setOpen(false)
                }}
                disabled={createMutation.isPending}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={createMutation.isPending}>
                {createMutation.isPending ? 'Updating...' : 'Update'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </section>
  )
}
