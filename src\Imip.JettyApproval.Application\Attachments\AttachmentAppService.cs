using Imip.JettyApproval.Attachments;
using Imip.JettyApproval.Mapping.Mappers;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.JettyApproval.Attachments;

/// <summary>
/// Application service for Attachment entity
/// </summary>
public class AttachmentAppService :
    CrudAppService<Attachment, AttachmentDto, Guid, PagedAndSortedResultRequestDto, CreateUpdateAttachmentDto, CreateUpdateAttachmentDto>,
    IAttachmentAppService
{
    private readonly IAttachmentRepository _attachmentRepository;
    private readonly AttachmentMapper _mapper;
    private readonly ILogger<AttachmentAppService> _logger;

    public AttachmentAppService(
        IAttachmentRepository attachmentRepository,
        AttachmentMapper mapper,
        ILogger<AttachmentAppService> logger)
        : base(attachmentRepository)
    {
        _attachmentRepository = attachmentRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public override async Task<AttachmentDto> CreateAsync(CreateUpdateAttachmentDto input)
    {
        await CheckCreatePolicyAsync();

        var entity = _mapper.CreateEntityWithId(input, Guid.NewGuid());

        await _attachmentRepository.InsertAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task<AttachmentDto> UpdateAsync(Guid id, CreateUpdateAttachmentDto input)
    {
        await CheckUpdatePolicyAsync();

        var entity = await _attachmentRepository.GetAsync(id);

        _mapper.MapToEntity(input, entity);

        await _attachmentRepository.UpdateAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task DeleteAsync(Guid id)
    {
        await CheckDeletePolicyAsync();

        var entity = await _attachmentRepository.GetAsync(id);

        await _attachmentRepository.DeleteAsync(entity, autoSave: true);
    }

    public override async Task<AttachmentDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var entity = await _attachmentRepository.GetAsync(id);
        return _mapper.MapToDto(entity);
    }

    public override async Task<PagedResultDto<AttachmentDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        await CheckGetListPolicyAsync();

        var queryable = await _attachmentRepository.GetQueryableAsync();

        // Apply sorting
        if (!string.IsNullOrWhiteSpace(input.Sorting))
        {
            queryable = queryable.OrderBy(input.Sorting);
        }
        else
        {
            queryable = queryable.OrderByDescending(x => x.CreationTime);
        }

        var totalCount = await AsyncExecuter.CountAsync(queryable);

        var entities = await AsyncExecuter.ToListAsync(
            queryable.PageBy(input.SkipCount, input.MaxResultCount)
        );

        var dtos = entities.Select(_mapper.MapToDto).ToList();

        return new PagedResultDto<AttachmentDto>(totalCount, dtos);
    }
}