using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace Imip.JettyApproval.DocumentTemplates;

/// <summary>
/// Repository interface for document templates
/// </summary>
public interface IDocumentTemplateRepository : IRepository<DocumentTemplate, Guid>
{
    /// <summary>
    /// Gets document templates by document type
    /// </summary>
    Task<List<DocumentTemplate>> GetByDocumentTypeAsync(DocumentType documentType);

    /// <summary>
    /// Gets the default template for a document type
    /// </summary>
    Task<DocumentTemplate?> GetDefaultTemplateAsync(DocumentType documentType);
}

