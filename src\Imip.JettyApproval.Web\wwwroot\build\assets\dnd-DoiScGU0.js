import{r as l,b as M}from"./vendor-CrSBzUoz.js";import{r as we}from"./radix-DaY-mnHi.js";const $e=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";function ge(e){const t=Object.prototype.toString.call(e);return t==="[object Window]"||t==="[object global]"}function it(e){return"nodeType"in e}function P(e){var t,n;return e?ge(e)?e:it(e)&&(t=(n=e.ownerDocument)==null?void 0:n.defaultView)!=null?t:window:window}function st(e){const{Document:t}=P(e);return e instanceof t}function Me(e){return ge(e)?!1:e instanceof P(e).HTMLElement}function Yt(e){return e instanceof P(e).SVGElement}function ve(e){return e?ge(e)?e.document:it(e)?st(e)?e:Me(e)||Yt(e)?e.ownerDocument:document:document:document}const G=$e?l.useLayoutEffect:l.useEffect;function Ke(e){const t=l.useRef(e);return G(()=>{t.current=e}),l.useCallback(function(){for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return t.current==null?void 0:t.current(...r)},[])}function pn(){const e=l.useRef(null),t=l.useCallback((r,o)=>{e.current=setInterval(r,o)},[]),n=l.useCallback(()=>{e.current!==null&&(clearInterval(e.current),e.current=null)},[]);return[t,n]}function Se(e,t){t===void 0&&(t=[e]);const n=l.useRef(e);return G(()=>{n.current!==e&&(n.current=e)},t),n}function Ae(e,t){const n=l.useRef();return l.useMemo(()=>{const r=e(n.current);return n.current=r,r},[...t])}function ze(e){const t=Ke(e),n=l.useRef(null),r=l.useCallback(o=>{o!==n.current&&t?.(o,n.current),n.current=o},[]);return[n,r]}function Be(e){const t=l.useRef();return l.useEffect(()=>{t.current=e},[e]),t.current}let Qe={};function Ue(e,t){return l.useMemo(()=>{if(t)return t;const n=Qe[e]==null?0:Qe[e]+1;return Qe[e]=n,e+"-"+n},[e,t])}function $t(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return r.reduce((i,a)=>{const s=Object.entries(a);for(const[c,u]of s){const f=i[c];f!=null&&(i[c]=f+e*u)}return i},{...t})}}const he=$t(1),Fe=$t(-1);function bn(e){return"clientX"in e&&"clientY"in e}function at(e){if(!e)return!1;const{KeyboardEvent:t}=P(e.target);return t&&e instanceof t}function mn(e){if(!e)return!1;const{TouchEvent:t}=P(e.target);return t&&e instanceof t}function je(e){if(mn(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}else if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return bn(e)?{x:e.clientX,y:e.clientY}:null}const Re=Object.freeze({Translate:{toString(e){if(!e)return;const{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;const{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(e)return[Re.Translate.toString(e),Re.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:r}=e;return t+" "+n+"ms "+r}}}),Ot="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function yn(e){return e.matches(Ot)?e:e.querySelector(Ot)}const wn={display:"none"};function xn(e){let{id:t,value:n}=e;return M.createElement("div",{id:t,style:wn},n)}function Dn(e){let{id:t,announcement:n,ariaLiveType:r="assertive"}=e;const o={position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"};return M.createElement("div",{id:t,style:o,role:"status","aria-live":r,"aria-atomic":!0},n)}function Cn(){const[e,t]=l.useState("");return{announce:l.useCallback(r=>{r!=null&&t(r)},[]),announcement:e}}const Kt=l.createContext(null);function Sn(e){const t=l.useContext(Kt);l.useEffect(()=>{if(!t)throw new Error("useDndMonitor must be used within a children of <DndContext>");return t(e)},[e,t])}function Rn(){const[e]=l.useState(()=>new Set),t=l.useCallback(r=>(e.add(r),()=>e.delete(r)),[e]);return[l.useCallback(r=>{let{type:o,event:i}=r;e.forEach(a=>{var s;return(s=a[o])==null?void 0:s.call(a,i)})},[e]),t]}const En={draggable:`
    To pick up a draggable item, press the space bar.
    While dragging, use the arrow keys to move the item.
    Press space again to drop the item in its new position, or press escape to cancel.
  `},Mn={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was moved over droppable area "+n.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was dropped over droppable area "+n.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function An(e){let{announcements:t=Mn,container:n,hiddenTextDescribedById:r,screenReaderInstructions:o=En}=e;const{announce:i,announcement:a}=Cn(),s=Ue("DndLiveRegion"),[c,u]=l.useState(!1);if(l.useEffect(()=>{u(!0)},[]),Sn(l.useMemo(()=>({onDragStart(d){let{active:v}=d;i(t.onDragStart({active:v}))},onDragMove(d){let{active:v,over:h}=d;t.onDragMove&&i(t.onDragMove({active:v,over:h}))},onDragOver(d){let{active:v,over:h}=d;i(t.onDragOver({active:v,over:h}))},onDragEnd(d){let{active:v,over:h}=d;i(t.onDragEnd({active:v,over:h}))},onDragCancel(d){let{active:v,over:h}=d;i(t.onDragCancel({active:v,over:h}))}}),[i,t])),!c)return null;const f=M.createElement(M.Fragment,null,M.createElement(xn,{id:r,value:o.draggable}),M.createElement(Dn,{id:s,announcement:a}));return n?we.createPortal(f,n):f}var O;(function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"})(O||(O={}));function Xe(){}function Ur(e,t){return l.useMemo(()=>({sensor:e,options:t??{}}),[e,t])}function Wr(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return l.useMemo(()=>[...t].filter(r=>r!=null),[...t])}const K=Object.freeze({x:0,y:0});function On(e,t){const n=je(e);if(!n)return"0 0";const r={x:(n.x-t.left)/t.width*100,y:(n.y-t.top)/t.height*100};return r.x+"% "+r.y+"%"}function Nn(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return r-n}function Tn(e,t){if(!e||e.length===0)return null;const[n]=e;return n[t]}function Ln(e,t){const n=Math.max(t.top,e.top),r=Math.max(t.left,e.left),o=Math.min(t.left+t.width,e.left+e.width),i=Math.min(t.top+t.height,e.top+e.height),a=o-r,s=i-n;if(r<o&&n<i){const c=t.width*t.height,u=e.width*e.height,f=a*s,d=f/(c+u-f);return Number(d.toFixed(4))}return 0}const kn=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const o=[];for(const i of r){const{id:a}=i,s=n.get(a);if(s){const c=Ln(s,t);c>0&&o.push({id:a,data:{droppableContainer:i,value:c}})}}return o.sort(Nn)};function In(e,t,n){return{...e,scaleX:t&&n?t.width/n.width:1,scaleY:t&&n?t.height/n.height:1}}function Ut(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:K}function Pn(e){return function(n){for(var r=arguments.length,o=new Array(r>1?r-1:0),i=1;i<r;i++)o[i-1]=arguments[i];return o.reduce((a,s)=>({...a,top:a.top+e*s.y,bottom:a.bottom+e*s.y,left:a.left+e*s.x,right:a.right+e*s.x}),{...n})}}const zn=Pn(1);function Wt(e){if(e.startsWith("matrix3d(")){const t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}else if(e.startsWith("matrix(")){const t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}function Bn(e,t,n){const r=Wt(t);if(!r)return e;const{scaleX:o,scaleY:i,x:a,y:s}=r,c=e.left-a-(1-o)*parseFloat(n),u=e.top-s-(1-i)*parseFloat(n.slice(n.indexOf(" ")+1)),f=o?e.width/o:e.width,d=i?e.height/i:e.height;return{width:f,height:d,top:u,right:c+f,bottom:u+d,left:c}}const Fn={ignoreTransform:!1};function Oe(e,t){t===void 0&&(t=Fn);let n=e.getBoundingClientRect();if(t.ignoreTransform){const{transform:u,transformOrigin:f}=P(e).getComputedStyle(e);u&&(n=Bn(n,u,f))}const{top:r,left:o,width:i,height:a,bottom:s,right:c}=n;return{top:r,left:o,width:i,height:a,bottom:s,right:c}}function Nt(e){return Oe(e,{ignoreTransform:!0})}function jn(e){const t=e.innerWidth,n=e.innerHeight;return{top:0,left:0,right:t,bottom:n,width:t,height:n}}function Xn(e,t){return t===void 0&&(t=P(e).getComputedStyle(e)),t.position==="fixed"}function Yn(e,t){t===void 0&&(t=P(e).getComputedStyle(e));const n=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some(o=>{const i=t[o];return typeof i=="string"?n.test(i):!1})}function lt(e,t){const n=[];function r(o){if(t!=null&&n.length>=t||!o)return n;if(st(o)&&o.scrollingElement!=null&&!n.includes(o.scrollingElement))return n.push(o.scrollingElement),n;if(!Me(o)||Yt(o)||n.includes(o))return n;const i=P(e).getComputedStyle(o);return o!==e&&Yn(o,i)&&n.push(o),Xn(o,i)?n:r(o.parentNode)}return e?r(e):n}function Ht(e){const[t]=lt(e,1);return t??null}function Ze(e){return!$e||!e?null:ge(e)?e:it(e)?st(e)||e===ve(e).scrollingElement?window:Me(e)?e:null:null}function Vt(e){return ge(e)?e.scrollX:e.scrollLeft}function qt(e){return ge(e)?e.scrollY:e.scrollTop}function nt(e){return{x:Vt(e),y:qt(e)}}var T;(function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"})(T||(T={}));function Jt(e){return!$e||!e?!1:e===document.scrollingElement}function Gt(e){const t={x:0,y:0},n=Jt(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},r={x:e.scrollWidth-n.width,y:e.scrollHeight-n.height},o=e.scrollTop<=t.y,i=e.scrollLeft<=t.x,a=e.scrollTop>=r.y,s=e.scrollLeft>=r.x;return{isTop:o,isLeft:i,isBottom:a,isRight:s,maxScroll:r,minScroll:t}}const $n={x:.2,y:.2};function Kn(e,t,n,r,o){let{top:i,left:a,right:s,bottom:c}=n;r===void 0&&(r=10),o===void 0&&(o=$n);const{isTop:u,isBottom:f,isLeft:d,isRight:v}=Gt(e),h={x:0,y:0},C={x:0,y:0},g={height:t.height*o.y,width:t.width*o.x};return!u&&i<=t.top+g.height?(h.y=T.Backward,C.y=r*Math.abs((t.top+g.height-i)/g.height)):!f&&c>=t.bottom-g.height&&(h.y=T.Forward,C.y=r*Math.abs((t.bottom-g.height-c)/g.height)),!v&&s>=t.right-g.width?(h.x=T.Forward,C.x=r*Math.abs((t.right-g.width-s)/g.width)):!d&&a<=t.left+g.width&&(h.x=T.Backward,C.x=r*Math.abs((t.left+g.width-a)/g.width)),{direction:h,speed:C}}function Un(e){if(e===document.scrollingElement){const{innerWidth:i,innerHeight:a}=window;return{top:0,left:0,right:i,bottom:a,width:i,height:a}}const{top:t,left:n,right:r,bottom:o}=e.getBoundingClientRect();return{top:t,left:n,right:r,bottom:o,width:e.clientWidth,height:e.clientHeight}}function _t(e){return e.reduce((t,n)=>he(t,nt(n)),K)}function Wn(e){return e.reduce((t,n)=>t+Vt(n),0)}function Hn(e){return e.reduce((t,n)=>t+qt(n),0)}function Qt(e,t){if(t===void 0&&(t=Oe),!e)return;const{top:n,left:r,bottom:o,right:i}=t(e);Ht(e)&&(o<=0||i<=0||n>=window.innerHeight||r>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}const Vn=[["x",["left","right"],Wn],["y",["top","bottom"],Hn]];class ct{constructor(t,n){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;const r=lt(n),o=_t(r);this.rect={...t},this.width=t.width,this.height=t.height;for(const[i,a,s]of Vn)for(const c of a)Object.defineProperty(this,c,{get:()=>{const u=s(r),f=o[i]-u;return this.rect[c]+f},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class xe{constructor(t){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach(n=>{var r;return(r=this.target)==null?void 0:r.removeEventListener(...n)})},this.target=t}add(t,n,r){var o;(o=this.target)==null||o.addEventListener(t,n,r),this.listeners.push([t,n,r])}}function qn(e){const{EventTarget:t}=P(e);return e instanceof t?e:ve(e)}function et(e,t){const n=Math.abs(e.x),r=Math.abs(e.y);return typeof t=="number"?Math.sqrt(n**2+r**2)>t:"x"in t&&"y"in t?n>t.x&&r>t.y:"x"in t?n>t.x:"y"in t?r>t.y:!1}var X;(function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"})(X||(X={}));function Tt(e){e.preventDefault()}function Jn(e){e.stopPropagation()}var D;(function(e){e.Space="Space",e.Down="ArrowDown",e.Right="ArrowRight",e.Left="ArrowLeft",e.Up="ArrowUp",e.Esc="Escape",e.Enter="Enter",e.Tab="Tab"})(D||(D={}));const Zt={start:[D.Space,D.Enter],cancel:[D.Esc],end:[D.Space,D.Enter,D.Tab]},Gn=(e,t)=>{let{currentCoordinates:n}=t;switch(e.code){case D.Right:return{...n,x:n.x+25};case D.Left:return{...n,x:n.x-25};case D.Down:return{...n,y:n.y+25};case D.Up:return{...n,y:n.y-25}}};class en{constructor(t){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=t;const{event:{target:n}}=t;this.props=t,this.listeners=new xe(ve(n)),this.windowListeners=new xe(P(n)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(X.Resize,this.handleCancel),this.windowListeners.add(X.VisibilityChange,this.handleCancel),setTimeout(()=>this.listeners.add(X.Keydown,this.handleKeyDown))}handleStart(){const{activeNode:t,onStart:n}=this.props,r=t.node.current;r&&Qt(r),n(K)}handleKeyDown(t){if(at(t)){const{active:n,context:r,options:o}=this.props,{keyboardCodes:i=Zt,coordinateGetter:a=Gn,scrollBehavior:s="smooth"}=o,{code:c}=t;if(i.end.includes(c)){this.handleEnd(t);return}if(i.cancel.includes(c)){this.handleCancel(t);return}const{collisionRect:u}=r.current,f=u?{x:u.left,y:u.top}:K;this.referenceCoordinates||(this.referenceCoordinates=f);const d=a(t,{active:n,context:r.current,currentCoordinates:f});if(d){const v=Fe(d,f),h={x:0,y:0},{scrollableAncestors:C}=r.current;for(const g of C){const p=t.code,{isTop:y,isRight:x,isLeft:w,isBottom:N,maxScroll:S,minScroll:R}=Gt(g),b=Un(g),m={x:Math.min(p===D.Right?b.right-b.width/2:b.right,Math.max(p===D.Right?b.left:b.left+b.width/2,d.x)),y:Math.min(p===D.Down?b.bottom-b.height/2:b.bottom,Math.max(p===D.Down?b.top:b.top+b.height/2,d.y))},E=p===D.Right&&!x||p===D.Left&&!w,L=p===D.Down&&!N||p===D.Up&&!y;if(E&&m.x!==d.x){const A=g.scrollLeft+v.x,H=p===D.Right&&A<=S.x||p===D.Left&&A>=R.x;if(H&&!v.y){g.scrollTo({left:A,behavior:s});return}H?h.x=g.scrollLeft-A:h.x=p===D.Right?g.scrollLeft-S.x:g.scrollLeft-R.x,h.x&&g.scrollBy({left:-h.x,behavior:s});break}else if(L&&m.y!==d.y){const A=g.scrollTop+v.y,H=p===D.Down&&A<=S.y||p===D.Up&&A>=R.y;if(H&&!v.x){g.scrollTo({top:A,behavior:s});return}H?h.y=g.scrollTop-A:h.y=p===D.Down?g.scrollTop-S.y:g.scrollTop-R.y,h.y&&g.scrollBy({top:-h.y,behavior:s});break}}this.handleMove(t,he(Fe(d,this.referenceCoordinates),h))}}}handleMove(t,n){const{onMove:r}=this.props;t.preventDefault(),r(n)}handleEnd(t){const{onEnd:n}=this.props;t.preventDefault(),this.detach(),n()}handleCancel(t){const{onCancel:n}=this.props;t.preventDefault(),this.detach(),n()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}en.activators=[{eventName:"onKeyDown",handler:(e,t,n)=>{let{keyboardCodes:r=Zt,onActivation:o}=t,{active:i}=n;const{code:a}=e.nativeEvent;if(r.start.includes(a)){const s=i.activatorNode.current;return s&&e.target!==s?!1:(e.preventDefault(),o?.({event:e.nativeEvent}),!0)}return!1}}];function Lt(e){return!!(e&&"distance"in e)}function kt(e){return!!(e&&"delay"in e)}class ut{constructor(t,n,r){var o;r===void 0&&(r=qn(t.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=t,this.events=n;const{event:i}=t,{target:a}=i;this.props=t,this.events=n,this.document=ve(a),this.documentListeners=new xe(this.document),this.listeners=new xe(r),this.windowListeners=new xe(P(a)),this.initialCoordinates=(o=je(i))!=null?o:K,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:t,props:{options:{activationConstraint:n,bypassActivationConstraint:r}}}=this;if(this.listeners.add(t.move.name,this.handleMove,{passive:!1}),this.listeners.add(t.end.name,this.handleEnd),t.cancel&&this.listeners.add(t.cancel.name,this.handleCancel),this.windowListeners.add(X.Resize,this.handleCancel),this.windowListeners.add(X.DragStart,Tt),this.windowListeners.add(X.VisibilityChange,this.handleCancel),this.windowListeners.add(X.ContextMenu,Tt),this.documentListeners.add(X.Keydown,this.handleKeydown),n){if(r!=null&&r({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(kt(n)){this.timeoutId=setTimeout(this.handleStart,n.delay),this.handlePending(n);return}if(Lt(n)){this.handlePending(n);return}}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),this.timeoutId!==null&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handlePending(t,n){const{active:r,onPending:o}=this.props;o(r,t,this.initialCoordinates,n)}handleStart(){const{initialCoordinates:t}=this,{onStart:n}=this.props;t&&(this.activated=!0,this.documentListeners.add(X.Click,Jn,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(X.SelectionChange,this.removeTextSelection),n(t))}handleMove(t){var n;const{activated:r,initialCoordinates:o,props:i}=this,{onMove:a,options:{activationConstraint:s}}=i;if(!o)return;const c=(n=je(t))!=null?n:K,u=Fe(o,c);if(!r&&s){if(Lt(s)){if(s.tolerance!=null&&et(u,s.tolerance))return this.handleCancel();if(et(u,s.distance))return this.handleStart()}if(kt(s)&&et(u,s.tolerance))return this.handleCancel();this.handlePending(s,u);return}t.cancelable&&t.preventDefault(),a(c)}handleEnd(){const{onAbort:t,onEnd:n}=this.props;this.detach(),this.activated||t(this.props.active),n()}handleCancel(){const{onAbort:t,onCancel:n}=this.props;this.detach(),this.activated||t(this.props.active),n()}handleKeydown(t){t.code===D.Esc&&this.handleCancel()}removeTextSelection(){var t;(t=this.document.getSelection())==null||t.removeAllRanges()}}const _n={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class tn extends ut{constructor(t){const{event:n}=t,r=ve(n.target);super(t,_n,r)}}tn.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return!n.isPrimary||n.button!==0?!1:(r?.({event:n}),!0)}}];const Qn={move:{name:"mousemove"},end:{name:"mouseup"}};var rt;(function(e){e[e.RightClick=2]="RightClick"})(rt||(rt={}));class Zn extends ut{constructor(t){super(t,Qn,ve(t.event.target))}}Zn.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return n.button===rt.RightClick?!1:(r?.({event:n}),!0)}}];const tt={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};class er extends ut{constructor(t){super(t,tt)}static setup(){return window.addEventListener(tt.move.name,t,{capture:!1,passive:!1}),function(){window.removeEventListener(tt.move.name,t)};function t(){}}}er.activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;const{touches:o}=n;return o.length>1?!1:(r?.({event:n}),!0)}}];var De;(function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"})(De||(De={}));var Ye;(function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"})(Ye||(Ye={}));function tr(e){let{acceleration:t,activator:n=De.Pointer,canScroll:r,draggingRect:o,enabled:i,interval:a=5,order:s=Ye.TreeOrder,pointerCoordinates:c,scrollableAncestors:u,scrollableAncestorRects:f,delta:d,threshold:v}=e;const h=rr({delta:d,disabled:!i}),[C,g]=pn(),p=l.useRef({x:0,y:0}),y=l.useRef({x:0,y:0}),x=l.useMemo(()=>{switch(n){case De.Pointer:return c?{top:c.y,bottom:c.y,left:c.x,right:c.x}:null;case De.DraggableRect:return o}},[n,o,c]),w=l.useRef(null),N=l.useCallback(()=>{const R=w.current;if(!R)return;const b=p.current.x*y.current.x,m=p.current.y*y.current.y;R.scrollBy(b,m)},[]),S=l.useMemo(()=>s===Ye.TreeOrder?[...u].reverse():u,[s,u]);l.useEffect(()=>{if(!i||!u.length||!x){g();return}for(const R of S){if(r?.(R)===!1)continue;const b=u.indexOf(R),m=f[b];if(!m)continue;const{direction:E,speed:L}=Kn(R,m,x,t,v);for(const A of["x","y"])h[A][E[A]]||(L[A]=0,E[A]=0);if(L.x>0||L.y>0){g(),w.current=R,C(N,a),p.current=L,y.current=E;return}}p.current={x:0,y:0},y.current={x:0,y:0},g()},[t,N,r,g,i,a,JSON.stringify(x),JSON.stringify(h),C,u,S,f,JSON.stringify(v)])}const nr={x:{[T.Backward]:!1,[T.Forward]:!1},y:{[T.Backward]:!1,[T.Forward]:!1}};function rr(e){let{delta:t,disabled:n}=e;const r=Be(t);return Ae(o=>{if(n||!r||!o)return nr;const i={x:Math.sign(t.x-r.x),y:Math.sign(t.y-r.y)};return{x:{[T.Backward]:o.x[T.Backward]||i.x===-1,[T.Forward]:o.x[T.Forward]||i.x===1},y:{[T.Backward]:o.y[T.Backward]||i.y===-1,[T.Forward]:o.y[T.Forward]||i.y===1}}},[n,t,r])}function or(e,t){const n=t!=null?e.get(t):void 0,r=n?n.node.current:null;return Ae(o=>{var i;return t==null?null:(i=r??o)!=null?i:null},[r,t])}function ir(e,t){return l.useMemo(()=>e.reduce((n,r)=>{const{sensor:o}=r,i=o.activators.map(a=>({eventName:a.eventName,handler:t(a.handler,r)}));return[...n,...i]},[]),[e,t])}var Ee;(function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"})(Ee||(Ee={}));var ot;(function(e){e.Optimized="optimized"})(ot||(ot={}));const It=new Map;function sr(e,t){let{dragging:n,dependencies:r,config:o}=t;const[i,a]=l.useState(null),{frequency:s,measure:c,strategy:u}=o,f=l.useRef(e),d=p(),v=Se(d),h=l.useCallback(function(y){y===void 0&&(y=[]),!v.current&&a(x=>x===null?y:x.concat(y.filter(w=>!x.includes(w))))},[v]),C=l.useRef(null),g=Ae(y=>{if(d&&!n)return It;if(!y||y===It||f.current!==e||i!=null){const x=new Map;for(let w of e){if(!w)continue;if(i&&i.length>0&&!i.includes(w.id)&&w.rect.current){x.set(w.id,w.rect.current);continue}const N=w.node.current,S=N?new ct(c(N),N):null;w.rect.current=S,S&&x.set(w.id,S)}return x}return y},[e,i,n,d,c]);return l.useEffect(()=>{f.current=e},[e]),l.useEffect(()=>{d||h()},[n,d]),l.useEffect(()=>{i&&i.length>0&&a(null)},[JSON.stringify(i)]),l.useEffect(()=>{d||typeof s!="number"||C.current!==null||(C.current=setTimeout(()=>{h(),C.current=null},s))},[s,d,h,...r]),{droppableRects:g,measureDroppableContainers:h,measuringScheduled:i!=null};function p(){switch(u){case Ee.Always:return!1;case Ee.BeforeDragging:return n;default:return!n}}}function dt(e,t){return Ae(n=>e?n||(typeof t=="function"?t(e):e):null,[t,e])}function ar(e,t){return dt(e,t)}function lr(e){let{callback:t,disabled:n}=e;const r=Ke(t),o=l.useMemo(()=>{if(n||typeof window>"u"||typeof window.MutationObserver>"u")return;const{MutationObserver:i}=window;return new i(r)},[r,n]);return l.useEffect(()=>()=>o?.disconnect(),[o]),o}function We(e){let{callback:t,disabled:n}=e;const r=Ke(t),o=l.useMemo(()=>{if(n||typeof window>"u"||typeof window.ResizeObserver>"u")return;const{ResizeObserver:i}=window;return new i(r)},[n]);return l.useEffect(()=>()=>o?.disconnect(),[o]),o}function cr(e){return new ct(Oe(e),e)}function Pt(e,t,n){t===void 0&&(t=cr);const[r,o]=l.useState(null);function i(){o(c=>{if(!e)return null;if(e.isConnected===!1){var u;return(u=c??n)!=null?u:null}const f=t(e);return JSON.stringify(c)===JSON.stringify(f)?c:f})}const a=lr({callback(c){if(e)for(const u of c){const{type:f,target:d}=u;if(f==="childList"&&d instanceof HTMLElement&&d.contains(e)){i();break}}}}),s=We({callback:i});return G(()=>{i(),e?(s?.observe(e),a?.observe(document.body,{childList:!0,subtree:!0})):(s?.disconnect(),a?.disconnect())},[e]),r}function ur(e){const t=dt(e);return Ut(e,t)}const zt=[];function dr(e){const t=l.useRef(e),n=Ae(r=>e?r&&r!==zt&&e&&t.current&&e.parentNode===t.current.parentNode?r:lt(e):zt,[e]);return l.useEffect(()=>{t.current=e},[e]),n}function fr(e){const[t,n]=l.useState(null),r=l.useRef(e),o=l.useCallback(i=>{const a=Ze(i.target);a&&n(s=>s?(s.set(a,nt(a)),new Map(s)):null)},[]);return l.useEffect(()=>{const i=r.current;if(e!==i){a(i);const s=e.map(c=>{const u=Ze(c);return u?(u.addEventListener("scroll",o,{passive:!0}),[u,nt(u)]):null}).filter(c=>c!=null);n(s.length?new Map(s):null),r.current=e}return()=>{a(e),a(i)};function a(s){s.forEach(c=>{const u=Ze(c);u?.removeEventListener("scroll",o)})}},[o,e]),l.useMemo(()=>e.length?t?Array.from(t.values()).reduce((i,a)=>he(i,a),K):_t(e):K,[e,t])}function Bt(e,t){t===void 0&&(t=[]);const n=l.useRef(null);return l.useEffect(()=>{n.current=null},t),l.useEffect(()=>{const r=e!==K;r&&!n.current&&(n.current=e),!r&&n.current&&(n.current=null)},[e]),n.current?Fe(e,n.current):K}function hr(e){l.useEffect(()=>{if(!$e)return;const t=e.map(n=>{let{sensor:r}=n;return r.setup==null?void 0:r.setup()});return()=>{for(const n of t)n?.()}},e.map(t=>{let{sensor:n}=t;return n}))}function gr(e,t){return l.useMemo(()=>e.reduce((n,r)=>{let{eventName:o,handler:i}=r;return n[o]=a=>{i(a,t)},n},{}),[e,t])}function nn(e){return l.useMemo(()=>e?jn(e):null,[e])}const Ft=[];function vr(e,t){t===void 0&&(t=Oe);const[n]=e,r=nn(n?P(n):null),[o,i]=l.useState(Ft);function a(){i(()=>e.length?e.map(c=>Jt(c)?r:new ct(t(c),c)):Ft)}const s=We({callback:a});return G(()=>{s?.disconnect(),a(),e.forEach(c=>s?.observe(c))},[e]),o}function rn(e){if(!e)return null;if(e.children.length>1)return e;const t=e.children[0];return Me(t)?t:e}function pr(e){let{measure:t}=e;const[n,r]=l.useState(null),o=l.useCallback(u=>{for(const{target:f}of u)if(Me(f)){r(d=>{const v=t(f);return d?{...d,width:v.width,height:v.height}:v});break}},[t]),i=We({callback:o}),a=l.useCallback(u=>{const f=rn(u);i?.disconnect(),f&&i?.observe(f),r(f?t(f):null)},[t,i]),[s,c]=ze(a);return l.useMemo(()=>({nodeRef:s,rect:n,setRef:c}),[n,s,c])}const br=[{sensor:tn,options:{}},{sensor:en,options:{}}],mr={current:{}},Pe={draggable:{measure:Nt},droppable:{measure:Nt,strategy:Ee.WhileDragging,frequency:ot.Optimized},dragOverlay:{measure:Oe}};class Ce extends Map{get(t){var n;return t!=null&&(n=super.get(t))!=null?n:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter(t=>{let{disabled:n}=t;return!n})}getNodeFor(t){var n,r;return(n=(r=this.get(t))==null?void 0:r.node.current)!=null?n:void 0}}const yr={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new Ce,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:Xe},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:Pe,measureDroppableContainers:Xe,windowRect:null,measuringScheduled:!1},on={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:Xe,draggableNodes:new Map,over:null,measureDroppableContainers:Xe},Ne=l.createContext(on),sn=l.createContext(yr);function wr(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new Ce}}}function xr(e,t){switch(t.type){case O.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case O.DragMove:return e.draggable.active==null?e:{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}};case O.DragEnd:case O.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case O.RegisterDroppable:{const{element:n}=t,{id:r}=n,o=new Ce(e.droppable.containers);return o.set(r,n),{...e,droppable:{...e.droppable,containers:o}}}case O.SetDroppableDisabled:{const{id:n,key:r,disabled:o}=t,i=e.droppable.containers.get(n);if(!i||r!==i.key)return e;const a=new Ce(e.droppable.containers);return a.set(n,{...i,disabled:o}),{...e,droppable:{...e.droppable,containers:a}}}case O.UnregisterDroppable:{const{id:n,key:r}=t,o=e.droppable.containers.get(n);if(!o||r!==o.key)return e;const i=new Ce(e.droppable.containers);return i.delete(n),{...e,droppable:{...e.droppable,containers:i}}}default:return e}}function Dr(e){let{disabled:t}=e;const{active:n,activatorEvent:r,draggableNodes:o}=l.useContext(Ne),i=Be(r),a=Be(n?.id);return l.useEffect(()=>{if(!t&&!r&&i&&a!=null){if(!at(i)||document.activeElement===i.target)return;const s=o.get(a);if(!s)return;const{activatorNode:c,node:u}=s;if(!c.current&&!u.current)return;requestAnimationFrame(()=>{for(const f of[c.current,u.current]){if(!f)continue;const d=yn(f);if(d){d.focus();break}}})}},[r,t,o,a,i]),null}function an(e,t){let{transform:n,...r}=t;return e!=null&&e.length?e.reduce((o,i)=>i({transform:o,...r}),n):n}function Cr(e){return l.useMemo(()=>({draggable:{...Pe.draggable,...e?.draggable},droppable:{...Pe.droppable,...e?.droppable},dragOverlay:{...Pe.dragOverlay,...e?.dragOverlay}}),[e?.draggable,e?.droppable,e?.dragOverlay])}function Sr(e){let{activeNode:t,measure:n,initialRect:r,config:o=!0}=e;const i=l.useRef(!1),{x:a,y:s}=typeof o=="boolean"?{x:o,y:o}:o;G(()=>{if(!a&&!s||!t){i.current=!1;return}if(i.current||!r)return;const u=t?.node.current;if(!u||u.isConnected===!1)return;const f=n(u),d=Ut(f,r);if(a||(d.x=0),s||(d.y=0),i.current=!0,Math.abs(d.x)>0||Math.abs(d.y)>0){const v=Ht(u);v&&v.scrollBy({top:d.y,left:d.x})}},[t,a,s,r,n])}const He=l.createContext({...K,scaleX:1,scaleY:1});var re;(function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"})(re||(re={}));const Hr=l.memo(function(t){var n,r,o,i;let{id:a,accessibility:s,autoScroll:c=!0,children:u,sensors:f=br,collisionDetection:d=kn,measuring:v,modifiers:h,...C}=t;const g=l.useReducer(xr,void 0,wr),[p,y]=g,[x,w]=Rn(),[N,S]=l.useState(re.Uninitialized),R=N===re.Initialized,{draggable:{active:b,nodes:m,translate:E},droppable:{containers:L}}=p,A=b!=null?m.get(b):null,H=l.useRef({initial:null,translated:null}),V=l.useMemo(()=>{var I;return b!=null?{id:b,data:(I=A?.data)!=null?I:mr,rect:H}:null},[b,A]),_=l.useRef(null),[ft,ht]=l.useState(null),[q,gt]=l.useState(null),oe=Se(C,Object.values(C)),Ve=Ue("DndDescribedBy",a),vt=l.useMemo(()=>L.getEnabled(),[L]),ie=Cr(v),{droppableRects:le,measureDroppableContainers:Te,measuringScheduled:pt}=sr(vt,{dragging:R,dependencies:[E.x,E.y],config:ie.droppable}),Y=or(m,b),bt=l.useMemo(()=>q?je(q):null,[q]),mt=vn(),yt=ar(Y,ie.draggable.measure);Sr({activeNode:b!=null?m.get(b):null,config:mt.layoutShiftCompensation,initialRect:yt,measure:ie.draggable.measure});const $=Pt(Y,ie.draggable.measure,yt),qe=Pt(Y?Y.parentElement:null),se=l.useRef({activatorEvent:null,active:null,activeNode:Y,collisionRect:null,collisions:null,droppableRects:le,draggableNodes:m,draggingNode:null,draggingNodeRect:null,droppableContainers:L,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),wt=L.getNodeFor((n=se.current.over)==null?void 0:n.id),ae=pr({measure:ie.dragOverlay.measure}),Le=(r=ae.nodeRef.current)!=null?r:Y,ce=R?(o=ae.rect)!=null?o:$:null,xt=!!(ae.nodeRef.current&&ae.rect),Dt=ur(xt?null:$),Je=nn(Le?P(Le):null),Q=dr(R?wt??Y:null),ke=vr(Q),Ie=an(h,{transform:{x:E.x-Dt.x,y:E.y-Dt.y,scaleX:1,scaleY:1},activatorEvent:q,active:V,activeNodeRect:$,containerNodeRect:qe,draggingNodeRect:ce,over:se.current.over,overlayNodeRect:ae.rect,scrollableAncestors:Q,scrollableAncestorRects:ke,windowRect:Je}),Ct=bt?he(bt,E):null,St=fr(Q),ln=Bt(St),cn=Bt(St,[$]),ue=he(Ie,ln),de=ce?zn(ce,Ie):null,pe=V&&de?d({active:V,collisionRect:de,droppableRects:le,droppableContainers:vt,pointerCoordinates:Ct}):null,Rt=Tn(pe,"id"),[Z,Et]=l.useState(null),un=xt?Ie:he(Ie,cn),dn=In(un,(i=Z?.rect)!=null?i:null,$),Ge=l.useRef(null),Mt=l.useCallback((I,z)=>{let{sensor:B,options:ee}=z;if(_.current==null)return;const j=m.get(_.current);if(!j)return;const F=I.nativeEvent,U=new B({active:_.current,activeNode:j,event:F,options:ee,context:se,onAbort(k){if(!m.get(k))return;const{onDragAbort:W}=oe.current,J={id:k};W?.(J),x({type:"onDragAbort",event:J})},onPending(k,te,W,J){if(!m.get(k))return;const{onDragPending:me}=oe.current,ne={id:k,constraint:te,initialCoordinates:W,offset:J};me?.(ne),x({type:"onDragPending",event:ne})},onStart(k){const te=_.current;if(te==null)return;const W=m.get(te);if(!W)return;const{onDragStart:J}=oe.current,be={activatorEvent:F,active:{id:te,data:W.data,rect:H}};we.unstable_batchedUpdates(()=>{J?.(be),S(re.Initializing),y({type:O.DragStart,initialCoordinates:k,active:te}),x({type:"onDragStart",event:be}),ht(Ge.current),gt(F)})},onMove(k){y({type:O.DragMove,coordinates:k})},onEnd:fe(O.DragEnd),onCancel:fe(O.DragCancel)});Ge.current=U;function fe(k){return async function(){const{active:W,collisions:J,over:be,scrollAdjustedTranslate:me}=se.current;let ne=null;if(W&&me){const{cancelDrop:ye}=oe.current;ne={activatorEvent:F,active:W,collisions:J,delta:me,over:be},k===O.DragEnd&&typeof ye=="function"&&await Promise.resolve(ye(ne))&&(k=O.DragCancel)}_.current=null,we.unstable_batchedUpdates(()=>{y({type:k}),S(re.Uninitialized),Et(null),ht(null),gt(null),Ge.current=null;const ye=k===O.DragEnd?"onDragEnd":"onDragCancel";if(ne){const _e=oe.current[ye];_e?.(ne),x({type:ye,event:ne})}})}}},[m]),fn=l.useCallback((I,z)=>(B,ee)=>{const j=B.nativeEvent,F=m.get(ee);if(_.current!==null||!F||j.dndKit||j.defaultPrevented)return;const U={active:F};I(B,z.options,U)===!0&&(j.dndKit={capturedBy:z.sensor},_.current=ee,Mt(B,z))},[m,Mt]),At=ir(f,fn);hr(f),G(()=>{$&&N===re.Initializing&&S(re.Initialized)},[$,N]),l.useEffect(()=>{const{onDragMove:I}=oe.current,{active:z,activatorEvent:B,collisions:ee,over:j}=se.current;if(!z||!B)return;const F={active:z,activatorEvent:B,collisions:ee,delta:{x:ue.x,y:ue.y},over:j};we.unstable_batchedUpdates(()=>{I?.(F),x({type:"onDragMove",event:F})})},[ue.x,ue.y]),l.useEffect(()=>{const{active:I,activatorEvent:z,collisions:B,droppableContainers:ee,scrollAdjustedTranslate:j}=se.current;if(!I||_.current==null||!z||!j)return;const{onDragOver:F}=oe.current,U=ee.get(Rt),fe=U&&U.rect.current?{id:U.id,rect:U.rect.current,data:U.data,disabled:U.disabled}:null,k={active:I,activatorEvent:z,collisions:B,delta:{x:j.x,y:j.y},over:fe};we.unstable_batchedUpdates(()=>{Et(fe),F?.(k),x({type:"onDragOver",event:k})})},[Rt]),G(()=>{se.current={activatorEvent:q,active:V,activeNode:Y,collisionRect:de,collisions:pe,droppableRects:le,draggableNodes:m,draggingNode:Le,draggingNodeRect:ce,droppableContainers:L,over:Z,scrollableAncestors:Q,scrollAdjustedTranslate:ue},H.current={initial:ce,translated:de}},[V,Y,pe,de,m,Le,ce,le,L,Z,Q,ue]),tr({...mt,delta:E,draggingRect:de,pointerCoordinates:Ct,scrollableAncestors:Q,scrollableAncestorRects:ke});const hn=l.useMemo(()=>({active:V,activeNode:Y,activeNodeRect:$,activatorEvent:q,collisions:pe,containerNodeRect:qe,dragOverlay:ae,draggableNodes:m,droppableContainers:L,droppableRects:le,over:Z,measureDroppableContainers:Te,scrollableAncestors:Q,scrollableAncestorRects:ke,measuringConfiguration:ie,measuringScheduled:pt,windowRect:Je}),[V,Y,$,q,pe,qe,ae,m,L,le,Z,Te,Q,ke,ie,pt,Je]),gn=l.useMemo(()=>({activatorEvent:q,activators:At,active:V,activeNodeRect:$,ariaDescribedById:{draggable:Ve},dispatch:y,draggableNodes:m,over:Z,measureDroppableContainers:Te}),[q,At,V,$,y,Ve,m,Z,Te]);return M.createElement(Kt.Provider,{value:w},M.createElement(Ne.Provider,{value:gn},M.createElement(sn.Provider,{value:hn},M.createElement(He.Provider,{value:dn},u)),M.createElement(Dr,{disabled:s?.restoreFocus===!1})),M.createElement(An,{...s,hiddenTextDescribedById:Ve}));function vn(){const I=ft?.autoScrollEnabled===!1,z=typeof c=="object"?c.enabled===!1:c===!1,B=R&&!I&&!z;return typeof c=="object"?{...c,enabled:B}:{enabled:B}}}),Rr=l.createContext(null),jt="button",Er="Draggable";function Vr(e){let{id:t,data:n,disabled:r=!1,attributes:o}=e;const i=Ue(Er),{activators:a,activatorEvent:s,active:c,activeNodeRect:u,ariaDescribedById:f,draggableNodes:d,over:v}=l.useContext(Ne),{role:h=jt,roleDescription:C="draggable",tabIndex:g=0}=o??{},p=c?.id===t,y=l.useContext(p?He:Rr),[x,w]=ze(),[N,S]=ze(),R=gr(a,t),b=Se(n);G(()=>(d.set(t,{id:t,key:i,node:x,activatorNode:N,data:b}),()=>{const E=d.get(t);E&&E.key===i&&d.delete(t)}),[d,t]);const m=l.useMemo(()=>({role:h,tabIndex:g,"aria-disabled":r,"aria-pressed":p&&h===jt?!0:void 0,"aria-roledescription":C,"aria-describedby":f.draggable}),[r,h,g,p,C,f.draggable]);return{active:c,activatorEvent:s,activeNodeRect:u,attributes:m,isDragging:p,listeners:r?void 0:R,node:x,over:v,setNodeRef:w,setActivatorNodeRef:S,transform:y}}function Mr(){return l.useContext(sn)}const Ar="Droppable",Or={timeout:25};function qr(e){let{data:t,disabled:n=!1,id:r,resizeObserverConfig:o}=e;const i=Ue(Ar),{active:a,dispatch:s,over:c,measureDroppableContainers:u}=l.useContext(Ne),f=l.useRef({disabled:n}),d=l.useRef(!1),v=l.useRef(null),h=l.useRef(null),{disabled:C,updateMeasurementsFor:g,timeout:p}={...Or,...o},y=Se(g??r),x=l.useCallback(()=>{if(!d.current){d.current=!0;return}h.current!=null&&clearTimeout(h.current),h.current=setTimeout(()=>{u(Array.isArray(y.current)?y.current:[y.current]),h.current=null},p)},[p]),w=We({callback:x,disabled:C||!a}),N=l.useCallback((m,E)=>{w&&(E&&(w.unobserve(E),d.current=!1),m&&w.observe(m))},[w]),[S,R]=ze(N),b=Se(t);return l.useEffect(()=>{!w||!S.current||(w.disconnect(),d.current=!1,w.observe(S.current))},[S,w]),l.useEffect(()=>(s({type:O.RegisterDroppable,element:{id:r,key:i,disabled:n,node:S,rect:v,data:b}}),()=>s({type:O.UnregisterDroppable,key:i,id:r})),[r]),l.useEffect(()=>{n!==f.current.disabled&&(s({type:O.SetDroppableDisabled,id:r,key:i,disabled:n}),f.current.disabled=n)},[r,i,n,s]),{active:a,rect:v,isOver:c?.id===r,node:S,over:c,setNodeRef:R}}function Nr(e){let{animation:t,children:n}=e;const[r,o]=l.useState(null),[i,a]=l.useState(null),s=Be(n);return!n&&!r&&s&&o(s),G(()=>{if(!i)return;const c=r?.key,u=r?.props.id;if(c==null||u==null){o(null);return}Promise.resolve(t(u,i)).then(()=>{o(null)})},[t,r,i]),M.createElement(M.Fragment,null,n,r?l.cloneElement(r,{ref:a}):null)}const Tr={x:0,y:0,scaleX:1,scaleY:1};function Lr(e){let{children:t}=e;return M.createElement(Ne.Provider,{value:on},M.createElement(He.Provider,{value:Tr},t))}const kr={position:"fixed",touchAction:"none"},Ir=e=>at(e)?"transform 250ms ease":void 0,Pr=l.forwardRef((e,t)=>{let{as:n,activatorEvent:r,adjustScale:o,children:i,className:a,rect:s,style:c,transform:u,transition:f=Ir}=e;if(!s)return null;const d=o?u:{...u,scaleX:1,scaleY:1},v={...kr,width:s.width,height:s.height,top:s.top,left:s.left,transform:Re.Transform.toString(d),transformOrigin:o&&r?On(r,s):void 0,transition:typeof f=="function"?f(r):f,...c};return M.createElement(n,{className:a,style:v,ref:t},i)}),zr=e=>t=>{let{active:n,dragOverlay:r}=t;const o={},{styles:i,className:a}=e;if(i!=null&&i.active)for(const[s,c]of Object.entries(i.active))c!==void 0&&(o[s]=n.node.style.getPropertyValue(s),n.node.style.setProperty(s,c));if(i!=null&&i.dragOverlay)for(const[s,c]of Object.entries(i.dragOverlay))c!==void 0&&r.node.style.setProperty(s,c);return a!=null&&a.active&&n.node.classList.add(a.active),a!=null&&a.dragOverlay&&r.node.classList.add(a.dragOverlay),function(){for(const[c,u]of Object.entries(o))n.node.style.setProperty(c,u);a!=null&&a.active&&n.node.classList.remove(a.active)}},Br=e=>{let{transform:{initial:t,final:n}}=e;return[{transform:Re.Transform.toString(t)},{transform:Re.Transform.toString(n)}]},Fr={duration:250,easing:"ease",keyframes:Br,sideEffects:zr({styles:{active:{opacity:"0"}}})};function jr(e){let{config:t,draggableNodes:n,droppableContainers:r,measuringConfiguration:o}=e;return Ke((i,a)=>{if(t===null)return;const s=n.get(i);if(!s)return;const c=s.node.current;if(!c)return;const u=rn(a);if(!u)return;const{transform:f}=P(a).getComputedStyle(a),d=Wt(f);if(!d)return;const v=typeof t=="function"?t:Xr(t);return Qt(c,o.draggable.measure),v({active:{id:i,data:s.data,node:c,rect:o.draggable.measure(c)},draggableNodes:n,dragOverlay:{node:a,rect:o.dragOverlay.measure(u)},droppableContainers:r,measuringConfiguration:o,transform:d})})}function Xr(e){const{duration:t,easing:n,sideEffects:r,keyframes:o}={...Fr,...e};return i=>{let{active:a,dragOverlay:s,transform:c,...u}=i;if(!t)return;const f={x:s.rect.left-a.rect.left,y:s.rect.top-a.rect.top},d={scaleX:c.scaleX!==1?a.rect.width*c.scaleX/s.rect.width:1,scaleY:c.scaleY!==1?a.rect.height*c.scaleY/s.rect.height:1},v={x:c.x-f.x,y:c.y-f.y,...d},h=o({...u,active:a,dragOverlay:s,transform:{initial:c,final:v}}),[C]=h,g=h[h.length-1];if(JSON.stringify(C)===JSON.stringify(g))return;const p=r?.({active:a,dragOverlay:s,...u}),y=s.node.animate(h,{duration:t,easing:n,fill:"forwards"});return new Promise(x=>{y.onfinish=()=>{p?.(),x()}})}}let Xt=0;function Yr(e){return l.useMemo(()=>{if(e!=null)return Xt++,Xt},[e])}const Jr=M.memo(e=>{let{adjustScale:t=!1,children:n,dropAnimation:r,style:o,transition:i,modifiers:a,wrapperElement:s="div",className:c,zIndex:u=999}=e;const{activatorEvent:f,active:d,activeNodeRect:v,containerNodeRect:h,draggableNodes:C,droppableContainers:g,dragOverlay:p,over:y,measuringConfiguration:x,scrollableAncestors:w,scrollableAncestorRects:N,windowRect:S}=Mr(),R=l.useContext(He),b=Yr(d?.id),m=an(a,{activatorEvent:f,active:d,activeNodeRect:v,containerNodeRect:h,draggingNodeRect:p.rect,over:y,overlayNodeRect:p.rect,scrollableAncestors:w,scrollableAncestorRects:N,transform:R,windowRect:S}),E=dt(v),L=jr({config:r,draggableNodes:C,droppableContainers:g,measuringConfiguration:x}),A=E?p.setRef:void 0;return M.createElement(Lr,null,M.createElement(Nr,{animation:L},d&&b?M.createElement(Pr,{key:b,id:d.id,ref:A,as:s,activatorEvent:f,adjustScale:t,className:c,transition:i,rect:E,style:{zIndex:u,...o},transform:m},n):null))});export{Re as C,Hr as D,Zn as M,tn as P,er as T,qr as a,Wr as b,Ur as c,Jr as d,Vr as u};
//# sourceMappingURL=dnd-DoiScGU0.js.map
