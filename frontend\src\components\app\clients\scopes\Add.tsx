'use client'
import { type CreateUpdateOpenIddictScopeDto, postApiOpeniddictScopes } from '@/client'
import { Button } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { useToast } from '@/lib/useToast'
import { QueryNames } from '@/lib/hooks/QueryConstants'
import { useGrantedPolicies } from '@/lib/hooks/useGrantedPolicies'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { Toaster } from '@/components/ui/toaster'
import { RiAddLine } from '@remixicon/react'
import { MultiSelect } from '@/components/ui/multi-select'
import { FormField, FormSection } from '@/components/ui/FormField'
import { Textarea } from '@/components/ui/textarea'
import { useOpeniddictResourceSelect } from '@/lib/hooks/useOpeniddictResourceSelect'
import { handleApiError } from '@/lib/handleApiError'

export type AddClientProps = {
  children?: React.ReactNode
}

export const Add = ({ children }: AddClientProps) => {
  const { can } = useGrantedPolicies()
  const [open, setOpen] = useState(false)
  const { toast } = useToast()
  const queryClient = useQueryClient()
  const { handleSubmit, register, setValue } = useForm<CreateUpdateOpenIddictScopeDto>()

  const [selectedResources, setSelectedResources] = useState<string[]>([])
  // Fetch available permissions from the API
  const { data: resourcesData, isLoading: resourcesLoading } = useOpeniddictResourceSelect()

  const createMutation = useMutation({
    mutationFn: async (dataMutation: CreateUpdateOpenIddictScopeDto) =>
      postApiOpeniddictScopes({
        body: dataMutation
      }),
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Client Created Successfully',
        variant: 'success',
      })
      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetOpeniddictScopes] })
      setOpen(false)
    },
    onError: (err: unknown) => {
      // Use the global helper to handle API errors
      const error = handleApiError(err);
      toast({
        title: error.title,
        description: error.description,
        variant: 'error',
      })
    }
  })

  const onSubmit = (formData: CreateUpdateOpenIddictScopeDto) => {
    // Merge form data with consent type and permissions
    const userData: CreateUpdateOpenIddictScopeDto = {
      ...formData,
      resources: selectedResources
    }

    createMutation.mutate(userData)
  }

  return (
    <section>
      <Toaster />
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>{children}</DialogTrigger>
        <section className="flex items-center justify-between pb-5">
          {can('AbpIdentity.Users.Create') && (
            <Button size='sm' className="w-full sm:py-1 sm:mt-0 sm:w-fit" onClick={() => setOpen(true)}>
              <RiAddLine className="-ml-1 size-4 shrink-0" aria-hidden="true" />
              <span className="hidden truncate sm:inline">Create Scope</span>
            </Button>
          )}
        </section>
        <DialogContent size='xl'>
          <DialogHeader>
            <DialogTitle>Create a New Scope</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSubmit(onSubmit)} className='mt-2'>
            <section className="flex w-full flex-col space-y-2">
              <FormSection>
                <FormField
                  label="Name"
                  description="The unique identifier for this resource. Used in requests"
                >
                  <Input required {...register('name')} placeholder="Name" />
                </FormField>
                <FormField
                  label="Display Name"
                  description="The display name for this resource"
                >
                  <Input required {...register('displayName')} placeholder="Display Name" />
                </FormField>
                <FormField
                  label="Description"
                  description="The description for this resource"
                >
                  <Textarea required {...register('description')} placeholder="Description" />
                </FormField>

                <FormField
                  label="Resources"
                  description="The resources of the scope"
                >
                  <MultiSelect
                    options={resourcesData?.map(resource => ({
                      value: resource.value ?? '',
                      label: resource.label ?? ''
                    })) ?? []}
                    value={selectedResources}
                    onChange={(values) => {
                      setSelectedResources(values);
                      setValue('resources', values);
                    }}
                    placeholder={resourcesLoading ? "Loading resources..." : "Select resources"}
                    disabled={resourcesLoading}
                    maxHeight={300}
                  />
                </FormField>
              </FormSection>
            </section>
            <DialogFooter className="mt-5">
              <Button
                variant="ghost"
                onClick={(e) => {
                  e.preventDefault()
                  setOpen(false)
                }}
                disabled={createMutation.isPending}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={createMutation.isPending}>
                {createMutation.isPending ? 'Saving...' : 'Save'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </section>
  )
}
