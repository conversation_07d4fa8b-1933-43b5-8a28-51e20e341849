{"version": 3, "file": "user-Bz9QvVXa.js", "sources": ["../../../../../frontend/src/lib/hooks/useUsers.ts", "../../../../../frontend/src/components/app/users/DeleteUser.tsx", "../../../../../frontend/src/components/ui/tabs.tsx", "../../../../../frontend/src/lib/hooks/useAssignableRoles.ts", "../../../../../frontend/src/lib/hooks/useUserRoles.ts", "../../../../../frontend/src/components/app/users/UserEdit.tsx", "../../../../../frontend/src/components/app/users/UserPermission.tsx", "../../../../../frontend/src/components/app/users/AddUser.tsx", "../../../../../frontend/src/components/app/users/UploadUsersCsv.tsx", "../../../../../frontend/src/components/app/users/UserActions.tsx", "../../../../../frontend/src/components/app/users/StatusBadge.tsx", "../../../../../frontend/src/components/app/users/columns.tsx", "../../../../../frontend/src/components/app/users/UserFilterbar.tsx", "../../../../../frontend/src/components/app/users/UserList.tsx", "../../../../../frontend/src/pages/user.tsx"], "sourcesContent": ["import { getApiIdentityUsers } from '@/client'\r\nimport { useQuery } from '@tanstack/react-query'\r\nimport { QueryNames } from './QueryConstants'\r\n\r\n/**\r\n * Custom hook to fetch a list of users.\r\n *\r\n * This hook uses the `useQuery` hook from `react-query` to fetch the user data\r\n * asynchronously. The query key used is `QueryNames.GetUsers` along with pagination,\r\n * filter, and sorting parameters.\r\n *\r\n * @param {number} pageIndex - The current page index.\r\n * @param {number} pageSize - The number of items per page.\r\n * @param {string} [filter] - Optional filter string.\r\n * @param {string} [sorting] - Optional sorting string.\r\n * @returns {UseQueryResult} The result of the query, which includes the user data and query status.\r\n */\r\nexport const useUsers = (\r\n  pageIndex: number,\r\n  pageSize: number,\r\n  filter?: string,\r\n  sorting?: string\r\n) => {\r\n  return useQuery({\r\n    queryKey: [QueryNames.GetUsers, pageIndex, pageSize, filter, sorting],\r\n    queryFn: async () => {\r\n      let skip = 0\r\n      if (pageIndex > 0) {\r\n        skip = pageIndex * pageSize\r\n      }\r\n      const response = await getApiIdentityUsers({\r\n        query: {\r\n          MaxResultCount: pageSize,\r\n          SkipCount: skip,\r\n          Filter: filter,\r\n          Sorting: sorting,\r\n        }\r\n      })\r\n      return response.data\r\n    },\r\n  })\r\n}\r\n", "import { deleteApiIdentityUsersById } from '@/client'\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@/components/ui/alert-dialog'\r\nimport { useToast } from \"@/hooks/use-toast\"\r\nimport { useEffect, useState } from 'react'\r\n\r\ntype DeleteUserProps = {\r\n  user: { userId: string; username: string }\r\n  onDismiss: () => void\r\n}\r\nexport const DeleteUser = ({ user: { userId, username }, onDismiss }: DeleteUserProps) => {\r\n  const { toast } = useToast()\r\n  const [open, setOpen] = useState<boolean>(false)\r\n  const onYesEvent = async () => {\r\n    try {\r\n      await deleteApiIdentityUsersById({\r\n        path: { id: userId },\r\n      })\r\n      toast({\r\n        title: 'Success',\r\n        description: `User \"${username}\" has been deleted successfully.`,\r\n      })\r\n      onDismiss()\r\n    } catch (err: unknown) {\r\n      if (err instanceof Error) {\r\n        toast({\r\n          title: 'Failed',\r\n          description: `There was a problem when deleting the user \"${username}\". Kindly try again.`,\r\n          variant: 'destructive',\r\n        })\r\n      }\r\n    }\r\n  }\r\n\r\n  useEffect(() => {\r\n    setOpen(true)\r\n  }, [])\r\n\r\n  return (\r\n    <AlertDialog open={open}>\r\n      <AlertDialogContent>\r\n        <AlertDialogHeader>\r\n          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>\r\n          <AlertDialogDescription>\r\n            This action cannot be undone. This will permanently delete your this user &quot;\r\n            {username}&quot;\r\n          </AlertDialogDescription>\r\n        </AlertDialogHeader>\r\n        <AlertDialogFooter>\r\n          <AlertDialogCancel onClick={onDismiss}>Cancel</AlertDialogCancel>\r\n          <AlertDialogAction onClick={onYesEvent}>Yes</AlertDialogAction>\r\n        </AlertDialogFooter>\r\n      </AlertDialogContent>\r\n    </AlertDialog>\r\n  )\r\n}\r\n", "\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Tabs({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\r\n  return (\r\n    <TabsPrimitive.Root\r\n      data-slot=\"tabs\"\r\n      className={cn(\"flex flex-col gap-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsList({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\r\n  return (\r\n    <TabsPrimitive.List\r\n      data-slot=\"tabs-list\"\r\n      className={cn(\r\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsTrigger({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\r\n  return (\r\n    <TabsPrimitive.Trigger\r\n      data-slot=\"tabs-trigger\"\r\n      className={cn(\r\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\r\n  return (\r\n    <TabsPrimitive.Content\r\n      data-slot=\"tabs-content\"\r\n      className={cn(\"flex-1 outline-none\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n", "import { getApiIdentityUsersAssignableRoles } from '@/client'\r\nimport { useQuery } from '@tanstack/react-query'\r\nimport { QueryNames } from './QueryConstants'\r\n\r\n/**\r\n * Custom hook to fetch assignable roles using a query.\r\n *\r\n * This hook uses the `useQuery` hook from the `react-query` library to fetch\r\n * the assignable roles. The query key used is `QueryNames.GetAssignableRoles`.\r\n *\r\n * @returns {object} The result of the `useQuery` hook, which includes the data,\r\n * status, and other properties related to the query.\r\n */\r\nexport const useAssignableRoles = () => {\r\n  return useQuery({\r\n    queryKey: [QueryNames.GetAssignableRoles],\r\n    queryFn: async () => {\r\n      const { data } = await getApiIdentityUsersAssignableRoles()\r\n      return data\r\n    },\r\n  })\r\n}\r\n", "import { getApiIdentityUsersByIdRoles } from '@/client'\r\nimport { useQuery } from '@tanstack/react-query'\r\nimport { QueryNames } from './QueryConstants'\r\n\r\ntype UseUserRolesProps = {\r\n  userId: string\r\n}\r\n\r\n/**\r\n * Custom hook to fetch the roles of a specific user.\r\n *\r\n * This hook uses the `useQuery` hook from `react-query` to fetch the user roles\r\n * asynchronously. The query key used is `QueryNames.GetUserRoles` along with the user ID.\r\n *\r\n * @param {UseUserRolesProps} props - The properties object containing the user ID.\r\n * @returns {UseQueryResult} The result of the query, which includes the user roles data and query status.\r\n */\r\nexport const useUserRoles = ({ userId }: UseUserRolesProps) => {\r\n  return useQuery({\r\n    queryKey: [QueryNames.GetUserRoles, userId],\r\n    queryFn: async () => {\r\n      const { data } = await getApiIdentityUsersByIdRoles({\r\n        path: {\r\n          id: userId\r\n        }\r\n      })\r\n      return data\r\n    },\r\n  })\r\n}\r\n", "import { type IdentityRoleDto, type IdentityUserUpdateDto, putApiIdentityUsersById } from '@/client'\r\nimport { useToast } from '@/lib/useToast'\r\nimport { type MouseEvent, useCallback, useEffect, useState } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { v4 } from 'uuid'\r\n\r\nimport Loader from '@/components/ui/Loader'\r\nimport { Button } from '@/components/ui/button'\r\nimport { Checkbox } from '@/components/ui/checkbox'\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog'\r\nimport { Input } from '@/components/ui/input'\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\r\nimport { useAssignableRoles } from '@/lib/hooks/useAssignableRoles'\r\nimport { useUserRoles } from '@/lib/hooks/useUserRoles'\r\nimport classNames from 'clsx'\r\nimport { FormField, FormSection } from '@/components/ui/FormField'\r\nimport { handleApiError } from '@/lib/handleApiError'\r\nimport { useMutation, useQueryClient } from '@tanstack/react-query'\r\nimport { QueryNames } from '@/lib/hooks/QueryConstants'\r\n\r\nconst TABS_NAME = {\r\n  USERS_EDIT: 'user_edit',\r\n  USERS_ROLE_ASSIGN: 'user_role_assign',\r\n}\r\n\r\ntype RoleType = {\r\n  name: string\r\n  id: string\r\n}\r\n\r\ntype UserEditProps = {\r\n  userDto: IdentityUserUpdateDto\r\n  userId: string\r\n  onDismiss: () => void\r\n}\r\nexport const UserEdit = ({ userDto, userId, onDismiss }: UserEditProps) => {\r\n  const [open, setOpen] = useState(false)\r\n  const { toast } = useToast()\r\n  const queryClient = useQueryClient()\r\n  const { handleSubmit, register } = useForm<IdentityUserUpdateDto>()\r\n  const [roles, setRoles] = useState<RoleType[]>([])\r\n  const userRole = useUserRoles({ userId })\r\n  const assignableRoles = useAssignableRoles()\r\n\r\n  const createDataMutation = useMutation({\r\n    mutationFn: async (dataMutation: IdentityUserUpdateDto) =>\r\n      putApiIdentityUsersById({\r\n        path: { id: userId },\r\n        body: { ...userDto, ...dataMutation },\r\n      }),\r\n    onSuccess: () => {\r\n      toast({\r\n        title: 'Success',\r\n        description: 'Claim Type Created Successfully',\r\n        variant: 'success',\r\n      })\r\n      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetUsers] })\r\n      setOpen(false)\r\n    },\r\n    onError: (err: unknown) => {\r\n      // Use the global helper to handle API errors\r\n      const error = handleApiError(err);\r\n      toast({\r\n        title: error.title,\r\n        description: error.description,\r\n        variant: 'error',\r\n      })\r\n    }\r\n  })\r\n\r\n  const onSubmit = (formData: IdentityUserUpdateDto) => {\r\n    // Merge form data with consent type and permissions\r\n    const userData: IdentityUserUpdateDto = {\r\n      ...formData,\r\n    }\r\n\r\n    // Explicitly mark the promise as handled\r\n    void createDataMutation.mutate(userData)\r\n  }\r\n\r\n  const onCloseEvent = () => {\r\n    setOpen(false)\r\n    onDismiss()\r\n  }\r\n\r\n  useEffect(() => {\r\n    setOpen(true)\r\n  }, [])\r\n\r\n  useEffect(() => {\r\n    if (userRole.data?.items) {\r\n      const temp: RoleType[] = []\r\n      userRole.data.items.forEach((r) => {\r\n        temp.push({ name: r.name!, id: r.id! })\r\n      })\r\n      setRoles(temp)\r\n    }\r\n  }, [userRole.data?.items])\r\n\r\n  const onRoleAssignEvent = useCallback(\r\n    (role: IdentityRoleDto) => {\r\n      const hasAssignedRoleExistAlready = roles.findIndex((r) => role.id === r.id)\r\n\r\n      console.log(hasAssignedRoleExistAlready, 'hasAssignedRoleExistAlready')\r\n      if (hasAssignedRoleExistAlready !== -1) {\r\n        roles.splice(hasAssignedRoleExistAlready, 1)\r\n        setRoles([...roles])\r\n      } else {\r\n        roles.push({ name: role.name!, id: role.id! })\r\n        setRoles([...roles])\r\n      }\r\n    },\r\n    [roles]\r\n  )\r\n\r\n  const onRoleAssignedSaveEvent = (e: MouseEvent) => {\r\n    e.preventDefault()\r\n    const updateUserDto: IdentityUserUpdateDto = {\r\n      ...userDto,\r\n      roleNames: roles?.map((r) => r.name) ?? [],\r\n    }\r\n    onSubmit(updateUserDto)\r\n  }\r\n  return (\r\n    <Dialog open={open} onOpenChange={onCloseEvent}>\r\n      <DialogContent size='2xl' className=\"\">\r\n        <DialogHeader>\r\n          <DialogTitle>Update a User: {userDto.userName}</DialogTitle>\r\n        </DialogHeader>\r\n        <Tabs defaultValue={TABS_NAME.USERS_EDIT}>\r\n          <TabsList className=\"w-full\">\r\n            <TabsTrigger value={TABS_NAME.USERS_EDIT} className=\"w-full\">\r\n              User Information\r\n            </TabsTrigger>\r\n            <TabsTrigger value={TABS_NAME.USERS_ROLE_ASSIGN} className=\"w-full\">\r\n              Roles\r\n            </TabsTrigger>\r\n          </TabsList>\r\n          <TabsContent value={TABS_NAME.USERS_EDIT}>\r\n            <form onSubmit={handleSubmit(onSubmit)}>\r\n              <section className=\"flex flex-col space-y-2 mt-4\">\r\n                <FormSection>\r\n                  <FormField\r\n                    label=\"Name\"\r\n                    description=\"The name of the user\"\r\n                  >\r\n                    <Input required {...register('name')} defaultValue={userDto.name ?? ''} placeholder=\"Name\" />\r\n                  </FormField>\r\n\r\n                  <FormField\r\n                    label=\"Surname\"\r\n                    description=\"The surname of the user\"\r\n                  >\r\n                    <Input required {...register('surname')} defaultValue={userDto.surname ?? ''} placeholder=\"Surname\" />\r\n                  </FormField>\r\n\r\n                  <FormField\r\n                    label=\"Email\"\r\n                    description=\"The email of the user\"\r\n                  >\r\n                    <Input required {...register('email')} defaultValue={userDto.email ?? ''} placeholder=\"Email\" />\r\n                  </FormField>\r\n\r\n                  <FormField\r\n                    label=\"Phone Number\"\r\n                    description=\"The phone number of the user\"\r\n                  >\r\n                    <Input required {...register('phoneNumber')} defaultValue={userDto.phoneNumber ?? ''} placeholder=\"Phone Number\" />\r\n                  </FormField>\r\n                </FormSection>\r\n              </section>\r\n\r\n              <DialogFooter className=\"mt-5\">\r\n                <Button\r\n                  variant='outline'\r\n                  onClick={(e) => {\r\n                    e.preventDefault()\r\n                    onCloseEvent()\r\n                  }}\r\n                >\r\n                  Cancel\r\n                </Button>\r\n                <Button type=\"submit\">Save</Button>\r\n              </DialogFooter>\r\n            </form>\r\n          </TabsContent>\r\n          <TabsContent className='max-h-[70vh] overflow-hidden flex flex-col' value={TABS_NAME.USERS_ROLE_ASSIGN}>\r\n            {assignableRoles?.isLoading && <Loader />}\r\n            {assignableRoles?.isError && (\r\n              <div className=\"bg-error p-10 text-3xl\">\r\n                There was an error while fetching roles information for the {userDto.userName}\r\n              </div>\r\n            )}\r\n            {!assignableRoles.isLoading && !assignableRoles.isError && (\r\n              <div className='flex-1 overflow-y-auto'>\r\n                {assignableRoles?.data?.items?.map((r) => (\r\n                  <div key={v4()} className={classNames('flex items-center space-x-2 pb-5 mt-3')}>\r\n                    <Checkbox\r\n                      id={r.id}\r\n                      name={r.name!}\r\n                      checked={!!roles?.find((l) => l.id === r.id)}\r\n                      onCheckedChange={() => {\r\n                        onRoleAssignEvent(r)\r\n                      }}\r\n                    />\r\n                    <label htmlFor={r.id} className=\"text-sm font-medium leading-none\">\r\n                      {r.name}\r\n                    </label>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n            <DialogFooter className=\"mt-5\">\r\n              <Button\r\n                variant='outline'\r\n                onClick={(e) => {\r\n                  e.preventDefault()\r\n                  onCloseEvent()\r\n                }}\r\n              >\r\n                Cancel\r\n              </Button>\r\n              <Button onClick={onRoleAssignedSaveEvent}>Save</Button>\r\n            </DialogFooter>\r\n          </TabsContent>\r\n        </Tabs>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n", "import {\r\n  type IdentityUserUpdateDto,\r\n  type PermissionGroupDto,\r\n  type UpdatePermissionsDto,\r\n  putApiPermissionManagementPermissions,\r\n} from '@/client'\r\nimport { Button } from '@/components/ui/button'\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog'\r\nimport { useToast } from \"@/hooks/use-toast\"\r\nimport { usePermissions } from '@/lib/hooks/usePermissions'\r\nimport { useUserRoles } from '@/lib/hooks/useUserRoles'\r\nimport { PermissionProvider, USER_ROLE } from '@/lib/utils'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { type FormEvent, useCallback, useEffect, useMemo, useState } from 'react'\r\nimport { type Management, Permission } from '@/components/app/permission/PermissionToggle'\r\nimport { TogglePermission } from '@/components/app/permission/TogglePermission'\r\n\r\ntype UserPermissionProps = {\r\n  userDto: IdentityUserUpdateDto\r\n  userId: string\r\n  onDismiss: () => void\r\n}\r\n\r\nexport const UserPermission = ({ userDto, userId, onDismiss }: UserPermissionProps) => {\r\n  const [open, setOpen] = useState(false)\r\n  const { toast } = useToast()\r\n  const userRoles = useUserRoles({ userId })\r\n\r\n  // flag determine to enable/disable all the permissions to a user.\r\n  const [hasAllGranted, setHasAllGranted] = useState(false)\r\n  const { data } = usePermissions(PermissionProvider.U, userId)\r\n  const queryClient = useQueryClient()\r\n\r\n  const [permissionGroups, setPermissionGroups] = useState<PermissionGroupDto[]>([])\r\n\r\n  useEffect(() => {\r\n    setOpen(true)\r\n    return () => {\r\n      void queryClient.invalidateQueries({ queryKey: [PermissionProvider.U] })\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [])\r\n\r\n  // Update the local state with the remote data\r\n  useEffect(() => {\r\n    if (data?.groups && Array.isArray(data.groups)) {\r\n      setPermissionGroups([...data.groups])\r\n    }\r\n  }, [data])\r\n\r\n  // check if user have all the permissions are granted already.\r\n  // Only run this when data changes, not when permissionGroups changes internally\r\n  useEffect(() => {\r\n    if (data?.groups && data.groups.length > 0) {\r\n      const hasAllPermissionGranted = data.groups\r\n        .map((g) => g.permissions?.every((p) => p.isGranted))\r\n        .every((e) => e)\r\n      setHasAllGranted(hasAllPermissionGranted)\r\n    }\r\n  }, [data])\r\n\r\n  // Apply hasAllGranted to all permissions - only when hasAllGranted changes\r\n  useEffect(() => {\r\n    if (permissionGroups.length > 0) {\r\n      const updatedGroups = permissionGroups.map(group => {\r\n        // Create a new group object to avoid reference issues\r\n        return {\r\n          ...group,\r\n          permissions: group.permissions?.map(permission => ({\r\n            ...permission,\r\n            isGranted: hasAllGranted\r\n          }))\r\n        };\r\n      });\r\n\r\n      // Break the circular dependency by using a ref to track updates\r\n      setPermissionGroups(updatedGroups);\r\n    }\r\n  }, [hasAllGranted]); // Remove permissionGroups from dependency array\r\n\r\n  const onSubmit = useCallback(\r\n    async (e: FormEvent) => {\r\n      e.preventDefault()\r\n      const payload = permissionGroups\r\n        ?.map((p) =>\r\n          p.permissions!.map((grant) => ({\r\n            name: grant.name,\r\n            isGranted: grant.isGranted,\r\n          }))\r\n        )\r\n        .flat()\r\n      const requestPayload: UpdatePermissionsDto = {\r\n        permissions: payload,\r\n      }\r\n      try {\r\n        await putApiPermissionManagementPermissions({\r\n          query: {\r\n            providerName: PermissionProvider.U,\r\n            providerKey: userId\r\n          },\r\n          body: requestPayload\r\n        })\r\n        toast({\r\n          title: 'Success',\r\n          description: 'Permission Updated Successfully',\r\n          variant: 'default',\r\n        })\r\n        void queryClient.invalidateQueries({\r\n          queryKey: [PermissionProvider.U],\r\n        })\r\n        onCloseEvent()\r\n      } catch (err: unknown) {\r\n        if (err instanceof Error) {\r\n          toast({\r\n            title: 'Failed',\r\n            description: \"Permission update wasn't successful.\",\r\n            variant: 'destructive',\r\n          })\r\n        }\r\n      }\r\n    },\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n    [permissionGroups]\r\n  )\r\n\r\n  const onCloseEvent = useCallback(() => {\r\n    setOpen(false)\r\n    onDismiss()\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [])\r\n\r\n  const hasAdmin = useMemo(() => {\r\n    if (userRoles?.data?.items) {\r\n      return userRoles.data.items.filter((role) => role.name?.includes(USER_ROLE.ADMIN)).length > 0\r\n    }\r\n    return false\r\n  }, [userRoles])\r\n\r\n  const formatDisplayName = (str: string | undefined): Management => {\r\n    if (!str) return 'identity' as Management;\r\n    return (str.split(' ')[0] ?? '').toLowerCase() as Management\r\n  }\r\n\r\n  // Group permissions into pairs for the grid layout\r\n  const groupedPermissions = useMemo(() => {\r\n    const result = [];\r\n    for (let i = 0; i < permissionGroups.length; i += 2) {\r\n      result.push(permissionGroups.slice(i, i + 2));\r\n    }\r\n    return result;\r\n  }, [permissionGroups]);\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={onCloseEvent}>\r\n      <DialogContent size={'4xl'} className=\"max-w-4xl max-h-[90vh] overflow-hidden flex flex-col\">\r\n        <DialogHeader>\r\n          <DialogTitle>Permissions - {userDto.userName}</DialogTitle>\r\n        </DialogHeader>\r\n        <form onSubmit={onSubmit} className=\"flex-1 overflow-y-auto\">\r\n          <div className=\"p-1\">\r\n            <Permission\r\n              name=\"Grant All Permissions\"\r\n              isGranted={hasAllGranted}\r\n              id=\"all_granted\"\r\n              disabled={!hasAdmin}\r\n              onUpdate={() => {\r\n                setHasAllGranted((prev) => !prev)\r\n              }}\r\n              className=\"ml-2 mb-4\"\r\n            />\r\n\r\n            <div className=\"space-y-6\">\r\n              {groupedPermissions.map((row, rowIndex) => (\r\n                <div key={rowIndex} className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                  {row.map((group, colIndex) => {\r\n                    // Ensure group is properly typed\r\n                    const displayName = group.displayName ?? '';\r\n                    const permissions = group.permissions ?? [];\r\n\r\n                    return (\r\n                      <div key={`${rowIndex}-${colIndex}`} className=\"border rounded-lg p-4\">\r\n                        <h3 className=\"text-lg font-medium mb-2\">{displayName}</h3>\r\n                        <div className=\"border-t pt-3\">\r\n                          <TogglePermission\r\n                            permissions={permissions}\r\n                            type={formatDisplayName(displayName)}\r\n                            disabled={hasAllGranted && hasAdmin}\r\n                            hideSelectAll={hasAllGranted}\r\n                            hideSave\r\n                          />\r\n                        </div>\r\n                      </div>\r\n                    );\r\n                  })}\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </form>\r\n        <DialogFooter className=\"mt-4 border-t pt-4 bg-white dark:bg-gray-950\">\r\n          <Button\r\n            onClick={(e) => {\r\n              e.preventDefault()\r\n              onCloseEvent()\r\n            }}\r\n            variant=\"ghost\"\r\n          >\r\n            Cancel\r\n          </Button>\r\n          <Button onClick={onSubmit}>Save</Button>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n", "'use client'\r\nimport { type IdentityUserCreateDto, postApiIdentityUsers } from '@/client'\r\nimport { Button } from '@/components/ui/button'\r\nimport { Checkbox } from '@/components/ui/checkbox'\r\nimport {\r\n  <PERSON><PERSON>,\r\n  DialogContent,\r\n  <PERSON>alogFooter,\r\n  <PERSON><PERSON>Header,\r\n  <PERSON>alogTitle,\r\n  DialogTrigger,\r\n} from '@/components/ui/dialog'\r\nimport { Input } from '@/components/ui/input'\r\nimport { useToast } from '@/lib/useToast'\r\nimport { QueryNames } from '@/lib/hooks/QueryConstants'\r\nimport { useGrantedPolicies } from '@/lib/hooks/useGrantedPolicies'\r\nimport { useMutation, useQueryClient } from '@tanstack/react-query'\r\nimport { useState } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { Toaster } from '@/components/ui/toaster'\r\nimport { RiAddLine } from '@remixicon/react'\r\nimport { FormSection, FormField } from '@/components/ui/FormField'\r\nimport { handleApiError } from '@/lib/handleApiError'\r\n\r\n\r\nexport type AddUserProps = {\r\n  children?: React.ReactNode\r\n}\r\n\r\nexport const AddUser = ({ children }: AddUserProps) => {\r\n  const { can } = useGrantedPolicies()\r\n  const [open, setOpen] = useState(false)\r\n  const [isActive, setIsActive] = useState(true)\r\n  const [lockoutEnabled, setLockoutEnabled] = useState(true)\r\n  const { toast } = useToast()\r\n  const queryClient = useQueryClient()\r\n  const { handleSubmit, register } = useForm<IdentityUserCreateDto>()\r\n\r\n  const createUserMutation = useMutation({\r\n    mutationFn: async (userData: IdentityUserCreateDto) =>\r\n      postApiIdentityUsers({ body: userData }),\r\n    onSuccess: () => {\r\n      toast({\r\n        title: 'Success',\r\n        description: 'User Created Successfully',\r\n        variant: 'success',\r\n      })\r\n      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetUsers] })\r\n      setOpen(false)\r\n    },\r\n    onError: (err: unknown) => {\r\n      const error = handleApiError(err);\r\n      toast({\r\n        title: error.title,\r\n        description: error.description,\r\n        variant: 'error',\r\n      })\r\n    }\r\n  })\r\n\r\n  const onSubmit = (formData: IdentityUserCreateDto) => {\r\n    // Merge form data with checkbox states\r\n    const userData: IdentityUserCreateDto = {\r\n      ...formData,\r\n      isActive,\r\n      lockoutEnabled\r\n    }\r\n\r\n    createUserMutation.mutate(userData)\r\n  }\r\n\r\n  return (\r\n    <section>\r\n      <Toaster />\r\n      <Dialog open={open} onOpenChange={setOpen}>\r\n        <DialogTrigger asChild>{children}</DialogTrigger>\r\n        <section className=\"flex items-center justify-between pb-5\">\r\n          {can('AbpIdentity.Users.Create') && (\r\n            <Button size='sm' className=\"w-full sm:py-1 sm:mt-0 sm:w-fit\" onClick={() => setOpen(true)}>\r\n              <RiAddLine className=\"-ml-1 size-4 shrink-0\" aria-hidden=\"true\" />\r\n              <span className=\"hidden truncate sm:inline\">Create New User</span>\r\n            </Button>\r\n          )}\r\n        </section>\r\n        <DialogContent>\r\n          <DialogHeader>\r\n            <DialogTitle>Create a New User</DialogTitle>\r\n          </DialogHeader>\r\n          <form onSubmit={handleSubmit(onSubmit)} className='mt-2'>\r\n            <section className=\"flex w-full flex-col space-y-2\">\r\n              <FormSection>\r\n                <FormField\r\n                  label=\"Username\"\r\n                  description=\"The username of the user\"\r\n                >\r\n                  <Input required {...register('userName')} placeholder=\"Username\" />\r\n                </FormField>\r\n                <FormField\r\n                  label=\"Password\"\r\n                  description=\"The password of the user\"\r\n                >\r\n                  <Input required type=\"password\" {...register('password')} placeholder=\"Password\" />\r\n                </FormField>\r\n                <FormField\r\n                  label=\"Name\"\r\n                  description=\"The name of the user\"\r\n                >\r\n                  <Input required {...register('name')} placeholder=\"Name\" />\r\n                </FormField>\r\n                <FormField\r\n                  label=\"Surname\"\r\n                  description=\"The surname of the user\"\r\n                >\r\n                  <Input required {...register('surname')} placeholder=\"Surname\" />\r\n                </FormField>\r\n                <FormField\r\n                  label=\"Email\"\r\n                  description=\"The email of the user\"\r\n                >\r\n                  <Input required {...register('email')} placeholder=\"Email\" />\r\n                </FormField>\r\n                <FormField\r\n                  label=\"Phone Number\"\r\n                  description=\"The phone number of the user\"\r\n                >\r\n                  <Input required {...register('phoneNumber')} placeholder=\"Phone Number\" />\r\n                </FormField>\r\n                <FormField\r\n                  label=\"Is Active\"\r\n                  description=\"The active status of the user\"\r\n                >\r\n                  <Checkbox\r\n                    id=\"isActive\"\r\n                    name=\"isActive\"\r\n                    defaultChecked\r\n                    checked={isActive}\r\n                    onCheckedChange={(checked) => setIsActive(!!checked.valueOf())}\r\n                  />\r\n                </FormField>\r\n                <FormField\r\n                  label=\"Lockout Enabled\"\r\n                  description=\"The lockout status of the user\"\r\n                >\r\n                  <Checkbox\r\n                    id=\"lockoutEnabled\"\r\n                    name=\"lockoutEnabled\"\r\n                    defaultChecked\r\n                    checked={lockoutEnabled}\r\n                    onCheckedChange={(checked) => setLockoutEnabled(!!checked.valueOf())}\r\n                  />\r\n                </FormField>\r\n              </FormSection>\r\n            </section>\r\n            <DialogFooter className=\"mt-5\">\r\n              <Button\r\n                variant=\"ghost\"\r\n                onClick={(e) => {\r\n                  e.preventDefault()\r\n                  setOpen(false)\r\n                }}\r\n                disabled={createUserMutation.isPending}\r\n              >\r\n                Cancel\r\n              </Button>\r\n              <Button type=\"submit\" disabled={createUserMutation.isPending}>\r\n                {createUserMutation.isPending ? 'Saving...' : 'Save'}\r\n              </Button>\r\n            </DialogFooter>\r\n          </form>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </section>\r\n  )\r\n}\r\n", "'use client'\r\nimport { getApiImportTemplates, postApiImportCsv } from '@/client'\r\n\r\n// Define interfaces for API error handling\r\ninterface ApiErrorDetails {\r\n  status?: number;\r\n  title?: string;\r\n  detail?: string;\r\n  message?: string;\r\n  error?: {\r\n    message?: string;\r\n    details?: string;\r\n  };\r\n  [key: string]: unknown;\r\n}\r\n\r\ninterface ApiError {\r\n  error?: {\r\n    message?: string;\r\n    details?: string;\r\n  };\r\n  message?: string;\r\n  details?: ApiErrorDetails;\r\n  status?: number;\r\n  url?: string;\r\n}\r\nimport { Button } from '@/components/ui/button'\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n} from '@/components/ui/dialog'\r\nimport { useToast } from '@/lib/useToast'\r\nimport { QueryNames } from '@/lib/hooks/QueryConstants'\r\nimport { useMutation, useQueryClient } from '@tanstack/react-query'\r\nimport { useState } from 'react'\r\nimport { RiDownloadLine, RiUploadLine } from '@remixicon/react'\r\nimport { Label } from '@/components/ui/label'\r\nimport { Input } from '@/components/ui/input'\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\r\n\r\nexport const UploadUsersCsv = () => {\r\n  const { toast } = useToast()\r\n  const queryClient = useQueryClient()\r\n  const [open, setOpen] = useState(false)\r\n  const [file, setFile] = useState<File | null>(null)\r\n  const [isUploading, setIsUploading] = useState(false)\r\n  const [isDownloading, setIsDownloading] = useState(false)\r\n  const [uploadType, setUploadType] = useState<'users' | 'roles' | 'userroles'>('users')\r\n  // Handle file selection\r\n  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (e.target.files && e.target.files.length > 0) {\r\n      const selectedFile = e.target.files[0]\r\n\r\n      // Validate file type\r\n      if (!selectedFile?.name.toLowerCase().endsWith('.csv')) {\r\n        toast({\r\n          title: 'Invalid File Type',\r\n          description: 'Please select a CSV file',\r\n          variant: 'destructive',\r\n        })\r\n        // Reset the input\r\n        e.target.value = ''\r\n        return\r\n      }\r\n\r\n      setFile(selectedFile)\r\n    }\r\n  }\r\n\r\n  // Download template mutation\r\n  const downloadTemplateMutation = useMutation({\r\n    mutationFn: async () => {\r\n      setIsDownloading(true)\r\n      try {\r\n        const response = await getApiImportTemplates({\r\n          query: {\r\n            type: uploadType\r\n          }\r\n        })\r\n\r\n        // Create a blob from the response data\r\n        console.log('response.data', response.data)\r\n        const blob = new Blob([response.data as unknown as BlobPart], {\r\n          type: 'text/csv'\r\n        })\r\n\r\n        // Create a download link and trigger the download\r\n        const url = window.URL.createObjectURL(blob)\r\n        const a = document.createElement('a')\r\n        a.href = url\r\n        a.download = 'users_import_template.csv'\r\n        document.body.appendChild(a)\r\n        a.click()\r\n        window.URL.revokeObjectURL(url)\r\n        document.body.removeChild(a)\r\n\r\n        return response.data\r\n      } finally {\r\n        setIsDownloading(false)\r\n      }\r\n    },\r\n    onError: (err: unknown) => {\r\n      console.log('Error downloading template:', err)\r\n      const errorApi = err as ApiError\r\n\r\n      toast({\r\n        title: 'Download Failed',\r\n        description: errorApi.error?.message ?? 'Failed to download template',\r\n        variant: 'destructive',\r\n      })\r\n    }\r\n  })\r\n\r\n  // Upload CSV mutation\r\n  const uploadCsvMutation = useMutation({\r\n    mutationFn: async () => {\r\n      if (!file) {\r\n        throw new Error('No file selected')\r\n      }\r\n\r\n      setIsUploading(true)\r\n\r\n      try {\r\n        // Upload the file using the SDK function\r\n        // The SDK will handle the FormData creation internally\r\n        const formData = new FormData();\r\n        formData.append('File', file);\r\n        formData.append('Type', uploadType);\r\n        formData.append('Delimiter', ';');\r\n\r\n        const response = await postApiImportCsv({\r\n          body: {\r\n            File: file,\r\n            Type: uploadType,\r\n            Delimiter: ';'\r\n          },\r\n        })\r\n\r\n        return response.data\r\n      } finally {\r\n        setIsUploading(false)\r\n      }\r\n    },\r\n    onSuccess: () => {\r\n      // Check if there were any errors during import\r\n      toast({\r\n        title: 'Import Successful',\r\n        description: `Imported successfully`,\r\n        variant: 'success',\r\n      })\r\n\r\n      // Refresh the users list\r\n      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetUsers] })\r\n\r\n      // Close the dialog\r\n      setOpen(false)\r\n\r\n      // Reset the file input\r\n      setFile(null)\r\n    },\r\n    onError: (err: unknown) => {\r\n      console.debug('Error uploading CSV:', err)\r\n      const errorApi = err as ApiError\r\n\r\n      // Default error message in case the structure is unexpected\r\n      let errorTitle = 'Upload Failed';\r\n      let errorDetails = 'An error occurred while uploading the file';\r\n\r\n      // Safely access nested properties\r\n      if (errorApi.details?.error?.message) {\r\n        errorTitle = errorApi.details.error.message;\r\n      }\r\n\r\n      if (errorApi.details?.error?.details) {\r\n        errorDetails = errorApi.details.error.details;\r\n      }\r\n\r\n      toast({\r\n        title: errorTitle,\r\n        description: errorDetails,\r\n        variant: 'destructive',\r\n      })\r\n    }\r\n  })\r\n\r\n  // Handle download template button click\r\n  const handleDownloadTemplate = () => {\r\n    downloadTemplateMutation.mutate()\r\n  }\r\n\r\n  // Handle upload button click\r\n  const handleUpload = () => {\r\n    if (!file) {\r\n      toast({\r\n        title: 'No File Selected',\r\n        description: 'Please select a CSV file to upload',\r\n        variant: 'warning',\r\n      })\r\n      return\r\n    }\r\n\r\n    uploadCsvMutation.mutate()\r\n  }\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={setOpen}>\r\n      <DialogTrigger asChild>\r\n        <Button size=\"sm\" className=\"w-full sm:py-1 sm:mt-0 sm:w-fit\">\r\n          <RiUploadLine className=\"-ml-1 size-4 shrink-0\" aria-hidden=\"true\" />\r\n          <span className=\"hidden truncate sm:inline\">Upload CSV</span>\r\n        </Button>\r\n      </DialogTrigger>\r\n      <DialogContent className=\"sm:max-w-md\">\r\n        <DialogHeader>\r\n          <DialogTitle>Upload Users CSV</DialogTitle>\r\n        </DialogHeader>\r\n\r\n        <div className=\"flex flex-col gap-4 py-4\">\r\n          <div className=\"flex flex-col gap-2\">\r\n            <Label htmlFor=\"csvFile\" className=\"text-left\">Upload Type</Label>\r\n            <Select value={uploadType} onValueChange={(value) => setUploadType(value as \"users\" | \"roles\" | \"userroles\")}>\r\n              <SelectTrigger className='w-full'>\r\n                <SelectValue placeholder=\"Select upload type\" />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                <SelectItem value=\"users\">Users</SelectItem>\r\n                <SelectItem value=\"roles\">Roles</SelectItem>\r\n                <SelectItem value=\"userroles\">User Roles</SelectItem>\r\n              </SelectContent>\r\n            </Select>\r\n          </div>\r\n          <div className=\"flex flex-col gap-2\">\r\n            <Label htmlFor=\"template\" className=\"text-left\">Download Template</Label>\r\n            <div className=\"flex items-center gap-2\">\r\n              <Button\r\n                variant=\"outline\"\r\n                onClick={handleDownloadTemplate}\r\n                disabled={isDownloading}\r\n                className=\"w-full\"\r\n              >\r\n                <RiDownloadLine className=\"mr-2 h-4 w-4\" />\r\n                {isDownloading ? 'Downloading...' : 'Download Template'}\r\n              </Button>\r\n            </div>\r\n            <p className=\"text-sm text-muted-foreground\">\r\n              Download the template first, fill it with your data, and then upload it.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"flex flex-col gap-2\">\r\n            <Label htmlFor=\"csvFile\" className=\"text-left\">Upload CSV File</Label>\r\n            <Input\r\n              id=\"csvFile\"\r\n              type=\"file\"\r\n              accept=\".csv\"\r\n              onChange={handleFileChange}\r\n              disabled={isUploading}\r\n            />\r\n            {file && (\r\n              <p className=\"text-sm text-muted-foreground\">\r\n                Selected file: {file.name}\r\n              </p>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        <DialogFooter>\r\n          <Button\r\n            variant=\"ghost\"\r\n            onClick={() => {\r\n              setOpen(false)\r\n              setFile(null)\r\n            }}\r\n            disabled={isUploading}\r\n          >\r\n            Cancel\r\n          </Button>\r\n          <Button\r\n            onClick={handleUpload}\r\n            disabled={!file || isUploading}\r\n          >\r\n            {isUploading ? 'Uploading...' : 'Upload'}\r\n          </Button>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n\r\n", "'use client'\r\n\r\nimport { type IdentityUserUpdateDto } from '@/client'\r\nimport { useGrantedPolicies } from '@/lib/hooks/useGrantedPolicies'\r\nimport { RiMoreLine, RiPencilLine, RiShieldKeyholeLine } from '@remixicon/react'\r\nimport { Button } from '@/components/ui/button'\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu'\r\n\r\ntype UserActionProps = {\r\n  userId: string\r\n  userDto: IdentityUserUpdateDto\r\n  onAction: (userId: string, userDto: IdentityUserUpdateDto, dialogType: 'edit' | 'permission' | 'delete') => void\r\n  variant?: 'dropdown' | 'buttons'\r\n}\r\n\r\nexport const UserActions = ({ userId, userDto, onAction, variant = 'dropdown' }: UserActionProps) => {\r\n  const { can } = useGrantedPolicies()\r\n\r\n  // For dropdown menu style (first image)\r\n  if (variant === 'dropdown') {\r\n    return (\r\n      <div className=\"flex justify-end\">\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"h-8 w-8 p-0\"\r\n            >\r\n              <RiMoreLine className=\"h-4 w-4\" />\r\n              <span className=\"sr-only\">Open menu</span>\r\n            </Button>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent align=\"end\" className=\"w-[160px]\">\r\n            {can('AbpIdentity.Users.ManagePermissions') && (\r\n              <DropdownMenuItem\r\n                className=\"cursor-pointer text-sm\"\r\n                onClick={() => onAction(userId, userDto, 'permission')}\r\n              >\r\n                Permission\r\n              </DropdownMenuItem>\r\n            )}\r\n            {can('AbpIdentity.Users.Update') && (\r\n              <DropdownMenuItem\r\n                className=\"cursor-pointer text-sm\"\r\n                onClick={() => onAction(userId, userDto, 'edit')}\r\n              >\r\n                Edit\r\n              </DropdownMenuItem>\r\n            )}\r\n            {can('AbpIdentity.Users.Delete') && (\r\n              <DropdownMenuItem\r\n                className=\"cursor-pointer text-sm text-red-500\"\r\n                onClick={() => onAction(userId, userDto, 'delete')}\r\n              >\r\n                Delete\r\n              </DropdownMenuItem>\r\n            )}\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // For button group style (second image)\r\n  return (\r\n    <div className=\"flex items-center justify-end gap-1\">\r\n      {can('AbpIdentity.Users.ManagePermissions') && (\r\n        <Button\r\n          variant=\"primary\"\r\n          size=\"sm\"\r\n          className=\"flex items-center gap-1 px-2 py-1\"\r\n          onClick={() => onAction(userId, userDto, 'permission')}\r\n        >\r\n          <RiShieldKeyholeLine className=\"h-4 w-4\" />\r\n          <span>Permission</span>\r\n        </Button>\r\n      )}\r\n      {can('AbpIdentity.Users.Update') && (\r\n        <Button\r\n          variant=\"primary\"\r\n          size=\"sm\"\r\n          className=\"flex items-center gap-1 px-2 py-1\"\r\n          onClick={() => onAction(userId, userDto, 'edit')}\r\n        >\r\n          <RiPencilLine className=\"h-4 w-4\" />\r\n          <span>Edit</span>\r\n        </Button>\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n", "'use client'\r\n\r\nimport { cn } from '@/lib/utils'\r\n\r\ntype StatusBadgeProps = {\r\n  status: boolean | string\r\n}\r\n\r\nexport const StatusBadge = ({ status }: StatusBadgeProps) => {\r\n  // Convert boolean to string status\r\n  let statusText = 'Inactive'\r\n  let statusClass = 'bg-gray-100 text-gray-700 border-gray-200'\r\n\r\n  if (typeof status === 'boolean') {\r\n    if (status) {\r\n      statusText = 'Active'\r\n      statusClass = 'bg-green-50 text-green-700 border-green-200'\r\n    }\r\n  } else if (typeof status === 'string') {\r\n    // Handle string status values\r\n    statusText = status\r\n\r\n    if (status.toLowerCase() === 'active') {\r\n      statusClass = 'bg-green-50 text-green-700 border-green-200'\r\n    } else if (status.toLowerCase() === 'archived') {\r\n      statusClass = 'bg-amber-50 text-amber-700 border-amber-200'\r\n    } else if (status.toLowerCase() === 'inactive') {\r\n      statusClass = 'bg-gray-100 text-gray-700 border-gray-200'\r\n    }\r\n  }\r\n\r\n  return (\r\n    <span className={cn(\r\n      'inline-flex items-center rounded-md border px-2 py-1 text-xs font-medium',\r\n      statusClass\r\n    )}>\r\n      {statusText}\r\n    </span>\r\n  )\r\n}\r\n", "'use client'\r\n\r\nimport { type IdentityUserDto, type IdentityUserUpdateDto } from '@/client'\r\nimport { type ColumnDef } from '@tanstack/react-table'\r\nimport { UserActions } from './UserActions'\r\nimport { StatusBadge } from './StatusBadge'\r\nimport { Checkbox } from '@/components/ui/checkbox'\r\nimport { DataTableColumnHeader } from '@/components/data-table/DataTableColumnHeader'\r\n\r\n// Type for the callback function to handle user actions\r\ntype UserActionCallback = (userId: string, userDto: IdentityUserUpdateDto, dialogType: 'edit' | 'permission' | 'delete') => void\r\n\r\n// Function to create user columns with the action callback\r\nexport const getUserColumns = (\r\n  handleUserAction: UserActionCallback\r\n): ColumnDef<IdentityUserDto>[] => {\r\n  return [\r\n    {\r\n      id: \"select\",\r\n      header: ({ table }) => (\r\n        <Checkbox\r\n          checked={\r\n            table.getIsAllPageRowsSelected()\r\n              ? true\r\n              : table.getIsSomeRowsSelected()\r\n                ? \"indeterminate\"\r\n                : false\r\n          }\r\n          onCheckedChange={() => table.toggleAllPageRowsSelected()}\r\n          className=\"translate-y-0.5\"\r\n          aria-label=\"Select all\"\r\n        />\r\n      ),\r\n      cell: ({ row }) => (\r\n        <Checkbox\r\n          checked={row.getIsSelected()}\r\n          onCheckedChange={() => row.toggleSelected()}\r\n          className=\"translate-y-0.5\"\r\n          aria-label=\"Select row\"\r\n        />\r\n      ),\r\n      enableSorting: false,\r\n      enableHiding: false,\r\n      meta: {\r\n        displayName: \"Select\",\r\n      },\r\n    },\r\n    {\r\n      accessorKey: 'userName',\r\n      header: ({ column }) => <DataTableColumnHeader column={column} title=\"Username\" />,\r\n      enableHiding: true,\r\n      enableSorting: true,\r\n      cell: (info) => info.getValue(),\r\n      meta: {\r\n        className: \"text-left\",\r\n        displayName: \"Username\",\r\n      },\r\n    },\r\n    {\r\n      accessorKey: 'name',\r\n      header: ({ column }) => <DataTableColumnHeader column={column} title=\"Name\" />,\r\n      enableHiding: true,\r\n      enableSorting: true,\r\n      cell: (info) => info.getValue(),\r\n      meta: {\r\n        className: \"text-left\",\r\n        displayName: \"Name\",\r\n      },\r\n    },\r\n    {\r\n      accessorKey: 'extraProperties.Company',\r\n      header: ({ column }) => <DataTableColumnHeader column={column} title=\"Company\" />,\r\n      enableHiding: true,\r\n      enableSorting: true,\r\n      cell: (info) => info.getValue(),\r\n      meta: {\r\n        className: \"text-left\",\r\n        displayName: \"Company\",\r\n      },\r\n    },\r\n    {\r\n      accessorKey: 'email',\r\n      header: ({ column }) => <DataTableColumnHeader column={column} title=\"Email\" />,\r\n      enableSorting: true,\r\n      enableHiding: true,\r\n      cell: (info) => info.getValue(),\r\n      meta: {\r\n        className: \"text-left\",\r\n        displayName: \"Email\",\r\n      },\r\n    },\r\n    {\r\n      accessorKey: 'isActive',\r\n      header: ({ column }) => <DataTableColumnHeader column={column} title=\"Status\" />,\r\n      enableHiding: true,\r\n      enableSorting: true,\r\n      cell: (info) => <StatusBadge status={info.getValue() as boolean} />,\r\n      meta: {\r\n        className: \"text-left\",\r\n        displayName: \"Status\",\r\n      },\r\n    },\r\n    {\r\n      id: 'actions',\r\n      header: \"Actions\",\r\n      enableHiding: true,\r\n      cell: (info) => (\r\n        <UserActions\r\n          userId={info.row.original.id!}\r\n          userDto={info.row.original as IdentityUserUpdateDto}\r\n          onAction={handleUserAction}\r\n          variant=\"dropdown\" // Use \"dropdown\" for the first image style or \"buttons\" for the second image style\r\n        />\r\n      ),\r\n      meta: {\r\n        className: \"text-right\",\r\n        displayName: \"Actions\",\r\n      },\r\n    },\r\n  ]\r\n}\r\n\r\n", "\"use client\"\r\n\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\nimport { type Table } from \"@tanstack/react-table\"\r\nimport { ViewOptions } from \"@/components/data-table/DataTableViewOptions\"\r\nimport { Search } from \"@/components/ui/search\"\r\n\r\ninterface UserFilterbarProps<TData> {\r\n  table: Table<TData>\r\n  onSearch?: (value: string) => void\r\n  searchValue?: string\r\n}\r\n\r\nexport function UserFilterbar<TData>({\r\n  table,\r\n  onSearch,\r\n  searchValue = \"\",\r\n}: UserFilterbarProps<TData>) {\r\n  const isFiltered = table.getState().columnFilters.length > 0\r\n\r\n  return (\r\n    <div className=\"flex flex-wrap items-center justify-between gap-2\">\r\n      {onSearch && (\r\n        <div className=\"w-full sm:w-auto sm:max-w-[250px]\">\r\n          <Search onUpdate={onSearch} value={searchValue} />\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"flex items-center gap-2 ml-auto\">\r\n        {isFiltered && (\r\n          <Button\r\n            variant=\"ghost\"\r\n            onClick={() => table.resetColumnFilters()}\r\n            className=\"border border-gray-200 px-2 font-semibold text-indigo-600 sm:border-none sm:py-1 dark:border-gray-800 dark:text-indigo-500\"\r\n          >\r\n            Clear filters\r\n          </Button>\r\n        )}\r\n        {/* <Button\r\n          variant=\"secondary\"\r\n          className=\"gap-x-2 px-2 py-1.5 text-sm sm:text-xs\"\r\n        >\r\n          <RiDownloadLine className=\"size-4 shrink-0\" aria-hidden=\"true\" />\r\n          <span className=\"hidden sm:inline\">Export</span>\r\n        </Button> */}\r\n        <ViewOptions table={table} />\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n", "'use client'\r\nimport { QueryNames } from '@/lib/hooks/QueryConstants'\r\nimport { useUsers } from '@/lib/hooks/useUsers'\r\nimport { useState } from 'react'\r\n\r\nimport { type IdentityUserUpdateDto } from '@/client'\r\nimport Error from '@/components/ui/Error'\r\nimport { type PaginationState, type SortingState } from '@tanstack/react-table'\r\n\r\nimport { useToast } from \"@/hooks/use-toast\"\r\nimport { DeleteUser } from './DeleteUser'\r\nimport { UserEdit } from './UserEdit'\r\nimport { UserPermission } from './UserPermission'\r\nimport { AddUser } from '@/components/app/users/AddUser'\r\nimport { UploadUsersCsv } from '@/components/app/users/UploadUsersCsv'\r\n\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { DataTable } from '@/components/data-table/DataTable'\r\nimport { getUserColumns } from './columns'\r\nimport { UserFilterbar } from './UserFilterbar'\r\nimport { TableSkeleton } from '@/components/ui/TableSkeleton'\r\n\r\nexport const UserList = () => {\r\n  const { toast } = useToast()\r\n  const queryClient = useQueryClient()\r\n\r\n  const [searchStr, setSearchStr] = useState<string>('')\r\n  const [userActionDialog, setUserActionDialog] = useState<{\r\n    userId: string\r\n    userDto: IdentityUserUpdateDto\r\n    dialogType?: 'edit' | 'permission' | 'delete'\r\n  } | null>()\r\n\r\n  const [pagination, setPagination] = useState<PaginationState>({\r\n    pageIndex: 0,\r\n    pageSize: 10,\r\n  })\r\n\r\n  // Initialize sorting state\r\n  const [sorting, setSorting] = useState<SortingState>([\r\n    { id: 'name', desc: false }\r\n  ])\r\n\r\n  // Convert SortingState to API sorting string\r\n  const getSortingString = (sortState: SortingState): string => {\r\n    if (!sortState.length) return 'name asc';\r\n\r\n    const sort = sortState[0] ?? { id: 'name', desc: false };\r\n    return `${sort.id} ${sort.desc ? 'desc' : 'asc'}`;\r\n  }\r\n\r\n  const { isLoading, data, isError } = useUsers(\r\n    pagination.pageIndex,\r\n    pagination.pageSize,\r\n    searchStr,\r\n    getSortingString(sorting)\r\n  )\r\n\r\n  // Handler for user actions (edit, permission, delete)\r\n  const handleUserAction = (userId: string, userDto: IdentityUserUpdateDto, dialogType: 'edit' | 'permission' | 'delete') => {\r\n    setUserActionDialog({\r\n      userId,\r\n      userDto,\r\n      dialogType,\r\n    })\r\n  }\r\n\r\n  // Get columns with the action handler\r\n  const columns = getUserColumns(handleUserAction)\r\n\r\n  const handleSearch = (value: string) => {\r\n    setSearchStr(value)\r\n    setPagination(prev => ({ ...prev, pageIndex: 0 })) // Reset to first page on search\r\n  }\r\n\r\n  const handlePaginationChange = (newPagination: PaginationState) => {\r\n    setPagination(newPagination)\r\n  }\r\n\r\n  // Handler for sorting change\r\n  const handleSortingChange = (newSorting: SortingState) => {\r\n    setSorting(newSorting)\r\n    setPagination(prev => ({ ...prev, pageIndex: 0 })) // Reset to first page on sort change\r\n  }\r\n\r\n  // Handler for refreshing the data\r\n  const handleRefresh = () => {\r\n    // Invalidate the query to fetch fresh data\r\n    void queryClient.invalidateQueries({ queryKey: [QueryNames.GetUsers] })\r\n\r\n    // Show toast notification after a short delay to match the animation\r\n    setTimeout(() => {\r\n      toast({\r\n        title: \"Data refreshed\",\r\n        description: \"The user list has been refreshed.\",\r\n        variant: \"default\",\r\n      })\r\n    }, 800)\r\n  }\r\n\r\n  if (isLoading) return (\r\n    <TableSkeleton\r\n      rowCount={pagination.pageSize}\r\n      columnCount={4}\r\n      hasTitle={true}\r\n      hasSearch={true}\r\n      hasFilters={true}\r\n      hasPagination={true}\r\n      hasActions={true}\r\n    />\r\n  )\r\n  if (isError) return <Error />\r\n\r\n  return (\r\n    <>\r\n      {userActionDialog && userActionDialog.dialogType === 'edit' && (\r\n        <UserEdit\r\n          userId={userActionDialog.userId}\r\n          userDto={userActionDialog.userDto}\r\n          onDismiss={() => {\r\n            void queryClient.invalidateQueries({ queryKey: [QueryNames.GetUsers] })\r\n            setUserActionDialog(null)\r\n          }}\r\n        />\r\n      )}\r\n      {userActionDialog && userActionDialog.dialogType === 'permission' && (\r\n        <UserPermission\r\n          userId={userActionDialog.userId}\r\n          userDto={userActionDialog.userDto}\r\n          onDismiss={() => setUserActionDialog(null)}\r\n        />\r\n      )}\r\n      {userActionDialog && userActionDialog.dialogType === 'delete' && (\r\n        <DeleteUser\r\n          user={{\r\n            username: userActionDialog.userDto.userName,\r\n            userId: userActionDialog.userId,\r\n          }}\r\n          onDismiss={() => {\r\n            void queryClient.invalidateQueries({ queryKey: [QueryNames.GetUsers] })\r\n            setUserActionDialog(null)\r\n          }}\r\n        />\r\n      )}\r\n\r\n      <div className=\"space-y-4\">\r\n        <DataTable\r\n          title=\"Users Management\"\r\n          columns={columns}\r\n          data={data?.items ?? []}\r\n          totalCount={data?.totalCount}\r\n          isLoading={isLoading}\r\n          manualPagination={true}\r\n          manualSorting={true}\r\n          pageSize={pagination.pageSize}\r\n          onPaginationChange={handlePaginationChange}\r\n          onSortingChange={handleSortingChange}\r\n          sortingState={sorting}\r\n          onSearch={handleSearch}\r\n          searchValue={searchStr}\r\n          customFilterbar={UserFilterbar}\r\n          hideDefaultFilterbar={true}\r\n          onRefresh={handleRefresh}\r\n          enableRowSelection={false}\r\n          actionButton={{\r\n            // label: \"Create New User\",\r\n            // No action needed - this is handled by the custom cell renderer\r\n            onClick: () => { /* Required but not used */ },\r\n            content: (\r\n              <div className=\"flex gap-2\">\r\n                <AddUser />\r\n                <UploadUsersCsv />\r\n              </div>\r\n            )\r\n          }}\r\n        />\r\n      </div>\r\n    </>\r\n  )\r\n}\r\n", "'use client';\r\n\r\nimport { UserList } from '@/components/app/users/UserList';\r\nimport AppLayout from '@/layouts/app-layout';\r\nimport { Head } from '@inertiajs/react';\r\nexport default function OverViewLayout() {\r\n  return (\r\n    <AppLayout>\r\n      <Head title=\"Dashboard\" />\r\n\r\n      <UserList />\r\n    </AppLayout>\r\n  );\r\n}\r\n"], "names": ["useUsers", "pageIndex", "pageSize", "filter", "sorting", "useQuery", "QueryNames", "skip", "getApiIdentityUsers", "DeleteUser", "userId", "username", "on<PERSON><PERSON><PERSON>", "toast", "useToast", "open", "<PERSON><PERSON><PERSON>", "useState", "onYesEvent", "deleteApiIdentityUsersById", "err", "useEffect", "jsx", "AlertDialog", "jsxs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Alert<PERSON><PERSON><PERSON>H<PERSON>er", "AlertDialogTitle", "AlertDialogDescription", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AlertDialogCancel", "AlertDialogAction", "Tabs", "className", "props", "TabsPrimitive.Root", "cn", "TabsList", "TabsPrimitive.List", "TabsTrigger", "TabsPrimitive.Trigger", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TabsPrimitive.Content", "useAssignableRoles", "data", "getApiIdentityUsersAssignableRoles", "useUserRoles", "getApiIdentityUsersByIdRoles", "TABS_NAME", "UserEdit", "userDto", "queryClient", "useQueryClient", "handleSubmit", "register", "useForm", "roles", "setRoles", "userRole", "assignableRoles", "createDataMutation", "useMutation", "dataMutation", "putApiIdentityUsersById", "error", "handleApiError", "onSubmit", "formData", "userData", "onCloseEvent", "temp", "r", "onRoleAssignEvent", "useCallback", "role", "hasAssignedRoleExistAlready", "onRoleAssignedSaveEvent", "e", "updateUserDto", "Dialog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "DialogTitle", "FormSection", "FormField", "Input", "<PERSON><PERSON><PERSON><PERSON>er", "<PERSON><PERSON>", "Loader", "classNames", "Checkbox", "l", "v4", "UserPermission", "userRoles", "hasAllGranted", "setHasAllGranted", "usePermissions", "PermissionProvider", "permissionGroups", "setPermissionGroups", "hasAllPermissionGranted", "g", "p", "updatedGroups", "group", "permission", "requestPayload", "grant", "putApiPermissionManagementPermissions", "has<PERSON>dmin", "useMemo", "USER_ROLE", "formatDisplayName", "str", "groupedPermissions", "result", "i", "Permission", "prev", "row", "rowIndex", "colIndex", "displayName", "permissions", "TogglePermission", "AddUser", "children", "can", "useGrantedPolicies", "isActive", "setIsActive", "lockoutEnabled", "setLockoutEnabled", "createUserMutation", "postApiIdentityUsers", "Toaster", "DialogTrigger", "RiAddLine", "checked", "UploadUsersCsv", "file", "setFile", "isUploading", "setIsUploading", "isDownloading", "setIsDownloading", "uploadType", "setUploadType", "handleFileChange", "selectedFile", "downloadTemplateMutation", "response", "getApiImportTemplates", "blob", "url", "a", "uploadCsvMutation", "postApiImportCsv", "errorApi", "errorTitle", "errorDetails", "handleDownloadTemplate", "handleUpload", "RiUploadLine", "Label", "Select", "value", "SelectTrigger", "SelectValue", "SelectContent", "SelectItem", "RiDownloadLine", "UserActions", "onAction", "variant", "DropdownMenu", "DropdownMenuTrigger", "RiMoreLine", "DropdownMenuContent", "DropdownMenuItem", "RiShieldKeyholeLine", "RiPencilLine", "StatusBadge", "status", "statusText", "statusClass", "getUserColumns", "handleUserAction", "table", "column", "DataTableColumnHeader", "info", "UserFilterbar", "onSearch", "searchValue", "isFiltered", "Search", "ViewOptions", "UserList", "searchStr", "setSearchStr", "userActionDialog", "setUserActionDialog", "pagination", "setPagination", "setSorting", "getSortingString", "sortState", "sort", "isLoading", "isError", "columns", "dialogType", "handleSearch", "handlePaginationChange", "newPagination", "handleSortingChange", "newSorting", "handleRefresh", "TableSkeleton", "Error", "Fragment", "DataTable", "OverViewLayout", "AppLayout", "Head"], "mappings": "yvCAiBO,MAAMA,GAAW,CACtBC,EACAC,EACAC,EACAC,IAEOC,EAAS,CACd,SAAU,CAACC,EAAW,SAAUL,EAAWC,EAAUC,EAAQC,CAAO,EACpE,QAAS,SAAY,CACnB,IAAIG,EAAO,EACX,OAAIN,EAAY,IACdM,EAAON,EAAYC,IAEJ,MAAMM,GAAoB,CACzC,MAAO,CACL,eAAgBN,EAChB,UAAWK,EACX,OAAQJ,EACR,QAASC,CAAA,CACX,CACD,GACe,IAAA,CAClB,CACD,ECtBUK,GAAa,CAAC,CAAE,KAAM,CAAE,OAAAC,EAAQ,SAAAC,CAAA,EAAY,UAAAC,KAAiC,CAClF,KAAA,CAAE,MAAAC,CAAM,EAAIC,EAAS,EACrB,CAACC,EAAMC,CAAO,EAAIC,EAAAA,SAAkB,EAAK,EACzCC,EAAa,SAAY,CACzB,GAAA,CACF,MAAMC,GAA2B,CAC/B,KAAM,CAAE,GAAIT,CAAO,CAAA,CACpB,EACKG,EAAA,CACJ,MAAO,UACP,YAAa,SAASF,CAAQ,kCAAA,CAC/B,EACSC,EAAA,QACHQ,EAAc,CACjBA,aAAe,OACXP,EAAA,CACJ,MAAO,SACP,YAAa,+CAA+CF,CAAQ,uBACpE,QAAS,aAAA,CACV,CACH,CAEJ,EAEAU,OAAAA,EAAAA,UAAU,IAAM,CACdL,EAAQ,EAAI,CACd,EAAG,EAAE,EAGFM,EAAA,IAAAC,GAAA,CAAY,KAAAR,EACX,SAAAS,OAACC,GACC,CAAA,SAAA,CAAAD,OAACE,GACC,CAAA,SAAA,CAAAJ,EAAAA,IAACK,IAAiB,SAAwB,0BAAA,CAAA,SACzCC,GAAuB,CAAA,SAAA,CAAA,8EAErBjB,EAAS,GAAA,CACZ,CAAA,CAAA,EACF,SACCkB,GACC,CAAA,SAAA,CAACP,EAAA,IAAAQ,GAAA,CAAkB,QAASlB,EAAW,SAAM,SAAA,EAC5CU,EAAA,IAAAS,GAAA,CAAkB,QAASb,EAAY,SAAG,KAAA,CAAA,CAAA,CAC7C,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,ECxDA,SAASc,GAAK,CACZ,UAAAC,EACA,GAAGC,CACL,EAAoD,CAEhD,OAAAZ,EAAA,IAACa,GAAA,CACC,YAAU,OACV,UAAWC,EAAG,sBAAuBH,CAAS,EAC7C,GAAGC,CAAA,CACN,CAEJ,CAEA,SAASG,GAAS,CAChB,UAAAJ,EACA,GAAGC,CACL,EAAoD,CAEhD,OAAAZ,EAAA,IAACgB,GAAA,CACC,YAAU,YACV,UAAWF,EACT,sGACAH,CACF,EACC,GAAGC,CAAA,CACN,CAEJ,CAEA,SAASK,EAAY,CACnB,UAAAN,EACA,GAAGC,CACL,EAAuD,CAEnD,OAAAZ,EAAA,IAACkB,GAAA,CACC,YAAU,eACV,UAAWJ,EACT,kqBACAH,CACF,EACC,GAAGC,CAAA,CACN,CAEJ,CAEA,SAASO,EAAY,CACnB,UAAAR,EACA,GAAGC,CACL,EAAuD,CAEnD,OAAAZ,EAAA,IAACoB,GAAA,CACC,YAAU,eACV,UAAWN,EAAG,sBAAuBH,CAAS,EAC7C,GAAGC,CAAA,CACN,CAEJ,CClDO,MAAMS,GAAqB,IACzBtC,EAAS,CACd,SAAU,CAACC,EAAW,kBAAkB,EACxC,QAAS,SAAY,CACnB,KAAM,CAAE,KAAAsC,GAAS,MAAMC,GAAmC,EACnD,OAAAD,CAAA,CACT,CACD,ECHUE,GAAe,CAAC,CAAE,OAAApC,KACtBL,EAAS,CACd,SAAU,CAACC,EAAW,aAAcI,CAAM,EAC1C,QAAS,SAAY,CACnB,KAAM,CAAE,KAAAkC,GAAS,MAAMG,GAA6B,CAClD,KAAM,CACJ,GAAIrC,CAAA,CACN,CACD,EACM,OAAAkC,CAAA,CACT,CACD,ECFGI,EAAY,CAChB,WAAY,YACZ,kBAAmB,kBACrB,EAYaC,GAAW,CAAC,CAAE,QAAAC,EAAS,OAAAxC,EAAQ,UAAAE,KAA+B,CACzE,KAAM,CAACG,EAAMC,CAAO,EAAIC,EAAAA,SAAS,EAAK,EAChC,CAAE,MAAAJ,CAAM,EAAIC,EAAS,EACrBqC,EAAcC,EAAe,EAC7B,CAAE,aAAAC,EAAc,SAAAC,CAAS,EAAIC,EAA+B,EAC5D,CAACC,EAAOC,CAAQ,EAAIxC,EAAAA,SAAqB,CAAA,CAAE,EAC3CyC,EAAWZ,GAAa,CAAE,OAAApC,EAAQ,EAClCiD,EAAkBhB,GAAmB,EAErCiB,EAAqBC,EAAY,CACrC,WAAY,MAAOC,GACjBC,GAAwB,CACtB,KAAM,CAAE,GAAIrD,CAAO,EACnB,KAAM,CAAE,GAAGwC,EAAS,GAAGY,CAAa,CAAA,CACrC,EACH,UAAW,IAAM,CACTjD,EAAA,CACJ,MAAO,UACP,YAAa,kCACb,QAAS,SAAA,CACV,EACIsC,EAAY,kBAAkB,CAAE,SAAU,CAAC7C,EAAW,QAAQ,EAAG,EACtEU,EAAQ,EAAK,CACf,EACA,QAAUI,GAAiB,CAEnB,MAAA4C,EAAQC,EAAe7C,CAAG,EAC1BP,EAAA,CACJ,MAAOmD,EAAM,MACb,YAAaA,EAAM,YACnB,QAAS,OAAA,CACV,CAAA,CACH,CACD,EAEKE,EAAYC,GAAoC,CAEpD,MAAMC,EAAkC,CACtC,GAAGD,CACL,EAGKP,EAAmB,OAAOQ,CAAQ,CACzC,EAEMC,EAAe,IAAM,CACzBrD,EAAQ,EAAK,EACHJ,EAAA,CACZ,EAEAS,EAAAA,UAAU,IAAM,CACdL,EAAQ,EAAI,CACd,EAAG,EAAE,EAELK,EAAAA,UAAU,IAAM,CACV,GAAAqC,EAAS,MAAM,MAAO,CACxB,MAAMY,EAAmB,CAAC,EAC1BZ,EAAS,KAAK,MAAM,QAASa,GAAM,CAC5BD,EAAA,KAAK,CAAE,KAAMC,EAAE,KAAO,GAAIA,EAAE,GAAK,CAAA,CACvC,EACDd,EAASa,CAAI,CAAA,CAEd,EAAA,CAACZ,EAAS,MAAM,KAAK,CAAC,EAEzB,MAAMc,EAAoBC,EAAA,YACvBC,GAA0B,CACnB,MAAAC,EAA8BnB,EAAM,UAAWe,GAAMG,EAAK,KAAOH,EAAE,EAAE,EAGvEI,IAAgC,IAC5BnB,EAAA,OAAOmB,EAA6B,CAAC,EAClClB,EAAA,CAAC,GAAGD,CAAK,CAAC,IAEbA,EAAA,KAAK,CAAE,KAAMkB,EAAK,KAAO,GAAIA,EAAK,GAAK,EACpCjB,EAAA,CAAC,GAAGD,CAAK,CAAC,EAEvB,EACA,CAACA,CAAK,CACR,EAEMoB,EAA2BC,GAAkB,CACjDA,EAAE,eAAe,EACjB,MAAMC,EAAuC,CAC3C,GAAG5B,EACH,UAAWM,GAAO,IAAKe,GAAMA,EAAE,IAAI,GAAK,CAAA,CAC1C,EACAL,EAASY,CAAa,CACxB,EAEE,OAAAxD,EAAAA,IAACyD,EAAO,CAAA,KAAAhE,EAAY,aAAcsD,EAChC,gBAACW,EAAc,CAAA,KAAK,MAAM,UAAU,GAClC,SAAA,CAAC1D,EAAA,IAAA2D,EAAA,CACC,gBAACC,EAAY,CAAA,SAAA,CAAA,kBAAgBhC,EAAQ,QAAA,CAAA,CAAS,CAChD,CAAA,EACC1B,EAAA,KAAAQ,GAAA,CAAK,aAAcgB,EAAU,WAC5B,SAAA,CAACxB,EAAAA,KAAAa,GAAA,CAAS,UAAU,SAClB,SAAA,CAAAf,MAACiB,GAAY,MAAOS,EAAU,WAAY,UAAU,SAAS,SAE7D,mBAAA,QACCT,EAAY,CAAA,MAAOS,EAAU,kBAAmB,UAAU,SAAS,SAEpE,OAAA,CAAA,CAAA,EACF,EACA1B,EAAAA,IAACmB,EAAY,CAAA,MAAOO,EAAU,WAC5B,gBAAC,OAAK,CAAA,SAAUK,EAAaa,CAAQ,EACnC,SAAA,CAAA5C,MAAC,UAAQ,CAAA,UAAU,+BACjB,SAAAE,EAAA,KAAC2D,EACC,CAAA,SAAA,CAAA7D,EAAA,IAAC8D,EAAA,CACC,MAAM,OACN,YAAY,uBAEZ,SAAC9D,EAAA,IAAA+D,EAAA,CAAM,SAAQ,GAAE,GAAG/B,EAAS,MAAM,EAAG,aAAcJ,EAAQ,MAAQ,GAAI,YAAY,MAAO,CAAA,CAAA,CAC7F,EAEA5B,EAAA,IAAC8D,EAAA,CACC,MAAM,UACN,YAAY,0BAEZ,SAAC9D,EAAA,IAAA+D,EAAA,CAAM,SAAQ,GAAE,GAAG/B,EAAS,SAAS,EAAG,aAAcJ,EAAQ,SAAW,GAAI,YAAY,SAAU,CAAA,CAAA,CACtG,EAEA5B,EAAA,IAAC8D,EAAA,CACC,MAAM,QACN,YAAY,wBAEZ,SAAC9D,EAAA,IAAA+D,EAAA,CAAM,SAAQ,GAAE,GAAG/B,EAAS,OAAO,EAAG,aAAcJ,EAAQ,OAAS,GAAI,YAAY,OAAQ,CAAA,CAAA,CAChG,EAEA5B,EAAA,IAAC8D,EAAA,CACC,MAAM,eACN,YAAY,+BAEZ,SAAC9D,EAAA,IAAA+D,EAAA,CAAM,SAAQ,GAAE,GAAG/B,EAAS,aAAa,EAAG,aAAcJ,EAAQ,aAAe,GAAI,YAAY,cAAe,CAAA,CAAA,CAAA,CACnH,CAAA,CACF,CACF,CAAA,EAEA1B,EAAAA,KAAC8D,EAAa,CAAA,UAAU,OACtB,SAAA,CAAAhE,EAAA,IAACiE,EAAA,CACC,QAAQ,UACR,QAAUV,GAAM,CACdA,EAAE,eAAe,EACJR,EAAA,CACf,EACD,SAAA,QAAA,CAED,EACC/C,EAAA,IAAAiE,EAAA,CAAO,KAAK,SAAS,SAAI,MAAA,CAAA,CAAA,CAC5B,CAAA,CAAA,CAAA,CACF,CACF,CAAA,SACC9C,EAAY,CAAA,UAAU,6CAA6C,MAAOO,EAAU,kBAClF,SAAA,CAAiBW,GAAA,iBAAc6B,GAAO,CAAA,CAAA,EACtC7B,GAAiB,SACfnC,OAAA,MAAA,CAAI,UAAU,yBAAyB,SAAA,CAAA,+DACuB0B,EAAQ,QAAA,EACvE,EAED,CAACS,EAAgB,WAAa,CAACA,EAAgB,SAC9CrC,MAAC,OAAI,UAAU,yBACZ,YAAiB,MAAM,OAAO,IAAKiD,UACjC,MAAe,CAAA,UAAWkB,GAAW,uCAAuC,EAC3E,SAAA,CAAAnE,EAAA,IAACoE,EAAA,CACC,GAAInB,EAAE,GACN,KAAMA,EAAE,KACR,QAAS,CAAC,CAACf,GAAO,KAAMmC,GAAMA,EAAE,KAAOpB,EAAE,EAAE,EAC3C,gBAAiB,IAAM,CACrBC,EAAkBD,CAAC,CAAA,CACrB,CACF,EACAjD,EAAAA,IAAC,SAAM,QAASiD,EAAE,GAAI,UAAU,mCAC7B,WAAE,IACL,CAAA,CAAA,GAXQqB,IAYV,CACD,EACH,EAEFpE,EAAAA,KAAC8D,EAAa,CAAA,UAAU,OACtB,SAAA,CAAAhE,EAAA,IAACiE,EAAA,CACC,QAAQ,UACR,QAAUV,GAAM,CACdA,EAAE,eAAe,EACJR,EAAA,CACf,EACD,SAAA,QAAA,CAED,EACC/C,EAAA,IAAAiE,EAAA,CAAO,QAASX,EAAyB,SAAI,MAAA,CAAA,CAAA,CAChD,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,EC9MaiB,GAAiB,CAAC,CAAE,QAAA3C,EAAS,OAAAxC,EAAQ,UAAAE,KAAqC,CACrF,KAAM,CAACG,EAAMC,CAAO,EAAIC,EAAAA,SAAS,EAAK,EAChC,CAAE,MAAAJ,CAAM,EAAIC,EAAS,EACrBgF,EAAYhD,GAAa,CAAE,OAAApC,EAAQ,EAGnC,CAACqF,EAAeC,CAAgB,EAAI/E,EAAAA,SAAS,EAAK,EAClD,CAAE,KAAA2B,CAAK,EAAIqD,GAAeC,EAAmB,EAAGxF,CAAM,EACtDyC,EAAcC,EAAe,EAE7B,CAAC+C,EAAkBC,CAAmB,EAAInF,EAAAA,SAA+B,CAAA,CAAE,EAEjFI,EAAAA,UAAU,KACRL,EAAQ,EAAI,EACL,IAAM,CACNmC,EAAY,kBAAkB,CAAE,SAAU,CAAC+C,EAAmB,CAAC,EAAG,CACzE,GAEC,EAAE,EAGL7E,EAAAA,UAAU,IAAM,CACVuB,GAAM,QAAU,MAAM,QAAQA,EAAK,MAAM,GAC3CwD,EAAoB,CAAC,GAAGxD,EAAK,MAAM,CAAC,CACtC,EACC,CAACA,CAAI,CAAC,EAITvB,EAAAA,UAAU,IAAM,CACd,GAAIuB,GAAM,QAAUA,EAAK,OAAO,OAAS,EAAG,CAC1C,MAAMyD,EAA0BzD,EAAK,OAClC,IAAK0D,GAAMA,EAAE,aAAa,MAAOC,GAAMA,EAAE,SAAS,CAAC,EACnD,MAAO1B,GAAMA,CAAC,EACjBmB,EAAiBK,CAAuB,CAAA,CAC1C,EACC,CAACzD,CAAI,CAAC,EAGTvB,EAAAA,UAAU,IAAM,CACV,GAAA8E,EAAiB,OAAS,EAAG,CACzB,MAAAK,EAAgBL,EAAiB,IAAaM,IAE3C,CACL,GAAGA,EACH,YAAaA,EAAM,aAAa,IAAmBC,IAAA,CACjD,GAAGA,EACH,UAAWX,CAAA,EACX,CACJ,EACD,EAGDK,EAAoBI,CAAa,CAAA,CACnC,EACC,CAACT,CAAa,CAAC,EAElB,MAAM7B,EAAWO,EAAA,YACf,MAAOI,GAAiB,CACtBA,EAAE,eAAe,EASjB,MAAM8B,EAAuC,CAC3C,YATcR,GACZ,IAAKI,GACLA,EAAE,YAAa,IAAKK,IAAW,CAC7B,KAAMA,EAAM,KACZ,UAAWA,EAAM,SAAA,EACjB,GAEH,KAAK,CAGR,EACI,GAAA,CACF,MAAMC,GAAsC,CAC1C,MAAO,CACL,aAAcX,EAAmB,EACjC,YAAaxF,CACf,EACA,KAAMiG,CAAA,CACP,EACK9F,EAAA,CACJ,MAAO,UACP,YAAa,kCACb,QAAS,SAAA,CACV,EACIsC,EAAY,kBAAkB,CACjC,SAAU,CAAC+C,EAAmB,CAAC,CAAA,CAChC,EACY7B,EAAA,QACNjD,EAAc,CACjBA,aAAe,OACXP,EAAA,CACJ,MAAO,SACP,YAAa,uCACb,QAAS,aAAA,CACV,CACH,CAEJ,EAEA,CAACsF,CAAgB,CACnB,EAEM9B,EAAeI,EAAAA,YAAY,IAAM,CACrCzD,EAAQ,EAAK,EACHJ,EAAA,CAEZ,EAAG,EAAE,EAECkG,EAAWC,EAAAA,QAAQ,IACnBjB,GAAW,MAAM,MACZA,EAAU,KAAK,MAAM,OAAQpB,GAASA,EAAK,MAAM,SAASsC,GAAU,KAAK,CAAC,EAAE,OAAS,EAEvF,GACN,CAAClB,CAAS,CAAC,EAERmB,EAAqBC,GACpBA,GACGA,EAAI,MAAM,GAAG,EAAE,CAAC,GAAK,IAAI,YAAY,EAD5B,WAKbC,EAAqBJ,EAAAA,QAAQ,IAAM,CACvC,MAAMK,EAAS,CAAC,EAChB,QAASC,EAAI,EAAGA,EAAIlB,EAAiB,OAAQkB,GAAK,EAChDD,EAAO,KAAKjB,EAAiB,MAAMkB,EAAGA,EAAI,CAAC,CAAC,EAEvC,OAAAD,CAAA,EACN,CAACjB,CAAgB,CAAC,EAGnB,OAAA7E,EAAAA,IAACyD,EAAO,CAAA,KAAAhE,EAAY,aAAcsD,EAChC,gBAACW,EAAc,CAAA,KAAM,MAAO,UAAU,uDACpC,SAAA,CAAC1D,EAAA,IAAA2D,EAAA,CACC,gBAACC,EAAY,CAAA,SAAA,CAAA,iBAAehC,EAAQ,QAAA,CAAA,CAAS,CAC/C,CAAA,EACA5B,EAAAA,IAAC,QAAK,SAAA4C,EAAoB,UAAU,yBAClC,SAAC1C,EAAAA,KAAA,MAAA,CAAI,UAAU,MACb,SAAA,CAAAF,EAAA,IAACgG,GAAA,CACC,KAAK,wBACL,UAAWvB,EACX,GAAG,cACH,SAAU,CAACe,EACX,SAAU,IAAM,CACGd,EAACuB,GAAS,CAACA,CAAI,CAClC,EACA,UAAU,WAAA,CACZ,QAEC,MAAI,CAAA,UAAU,YACZ,SAAAJ,EAAmB,IAAI,CAACK,EAAKC,IAC5BnG,EAAAA,IAAC,OAAmB,UAAU,wCAC3B,WAAI,IAAI,CAACmF,EAAOiB,IAAa,CAEtB,MAAAC,EAAclB,EAAM,aAAe,GACnCmB,GAAcnB,EAAM,aAAe,CAAC,EAGxC,OAAAjF,EAAA,KAAC,MAAoC,CAAA,UAAU,wBAC7C,SAAA,CAACF,EAAA,IAAA,KAAA,CAAG,UAAU,2BAA4B,SAAYqG,EAAA,EACtDrG,EAAAA,IAAC,MAAI,CAAA,UAAU,gBACb,SAAAA,EAAA,IAACuG,GAAA,CACC,YAAAD,GACA,KAAMX,EAAkBU,CAAW,EACnC,SAAU5B,GAAiBe,EAC3B,cAAef,EACf,SAAQ,EAAA,CAAA,CAEZ,CAAA,CAAA,CAAA,EAVQ,GAAG0B,CAAQ,IAAIC,CAAQ,EAWjC,CAAA,CAEH,CAAA,EApBOD,CAqBV,CACD,CACH,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EACAjG,EAAAA,KAAC8D,EAAa,CAAA,UAAU,+CACtB,SAAA,CAAAhE,EAAA,IAACiE,EAAA,CACC,QAAUV,GAAM,CACdA,EAAE,eAAe,EACJR,EAAA,CACf,EACA,QAAQ,QACT,SAAA,QAAA,CAED,EACC/C,EAAA,IAAAiE,EAAA,CAAO,QAASrB,EAAU,SAAI,MAAA,CAAA,CAAA,CACjC,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,EC/La4D,GAAU,CAAC,CAAE,SAAAC,KAA6B,CAC/C,KAAA,CAAE,IAAAC,CAAI,EAAIC,EAAmB,EAC7B,CAAClH,EAAMC,CAAO,EAAIC,EAAAA,SAAS,EAAK,EAChC,CAACiH,EAAUC,CAAW,EAAIlH,EAAAA,SAAS,EAAI,EACvC,CAACmH,EAAgBC,CAAiB,EAAIpH,EAAAA,SAAS,EAAI,EACnD,CAAE,MAAAJ,CAAM,EAAIC,EAAS,EACrBqC,EAAcC,EAAe,EAC7B,CAAE,aAAAC,EAAc,SAAAC,CAAS,EAAIC,EAA+B,EAE5D+E,EAAqBzE,EAAY,CACrC,WAAY,MAAOO,GACjBmE,GAAqB,CAAE,KAAMnE,EAAU,EACzC,UAAW,IAAM,CACTvD,EAAA,CACJ,MAAO,UACP,YAAa,4BACb,QAAS,SAAA,CACV,EACIsC,EAAY,kBAAkB,CAAE,SAAU,CAAC7C,EAAW,QAAQ,EAAG,EACtEU,EAAQ,EAAK,CACf,EACA,QAAUI,GAAiB,CACnB,MAAA4C,EAAQC,EAAe7C,CAAG,EAC1BP,EAAA,CACJ,MAAOmD,EAAM,MACb,YAAaA,EAAM,YACnB,QAAS,OAAA,CACV,CAAA,CACH,CACD,EAEKE,EAAYC,GAAoC,CAEpD,MAAMC,EAAkC,CACtC,GAAGD,EACH,SAAA+D,EACA,eAAAE,CACF,EAEAE,EAAmB,OAAOlE,CAAQ,CACpC,EAEA,cACG,UACC,CAAA,SAAA,CAAA9C,EAAA,IAACkH,GAAQ,EAAA,EACRhH,EAAA,KAAAuD,EAAA,CAAO,KAAAhE,EAAY,aAAcC,EAChC,SAAA,CAACM,EAAAA,IAAAmH,EAAA,CAAc,QAAO,GAAE,SAAAV,CAAS,CAAA,QAChC,UAAQ,CAAA,UAAU,yCAChB,SAAAC,EAAI,0BAA0B,GAC7BxG,OAAC+D,EAAO,CAAA,KAAK,KAAK,UAAU,kCAAkC,QAAS,IAAMvE,EAAQ,EAAI,EACvF,SAAA,CAAAM,EAAA,IAACoH,GAAU,CAAA,UAAU,wBAAwB,cAAY,OAAO,EAC/DpH,EAAA,IAAA,OAAA,CAAK,UAAU,4BAA4B,SAAe,iBAAA,CAAA,CAAA,CAAA,CAC7D,CAEJ,CAAA,SACC0D,EACC,CAAA,SAAA,CAAA1D,MAAC2D,EACC,CAAA,SAAA3D,EAAA,IAAC4D,EAAY,CAAA,SAAA,mBAAiB,CAAA,EAChC,SACC,OAAK,CAAA,SAAU7B,EAAaa,CAAQ,EAAG,UAAU,OAChD,SAAA,CAAA5C,MAAC,UAAQ,CAAA,UAAU,iCACjB,SAAAE,EAAA,KAAC2D,EACC,CAAA,SAAA,CAAA7D,EAAA,IAAC8D,EAAA,CACC,MAAM,WACN,YAAY,2BAEZ,SAAA9D,EAAA,IAAC+D,GAAM,SAAQ,GAAE,GAAG/B,EAAS,UAAU,EAAG,YAAY,UAAW,CAAA,CAAA,CACnE,EACAhC,EAAA,IAAC8D,EAAA,CACC,MAAM,WACN,YAAY,2BAEZ,SAAA9D,EAAAA,IAAC+D,EAAM,CAAA,SAAQ,GAAC,KAAK,WAAY,GAAG/B,EAAS,UAAU,EAAG,YAAY,UAAW,CAAA,CAAA,CACnF,EACAhC,EAAA,IAAC8D,EAAA,CACC,MAAM,OACN,YAAY,uBAEZ,SAAA9D,EAAA,IAAC+D,GAAM,SAAQ,GAAE,GAAG/B,EAAS,MAAM,EAAG,YAAY,MAAO,CAAA,CAAA,CAC3D,EACAhC,EAAA,IAAC8D,EAAA,CACC,MAAM,UACN,YAAY,0BAEZ,SAAA9D,EAAA,IAAC+D,GAAM,SAAQ,GAAE,GAAG/B,EAAS,SAAS,EAAG,YAAY,SAAU,CAAA,CAAA,CACjE,EACAhC,EAAA,IAAC8D,EAAA,CACC,MAAM,QACN,YAAY,wBAEZ,SAAA9D,EAAA,IAAC+D,GAAM,SAAQ,GAAE,GAAG/B,EAAS,OAAO,EAAG,YAAY,OAAQ,CAAA,CAAA,CAC7D,EACAhC,EAAA,IAAC8D,EAAA,CACC,MAAM,eACN,YAAY,+BAEZ,SAAA9D,EAAA,IAAC+D,GAAM,SAAQ,GAAE,GAAG/B,EAAS,aAAa,EAAG,YAAY,cAAe,CAAA,CAAA,CAC1E,EACAhC,EAAA,IAAC8D,EAAA,CACC,MAAM,YACN,YAAY,gCAEZ,SAAA9D,EAAA,IAACoE,EAAA,CACC,GAAG,WACH,KAAK,WACL,eAAc,GACd,QAASwC,EACT,gBAAkBS,GAAYR,EAAY,CAAC,CAACQ,EAAQ,QAAS,CAAA,CAAA,CAAA,CAC/D,CACF,EACArH,EAAA,IAAC8D,EAAA,CACC,MAAM,kBACN,YAAY,iCAEZ,SAAA9D,EAAA,IAACoE,EAAA,CACC,GAAG,iBACH,KAAK,iBACL,eAAc,GACd,QAAS0C,EACT,gBAAkBO,GAAYN,EAAkB,CAAC,CAACM,EAAQ,QAAS,CAAA,CAAA,CAAA,CACrE,CAAA,CACF,CAAA,CACF,CACF,CAAA,EACAnH,EAAAA,KAAC8D,EAAa,CAAA,UAAU,OACtB,SAAA,CAAAhE,EAAA,IAACiE,EAAA,CACC,QAAQ,QACR,QAAUV,GAAM,CACdA,EAAE,eAAe,EACjB7D,EAAQ,EAAK,CACf,EACA,SAAUsH,EAAmB,UAC9B,SAAA,QAAA,CAED,EACAhH,EAAAA,IAACiE,EAAO,CAAA,KAAK,SAAS,SAAU+C,EAAmB,UAChD,SAAAA,EAAmB,UAAY,YAAc,MAChD,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,CAEJ,ECjIaM,GAAiB,IAAM,CAC5B,KAAA,CAAE,MAAA/H,CAAM,EAAIC,EAAS,EACrBqC,EAAcC,EAAe,EAC7B,CAACrC,EAAMC,CAAO,EAAIC,EAAAA,SAAS,EAAK,EAChC,CAAC4H,EAAMC,CAAO,EAAI7H,EAAAA,SAAsB,IAAI,EAC5C,CAAC8H,EAAaC,CAAc,EAAI/H,EAAAA,SAAS,EAAK,EAC9C,CAACgI,EAAeC,CAAgB,EAAIjI,EAAAA,SAAS,EAAK,EAClD,CAACkI,EAAYC,CAAa,EAAInI,EAAAA,SAA0C,OAAO,EAE/EoI,EAAoBxE,GAA2C,CACnE,GAAIA,EAAE,OAAO,OAASA,EAAE,OAAO,MAAM,OAAS,EAAG,CAC/C,MAAMyE,EAAezE,EAAE,OAAO,MAAM,CAAC,EAGrC,GAAI,CAACyE,GAAc,KAAK,cAAc,SAAS,MAAM,EAAG,CAChDzI,EAAA,CACJ,MAAO,oBACP,YAAa,2BACb,QAAS,aAAA,CACV,EAEDgE,EAAE,OAAO,MAAQ,GACjB,MAAA,CAGFiE,EAAQQ,CAAY,CAAA,CAExB,EAGMC,EAA2B1F,EAAY,CAC3C,WAAY,SAAY,CACtBqF,EAAiB,EAAI,EACjB,GAAA,CACI,MAAAM,EAAW,MAAMC,GAAsB,CAC3C,MAAO,CACL,KAAMN,CAAA,CACR,CACD,EAIKO,EAAO,IAAI,KAAK,CAACF,EAAS,IAA2B,EAAG,CAC5D,KAAM,UAAA,CACP,EAGKG,EAAM,OAAO,IAAI,gBAAgBD,CAAI,EACrCE,EAAI,SAAS,cAAc,GAAG,EACpC,OAAAA,EAAE,KAAOD,EACTC,EAAE,SAAW,4BACJ,SAAA,KAAK,YAAYA,CAAC,EAC3BA,EAAE,MAAM,EACD,OAAA,IAAI,gBAAgBD,CAAG,EACrB,SAAA,KAAK,YAAYC,CAAC,EAEpBJ,EAAS,IAAA,QAChB,CACAN,EAAiB,EAAK,CAAA,CAE1B,EACA,QAAU9H,GAAiB,CAInBP,EAAA,CACJ,MAAO,kBACP,YAJeO,EAIO,OAAO,SAAW,8BACxC,QAAS,aAAA,CACV,CAAA,CACH,CACD,EAGKyI,EAAoBhG,EAAY,CACpC,WAAY,SAAY,CACtB,GAAI,CAACgF,EACG,MAAA,IAAI,MAAM,kBAAkB,EAGpCG,EAAe,EAAI,EAEf,GAAA,CAGI,MAAA7E,EAAW,IAAI,SACZ,OAAAA,EAAA,OAAO,OAAQ0E,CAAI,EACnB1E,EAAA,OAAO,OAAQgF,CAAU,EACzBhF,EAAA,OAAO,YAAa,GAAG,GAEf,MAAM2F,GAAiB,CACtC,KAAM,CACJ,KAAMjB,EACN,KAAMM,EACN,UAAW,GAAA,CACb,CACD,GAEe,IAAA,QAChB,CACAH,EAAe,EAAK,CAAA,CAExB,EACA,UAAW,IAAM,CAETnI,EAAA,CACJ,MAAO,oBACP,YAAa,wBACb,QAAS,SAAA,CACV,EAGIsC,EAAY,kBAAkB,CAAE,SAAU,CAAC7C,EAAW,QAAQ,EAAG,EAGtEU,EAAQ,EAAK,EAGb8H,EAAQ,IAAI,CACd,EACA,QAAU1H,GAAiB,CAEzB,MAAM2I,EAAW3I,EAGjB,IAAI4I,EAAa,gBACbC,EAAe,6CAGfF,EAAS,SAAS,OAAO,UACdC,EAAAD,EAAS,QAAQ,MAAM,SAGlCA,EAAS,SAAS,OAAO,UACZE,EAAAF,EAAS,QAAQ,MAAM,SAGlClJ,EAAA,CACJ,MAAOmJ,EACP,YAAaC,EACb,QAAS,aAAA,CACV,CAAA,CACH,CACD,EAGKC,EAAyB,IAAM,CACnCX,EAAyB,OAAO,CAClC,EAGMY,EAAe,IAAM,CACzB,GAAI,CAACtB,EAAM,CACHhI,EAAA,CACJ,MAAO,mBACP,YAAa,qCACb,QAAS,SAAA,CACV,EACD,MAAA,CAGFgJ,EAAkB,OAAO,CAC3B,EAEA,OACGrI,EAAAA,KAAAuD,EAAA,CAAO,KAAAhE,EAAY,aAAcC,EAChC,SAAA,CAACM,EAAAA,IAAAmH,EAAA,CAAc,QAAO,GACpB,SAAAjH,EAAAA,KAAC+D,GAAO,KAAK,KAAK,UAAU,kCAC1B,SAAA,CAAAjE,EAAA,IAAC8I,GAAa,CAAA,UAAU,wBAAwB,cAAY,OAAO,EAClE9I,EAAA,IAAA,OAAA,CAAK,UAAU,4BAA4B,SAAU,YAAA,CAAA,CAAA,CAAA,CACxD,CACF,CAAA,EACAE,EAAAA,KAACwD,EAAc,CAAA,UAAU,cACvB,SAAA,CAAA1D,MAAC2D,EACC,CAAA,SAAA3D,EAAA,IAAC4D,EAAY,CAAA,SAAA,kBAAgB,CAAA,EAC/B,EAEA1D,EAAAA,KAAC,MAAI,CAAA,UAAU,2BACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,sBACb,SAAA,CAAAF,MAAC+I,EAAM,CAAA,QAAQ,UAAU,UAAU,YAAY,SAAW,cAAA,EAC1D7I,EAAAA,KAAC8I,IAAO,MAAOnB,EAAY,cAAgBoB,GAAUnB,EAAcmB,CAAwC,EACzG,SAAA,CAAAjJ,EAAAA,IAACkJ,IAAc,UAAU,SACvB,eAACC,GAAY,CAAA,YAAY,qBAAqB,CAChD,CAAA,SACCC,GACC,CAAA,SAAA,CAACpJ,EAAA,IAAAqJ,EAAA,CAAW,MAAM,QAAQ,SAAK,QAAA,EAC9BrJ,EAAA,IAAAqJ,EAAA,CAAW,MAAM,QAAQ,SAAK,QAAA,EAC9BrJ,EAAA,IAAAqJ,EAAA,CAAW,MAAM,YAAY,SAAU,YAAA,CAAA,CAAA,CAC1C,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EACAnJ,EAAAA,KAAC,MAAI,CAAA,UAAU,sBACb,SAAA,CAAAF,MAAC+I,EAAM,CAAA,QAAQ,WAAW,UAAU,YAAY,SAAiB,oBAAA,EACjE/I,EAAAA,IAAC,MAAI,CAAA,UAAU,0BACb,SAAAE,EAAA,KAAC+D,EAAA,CACC,QAAQ,UACR,QAAS2E,EACT,SAAUjB,EACV,UAAU,SAEV,SAAA,CAAC3H,EAAAA,IAAAsJ,GAAA,CAAe,UAAU,cAAe,CAAA,EACxC3B,EAAgB,iBAAmB,mBAAA,CAAA,CAAA,EAExC,EACC3H,EAAA,IAAA,IAAA,CAAE,UAAU,gCAAgC,SAE7C,0EAAA,CAAA,CAAA,EACF,EAEAE,EAAAA,KAAC,MAAI,CAAA,UAAU,sBACb,SAAA,CAAAF,MAAC+I,EAAM,CAAA,QAAQ,UAAU,UAAU,YAAY,SAAe,kBAAA,EAC9D/I,EAAA,IAAC+D,EAAA,CACC,GAAG,UACH,KAAK,OACL,OAAO,OACP,SAAUgE,EACV,SAAUN,CAAA,CACZ,EACCF,GACCrH,EAAA,KAAC,IAAE,CAAA,UAAU,gCAAgC,SAAA,CAAA,kBAC3BqH,EAAK,IAAA,CACvB,CAAA,CAAA,CAEJ,CAAA,CAAA,EACF,SAECvD,EACC,CAAA,SAAA,CAAAhE,EAAA,IAACiE,EAAA,CACC,QAAQ,QACR,QAAS,IAAM,CACbvE,EAAQ,EAAK,EACb8H,EAAQ,IAAI,CACd,EACA,SAAUC,EACX,SAAA,QAAA,CAED,EACAzH,EAAA,IAACiE,EAAA,CACC,QAAS4E,EACT,SAAU,CAACtB,GAAQE,EAElB,WAAc,eAAiB,QAAA,CAAA,CAClC,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,CAEJ,EC/Qa8B,GAAc,CAAC,CAAE,OAAAnK,EAAQ,QAAAwC,EAAS,SAAA4H,EAAU,QAAAC,EAAU,cAAkC,CAC7F,KAAA,CAAE,IAAA/C,CAAI,EAAIC,EAAmB,EAGnC,OAAI8C,IAAY,WAEXzJ,EAAA,IAAA,MAAA,CAAI,UAAU,mBACb,gBAAC0J,GACC,CAAA,SAAA,CAAC1J,EAAAA,IAAA2J,GAAA,CAAoB,QAAO,GAC1B,SAAAzJ,EAAA,KAAC+D,EAAA,CACC,QAAQ,QACR,KAAK,KACL,UAAU,cAEV,SAAA,CAACjE,EAAAA,IAAA4J,GAAA,CAAW,UAAU,SAAU,CAAA,EAC/B5J,EAAA,IAAA,OAAA,CAAK,UAAU,UAAU,SAAS,WAAA,CAAA,CAAA,CAAA,CAAA,EAEvC,EACCE,EAAA,KAAA2J,GAAA,CAAoB,MAAM,MAAM,UAAU,YACxC,SAAA,CAAAnD,EAAI,qCAAqC,GACxC1G,EAAA,IAAC8J,EAAA,CACC,UAAU,yBACV,QAAS,IAAMN,EAASpK,EAAQwC,EAAS,YAAY,EACtD,SAAA,YAAA,CAED,EAED8E,EAAI,0BAA0B,GAC7B1G,EAAA,IAAC8J,EAAA,CACC,UAAU,yBACV,QAAS,IAAMN,EAASpK,EAAQwC,EAAS,MAAM,EAChD,SAAA,MAAA,CAED,EAED8E,EAAI,0BAA0B,GAC7B1G,EAAA,IAAC8J,EAAA,CACC,UAAU,sCACV,QAAS,IAAMN,EAASpK,EAAQwC,EAAS,QAAQ,EAClD,SAAA,QAAA,CAAA,CAED,CAEJ,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAMF1B,EAAA,KAAC,MAAI,CAAA,UAAU,sCACZ,SAAA,CAAAwG,EAAI,qCAAqC,GACxCxG,EAAA,KAAC+D,EAAA,CACC,QAAQ,UACR,KAAK,KACL,UAAU,oCACV,QAAS,IAAMuF,EAASpK,EAAQwC,EAAS,YAAY,EAErD,SAAA,CAAC5B,EAAAA,IAAA+J,GAAA,CAAoB,UAAU,SAAU,CAAA,EACzC/J,EAAAA,IAAC,QAAK,SAAU,YAAA,CAAA,CAAA,CAAA,CAClB,EAED0G,EAAI,0BAA0B,GAC7BxG,EAAA,KAAC+D,EAAA,CACC,QAAQ,UACR,KAAK,KACL,UAAU,oCACV,QAAS,IAAMuF,EAASpK,EAAQwC,EAAS,MAAM,EAE/C,SAAA,CAAC5B,EAAAA,IAAAgK,GAAA,CAAa,UAAU,SAAU,CAAA,EAClChK,EAAAA,IAAC,QAAK,SAAI,MAAA,CAAA,CAAA,CAAA,CAAA,CACZ,EAEJ,CAEJ,ECxFaiK,GAAc,CAAC,CAAE,OAAAC,KAA+B,CAE3D,IAAIC,EAAa,WACbC,EAAc,4CAEd,OAAA,OAAOF,GAAW,UAChBA,IACWC,EAAA,SACCC,EAAA,+CAEP,OAAOF,GAAW,WAEdC,EAAAD,EAETA,EAAO,YAAY,IAAM,SACbE,EAAA,8CACLF,EAAO,YAAY,IAAM,WACpBE,EAAA,8CACLF,EAAO,YAAY,IAAM,aACpBE,EAAA,8CAKhBpK,MAAC,QAAK,UAAWc,EACf,2EACAsJ,CAAA,EAEC,SACHD,EAAA,CAEJ,EC1BaE,GACXC,GAEO,CACL,CACE,GAAI,SACJ,OAAQ,CAAC,CAAE,MAAAC,CAAA,IACTvK,EAAA,IAACoE,EAAA,CACC,QACEmG,EAAM,2BACF,GACAA,EAAM,sBAAA,EACJ,gBACA,GAER,gBAAiB,IAAMA,EAAM,0BAA0B,EACvD,UAAU,kBACV,aAAW,YAAA,CACb,EAEF,KAAM,CAAC,CAAE,IAAArE,CAAA,IACPlG,EAAA,IAACoE,EAAA,CACC,QAAS8B,EAAI,cAAc,EAC3B,gBAAiB,IAAMA,EAAI,eAAe,EAC1C,UAAU,kBACV,aAAW,YAAA,CACb,EAEF,cAAe,GACf,aAAc,GACd,KAAM,CACJ,YAAa,QAAA,CAEjB,EACA,CACE,YAAa,WACb,OAAQ,CAAC,CAAE,OAAAsE,CAAA,IAAcxK,EAAA,IAAAyK,EAAA,CAAsB,OAAAD,EAAgB,MAAM,WAAW,EAChF,aAAc,GACd,cAAe,GACf,KAAOE,GAASA,EAAK,SAAS,EAC9B,KAAM,CACJ,UAAW,YACX,YAAa,UAAA,CAEjB,EACA,CACE,YAAa,OACb,OAAQ,CAAC,CAAE,OAAAF,CAAA,IAAcxK,EAAA,IAAAyK,EAAA,CAAsB,OAAAD,EAAgB,MAAM,OAAO,EAC5E,aAAc,GACd,cAAe,GACf,KAAOE,GAASA,EAAK,SAAS,EAC9B,KAAM,CACJ,UAAW,YACX,YAAa,MAAA,CAEjB,EACA,CACE,YAAa,0BACb,OAAQ,CAAC,CAAE,OAAAF,CAAA,IAAcxK,EAAA,IAAAyK,EAAA,CAAsB,OAAAD,EAAgB,MAAM,UAAU,EAC/E,aAAc,GACd,cAAe,GACf,KAAOE,GAASA,EAAK,SAAS,EAC9B,KAAM,CACJ,UAAW,YACX,YAAa,SAAA,CAEjB,EACA,CACE,YAAa,QACb,OAAQ,CAAC,CAAE,OAAAF,CAAA,IAAcxK,EAAA,IAAAyK,EAAA,CAAsB,OAAAD,EAAgB,MAAM,QAAQ,EAC7E,cAAe,GACf,aAAc,GACd,KAAOE,GAASA,EAAK,SAAS,EAC9B,KAAM,CACJ,UAAW,YACX,YAAa,OAAA,CAEjB,EACA,CACE,YAAa,WACb,OAAQ,CAAC,CAAE,OAAAF,CAAA,IAAcxK,EAAA,IAAAyK,EAAA,CAAsB,OAAAD,EAAgB,MAAM,SAAS,EAC9E,aAAc,GACd,cAAe,GACf,KAAOE,GAAS1K,MAACiK,IAAY,OAAQS,EAAK,WAAuB,EACjE,KAAM,CACJ,UAAW,YACX,YAAa,QAAA,CAEjB,EACA,CACE,GAAI,UACJ,OAAQ,UACR,aAAc,GACd,KAAOA,GACL1K,EAAA,IAACuJ,GAAA,CACC,OAAQmB,EAAK,IAAI,SAAS,GAC1B,QAASA,EAAK,IAAI,SAClB,SAAUJ,EACV,QAAQ,UAAA,CACV,EAEF,KAAM,CACJ,UAAW,aACX,YAAa,SAAA,CACf,CAEJ,EC1GK,SAASK,GAAqB,CACnC,MAAAJ,EACA,SAAAK,EACA,YAAAC,EAAc,EAChB,EAA8B,CAC5B,MAAMC,EAAaP,EAAM,SAAS,EAAE,cAAc,OAAS,EAGzD,OAAArK,EAAA,KAAC,MAAI,CAAA,UAAU,oDACZ,SAAA,CACC0K,GAAA5K,EAAA,IAAC,MAAI,CAAA,UAAU,oCACb,SAAAA,EAAAA,IAAC+K,IAAO,SAAUH,EAAU,MAAOC,CAAA,CAAa,CAClD,CAAA,EAGF3K,EAAAA,KAAC,MAAI,CAAA,UAAU,kCACZ,SAAA,CACC4K,GAAA9K,EAAA,IAACiE,EAAA,CACC,QAAQ,QACR,QAAS,IAAMsG,EAAM,mBAAmB,EACxC,UAAU,6HACX,SAAA,eAAA,CAED,EASFvK,MAACgL,IAAY,MAAAT,CAAc,CAAA,CAAA,CAC7B,CAAA,CAAA,EACF,CAEJ,CC3BO,MAAMU,GAAW,IAAM,CACtB,KAAA,CAAE,MAAA1L,CAAM,EAAIC,EAAS,EACrBqC,EAAcC,EAAe,EAE7B,CAACoJ,EAAWC,CAAY,EAAIxL,EAAAA,SAAiB,EAAE,EAC/C,CAACyL,EAAkBC,CAAmB,EAAI1L,WAItC,EAEJ,CAAC2L,EAAYC,CAAa,EAAI5L,WAA0B,CAC5D,UAAW,EACX,SAAU,EAAA,CACX,EAGK,CAACb,EAAS0M,CAAU,EAAI7L,WAAuB,CACnD,CAAE,GAAI,OAAQ,KAAM,EAAM,CAAA,CAC3B,EAGK8L,EAAoBC,GAAoC,CACxD,GAAA,CAACA,EAAU,OAAe,MAAA,WAExB,MAAAC,EAAOD,EAAU,CAAC,GAAK,CAAE,GAAI,OAAQ,KAAM,EAAM,EACvD,MAAO,GAAGC,EAAK,EAAE,IAAIA,EAAK,KAAO,OAAS,KAAK,EACjD,EAEM,CAAE,UAAAC,EAAW,KAAAtK,EAAM,QAAAuK,CAAY,EAAAnN,GACnC4M,EAAW,UACXA,EAAW,SACXJ,EACAO,EAAiB3M,CAAO,CAC1B,EAYMgN,EAAUzB,GATS,CAACjL,EAAgBwC,EAAgCmK,IAAiD,CACrGV,EAAA,CAClB,OAAAjM,EACA,QAAAwC,EACA,WAAAmK,CAAA,CACD,CACH,CAG+C,EAEzCC,EAAgB/C,GAAkB,CACtCkC,EAAalC,CAAK,EAClBsC,MAAuB,CAAE,GAAGtF,EAAM,UAAW,GAAI,CACnD,EAEMgG,EAA0BC,GAAmC,CACjEX,EAAcW,CAAa,CAC7B,EAGMC,EAAuBC,GAA6B,CACxDZ,EAAWY,CAAU,EACrBb,MAAuB,CAAE,GAAGtF,EAAM,UAAW,GAAI,CACnD,EAGMoG,EAAgB,IAAM,CAErBxK,EAAY,kBAAkB,CAAE,SAAU,CAAC7C,EAAW,QAAQ,EAAG,EAGtE,WAAW,IAAM,CACTO,EAAA,CACJ,MAAO,iBACP,YAAa,oCACb,QAAS,SAAA,CACV,GACA,GAAG,CACR,EAEA,OAAIqM,EACF5L,EAAA,IAACsM,GAAA,CACC,SAAUhB,EAAW,SACrB,YAAa,EACb,SAAU,GACV,UAAW,GACX,WAAY,GACZ,cAAe,GACf,WAAY,EAAA,CACd,EAEEO,EAAgB7L,MAACuM,GAAM,CAAA,CAAA,EAItBrM,EAAA,KAAAsM,WAAA,CAAA,SAAA,CAAoBpB,GAAAA,EAAiB,aAAe,QACnDpL,EAAA,IAAC2B,GAAA,CACC,OAAQyJ,EAAiB,OACzB,QAASA,EAAiB,QAC1B,UAAW,IAAM,CACVvJ,EAAY,kBAAkB,CAAE,SAAU,CAAC7C,EAAW,QAAQ,EAAG,EACtEqM,EAAoB,IAAI,CAAA,CAC1B,CACF,EAEDD,GAAoBA,EAAiB,aAAe,cACnDpL,EAAA,IAACuE,GAAA,CACC,OAAQ6G,EAAiB,OACzB,QAASA,EAAiB,QAC1B,UAAW,IAAMC,EAAoB,IAAI,CAAA,CAC3C,EAEDD,GAAoBA,EAAiB,aAAe,UACnDpL,EAAA,IAACb,GAAA,CACC,KAAM,CACJ,SAAUiM,EAAiB,QAAQ,SACnC,OAAQA,EAAiB,MAC3B,EACA,UAAW,IAAM,CACVvJ,EAAY,kBAAkB,CAAE,SAAU,CAAC7C,EAAW,QAAQ,EAAG,EACtEqM,EAAoB,IAAI,CAAA,CAC1B,CACF,EAGFrL,EAAAA,IAAC,MAAI,CAAA,UAAU,YACb,SAAAA,EAAA,IAACyM,GAAA,CACC,MAAM,mBACN,QAAAX,EACA,KAAMxK,GAAM,OAAS,CAAC,EACtB,WAAYA,GAAM,WAClB,UAAAsK,EACA,iBAAkB,GAClB,cAAe,GACf,SAAUN,EAAW,SACrB,mBAAoBW,EACpB,gBAAiBE,EACjB,aAAcrN,EACd,SAAUkN,EACV,YAAad,EACb,gBAAiBP,GACjB,qBAAsB,GACtB,UAAW0B,EACX,mBAAoB,GACpB,aAAc,CAGZ,QAAS,IAAM,CAA8B,EAC7C,QACEnM,EAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAAAF,EAAA,IAACwG,GAAQ,EAAA,QACRc,GAAe,CAAA,CAAA,CAAA,CAClB,CAAA,CAAA,CAEJ,CAAA,CAEJ,CAAA,CAAA,EACF,CAEJ,EC9KA,SAAwBoF,IAAiB,CACvC,cACGC,GACC,CAAA,SAAA,CAAC3M,EAAAA,IAAA4M,GAAA,CAAK,MAAM,WAAY,CAAA,QAEvB3B,GAAS,CAAA,CAAA,CAAA,EACZ,CAEJ"}