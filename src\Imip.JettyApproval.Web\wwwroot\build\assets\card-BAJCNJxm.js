import{j as r}from"./vendor-CrSBzUoz.js";import{M as s}from"./app-layout-CNB1Wtrx.js";function d({className:a,...t}){return r.jsx("div",{"data-slot":"card",className:s("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...t})}function n({className:a,...t}){return r.jsx("div",{"data-slot":"card-header",className:s("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...t})}function c({className:a,...t}){return r.jsx("div",{"data-slot":"card-title",className:s("leading-none font-semibold",a),...t})}function i({className:a,...t}){return r.jsx("div",{"data-slot":"card-description",className:s("text-muted-foreground text-sm",a),...t})}function l({className:a,...t}){return r.jsx("div",{"data-slot":"card-action",className:s("col-start-2 row-span-2 row-start-1 self-start justify-self-end",a),...t})}function u({className:a,...t}){return r.jsx("div",{"data-slot":"card-content",className:s("px-6",a),...t})}function f({className:a,...t}){return r.jsx("div",{"data-slot":"card-footer",className:s("flex items-center px-6 [.border-t]:pt-6",a),...t})}export{d as C,n as a,c as b,u as c,f as d,i as e,l as f};
//# sourceMappingURL=card-BAJCNJxm.js.map
