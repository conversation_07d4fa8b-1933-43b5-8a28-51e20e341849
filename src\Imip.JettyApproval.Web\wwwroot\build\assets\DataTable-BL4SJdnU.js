import{r as H,j as u,b as Y}from"./vendor-CrSBzUoz.js";import{T as jn,a as Cn,b as Qe,c as Tn,d as On,e as pt}from"./table-CAbNlII1.js";import{az as te,aA as An,aB as Nn,aC as En,L as St,B as ce,S as jt,a as Ct,b as Tt,c as Ot,d as At,I as Yt,aD as In}from"./app-layout-CNB1Wtrx.js";import{h as Rn,P as Zr,b as Vr,a as Fr,g as Pn,c as Lr,i as Dn,k as Mn,E as zn,w as $n,j as Zn,J as Vn,r as Fn,m as Ln,d as Un,n as Wn,u as Gn}from"./popover-7NwOVASC.js";import{C as Ur}from"./checkbox-CayrCcBd.js";import{m as Bn}from"./radix-DaY-mnHi.js";import{i as at}from"./tiny-invariant-CopsF_GD.js";import{S as Hn}from"./search-YAT3sv3T.js";import{f as ht,u as qn,g as Kn,a as Yn,b as Xn,c as Jn}from"./index-CZZWNLgr.js";var Xt=t=>typeof t=="boolean"?`${t}`:t===0?"0":t,re=t=>!t||typeof t!="object"||Object.keys(t).length===0,Qn=(t,e)=>JSON.stringify(t)===JSON.stringify(e);function Wr(t,e){t.forEach(function(r){Array.isArray(r)?Wr(r,e):e.push(r)})}function Gr(t){let e=[];return Wr(t,e),e}var Br=(...t)=>Gr(t).filter(Boolean),Hr=(t,e)=>{let r={},n=Object.keys(t),a=Object.keys(e);for(let s of n)if(a.includes(s)){let i=t[s],o=e[s];Array.isArray(i)||Array.isArray(o)?r[s]=Br(o,i):typeof i=="object"&&typeof o=="object"?r[s]=Hr(i,o):r[s]=o+" "+i}else r[s]=t[s];for(let s of a)n.includes(s)||(r[s]=e[s]);return r},Jt=t=>!t||typeof t!="string"?t:t.replace(/\s+/g," ").trim();const Gt="-",ea=t=>{const e=ra(t),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=t;return{getClassGroupId:i=>{const o=i.split(Gt);return o[0]===""&&o.length!==1&&o.shift(),qr(o,e)||ta(i)},getConflictingClassGroupIds:(i,o)=>{const l=r[i]||[];return o&&n[i]?[...l,...n[i]]:l}}},qr=(t,e)=>{if(t.length===0)return e.classGroupId;const r=t[0],n=e.nextPart.get(r),a=n?qr(t.slice(1),n):void 0;if(a)return a;if(e.validators.length===0)return;const s=t.join(Gt);return e.validators.find(({validator:i})=>i(s))?.classGroupId},Qt=/^\[(.+)\]$/,ta=t=>{if(Qt.test(t)){const e=Qt.exec(t)[1],r=e?.substring(0,e.indexOf(":"));if(r)return"arbitrary.."+r}},ra=t=>{const{theme:e,classGroups:r}=t,n={nextPart:new Map,validators:[]};for(const a in r)Nt(r[a],n,a,e);return n},Nt=(t,e,r,n)=>{t.forEach(a=>{if(typeof a=="string"){const s=a===""?e:er(e,a);s.classGroupId=r;return}if(typeof a=="function"){if(na(a)){Nt(a(n),e,r,n);return}e.validators.push({validator:a,classGroupId:r});return}Object.entries(a).forEach(([s,i])=>{Nt(i,er(e,s),r,n)})})},er=(t,e)=>{let r=t;return e.split(Gt).forEach(n=>{r.nextPart.has(n)||r.nextPart.set(n,{nextPart:new Map,validators:[]}),r=r.nextPart.get(n)}),r},na=t=>t.isThemeGetter,aa=t=>{if(t<1)return{get:()=>{},set:()=>{}};let e=0,r=new Map,n=new Map;const a=(s,i)=>{r.set(s,i),e++,e>t&&(e=0,n=r,r=new Map)};return{get(s){let i=r.get(s);if(i!==void 0)return i;if((i=n.get(s))!==void 0)return a(s,i),i},set(s,i){r.has(s)?r.set(s,i):a(s,i)}}},Et="!",It=":",sa=It.length,ia=t=>{const{prefix:e,experimentalParseClassName:r}=t;let n=a=>{const s=[];let i=0,o=0,l=0,d;for(let h=0;h<a.length;h++){let j=a[h];if(i===0&&o===0){if(j===It){s.push(a.slice(l,h)),l=h+sa;continue}if(j==="/"){d=h;continue}}j==="["?i++:j==="]"?i--:j==="("?o++:j===")"&&o--}const c=s.length===0?a:a.substring(l),f=oa(c),y=f!==c,g=d&&d>l?d-l:void 0;return{modifiers:s,hasImportantModifier:y,baseClassName:f,maybePostfixModifierPosition:g}};if(e){const a=e+It,s=n;n=i=>i.startsWith(a)?s(i.substring(a.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:i,maybePostfixModifierPosition:void 0}}if(r){const a=n;n=s=>r({className:s,parseClassName:a})}return n},oa=t=>t.endsWith(Et)?t.substring(0,t.length-1):t.startsWith(Et)?t.substring(1):t,la=t=>{const e=Object.fromEntries(t.orderSensitiveModifiers.map(n=>[n,!0]));return n=>{if(n.length<=1)return n;const a=[];let s=[];return n.forEach(i=>{i[0]==="["||e[i]?(a.push(...s.sort(),i),s=[]):s.push(i)}),a.push(...s.sort()),a}},ca=t=>({cache:aa(t.cacheSize),parseClassName:ia(t),sortModifiers:la(t),...ea(t)}),da=/\s+/,ua=(t,e)=>{const{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:a,sortModifiers:s}=e,i=[],o=t.trim().split(da);let l="";for(let d=o.length-1;d>=0;d-=1){const c=o[d],{isExternal:f,modifiers:y,hasImportantModifier:g,baseClassName:h,maybePostfixModifierPosition:j}=r(c);if(f){l=c+(l.length>0?" "+l:l);continue}let E=!!j,M=n(E?h.substring(0,j):h);if(!M){if(!E){l=c+(l.length>0?" "+l:l);continue}if(M=n(h),!M){l=c+(l.length>0?" "+l:l);continue}E=!1}const N=s(y).join(":"),F=g?N+Et:N,D=F+M;if(i.includes(D))continue;i.push(D);const U=a(M,E);for(let m=0;m<U.length;++m){const W=U[m];i.push(F+W)}l=c+(l.length>0?" "+l:l)}return l};function fa(){let t=0,e,r,n="";for(;t<arguments.length;)(e=arguments[t++])&&(r=Kr(e))&&(n&&(n+=" "),n+=r);return n}const Kr=t=>{if(typeof t=="string")return t;let e,r="";for(let n=0;n<t.length;n++)t[n]&&(e=Kr(t[n]))&&(r&&(r+=" "),r+=e);return r};function Rt(t,...e){let r,n,a,s=i;function i(l){const d=e.reduce((c,f)=>f(c),t());return r=ca(d),n=r.cache.get,a=r.cache.set,s=o,o(l)}function o(l){const d=n(l);if(d)return d;const c=ua(l,r);return a(l,c),c}return function(){return s(fa.apply(null,arguments))}}const Q=t=>{const e=r=>r[t]||[];return e.isThemeGetter=!0,e},Yr=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Xr=/^\((?:(\w[\w-]*):)?(.+)\)$/i,pa=/^\d+\/\d+$/,ha=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,ma=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,ga=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,va=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,ya=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Ae=t=>pa.test(t),Z=t=>!!t&&!Number.isNaN(Number(t)),we=t=>!!t&&Number.isInteger(Number(t)),tr=t=>t.endsWith("%")&&Z(t.slice(0,-1)),pe=t=>ha.test(t),ba=()=>!0,xa=t=>ma.test(t)&&!ga.test(t),Bt=()=>!1,wa=t=>va.test(t),_a=t=>ya.test(t),ka=t=>!k(t)&&!S(t),Sa=t=>ze(t,en,Bt),k=t=>Yr.test(t),_e=t=>ze(t,tn,xa),mt=t=>ze(t,Da,Z),ja=t=>ze(t,Jr,Bt),Ca=t=>ze(t,Qr,_a),Ta=t=>ze(t,Bt,wa),S=t=>Xr.test(t),et=t=>$e(t,tn),Oa=t=>$e(t,Ma),Aa=t=>$e(t,Jr),Na=t=>$e(t,en),Ea=t=>$e(t,Qr),Ia=t=>$e(t,za,!0),ze=(t,e,r)=>{const n=Yr.exec(t);return n?n[1]?e(n[1]):r(n[2]):!1},$e=(t,e,r=!1)=>{const n=Xr.exec(t);return n?n[1]?e(n[1]):r:!1},Jr=t=>t==="position",Ra=new Set(["image","url"]),Qr=t=>Ra.has(t),Pa=new Set(["length","size","percentage"]),en=t=>Pa.has(t),tn=t=>t==="length",Da=t=>t==="number",Ma=t=>t==="family-name",za=t=>t==="shadow",Pt=()=>{const t=Q("color"),e=Q("font"),r=Q("text"),n=Q("font-weight"),a=Q("tracking"),s=Q("leading"),i=Q("breakpoint"),o=Q("container"),l=Q("spacing"),d=Q("radius"),c=Q("shadow"),f=Q("inset-shadow"),y=Q("drop-shadow"),g=Q("blur"),h=Q("perspective"),j=Q("aspect"),E=Q("ease"),M=Q("animate"),N=()=>["auto","avoid","all","avoid-page","page","left","right","column"],F=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],D=()=>["auto","hidden","clip","visible","scroll"],U=()=>["auto","contain","none"],m=()=>[S,k,l],W=()=>[Ae,"full","auto",...m()],G=()=>[we,"none","subgrid",S,k],K=()=>["auto",{span:["full",we,S,k]},S,k],q=()=>[we,"auto",S,k],ee=()=>["auto","min","max","fr",S,k],A=()=>["start","end","center","between","around","evenly","stretch","baseline"],_=()=>["start","end","center","stretch"],x=()=>["auto",...m()],I=()=>[Ae,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...m()],v=()=>[t,S,k],w=()=>[tr,_e],T=()=>["","none","full",d,S,k],z=()=>["",Z,et,_e],B=()=>["solid","dashed","dotted","double"],se=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],J=()=>["","none",g,S,k],xe=()=>["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",S,k],ue=()=>["none",Z,S,k],fe=()=>["none",Z,S,k],Te=()=>[Z,S,k],Oe=()=>[Ae,"full",...m()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[pe],breakpoint:[pe],color:[ba],container:[pe],"drop-shadow":[pe],ease:["in","out","in-out"],font:[ka],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[pe],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[pe],shadow:[pe],spacing:["px",Z],text:[pe],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Ae,k,S,j]}],container:["container"],columns:[{columns:[Z,k,S,o]}],"break-after":[{"break-after":N()}],"break-before":[{"break-before":N()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...F(),k,S]}],overflow:[{overflow:D()}],"overflow-x":[{"overflow-x":D()}],"overflow-y":[{"overflow-y":D()}],overscroll:[{overscroll:U()}],"overscroll-x":[{"overscroll-x":U()}],"overscroll-y":[{"overscroll-y":U()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:W()}],"inset-x":[{"inset-x":W()}],"inset-y":[{"inset-y":W()}],start:[{start:W()}],end:[{end:W()}],top:[{top:W()}],right:[{right:W()}],bottom:[{bottom:W()}],left:[{left:W()}],visibility:["visible","invisible","collapse"],z:[{z:[we,"auto",S,k]}],basis:[{basis:[Ae,"full","auto",o,...m()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[Z,Ae,"auto","initial","none",k]}],grow:[{grow:["",Z,S,k]}],shrink:[{shrink:["",Z,S,k]}],order:[{order:[we,"first","last","none",S,k]}],"grid-cols":[{"grid-cols":G()}],"col-start-end":[{col:K()}],"col-start":[{"col-start":q()}],"col-end":[{"col-end":q()}],"grid-rows":[{"grid-rows":G()}],"row-start-end":[{row:K()}],"row-start":[{"row-start":q()}],"row-end":[{"row-end":q()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":ee()}],"auto-rows":[{"auto-rows":ee()}],gap:[{gap:m()}],"gap-x":[{"gap-x":m()}],"gap-y":[{"gap-y":m()}],"justify-content":[{justify:[...A(),"normal"]}],"justify-items":[{"justify-items":[..._(),"normal"]}],"justify-self":[{"justify-self":["auto",..._()]}],"align-content":[{content:["normal",...A()]}],"align-items":[{items:[..._(),"baseline"]}],"align-self":[{self:["auto",..._(),"baseline"]}],"place-content":[{"place-content":A()}],"place-items":[{"place-items":[..._(),"baseline"]}],"place-self":[{"place-self":["auto",..._()]}],p:[{p:m()}],px:[{px:m()}],py:[{py:m()}],ps:[{ps:m()}],pe:[{pe:m()}],pt:[{pt:m()}],pr:[{pr:m()}],pb:[{pb:m()}],pl:[{pl:m()}],m:[{m:x()}],mx:[{mx:x()}],my:[{my:x()}],ms:[{ms:x()}],me:[{me:x()}],mt:[{mt:x()}],mr:[{mr:x()}],mb:[{mb:x()}],ml:[{ml:x()}],"space-x":[{"space-x":m()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":m()}],"space-y-reverse":["space-y-reverse"],size:[{size:I()}],w:[{w:[o,"screen",...I()]}],"min-w":[{"min-w":[o,"screen","none",...I()]}],"max-w":[{"max-w":[o,"screen","none","prose",{screen:[i]},...I()]}],h:[{h:["screen",...I()]}],"min-h":[{"min-h":["screen","none",...I()]}],"max-h":[{"max-h":["screen",...I()]}],"font-size":[{text:["base",r,et,_e]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,S,mt]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",tr,k]}],"font-family":[{font:[Oa,k,e]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[a,S,k]}],"line-clamp":[{"line-clamp":[Z,"none",S,mt]}],leading:[{leading:[s,...m()]}],"list-image":[{"list-image":["none",S,k]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",S,k]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:v()}],"text-color":[{text:v()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...B(),"wavy"]}],"text-decoration-thickness":[{decoration:[Z,"from-font","auto",S,_e]}],"text-decoration-color":[{decoration:v()}],"underline-offset":[{"underline-offset":[Z,"auto",S,k]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:m()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",S,k]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",S,k]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...F(),Aa,ja]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","space","round"]}]}],"bg-size":[{bg:["auto","cover","contain",Na,Sa]}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},we,S,k],radial:["",S,k],conic:[we,S,k]},Ea,Ca]}],"bg-color":[{bg:v()}],"gradient-from-pos":[{from:w()}],"gradient-via-pos":[{via:w()}],"gradient-to-pos":[{to:w()}],"gradient-from":[{from:v()}],"gradient-via":[{via:v()}],"gradient-to":[{to:v()}],rounded:[{rounded:T()}],"rounded-s":[{"rounded-s":T()}],"rounded-e":[{"rounded-e":T()}],"rounded-t":[{"rounded-t":T()}],"rounded-r":[{"rounded-r":T()}],"rounded-b":[{"rounded-b":T()}],"rounded-l":[{"rounded-l":T()}],"rounded-ss":[{"rounded-ss":T()}],"rounded-se":[{"rounded-se":T()}],"rounded-ee":[{"rounded-ee":T()}],"rounded-es":[{"rounded-es":T()}],"rounded-tl":[{"rounded-tl":T()}],"rounded-tr":[{"rounded-tr":T()}],"rounded-br":[{"rounded-br":T()}],"rounded-bl":[{"rounded-bl":T()}],"border-w":[{border:z()}],"border-w-x":[{"border-x":z()}],"border-w-y":[{"border-y":z()}],"border-w-s":[{"border-s":z()}],"border-w-e":[{"border-e":z()}],"border-w-t":[{"border-t":z()}],"border-w-r":[{"border-r":z()}],"border-w-b":[{"border-b":z()}],"border-w-l":[{"border-l":z()}],"divide-x":[{"divide-x":z()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":z()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...B(),"hidden","none"]}],"divide-style":[{divide:[...B(),"hidden","none"]}],"border-color":[{border:v()}],"border-color-x":[{"border-x":v()}],"border-color-y":[{"border-y":v()}],"border-color-s":[{"border-s":v()}],"border-color-e":[{"border-e":v()}],"border-color-t":[{"border-t":v()}],"border-color-r":[{"border-r":v()}],"border-color-b":[{"border-b":v()}],"border-color-l":[{"border-l":v()}],"divide-color":[{divide:v()}],"outline-style":[{outline:[...B(),"none","hidden"]}],"outline-offset":[{"outline-offset":[Z,S,k]}],"outline-w":[{outline:["",Z,et,_e]}],"outline-color":[{outline:[t]}],shadow:[{shadow:["","none",c,Ia,Ta]}],"shadow-color":[{shadow:v()}],"inset-shadow":[{"inset-shadow":["none",S,k,f]}],"inset-shadow-color":[{"inset-shadow":v()}],"ring-w":[{ring:z()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:v()}],"ring-offset-w":[{"ring-offset":[Z,_e]}],"ring-offset-color":[{"ring-offset":v()}],"inset-ring-w":[{"inset-ring":z()}],"inset-ring-color":[{"inset-ring":v()}],opacity:[{opacity:[Z,S,k]}],"mix-blend":[{"mix-blend":[...se(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":se()}],filter:[{filter:["","none",S,k]}],blur:[{blur:J()}],brightness:[{brightness:[Z,S,k]}],contrast:[{contrast:[Z,S,k]}],"drop-shadow":[{"drop-shadow":["","none",y,S,k]}],grayscale:[{grayscale:["",Z,S,k]}],"hue-rotate":[{"hue-rotate":[Z,S,k]}],invert:[{invert:["",Z,S,k]}],saturate:[{saturate:[Z,S,k]}],sepia:[{sepia:["",Z,S,k]}],"backdrop-filter":[{"backdrop-filter":["","none",S,k]}],"backdrop-blur":[{"backdrop-blur":J()}],"backdrop-brightness":[{"backdrop-brightness":[Z,S,k]}],"backdrop-contrast":[{"backdrop-contrast":[Z,S,k]}],"backdrop-grayscale":[{"backdrop-grayscale":["",Z,S,k]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[Z,S,k]}],"backdrop-invert":[{"backdrop-invert":["",Z,S,k]}],"backdrop-opacity":[{"backdrop-opacity":[Z,S,k]}],"backdrop-saturate":[{"backdrop-saturate":[Z,S,k]}],"backdrop-sepia":[{"backdrop-sepia":["",Z,S,k]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":m()}],"border-spacing-x":[{"border-spacing-x":m()}],"border-spacing-y":[{"border-spacing-y":m()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",S,k]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[Z,"initial",S,k]}],ease:[{ease:["linear","initial",E,S,k]}],delay:[{delay:[Z,S,k]}],animate:[{animate:["none",M,S,k]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[h,S,k]}],"perspective-origin":[{"perspective-origin":xe()}],rotate:[{rotate:ue()}],"rotate-x":[{"rotate-x":ue()}],"rotate-y":[{"rotate-y":ue()}],"rotate-z":[{"rotate-z":ue()}],scale:[{scale:fe()}],"scale-x":[{"scale-x":fe()}],"scale-y":[{"scale-y":fe()}],"scale-z":[{"scale-z":fe()}],"scale-3d":["scale-3d"],skew:[{skew:Te()}],"skew-x":[{"skew-x":Te()}],"skew-y":[{"skew-y":Te()}],transform:[{transform:[S,k,"","none","gpu","cpu"]}],"transform-origin":[{origin:xe()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Oe()}],"translate-x":[{"translate-x":Oe()}],"translate-y":[{"translate-y":Oe()}],"translate-z":[{"translate-z":Oe()}],"translate-none":["translate-none"],accent:[{accent:v()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:v()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",S,k]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":m()}],"scroll-mx":[{"scroll-mx":m()}],"scroll-my":[{"scroll-my":m()}],"scroll-ms":[{"scroll-ms":m()}],"scroll-me":[{"scroll-me":m()}],"scroll-mt":[{"scroll-mt":m()}],"scroll-mr":[{"scroll-mr":m()}],"scroll-mb":[{"scroll-mb":m()}],"scroll-ml":[{"scroll-ml":m()}],"scroll-p":[{"scroll-p":m()}],"scroll-px":[{"scroll-px":m()}],"scroll-py":[{"scroll-py":m()}],"scroll-ps":[{"scroll-ps":m()}],"scroll-pe":[{"scroll-pe":m()}],"scroll-pt":[{"scroll-pt":m()}],"scroll-pr":[{"scroll-pr":m()}],"scroll-pb":[{"scroll-pb":m()}],"scroll-pl":[{"scroll-pl":m()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",S,k]}],fill:[{fill:["none",...v()]}],"stroke-w":[{stroke:[Z,et,_e,mt]}],stroke:[{stroke:["none",...v()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["before","after","placeholder","file","marker","selection","first-line","first-letter","backdrop","*","**"]}},$a=(t,{cacheSize:e,prefix:r,experimentalParseClassName:n,extend:a={},override:s={}})=>(Fe(t,"cacheSize",e),Fe(t,"prefix",r),Fe(t,"experimentalParseClassName",n),tt(t.theme,s.theme),tt(t.classGroups,s.classGroups),tt(t.conflictingClassGroups,s.conflictingClassGroups),tt(t.conflictingClassGroupModifiers,s.conflictingClassGroupModifiers),Fe(t,"orderSensitiveModifiers",s.orderSensitiveModifiers),rt(t.theme,a.theme),rt(t.classGroups,a.classGroups),rt(t.conflictingClassGroups,a.conflictingClassGroups),rt(t.conflictingClassGroupModifiers,a.conflictingClassGroupModifiers),rn(t,a,"orderSensitiveModifiers"),t),Fe=(t,e,r)=>{r!==void 0&&(t[e]=r)},tt=(t,e)=>{if(e)for(const r in e)Fe(t,r,e[r])},rt=(t,e)=>{if(e)for(const r in e)rn(t,e,r)},rn=(t,e,r)=>{const n=e[r];n!==void 0&&(t[r]=t[r]?t[r].concat(n):n)},Za=(t,...e)=>typeof t=="function"?Rt(Pt,t,...e):Rt(()=>$a(Pt(),t),...e),Va=Rt(Pt);var Fa={twMerge:!0,twMergeConfig:{},responsiveVariants:!1},nn=t=>t||void 0,Be=(...t)=>nn(Gr(t).filter(Boolean).join(" ")),gt=null,le={},Dt=!1,Ze=(...t)=>e=>e.twMerge?((!gt||Dt)&&(Dt=!1,gt=re(le)?Va:Za({...le,extend:{theme:le.theme,classGroups:le.classGroups,conflictingClassGroupModifiers:le.conflictingClassGroupModifiers,conflictingClassGroups:le.conflictingClassGroups,...le.extend}})),nn(gt(Be(t)))):Be(t),rr=(t,e)=>{for(let r in e)t.hasOwnProperty(r)?t[r]=Be(t[r],e[r]):t[r]=e[r];return t},La=(t,e)=>{let{extend:r=null,slots:n={},variants:a={},compoundVariants:s=[],compoundSlots:i=[],defaultVariants:o={}}=t,l={...Fa,...e},d=r!=null&&r.base?Be(r.base,t?.base):t?.base,c=r!=null&&r.variants&&!re(r.variants)?Hr(a,r.variants):a,f=r!=null&&r.defaultVariants&&!re(r.defaultVariants)?{...r.defaultVariants,...o}:o;!re(l.twMergeConfig)&&!Qn(l.twMergeConfig,le)&&(Dt=!0,le=l.twMergeConfig);let y=re(r?.slots),g=re(n)?{}:{base:Be(t?.base,y&&r?.base),...n},h=y?g:rr({...r?.slots},re(g)?{base:t?.base}:g),j=re(r?.compoundVariants)?s:Br(r?.compoundVariants,s),E=N=>{if(re(c)&&re(n)&&y)return Ze(d,N?.class,N?.className)(l);if(j&&!Array.isArray(j))throw new TypeError(`The "compoundVariants" prop must be an array. Received: ${typeof j}`);if(i&&!Array.isArray(i))throw new TypeError(`The "compoundSlots" prop must be an array. Received: ${typeof i}`);let F=(A,_,x=[],I)=>{let v=x;if(typeof _=="string")v=v.concat(Jt(_).split(" ").map(w=>`${A}:${w}`));else if(Array.isArray(_))v=v.concat(_.reduce((w,T)=>w.concat(`${A}:${T}`),[]));else if(typeof _=="object"&&typeof I=="string"){for(let w in _)if(_.hasOwnProperty(w)&&w===I){let T=_[w];if(T&&typeof T=="string"){let z=Jt(T);v[I]?v[I]=v[I].concat(z.split(" ").map(B=>`${A}:${B}`)):v[I]=z.split(" ").map(B=>`${A}:${B}`)}else Array.isArray(T)&&T.length>0&&(v[I]=T.reduce((z,B)=>z.concat(`${A}:${B}`),[]))}}return v},D=(A,_=c,x=null,I=null)=>{var v;let w=_[A];if(!w||re(w))return null;let T=(v=I?.[A])!=null?v:N?.[A];if(T===null)return null;let z=Xt(T),B=Array.isArray(l.responsiveVariants)&&l.responsiveVariants.length>0||l.responsiveVariants===!0,se=f?.[A],J=[];if(typeof z=="object"&&B)for(let[fe,Te]of Object.entries(z)){let Oe=w[Te];if(fe==="initial"){se=Te;continue}Array.isArray(l.responsiveVariants)&&!l.responsiveVariants.includes(fe)||(J=F(fe,Oe,J,x))}let xe=z!=null&&typeof z!="object"?z:Xt(se),ue=w[xe||"false"];return typeof J=="object"&&typeof x=="string"&&J[x]?rr(J,ue):J.length>0?(J.push(ue),x==="base"?J.join(" "):J):ue},U=()=>c?Object.keys(c).map(A=>D(A,c)):null,m=(A,_)=>{if(!c||typeof c!="object")return null;let x=new Array;for(let I in c){let v=D(I,c,A,_),w=A==="base"&&typeof v=="string"?v:v&&v[A];w&&(x[x.length]=w)}return x},W={};for(let A in N)N[A]!==void 0&&(W[A]=N[A]);let G=(A,_)=>{var x;let I=typeof N?.[A]=="object"?{[A]:(x=N[A])==null?void 0:x.initial}:{};return{...f,...W,...I,..._}},K=(A=[],_)=>{let x=[];for(let{class:I,className:v,...w}of A){let T=!0;for(let[z,B]of Object.entries(w)){let se=G(z,_)[z];if(Array.isArray(B)){if(!B.includes(se)){T=!1;break}}else{let J=xe=>xe==null||xe===!1;if(J(B)&&J(se))continue;if(se!==B){T=!1;break}}}T&&(I&&x.push(I),v&&x.push(v))}return x},q=A=>{let _=K(j,A);if(!Array.isArray(_))return _;let x={};for(let I of _)if(typeof I=="string"&&(x.base=Ze(x.base,I)(l)),typeof I=="object")for(let[v,w]of Object.entries(I))x[v]=Ze(x[v],w)(l);return x},ee=A=>{if(i.length<1)return null;let _={};for(let{slots:x=[],class:I,className:v,...w}of i){if(!re(w)){let T=!0;for(let z of Object.keys(w)){let B=G(z,A)[z];if(B===void 0||(Array.isArray(w[z])?!w[z].includes(B):w[z]!==B)){T=!1;break}}if(!T)continue}for(let T of x)_[T]=_[T]||[],_[T].push([I,v])}return _};if(!re(n)||!y){let A={};if(typeof h=="object"&&!re(h))for(let _ of Object.keys(h))A[_]=x=>{var I,v;return Ze(h[_],m(_,x),((I=q(x))!=null?I:[])[_],((v=ee(x))!=null?v:[])[_],x?.class,x?.className)(l)};return A}return Ze(d,U(),K(j),N?.class,N?.className)(l)},M=()=>{if(!(!c||typeof c!="object"))return Object.keys(c)};return E.variantKeys=M(),E.extend=r,E.base=d,E.slots=h,E.variants=c,E.defaultVariants=f,E.compoundSlots=i,E.compoundVariants=j,E};const Ua=La({base:["relative block w-full appearance-none rounded-md border px-2.5 py-1.5 outline-hidden transition sm:text-sm","border-transparent dark:border-gray-800","text-gray-900 dark:text-gray-50","placeholder-gray-400 dark:placeholder-gray-500","bg-gray-100 dark:bg-gray-950","disabled:border-gray-300 disabled:bg-gray-100 disabled:text-gray-400","dark:disabled:border-gray-700 dark:disabled:bg-gray-800 dark:disabled:text-gray-500",An,"[&::--webkit-search-cancel-button]:hidden [&::-webkit-search-cancel-button]:hidden [&::-webkit-search-decoration]:hidden"],variants:{hasError:{true:Nn},enableStepper:{true:"[appearance:textfield] [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none"}}}),an=H.forwardRef(({className:t,inputClassName:e,hasError:r,enableStepper:n,type:a="search",...s},i)=>u.jsxs("div",{className:te("relative w-full",t),children:[u.jsx("input",{ref:i,type:a,className:te(Ua({hasError:r,enableStepper:n}),"pl-8",e),...s}),u.jsx("div",{className:te("pointer-events-none absolute bottom-0 left-2 flex h-full items-center justify-center","text-gray-400 dark:text-gray-600"),children:u.jsx(Rn,{className:"size-[1.125rem] shrink-0","aria-hidden":"true"})})]}));an.displayName="Searchbar";var L;(function(t){t.assertEqual=a=>{};function e(a){}t.assertIs=e;function r(a){throw new Error}t.assertNever=r,t.arrayToEnum=a=>{const s={};for(const i of a)s[i]=i;return s},t.getValidEnumValues=a=>{const s=t.objectKeys(a).filter(o=>typeof a[a[o]]!="number"),i={};for(const o of s)i[o]=a[o];return t.objectValues(i)},t.objectValues=a=>t.objectKeys(a).map(function(s){return a[s]}),t.objectKeys=typeof Object.keys=="function"?a=>Object.keys(a):a=>{const s=[];for(const i in a)Object.prototype.hasOwnProperty.call(a,i)&&s.push(i);return s},t.find=(a,s)=>{for(const i of a)if(s(i))return i},t.isInteger=typeof Number.isInteger=="function"?a=>Number.isInteger(a):a=>typeof a=="number"&&Number.isFinite(a)&&Math.floor(a)===a;function n(a,s=" | "){return a.map(i=>typeof i=="string"?`'${i}'`:i).join(s)}t.joinValues=n,t.jsonStringifyReplacer=(a,s)=>typeof s=="bigint"?s.toString():s})(L||(L={}));var nr;(function(t){t.mergeShapes=(e,r)=>({...e,...r})})(nr||(nr={}));const C=L.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),me=t=>{switch(typeof t){case"undefined":return C.undefined;case"string":return C.string;case"number":return Number.isNaN(t)?C.nan:C.number;case"boolean":return C.boolean;case"function":return C.function;case"bigint":return C.bigint;case"symbol":return C.symbol;case"object":return Array.isArray(t)?C.array:t===null?C.null:t.then&&typeof t.then=="function"&&t.catch&&typeof t.catch=="function"?C.promise:typeof Map<"u"&&t instanceof Map?C.map:typeof Set<"u"&&t instanceof Set?C.set:typeof Date<"u"&&t instanceof Date?C.date:C.object;default:return C.unknown}},p=L.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class de extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=n=>{this.issues=[...this.issues,n]},this.addIssues=(n=[])=>{this.issues=[...this.issues,...n]};const r=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,r):this.__proto__=r,this.name="ZodError",this.issues=e}format(e){const r=e||function(s){return s.message},n={_errors:[]},a=s=>{for(const i of s.issues)if(i.code==="invalid_union")i.unionErrors.map(a);else if(i.code==="invalid_return_type")a(i.returnTypeError);else if(i.code==="invalid_arguments")a(i.argumentsError);else if(i.path.length===0)n._errors.push(r(i));else{let o=n,l=0;for(;l<i.path.length;){const d=i.path[l];l===i.path.length-1?(o[d]=o[d]||{_errors:[]},o[d]._errors.push(r(i))):o[d]=o[d]||{_errors:[]},o=o[d],l++}}};return a(this),n}static assert(e){if(!(e instanceof de))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,L.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=r=>r.message){const r={},n=[];for(const a of this.issues)a.path.length>0?(r[a.path[0]]=r[a.path[0]]||[],r[a.path[0]].push(e(a))):n.push(e(a));return{formErrors:n,fieldErrors:r}}get formErrors(){return this.flatten()}}de.create=t=>new de(t);const Mt=(t,e)=>{let r;switch(t.code){case p.invalid_type:t.received===C.undefined?r="Required":r=`Expected ${t.expected}, received ${t.received}`;break;case p.invalid_literal:r=`Invalid literal value, expected ${JSON.stringify(t.expected,L.jsonStringifyReplacer)}`;break;case p.unrecognized_keys:r=`Unrecognized key(s) in object: ${L.joinValues(t.keys,", ")}`;break;case p.invalid_union:r="Invalid input";break;case p.invalid_union_discriminator:r=`Invalid discriminator value. Expected ${L.joinValues(t.options)}`;break;case p.invalid_enum_value:r=`Invalid enum value. Expected ${L.joinValues(t.options)}, received '${t.received}'`;break;case p.invalid_arguments:r="Invalid function arguments";break;case p.invalid_return_type:r="Invalid function return type";break;case p.invalid_date:r="Invalid date";break;case p.invalid_string:typeof t.validation=="object"?"includes"in t.validation?(r=`Invalid input: must include "${t.validation.includes}"`,typeof t.validation.position=="number"&&(r=`${r} at one or more positions greater than or equal to ${t.validation.position}`)):"startsWith"in t.validation?r=`Invalid input: must start with "${t.validation.startsWith}"`:"endsWith"in t.validation?r=`Invalid input: must end with "${t.validation.endsWith}"`:L.assertNever(t.validation):t.validation!=="regex"?r=`Invalid ${t.validation}`:r="Invalid";break;case p.too_small:t.type==="array"?r=`Array must contain ${t.exact?"exactly":t.inclusive?"at least":"more than"} ${t.minimum} element(s)`:t.type==="string"?r=`String must contain ${t.exact?"exactly":t.inclusive?"at least":"over"} ${t.minimum} character(s)`:t.type==="number"?r=`Number must be ${t.exact?"exactly equal to ":t.inclusive?"greater than or equal to ":"greater than "}${t.minimum}`:t.type==="date"?r=`Date must be ${t.exact?"exactly equal to ":t.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(t.minimum))}`:r="Invalid input";break;case p.too_big:t.type==="array"?r=`Array must contain ${t.exact?"exactly":t.inclusive?"at most":"less than"} ${t.maximum} element(s)`:t.type==="string"?r=`String must contain ${t.exact?"exactly":t.inclusive?"at most":"under"} ${t.maximum} character(s)`:t.type==="number"?r=`Number must be ${t.exact?"exactly":t.inclusive?"less than or equal to":"less than"} ${t.maximum}`:t.type==="bigint"?r=`BigInt must be ${t.exact?"exactly":t.inclusive?"less than or equal to":"less than"} ${t.maximum}`:t.type==="date"?r=`Date must be ${t.exact?"exactly":t.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(t.maximum))}`:r="Invalid input";break;case p.custom:r="Invalid input";break;case p.invalid_intersection_types:r="Intersection results could not be merged";break;case p.not_multiple_of:r=`Number must be a multiple of ${t.multipleOf}`;break;case p.not_finite:r="Number must be finite";break;default:r=e.defaultError,L.assertNever(t)}return{message:r}};let Wa=Mt;function Ga(){return Wa}const Ba=t=>{const{data:e,path:r,errorMaps:n,issueData:a}=t,s=[...r,...a.path||[]],i={...a,path:s};if(a.message!==void 0)return{...a,path:s,message:a.message};let o="";const l=n.filter(d=>!!d).slice().reverse();for(const d of l)o=d(i,{data:e,defaultError:o}).message;return{...a,path:s,message:o}};function b(t,e){const r=Ga(),n=Ba({issueData:e,data:t.data,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,r,r===Mt?void 0:Mt].filter(a=>!!a)});t.common.issues.push(n)}class ne{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,r){const n=[];for(const a of r){if(a.status==="aborted")return R;a.status==="dirty"&&e.dirty(),n.push(a.value)}return{status:e.value,value:n}}static async mergeObjectAsync(e,r){const n=[];for(const a of r){const s=await a.key,i=await a.value;n.push({key:s,value:i})}return ne.mergeObjectSync(e,n)}static mergeObjectSync(e,r){const n={};for(const a of r){const{key:s,value:i}=a;if(s.status==="aborted"||i.status==="aborted")return R;s.status==="dirty"&&e.dirty(),i.status==="dirty"&&e.dirty(),s.value!=="__proto__"&&(typeof i.value<"u"||a.alwaysSet)&&(n[s.value]=i.value)}return{status:e.value,value:n}}}const R=Object.freeze({status:"aborted"}),Le=t=>({status:"dirty",value:t}),ae=t=>({status:"valid",value:t}),ar=t=>t.status==="aborted",sr=t=>t.status==="dirty",Ee=t=>t.status==="valid",it=t=>typeof Promise<"u"&&t instanceof Promise;var O;(function(t){t.errToObj=e=>typeof e=="string"?{message:e}:e||{},t.toString=e=>typeof e=="string"?e:e?.message})(O||(O={}));class ye{constructor(e,r,n,a){this._cachedPath=[],this.parent=e,this.data=r,this._path=n,this._key=a}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const ir=(t,e)=>{if(Ee(e))return{success:!0,data:e.value};if(!t.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const r=new de(t.common.issues);return this._error=r,this._error}}};function $(t){if(!t)return{};const{errorMap:e,invalid_type_error:r,required_error:n,description:a}=t;if(e&&(r||n))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:a}:{errorMap:(i,o)=>{const{message:l}=t;return i.code==="invalid_enum_value"?{message:l??o.defaultError}:typeof o.data>"u"?{message:l??n??o.defaultError}:i.code!=="invalid_type"?{message:o.defaultError}:{message:l??r??o.defaultError}},description:a}}class V{get description(){return this._def.description}_getType(e){return me(e.data)}_getOrReturnCtx(e,r){return r||{common:e.parent.common,data:e.data,parsedType:me(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new ne,ctx:{common:e.parent.common,data:e.data,parsedType:me(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const r=this._parse(e);if(it(r))throw new Error("Synchronous parse encountered promise.");return r}_parseAsync(e){const r=this._parse(e);return Promise.resolve(r)}parse(e,r){const n=this.safeParse(e,r);if(n.success)return n.data;throw n.error}safeParse(e,r){const n={common:{issues:[],async:r?.async??!1,contextualErrorMap:r?.errorMap},path:r?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:me(e)},a=this._parseSync({data:e,path:n.path,parent:n});return ir(n,a)}"~validate"(e){const r={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:me(e)};if(!this["~standard"].async)try{const n=this._parseSync({data:e,path:[],parent:r});return Ee(n)?{value:n.value}:{issues:r.common.issues}}catch(n){n?.message?.toLowerCase()?.includes("encountered")&&(this["~standard"].async=!0),r.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:r}).then(n=>Ee(n)?{value:n.value}:{issues:r.common.issues})}async parseAsync(e,r){const n=await this.safeParseAsync(e,r);if(n.success)return n.data;throw n.error}async safeParseAsync(e,r){const n={common:{issues:[],contextualErrorMap:r?.errorMap,async:!0},path:r?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:me(e)},a=this._parse({data:e,path:n.path,parent:n}),s=await(it(a)?a:Promise.resolve(a));return ir(n,s)}refine(e,r){const n=a=>typeof r=="string"||typeof r>"u"?{message:r}:typeof r=="function"?r(a):r;return this._refinement((a,s)=>{const i=e(a),o=()=>s.addIssue({code:p.custom,...n(a)});return typeof Promise<"u"&&i instanceof Promise?i.then(l=>l?!0:(o(),!1)):i?!0:(o(),!1)})}refinement(e,r){return this._refinement((n,a)=>e(n)?!0:(a.addIssue(typeof r=="function"?r(n,a):r),!1))}_refinement(e){return new Pe({schema:this,typeName:P.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:r=>this["~validate"](r)}}optional(){return ve.create(this,this._def)}nullable(){return De.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return oe.create(this)}promise(){return dt.create(this,this._def)}or(e){return lt.create([this,e],this._def)}and(e){return ct.create(this,e,this._def)}transform(e){return new Pe({...$(this._def),schema:this,typeName:P.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const r=typeof e=="function"?e:()=>e;return new $t({...$(this._def),innerType:this,defaultValue:r,typeName:P.ZodDefault})}brand(){return new ms({typeName:P.ZodBranded,type:this,...$(this._def)})}catch(e){const r=typeof e=="function"?e:()=>e;return new Zt({...$(this._def),innerType:this,catchValue:r,typeName:P.ZodCatch})}describe(e){const r=this.constructor;return new r({...this._def,description:e})}pipe(e){return Ht.create(this,e)}readonly(){return Vt.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const Ha=/^c[^\s-]{8,}$/i,qa=/^[0-9a-z]+$/,Ka=/^[0-9A-HJKMNP-TV-Z]{26}$/i,Ya=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,Xa=/^[a-z0-9_-]{21}$/i,Ja=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,Qa=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,es=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,ts="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";let vt;const rs=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,ns=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,as=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,ss=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,is=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,os=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,sn="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",ls=new RegExp(`^${sn}$`);function on(t){let e="[0-5]\\d";t.precision?e=`${e}\\.\\d{${t.precision}}`:t.precision==null&&(e=`${e}(\\.\\d+)?`);const r=t.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${e})${r}`}function cs(t){return new RegExp(`^${on(t)}$`)}function ds(t){let e=`${sn}T${on(t)}`;const r=[];return r.push(t.local?"Z?":"Z"),t.offset&&r.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${r.join("|")})`,new RegExp(`^${e}$`)}function us(t,e){return!!((e==="v4"||!e)&&rs.test(t)||(e==="v6"||!e)&&as.test(t))}function fs(t,e){if(!Ja.test(t))return!1;try{const[r]=t.split("."),n=r.replace(/-/g,"+").replace(/_/g,"/").padEnd(r.length+(4-r.length%4)%4,"="),a=JSON.parse(atob(n));return!(typeof a!="object"||a===null||"typ"in a&&a?.typ!=="JWT"||!a.alg||e&&a.alg!==e)}catch{return!1}}function ps(t,e){return!!((e==="v4"||!e)&&ns.test(t)||(e==="v6"||!e)&&ss.test(t))}class ge extends V{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==C.string){const s=this._getOrReturnCtx(e);return b(s,{code:p.invalid_type,expected:C.string,received:s.parsedType}),R}const n=new ne;let a;for(const s of this._def.checks)if(s.kind==="min")e.data.length<s.value&&(a=this._getOrReturnCtx(e,a),b(a,{code:p.too_small,minimum:s.value,type:"string",inclusive:!0,exact:!1,message:s.message}),n.dirty());else if(s.kind==="max")e.data.length>s.value&&(a=this._getOrReturnCtx(e,a),b(a,{code:p.too_big,maximum:s.value,type:"string",inclusive:!0,exact:!1,message:s.message}),n.dirty());else if(s.kind==="length"){const i=e.data.length>s.value,o=e.data.length<s.value;(i||o)&&(a=this._getOrReturnCtx(e,a),i?b(a,{code:p.too_big,maximum:s.value,type:"string",inclusive:!0,exact:!0,message:s.message}):o&&b(a,{code:p.too_small,minimum:s.value,type:"string",inclusive:!0,exact:!0,message:s.message}),n.dirty())}else if(s.kind==="email")es.test(e.data)||(a=this._getOrReturnCtx(e,a),b(a,{validation:"email",code:p.invalid_string,message:s.message}),n.dirty());else if(s.kind==="emoji")vt||(vt=new RegExp(ts,"u")),vt.test(e.data)||(a=this._getOrReturnCtx(e,a),b(a,{validation:"emoji",code:p.invalid_string,message:s.message}),n.dirty());else if(s.kind==="uuid")Ya.test(e.data)||(a=this._getOrReturnCtx(e,a),b(a,{validation:"uuid",code:p.invalid_string,message:s.message}),n.dirty());else if(s.kind==="nanoid")Xa.test(e.data)||(a=this._getOrReturnCtx(e,a),b(a,{validation:"nanoid",code:p.invalid_string,message:s.message}),n.dirty());else if(s.kind==="cuid")Ha.test(e.data)||(a=this._getOrReturnCtx(e,a),b(a,{validation:"cuid",code:p.invalid_string,message:s.message}),n.dirty());else if(s.kind==="cuid2")qa.test(e.data)||(a=this._getOrReturnCtx(e,a),b(a,{validation:"cuid2",code:p.invalid_string,message:s.message}),n.dirty());else if(s.kind==="ulid")Ka.test(e.data)||(a=this._getOrReturnCtx(e,a),b(a,{validation:"ulid",code:p.invalid_string,message:s.message}),n.dirty());else if(s.kind==="url")try{new URL(e.data)}catch{a=this._getOrReturnCtx(e,a),b(a,{validation:"url",code:p.invalid_string,message:s.message}),n.dirty()}else s.kind==="regex"?(s.regex.lastIndex=0,s.regex.test(e.data)||(a=this._getOrReturnCtx(e,a),b(a,{validation:"regex",code:p.invalid_string,message:s.message}),n.dirty())):s.kind==="trim"?e.data=e.data.trim():s.kind==="includes"?e.data.includes(s.value,s.position)||(a=this._getOrReturnCtx(e,a),b(a,{code:p.invalid_string,validation:{includes:s.value,position:s.position},message:s.message}),n.dirty()):s.kind==="toLowerCase"?e.data=e.data.toLowerCase():s.kind==="toUpperCase"?e.data=e.data.toUpperCase():s.kind==="startsWith"?e.data.startsWith(s.value)||(a=this._getOrReturnCtx(e,a),b(a,{code:p.invalid_string,validation:{startsWith:s.value},message:s.message}),n.dirty()):s.kind==="endsWith"?e.data.endsWith(s.value)||(a=this._getOrReturnCtx(e,a),b(a,{code:p.invalid_string,validation:{endsWith:s.value},message:s.message}),n.dirty()):s.kind==="datetime"?ds(s).test(e.data)||(a=this._getOrReturnCtx(e,a),b(a,{code:p.invalid_string,validation:"datetime",message:s.message}),n.dirty()):s.kind==="date"?ls.test(e.data)||(a=this._getOrReturnCtx(e,a),b(a,{code:p.invalid_string,validation:"date",message:s.message}),n.dirty()):s.kind==="time"?cs(s).test(e.data)||(a=this._getOrReturnCtx(e,a),b(a,{code:p.invalid_string,validation:"time",message:s.message}),n.dirty()):s.kind==="duration"?Qa.test(e.data)||(a=this._getOrReturnCtx(e,a),b(a,{validation:"duration",code:p.invalid_string,message:s.message}),n.dirty()):s.kind==="ip"?us(e.data,s.version)||(a=this._getOrReturnCtx(e,a),b(a,{validation:"ip",code:p.invalid_string,message:s.message}),n.dirty()):s.kind==="jwt"?fs(e.data,s.alg)||(a=this._getOrReturnCtx(e,a),b(a,{validation:"jwt",code:p.invalid_string,message:s.message}),n.dirty()):s.kind==="cidr"?ps(e.data,s.version)||(a=this._getOrReturnCtx(e,a),b(a,{validation:"cidr",code:p.invalid_string,message:s.message}),n.dirty()):s.kind==="base64"?is.test(e.data)||(a=this._getOrReturnCtx(e,a),b(a,{validation:"base64",code:p.invalid_string,message:s.message}),n.dirty()):s.kind==="base64url"?os.test(e.data)||(a=this._getOrReturnCtx(e,a),b(a,{validation:"base64url",code:p.invalid_string,message:s.message}),n.dirty()):L.assertNever(s);return{status:n.value,value:e.data}}_regex(e,r,n){return this.refinement(a=>e.test(a),{validation:r,code:p.invalid_string,...O.errToObj(n)})}_addCheck(e){return new ge({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...O.errToObj(e)})}url(e){return this._addCheck({kind:"url",...O.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...O.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...O.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...O.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...O.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...O.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...O.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...O.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...O.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...O.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...O.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...O.errToObj(e)})}datetime(e){return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:typeof e?.precision>"u"?null:e?.precision,offset:e?.offset??!1,local:e?.local??!1,...O.errToObj(e?.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:typeof e?.precision>"u"?null:e?.precision,...O.errToObj(e?.message)})}duration(e){return this._addCheck({kind:"duration",...O.errToObj(e)})}regex(e,r){return this._addCheck({kind:"regex",regex:e,...O.errToObj(r)})}includes(e,r){return this._addCheck({kind:"includes",value:e,position:r?.position,...O.errToObj(r?.message)})}startsWith(e,r){return this._addCheck({kind:"startsWith",value:e,...O.errToObj(r)})}endsWith(e,r){return this._addCheck({kind:"endsWith",value:e,...O.errToObj(r)})}min(e,r){return this._addCheck({kind:"min",value:e,...O.errToObj(r)})}max(e,r){return this._addCheck({kind:"max",value:e,...O.errToObj(r)})}length(e,r){return this._addCheck({kind:"length",value:e,...O.errToObj(r)})}nonempty(e){return this.min(1,O.errToObj(e))}trim(){return new ge({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new ge({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new ge({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isCIDR(){return!!this._def.checks.find(e=>e.kind==="cidr")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get isBase64url(){return!!this._def.checks.find(e=>e.kind==="base64url")}get minLength(){let e=null;for(const r of this._def.checks)r.kind==="min"&&(e===null||r.value>e)&&(e=r.value);return e}get maxLength(){let e=null;for(const r of this._def.checks)r.kind==="max"&&(e===null||r.value<e)&&(e=r.value);return e}}ge.create=t=>new ge({checks:[],typeName:P.ZodString,coerce:t?.coerce??!1,...$(t)});function hs(t,e){const r=(t.toString().split(".")[1]||"").length,n=(e.toString().split(".")[1]||"").length,a=r>n?r:n,s=Number.parseInt(t.toFixed(a).replace(".","")),i=Number.parseInt(e.toFixed(a).replace(".",""));return s%i/10**a}class Ie extends V{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==C.number){const s=this._getOrReturnCtx(e);return b(s,{code:p.invalid_type,expected:C.number,received:s.parsedType}),R}let n;const a=new ne;for(const s of this._def.checks)s.kind==="int"?L.isInteger(e.data)||(n=this._getOrReturnCtx(e,n),b(n,{code:p.invalid_type,expected:"integer",received:"float",message:s.message}),a.dirty()):s.kind==="min"?(s.inclusive?e.data<s.value:e.data<=s.value)&&(n=this._getOrReturnCtx(e,n),b(n,{code:p.too_small,minimum:s.value,type:"number",inclusive:s.inclusive,exact:!1,message:s.message}),a.dirty()):s.kind==="max"?(s.inclusive?e.data>s.value:e.data>=s.value)&&(n=this._getOrReturnCtx(e,n),b(n,{code:p.too_big,maximum:s.value,type:"number",inclusive:s.inclusive,exact:!1,message:s.message}),a.dirty()):s.kind==="multipleOf"?hs(e.data,s.value)!==0&&(n=this._getOrReturnCtx(e,n),b(n,{code:p.not_multiple_of,multipleOf:s.value,message:s.message}),a.dirty()):s.kind==="finite"?Number.isFinite(e.data)||(n=this._getOrReturnCtx(e,n),b(n,{code:p.not_finite,message:s.message}),a.dirty()):L.assertNever(s);return{status:a.value,value:e.data}}gte(e,r){return this.setLimit("min",e,!0,O.toString(r))}gt(e,r){return this.setLimit("min",e,!1,O.toString(r))}lte(e,r){return this.setLimit("max",e,!0,O.toString(r))}lt(e,r){return this.setLimit("max",e,!1,O.toString(r))}setLimit(e,r,n,a){return new Ie({...this._def,checks:[...this._def.checks,{kind:e,value:r,inclusive:n,message:O.toString(a)}]})}_addCheck(e){return new Ie({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:O.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:O.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:O.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:O.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:O.toString(e)})}multipleOf(e,r){return this._addCheck({kind:"multipleOf",value:e,message:O.toString(r)})}finite(e){return this._addCheck({kind:"finite",message:O.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:O.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:O.toString(e)})}get minValue(){let e=null;for(const r of this._def.checks)r.kind==="min"&&(e===null||r.value>e)&&(e=r.value);return e}get maxValue(){let e=null;for(const r of this._def.checks)r.kind==="max"&&(e===null||r.value<e)&&(e=r.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&L.isInteger(e.value))}get isFinite(){let e=null,r=null;for(const n of this._def.checks){if(n.kind==="finite"||n.kind==="int"||n.kind==="multipleOf")return!0;n.kind==="min"?(r===null||n.value>r)&&(r=n.value):n.kind==="max"&&(e===null||n.value<e)&&(e=n.value)}return Number.isFinite(r)&&Number.isFinite(e)}}Ie.create=t=>new Ie({checks:[],typeName:P.ZodNumber,coerce:t?.coerce||!1,...$(t)});class He extends V{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==C.bigint)return this._getInvalidInput(e);let n;const a=new ne;for(const s of this._def.checks)s.kind==="min"?(s.inclusive?e.data<s.value:e.data<=s.value)&&(n=this._getOrReturnCtx(e,n),b(n,{code:p.too_small,type:"bigint",minimum:s.value,inclusive:s.inclusive,message:s.message}),a.dirty()):s.kind==="max"?(s.inclusive?e.data>s.value:e.data>=s.value)&&(n=this._getOrReturnCtx(e,n),b(n,{code:p.too_big,type:"bigint",maximum:s.value,inclusive:s.inclusive,message:s.message}),a.dirty()):s.kind==="multipleOf"?e.data%s.value!==BigInt(0)&&(n=this._getOrReturnCtx(e,n),b(n,{code:p.not_multiple_of,multipleOf:s.value,message:s.message}),a.dirty()):L.assertNever(s);return{status:a.value,value:e.data}}_getInvalidInput(e){const r=this._getOrReturnCtx(e);return b(r,{code:p.invalid_type,expected:C.bigint,received:r.parsedType}),R}gte(e,r){return this.setLimit("min",e,!0,O.toString(r))}gt(e,r){return this.setLimit("min",e,!1,O.toString(r))}lte(e,r){return this.setLimit("max",e,!0,O.toString(r))}lt(e,r){return this.setLimit("max",e,!1,O.toString(r))}setLimit(e,r,n,a){return new He({...this._def,checks:[...this._def.checks,{kind:e,value:r,inclusive:n,message:O.toString(a)}]})}_addCheck(e){return new He({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:O.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:O.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:O.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:O.toString(e)})}multipleOf(e,r){return this._addCheck({kind:"multipleOf",value:e,message:O.toString(r)})}get minValue(){let e=null;for(const r of this._def.checks)r.kind==="min"&&(e===null||r.value>e)&&(e=r.value);return e}get maxValue(){let e=null;for(const r of this._def.checks)r.kind==="max"&&(e===null||r.value<e)&&(e=r.value);return e}}He.create=t=>new He({checks:[],typeName:P.ZodBigInt,coerce:t?.coerce??!1,...$(t)});class or extends V{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==C.boolean){const n=this._getOrReturnCtx(e);return b(n,{code:p.invalid_type,expected:C.boolean,received:n.parsedType}),R}return ae(e.data)}}or.create=t=>new or({typeName:P.ZodBoolean,coerce:t?.coerce||!1,...$(t)});class ot extends V{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==C.date){const s=this._getOrReturnCtx(e);return b(s,{code:p.invalid_type,expected:C.date,received:s.parsedType}),R}if(Number.isNaN(e.data.getTime())){const s=this._getOrReturnCtx(e);return b(s,{code:p.invalid_date}),R}const n=new ne;let a;for(const s of this._def.checks)s.kind==="min"?e.data.getTime()<s.value&&(a=this._getOrReturnCtx(e,a),b(a,{code:p.too_small,message:s.message,inclusive:!0,exact:!1,minimum:s.value,type:"date"}),n.dirty()):s.kind==="max"?e.data.getTime()>s.value&&(a=this._getOrReturnCtx(e,a),b(a,{code:p.too_big,message:s.message,inclusive:!0,exact:!1,maximum:s.value,type:"date"}),n.dirty()):L.assertNever(s);return{status:n.value,value:new Date(e.data.getTime())}}_addCheck(e){return new ot({...this._def,checks:[...this._def.checks,e]})}min(e,r){return this._addCheck({kind:"min",value:e.getTime(),message:O.toString(r)})}max(e,r){return this._addCheck({kind:"max",value:e.getTime(),message:O.toString(r)})}get minDate(){let e=null;for(const r of this._def.checks)r.kind==="min"&&(e===null||r.value>e)&&(e=r.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(const r of this._def.checks)r.kind==="max"&&(e===null||r.value<e)&&(e=r.value);return e!=null?new Date(e):null}}ot.create=t=>new ot({checks:[],coerce:t?.coerce||!1,typeName:P.ZodDate,...$(t)});class lr extends V{_parse(e){if(this._getType(e)!==C.symbol){const n=this._getOrReturnCtx(e);return b(n,{code:p.invalid_type,expected:C.symbol,received:n.parsedType}),R}return ae(e.data)}}lr.create=t=>new lr({typeName:P.ZodSymbol,...$(t)});class cr extends V{_parse(e){if(this._getType(e)!==C.undefined){const n=this._getOrReturnCtx(e);return b(n,{code:p.invalid_type,expected:C.undefined,received:n.parsedType}),R}return ae(e.data)}}cr.create=t=>new cr({typeName:P.ZodUndefined,...$(t)});class dr extends V{_parse(e){if(this._getType(e)!==C.null){const n=this._getOrReturnCtx(e);return b(n,{code:p.invalid_type,expected:C.null,received:n.parsedType}),R}return ae(e.data)}}dr.create=t=>new dr({typeName:P.ZodNull,...$(t)});class ur extends V{constructor(){super(...arguments),this._any=!0}_parse(e){return ae(e.data)}}ur.create=t=>new ur({typeName:P.ZodAny,...$(t)});class fr extends V{constructor(){super(...arguments),this._unknown=!0}_parse(e){return ae(e.data)}}fr.create=t=>new fr({typeName:P.ZodUnknown,...$(t)});class be extends V{_parse(e){const r=this._getOrReturnCtx(e);return b(r,{code:p.invalid_type,expected:C.never,received:r.parsedType}),R}}be.create=t=>new be({typeName:P.ZodNever,...$(t)});class pr extends V{_parse(e){if(this._getType(e)!==C.undefined){const n=this._getOrReturnCtx(e);return b(n,{code:p.invalid_type,expected:C.void,received:n.parsedType}),R}return ae(e.data)}}pr.create=t=>new pr({typeName:P.ZodVoid,...$(t)});class oe extends V{_parse(e){const{ctx:r,status:n}=this._processInputParams(e),a=this._def;if(r.parsedType!==C.array)return b(r,{code:p.invalid_type,expected:C.array,received:r.parsedType}),R;if(a.exactLength!==null){const i=r.data.length>a.exactLength.value,o=r.data.length<a.exactLength.value;(i||o)&&(b(r,{code:i?p.too_big:p.too_small,minimum:o?a.exactLength.value:void 0,maximum:i?a.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:a.exactLength.message}),n.dirty())}if(a.minLength!==null&&r.data.length<a.minLength.value&&(b(r,{code:p.too_small,minimum:a.minLength.value,type:"array",inclusive:!0,exact:!1,message:a.minLength.message}),n.dirty()),a.maxLength!==null&&r.data.length>a.maxLength.value&&(b(r,{code:p.too_big,maximum:a.maxLength.value,type:"array",inclusive:!0,exact:!1,message:a.maxLength.message}),n.dirty()),r.common.async)return Promise.all([...r.data].map((i,o)=>a.type._parseAsync(new ye(r,i,r.path,o)))).then(i=>ne.mergeArray(n,i));const s=[...r.data].map((i,o)=>a.type._parseSync(new ye(r,i,r.path,o)));return ne.mergeArray(n,s)}get element(){return this._def.type}min(e,r){return new oe({...this._def,minLength:{value:e,message:O.toString(r)}})}max(e,r){return new oe({...this._def,maxLength:{value:e,message:O.toString(r)}})}length(e,r){return new oe({...this._def,exactLength:{value:e,message:O.toString(r)}})}nonempty(e){return this.min(1,e)}}oe.create=(t,e)=>new oe({type:t,minLength:null,maxLength:null,exactLength:null,typeName:P.ZodArray,...$(e)});function Ne(t){if(t instanceof X){const e={};for(const r in t.shape){const n=t.shape[r];e[r]=ve.create(Ne(n))}return new X({...t._def,shape:()=>e})}else return t instanceof oe?new oe({...t._def,type:Ne(t.element)}):t instanceof ve?ve.create(Ne(t.unwrap())):t instanceof De?De.create(Ne(t.unwrap())):t instanceof je?je.create(t.items.map(e=>Ne(e))):t}class X extends V{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const e=this._def.shape(),r=L.objectKeys(e);return this._cached={shape:e,keys:r},this._cached}_parse(e){if(this._getType(e)!==C.object){const d=this._getOrReturnCtx(e);return b(d,{code:p.invalid_type,expected:C.object,received:d.parsedType}),R}const{status:n,ctx:a}=this._processInputParams(e),{shape:s,keys:i}=this._getCached(),o=[];if(!(this._def.catchall instanceof be&&this._def.unknownKeys==="strip"))for(const d in a.data)i.includes(d)||o.push(d);const l=[];for(const d of i){const c=s[d],f=a.data[d];l.push({key:{status:"valid",value:d},value:c._parse(new ye(a,f,a.path,d)),alwaysSet:d in a.data})}if(this._def.catchall instanceof be){const d=this._def.unknownKeys;if(d==="passthrough")for(const c of o)l.push({key:{status:"valid",value:c},value:{status:"valid",value:a.data[c]}});else if(d==="strict")o.length>0&&(b(a,{code:p.unrecognized_keys,keys:o}),n.dirty());else if(d!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const d=this._def.catchall;for(const c of o){const f=a.data[c];l.push({key:{status:"valid",value:c},value:d._parse(new ye(a,f,a.path,c)),alwaysSet:c in a.data})}}return a.common.async?Promise.resolve().then(async()=>{const d=[];for(const c of l){const f=await c.key,y=await c.value;d.push({key:f,value:y,alwaysSet:c.alwaysSet})}return d}).then(d=>ne.mergeObjectSync(n,d)):ne.mergeObjectSync(n,l)}get shape(){return this._def.shape()}strict(e){return O.errToObj,new X({...this._def,unknownKeys:"strict",...e!==void 0?{errorMap:(r,n)=>{const a=this._def.errorMap?.(r,n).message??n.defaultError;return r.code==="unrecognized_keys"?{message:O.errToObj(e).message??a}:{message:a}}}:{}})}strip(){return new X({...this._def,unknownKeys:"strip"})}passthrough(){return new X({...this._def,unknownKeys:"passthrough"})}extend(e){return new X({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new X({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:P.ZodObject})}setKey(e,r){return this.augment({[e]:r})}catchall(e){return new X({...this._def,catchall:e})}pick(e){const r={};for(const n of L.objectKeys(e))e[n]&&this.shape[n]&&(r[n]=this.shape[n]);return new X({...this._def,shape:()=>r})}omit(e){const r={};for(const n of L.objectKeys(this.shape))e[n]||(r[n]=this.shape[n]);return new X({...this._def,shape:()=>r})}deepPartial(){return Ne(this)}partial(e){const r={};for(const n of L.objectKeys(this.shape)){const a=this.shape[n];e&&!e[n]?r[n]=a:r[n]=a.optional()}return new X({...this._def,shape:()=>r})}required(e){const r={};for(const n of L.objectKeys(this.shape))if(e&&!e[n])r[n]=this.shape[n];else{let s=this.shape[n];for(;s instanceof ve;)s=s._def.innerType;r[n]=s}return new X({...this._def,shape:()=>r})}keyof(){return ln(L.objectKeys(this.shape))}}X.create=(t,e)=>new X({shape:()=>t,unknownKeys:"strip",catchall:be.create(),typeName:P.ZodObject,...$(e)});X.strictCreate=(t,e)=>new X({shape:()=>t,unknownKeys:"strict",catchall:be.create(),typeName:P.ZodObject,...$(e)});X.lazycreate=(t,e)=>new X({shape:t,unknownKeys:"strip",catchall:be.create(),typeName:P.ZodObject,...$(e)});class lt extends V{_parse(e){const{ctx:r}=this._processInputParams(e),n=this._def.options;function a(s){for(const o of s)if(o.result.status==="valid")return o.result;for(const o of s)if(o.result.status==="dirty")return r.common.issues.push(...o.ctx.common.issues),o.result;const i=s.map(o=>new de(o.ctx.common.issues));return b(r,{code:p.invalid_union,unionErrors:i}),R}if(r.common.async)return Promise.all(n.map(async s=>{const i={...r,common:{...r.common,issues:[]},parent:null};return{result:await s._parseAsync({data:r.data,path:r.path,parent:i}),ctx:i}})).then(a);{let s;const i=[];for(const l of n){const d={...r,common:{...r.common,issues:[]},parent:null},c=l._parseSync({data:r.data,path:r.path,parent:d});if(c.status==="valid")return c;c.status==="dirty"&&!s&&(s={result:c,ctx:d}),d.common.issues.length&&i.push(d.common.issues)}if(s)return r.common.issues.push(...s.ctx.common.issues),s.result;const o=i.map(l=>new de(l));return b(r,{code:p.invalid_union,unionErrors:o}),R}}get options(){return this._def.options}}lt.create=(t,e)=>new lt({options:t,typeName:P.ZodUnion,...$(e)});function zt(t,e){const r=me(t),n=me(e);if(t===e)return{valid:!0,data:t};if(r===C.object&&n===C.object){const a=L.objectKeys(e),s=L.objectKeys(t).filter(o=>a.indexOf(o)!==-1),i={...t,...e};for(const o of s){const l=zt(t[o],e[o]);if(!l.valid)return{valid:!1};i[o]=l.data}return{valid:!0,data:i}}else if(r===C.array&&n===C.array){if(t.length!==e.length)return{valid:!1};const a=[];for(let s=0;s<t.length;s++){const i=t[s],o=e[s],l=zt(i,o);if(!l.valid)return{valid:!1};a.push(l.data)}return{valid:!0,data:a}}else return r===C.date&&n===C.date&&+t==+e?{valid:!0,data:t}:{valid:!1}}class ct extends V{_parse(e){const{status:r,ctx:n}=this._processInputParams(e),a=(s,i)=>{if(ar(s)||ar(i))return R;const o=zt(s.value,i.value);return o.valid?((sr(s)||sr(i))&&r.dirty(),{status:r.value,value:o.data}):(b(n,{code:p.invalid_intersection_types}),R)};return n.common.async?Promise.all([this._def.left._parseAsync({data:n.data,path:n.path,parent:n}),this._def.right._parseAsync({data:n.data,path:n.path,parent:n})]).then(([s,i])=>a(s,i)):a(this._def.left._parseSync({data:n.data,path:n.path,parent:n}),this._def.right._parseSync({data:n.data,path:n.path,parent:n}))}}ct.create=(t,e,r)=>new ct({left:t,right:e,typeName:P.ZodIntersection,...$(r)});class je extends V{_parse(e){const{status:r,ctx:n}=this._processInputParams(e);if(n.parsedType!==C.array)return b(n,{code:p.invalid_type,expected:C.array,received:n.parsedType}),R;if(n.data.length<this._def.items.length)return b(n,{code:p.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),R;!this._def.rest&&n.data.length>this._def.items.length&&(b(n,{code:p.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),r.dirty());const s=[...n.data].map((i,o)=>{const l=this._def.items[o]||this._def.rest;return l?l._parse(new ye(n,i,n.path,o)):null}).filter(i=>!!i);return n.common.async?Promise.all(s).then(i=>ne.mergeArray(r,i)):ne.mergeArray(r,s)}get items(){return this._def.items}rest(e){return new je({...this._def,rest:e})}}je.create=(t,e)=>{if(!Array.isArray(t))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new je({items:t,typeName:P.ZodTuple,rest:null,...$(e)})};class hr extends V{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:r,ctx:n}=this._processInputParams(e);if(n.parsedType!==C.map)return b(n,{code:p.invalid_type,expected:C.map,received:n.parsedType}),R;const a=this._def.keyType,s=this._def.valueType,i=[...n.data.entries()].map(([o,l],d)=>({key:a._parse(new ye(n,o,n.path,[d,"key"])),value:s._parse(new ye(n,l,n.path,[d,"value"]))}));if(n.common.async){const o=new Map;return Promise.resolve().then(async()=>{for(const l of i){const d=await l.key,c=await l.value;if(d.status==="aborted"||c.status==="aborted")return R;(d.status==="dirty"||c.status==="dirty")&&r.dirty(),o.set(d.value,c.value)}return{status:r.value,value:o}})}else{const o=new Map;for(const l of i){const d=l.key,c=l.value;if(d.status==="aborted"||c.status==="aborted")return R;(d.status==="dirty"||c.status==="dirty")&&r.dirty(),o.set(d.value,c.value)}return{status:r.value,value:o}}}}hr.create=(t,e,r)=>new hr({valueType:e,keyType:t,typeName:P.ZodMap,...$(r)});class qe extends V{_parse(e){const{status:r,ctx:n}=this._processInputParams(e);if(n.parsedType!==C.set)return b(n,{code:p.invalid_type,expected:C.set,received:n.parsedType}),R;const a=this._def;a.minSize!==null&&n.data.size<a.minSize.value&&(b(n,{code:p.too_small,minimum:a.minSize.value,type:"set",inclusive:!0,exact:!1,message:a.minSize.message}),r.dirty()),a.maxSize!==null&&n.data.size>a.maxSize.value&&(b(n,{code:p.too_big,maximum:a.maxSize.value,type:"set",inclusive:!0,exact:!1,message:a.maxSize.message}),r.dirty());const s=this._def.valueType;function i(l){const d=new Set;for(const c of l){if(c.status==="aborted")return R;c.status==="dirty"&&r.dirty(),d.add(c.value)}return{status:r.value,value:d}}const o=[...n.data.values()].map((l,d)=>s._parse(new ye(n,l,n.path,d)));return n.common.async?Promise.all(o).then(l=>i(l)):i(o)}min(e,r){return new qe({...this._def,minSize:{value:e,message:O.toString(r)}})}max(e,r){return new qe({...this._def,maxSize:{value:e,message:O.toString(r)}})}size(e,r){return this.min(e,r).max(e,r)}nonempty(e){return this.min(1,e)}}qe.create=(t,e)=>new qe({valueType:t,minSize:null,maxSize:null,typeName:P.ZodSet,...$(e)});class mr extends V{get schema(){return this._def.getter()}_parse(e){const{ctx:r}=this._processInputParams(e);return this._def.getter()._parse({data:r.data,path:r.path,parent:r})}}mr.create=(t,e)=>new mr({getter:t,typeName:P.ZodLazy,...$(e)});class gr extends V{_parse(e){if(e.data!==this._def.value){const r=this._getOrReturnCtx(e);return b(r,{received:r.data,code:p.invalid_literal,expected:this._def.value}),R}return{status:"valid",value:e.data}}get value(){return this._def.value}}gr.create=(t,e)=>new gr({value:t,typeName:P.ZodLiteral,...$(e)});function ln(t,e){return new Re({values:t,typeName:P.ZodEnum,...$(e)})}class Re extends V{_parse(e){if(typeof e.data!="string"){const r=this._getOrReturnCtx(e),n=this._def.values;return b(r,{expected:L.joinValues(n),received:r.parsedType,code:p.invalid_type}),R}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(e.data)){const r=this._getOrReturnCtx(e),n=this._def.values;return b(r,{received:r.data,code:p.invalid_enum_value,options:n}),R}return ae(e.data)}get options(){return this._def.values}get enum(){const e={};for(const r of this._def.values)e[r]=r;return e}get Values(){const e={};for(const r of this._def.values)e[r]=r;return e}get Enum(){const e={};for(const r of this._def.values)e[r]=r;return e}extract(e,r=this._def){return Re.create(e,{...this._def,...r})}exclude(e,r=this._def){return Re.create(this.options.filter(n=>!e.includes(n)),{...this._def,...r})}}Re.create=ln;class vr extends V{_parse(e){const r=L.getValidEnumValues(this._def.values),n=this._getOrReturnCtx(e);if(n.parsedType!==C.string&&n.parsedType!==C.number){const a=L.objectValues(r);return b(n,{expected:L.joinValues(a),received:n.parsedType,code:p.invalid_type}),R}if(this._cache||(this._cache=new Set(L.getValidEnumValues(this._def.values))),!this._cache.has(e.data)){const a=L.objectValues(r);return b(n,{received:n.data,code:p.invalid_enum_value,options:a}),R}return ae(e.data)}get enum(){return this._def.values}}vr.create=(t,e)=>new vr({values:t,typeName:P.ZodNativeEnum,...$(e)});class dt extends V{unwrap(){return this._def.type}_parse(e){const{ctx:r}=this._processInputParams(e);if(r.parsedType!==C.promise&&r.common.async===!1)return b(r,{code:p.invalid_type,expected:C.promise,received:r.parsedType}),R;const n=r.parsedType===C.promise?r.data:Promise.resolve(r.data);return ae(n.then(a=>this._def.type.parseAsync(a,{path:r.path,errorMap:r.common.contextualErrorMap})))}}dt.create=(t,e)=>new dt({type:t,typeName:P.ZodPromise,...$(e)});class Pe extends V{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===P.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:r,ctx:n}=this._processInputParams(e),a=this._def.effect||null,s={addIssue:i=>{b(n,i),i.fatal?r.abort():r.dirty()},get path(){return n.path}};if(s.addIssue=s.addIssue.bind(s),a.type==="preprocess"){const i=a.transform(n.data,s);if(n.common.async)return Promise.resolve(i).then(async o=>{if(r.value==="aborted")return R;const l=await this._def.schema._parseAsync({data:o,path:n.path,parent:n});return l.status==="aborted"?R:l.status==="dirty"||r.value==="dirty"?Le(l.value):l});{if(r.value==="aborted")return R;const o=this._def.schema._parseSync({data:i,path:n.path,parent:n});return o.status==="aborted"?R:o.status==="dirty"||r.value==="dirty"?Le(o.value):o}}if(a.type==="refinement"){const i=o=>{const l=a.refinement(o,s);if(n.common.async)return Promise.resolve(l);if(l instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return o};if(n.common.async===!1){const o=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});return o.status==="aborted"?R:(o.status==="dirty"&&r.dirty(),i(o.value),{status:r.value,value:o.value})}else return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(o=>o.status==="aborted"?R:(o.status==="dirty"&&r.dirty(),i(o.value).then(()=>({status:r.value,value:o.value}))))}if(a.type==="transform")if(n.common.async===!1){const i=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});if(!Ee(i))return R;const o=a.transform(i.value,s);if(o instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:r.value,value:o}}else return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(i=>Ee(i)?Promise.resolve(a.transform(i.value,s)).then(o=>({status:r.value,value:o})):R);L.assertNever(a)}}Pe.create=(t,e,r)=>new Pe({schema:t,typeName:P.ZodEffects,effect:e,...$(r)});Pe.createWithPreprocess=(t,e,r)=>new Pe({schema:e,effect:{type:"preprocess",transform:t},typeName:P.ZodEffects,...$(r)});class ve extends V{_parse(e){return this._getType(e)===C.undefined?ae(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}ve.create=(t,e)=>new ve({innerType:t,typeName:P.ZodOptional,...$(e)});class De extends V{_parse(e){return this._getType(e)===C.null?ae(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}De.create=(t,e)=>new De({innerType:t,typeName:P.ZodNullable,...$(e)});class $t extends V{_parse(e){const{ctx:r}=this._processInputParams(e);let n=r.data;return r.parsedType===C.undefined&&(n=this._def.defaultValue()),this._def.innerType._parse({data:n,path:r.path,parent:r})}removeDefault(){return this._def.innerType}}$t.create=(t,e)=>new $t({innerType:t,typeName:P.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default,...$(e)});class Zt extends V{_parse(e){const{ctx:r}=this._processInputParams(e),n={...r,common:{...r.common,issues:[]}},a=this._def.innerType._parse({data:n.data,path:n.path,parent:{...n}});return it(a)?a.then(s=>({status:"valid",value:s.status==="valid"?s.value:this._def.catchValue({get error(){return new de(n.common.issues)},input:n.data})})):{status:"valid",value:a.status==="valid"?a.value:this._def.catchValue({get error(){return new de(n.common.issues)},input:n.data})}}removeCatch(){return this._def.innerType}}Zt.create=(t,e)=>new Zt({innerType:t,typeName:P.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch,...$(e)});class yr extends V{_parse(e){if(this._getType(e)!==C.nan){const n=this._getOrReturnCtx(e);return b(n,{code:p.invalid_type,expected:C.nan,received:n.parsedType}),R}return{status:"valid",value:e.data}}}yr.create=t=>new yr({typeName:P.ZodNaN,...$(t)});class ms extends V{_parse(e){const{ctx:r}=this._processInputParams(e),n=r.data;return this._def.type._parse({data:n,path:r.path,parent:r})}unwrap(){return this._def.type}}class Ht extends V{_parse(e){const{status:r,ctx:n}=this._processInputParams(e);if(n.common.async)return(async()=>{const s=await this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return s.status==="aborted"?R:s.status==="dirty"?(r.dirty(),Le(s.value)):this._def.out._parseAsync({data:s.value,path:n.path,parent:n})})();{const a=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return a.status==="aborted"?R:a.status==="dirty"?(r.dirty(),{status:"dirty",value:a.value}):this._def.out._parseSync({data:a.value,path:n.path,parent:n})}}static create(e,r){return new Ht({in:e,out:r,typeName:P.ZodPipeline})}}class Vt extends V{_parse(e){const r=this._def.innerType._parse(e),n=a=>(Ee(a)&&(a.value=Object.freeze(a.value)),a);return it(r)?r.then(a=>n(a)):n(r)}unwrap(){return this._def.innerType}}Vt.create=(t,e)=>new Vt({innerType:t,typeName:P.ZodReadonly,...$(e)});var P;(function(t){t.ZodString="ZodString",t.ZodNumber="ZodNumber",t.ZodNaN="ZodNaN",t.ZodBigInt="ZodBigInt",t.ZodBoolean="ZodBoolean",t.ZodDate="ZodDate",t.ZodSymbol="ZodSymbol",t.ZodUndefined="ZodUndefined",t.ZodNull="ZodNull",t.ZodAny="ZodAny",t.ZodUnknown="ZodUnknown",t.ZodNever="ZodNever",t.ZodVoid="ZodVoid",t.ZodArray="ZodArray",t.ZodObject="ZodObject",t.ZodUnion="ZodUnion",t.ZodDiscriminatedUnion="ZodDiscriminatedUnion",t.ZodIntersection="ZodIntersection",t.ZodTuple="ZodTuple",t.ZodRecord="ZodRecord",t.ZodMap="ZodMap",t.ZodSet="ZodSet",t.ZodFunction="ZodFunction",t.ZodLazy="ZodLazy",t.ZodLiteral="ZodLiteral",t.ZodEnum="ZodEnum",t.ZodEffects="ZodEffects",t.ZodNativeEnum="ZodNativeEnum",t.ZodOptional="ZodOptional",t.ZodNullable="ZodNullable",t.ZodDefault="ZodDefault",t.ZodCatch="ZodCatch",t.ZodPromise="ZodPromise",t.ZodBranded="ZodBranded",t.ZodPipeline="ZodPipeline",t.ZodReadonly="ZodReadonly"})(P||(P={}));const ie=ge.create,gs=Ie.create;be.create;oe.create;const vs=X.create;lt.create;ct.create;je.create;Re.create;dt.create;ve.create;De.create;vs({transaction_id:ie(),transaction_date:ie(),expense_status:ie(),payment_status:ie(),merchant:ie(),category:ie(),amount:gs(),currency:ie(),lastEdited:ie(),continent:ie(),country:ie()});const ys=[{value:"live",label:"Live",variant:"success"},{value:"inactive",label:"Inactive",variant:"neutral"},{value:"archived",label:"Archived",variant:"warning"}],bs=[{value:"US-West 1",label:"US-West 1"},{value:"US-West 2",label:"US-West 2"},{value:"US-East 1",label:"US-East 1"},{value:"US-East 2",label:"US-East 2"},{value:"EU-West 1",label:"EU-West 1"},{value:"EU-North 1",label:"EU-North 1"},{value:"EU-Central 1",label:"EU-Central 1"}],xs=[{value:"is-equal-to",label:"is equal to"},{value:"is-between",label:"is between"},{value:"is-greater-than",label:"is greater than"},{value:"is-less-than",label:"is less than"}];function ws(t,e,r,n){var a=this,s=H.useRef(null),i=H.useRef(0),o=H.useRef(0),l=H.useRef(null),d=H.useRef([]),c=H.useRef(),f=H.useRef(),y=H.useRef(t),g=H.useRef(!0);y.current=t;var h=typeof window<"u",j=!e&&e!==0&&h;if(typeof t!="function")throw new TypeError("Expected a function");e=+e||0;var E=!!(r=r||{}).leading,M=!("trailing"in r)||!!r.trailing,N="maxWait"in r,F="debounceOnServer"in r&&!!r.debounceOnServer,D=N?Math.max(+r.maxWait||0,e):null;H.useEffect(function(){return g.current=!0,function(){g.current=!1}},[]);var U=H.useMemo(function(){var m=function(_){var x=d.current,I=c.current;return d.current=c.current=null,i.current=_,o.current=o.current||_,f.current=y.current.apply(I,x)},W=function(_,x){j&&cancelAnimationFrame(l.current),l.current=j?requestAnimationFrame(_):setTimeout(_,x)},G=function(_){if(!g.current)return!1;var x=_-s.current;return!s.current||x>=e||x<0||N&&_-i.current>=D},K=function(_){return l.current=null,M&&d.current?m(_):(d.current=c.current=null,f.current)},q=function _(){var x=Date.now();if(E&&o.current===i.current&&ee(),G(x))return K(x);if(g.current){var I=e-(x-s.current),v=N?Math.min(I,D-(x-i.current)):I;W(_,v)}},ee=function(){},A=function(){if(h||F){var _=Date.now(),x=G(_);if(d.current=[].slice.call(arguments),c.current=a,s.current=_,x){if(!l.current&&g.current)return i.current=s.current,W(q,e),E?m(s.current):f.current;if(N)return W(q,e),m(s.current)}return l.current||W(q,e),f.current}};return A.cancel=function(){l.current&&(j?cancelAnimationFrame(l.current):clearTimeout(l.current)),i.current=0,d.current=s.current=c.current=l.current=null},A.isPending=function(){return!!l.current},A.flush=function(){return l.current?K(Date.now()):f.current},A},[E,N,e,D,M,j,h,F,n]);return U}const _s=({columnFilterLabels:t,className:e})=>t?t.length<3?u.jsx("span",{className:te("truncate",e),children:t.map((r,n)=>u.jsxs("span",{className:te("font-semibold text-indigo-600 dark:text-indigo-400"),children:[r,n<t.length-1&&", "]},r))}):u.jsx(u.Fragment,{children:u.jsxs("span",{className:te("font-semibold text-indigo-600 dark:text-indigo-400",e),children:[t[0]," and ",t.length-1," more"]})}):null;function yt({column:t,title:e,options:r,type:n="select",formatter:a=s=>String(s)}){const s=t?.getFilterValue(),[i,o]=Y.useState(s),l=Y.useMemo(()=>{if(i){if(Array.isArray(i))return i.map(c=>a(c));if(typeof i=="string")return[a(i)];if(typeof i=="object"&&"condition"in i){const c=r?.find(f=>f.value===i.condition)?.label;return c?!i.value?.[0]&&!i.value?.[1]?[`${c}`]:i.value?.[1]?[`${c} ${a(i.value?.[0])} and ${a(i.value?.[1])}`]:[`${c} ${a(i.value?.[0])}`]:void 0}}},[i,r,a]),d=()=>{switch(n){case"select":return u.jsxs(jt,{value:i,onValueChange:c=>{o(c)},children:[u.jsx(Ct,{className:"mt-2 sm:py-1",children:u.jsx(Tt,{placeholder:"Select"})}),u.jsx(Ot,{children:r?.map(c=>u.jsx(At,{value:c.value,children:c.label},c.value))})]});case"checkbox":return u.jsx("div",{className:"mt-2 space-y-2 overflow-y-auto sm:max-h-36",children:r?.map(c=>u.jsxs("div",{className:"flex items-center gap-2",children:[u.jsx(Ur,{id:c.value,checked:i?.includes(c.value),onCheckedChange:f=>{o(y=>f?y?[...y,c.value]:[c.value]:y.filter(g=>g!==c.value))}}),u.jsx(St,{htmlFor:c.value,className:"text-base sm:text-sm",children:c.label})]},c.label))});case"number":{const c=i?.condition==="is-between";return u.jsxs("div",{className:"space-y-2",children:[u.jsxs(jt,{value:i?.condition,onValueChange:f=>{o(y=>({condition:f,value:[f!==""?y?.value?.[0]:"",""]}))},children:[u.jsx(Ct,{className:"mt-2 sm:py-1",children:u.jsx(Tt,{placeholder:"Select condition"})}),u.jsx(Ot,{children:r?.map(f=>u.jsx(At,{value:f.value,children:f.label},f.value))})]}),u.jsxs("div",{className:"flex w-full items-center gap-2",children:[u.jsx(Dn,{className:"size-4 shrink-0 text-gray-500","aria-hidden":"true"}),u.jsx(Yt,{disabled:!i?.condition,type:"number",placeholder:"$0",className:"sm:[&>input]:py-1",value:i?.value?.[0],onChange:f=>{o(y=>({condition:y?.condition,value:[f.target.value,c?y?.value?.[1]:""]}))}}),i?.condition==="is-between"&&u.jsxs(u.Fragment,{children:[u.jsx("span",{className:"text-xs font-medium text-gray-500",children:"and"}),u.jsx(Yt,{disabled:!i?.condition,type:"number",placeholder:"$0",className:"sm:[&>input]:py-1",value:i?.value?.[1],onChange:f=>{o(y=>({condition:y?.condition,value:[y?.value?.[0],f.target.value]}))}})]})]})]})}}};return Y.useEffect(()=>{o(s)},[s]),u.jsxs(Zr,{children:[u.jsx(Vr,{asChild:!0,children:u.jsxs("button",{type:"button",className:te("flex w-full items-center gap-x-1.5 whitespace-nowrap rounded-md border border-gray-300 px-2 py-1.5 font-medium text-gray-600 hover:bg-gray-50 sm:w-fit sm:text-xs dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-900",i&&(typeof i=="object"&&"condition"in i&&i.condition!==""||typeof i=="string"&&i!==""||Array.isArray(i)&&i.length>0)?"":"border-dashed",En),children:[u.jsx("span",{"aria-hidden":"true",onClick:c=>{i&&(c.stopPropagation(),t?.setFilterValue(""),o(""))},children:u.jsx(Fr,{className:te("-ml-px size-5 shrink-0 transition sm:size-4",i&&"rotate-45 hover:text-red-500"),"aria-hidden":"true"})}),l&&l.length>0?u.jsx("span",{children:e}):u.jsx("span",{className:"w-full text-left sm:w-fit",children:e}),l&&l.length>0&&u.jsx("span",{className:"h-4 w-px bg-gray-300 dark:bg-gray-700","aria-hidden":"true"}),u.jsx(_s,{columnFilterLabels:l,className:"w-full text-left sm:w-fit"}),u.jsx(Pn,{className:"size-5 shrink-0 text-gray-500 sm:size-4","aria-hidden":"true"})]})}),u.jsx(Lr,{align:"start",sideOffset:7,className:"min-w-[calc(var(--radix-popover-trigger-width))] max-w-[calc(var(--radix-popover-trigger-width))] sm:min-w-56 sm:max-w-56",onInteractOutside:()=>{(!s||typeof s=="string"&&s===""||Array.isArray(s)&&s.length===0||typeof s=="object"&&"condition"in s&&s.condition==="")&&(t?.setFilterValue(""),o(""))},children:u.jsx("form",{onSubmit:c=>{c.preventDefault(),t?.setFilterValue(i)},children:u.jsxs("div",{className:"space-y-2",children:[u.jsxs("div",{children:[u.jsxs(St,{className:"text-base font-medium sm:text-sm",children:["Filter by ",e]}),d()]}),u.jsx("div",{className:"w-full",children:u.jsx(ce,{type:"submit",className:"w-full sm:py-1",children:"Apply"})}),l&&l.length>0&&u.jsx(ce,{variant:"secondary",className:"w-full sm:py-1",type:"button",onClick:()=>{t?.setFilterValue(""),o(n==="checkbox"?[]:n==="number"?{condition:"",value:["",""]}:"")},children:"Reset"})]})})})]})}var ks={large:700};function Ss(t){t.animate([{backgroundColor:"var(--ds-background-selected, #E9F2FF)"},{}],{duration:ks.large,easing:"cubic-bezier(0.25, 0.1, 0.25, 1.0)",iterations:1})}function Ke(t){"@babel/helpers - typeof";return Ke=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ke(t)}function js(t,e){if(Ke(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(Ke(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Cs(t){var e=js(t,"string");return Ke(e)=="symbol"?e:e+""}function Xe(t,e,r){return(e=Cs(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function br(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable})),r.push.apply(r,n)}return r}function xr(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?br(Object(r),!0).forEach(function(n){Xe(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):br(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}var Ts={top:function(e,r){return Math.abs(r.y-e.top)},right:function(e,r){return Math.abs(e.right-r.x)},bottom:function(e,r){return Math.abs(e.bottom-r.y)},left:function(e,r){return Math.abs(r.x-e.left)}},cn=Symbol("closestEdge");function Os(t,e){var r,n,a=e.element,s=e.input,i=e.allowedEdges,o={x:s.clientX,y:s.clientY},l=a.getBoundingClientRect(),d=i.map(function(f){return{edge:f,value:Ts[f](l,o)}}),c=(r=(n=d.sort(function(f,y){return f.value-y.value})[0])===null||n===void 0?void 0:n.edge)!==null&&r!==void 0?r:null;return xr(xr({},t),{},Xe({},cn,c))}function dn(t){var e;return(e=t[cn])!==null&&e!==void 0?e:null}function As(t){var e=t.startIndex,r=t.closestEdgeOfTarget,n=t.indexOfTarget,a=t.axis;if(e===-1||n===-1||e===n)return e;if(r==null)return n;var s=r==="bottom"||a==="horizontal",i=e<n;return i?s?n:n-1:s?n+1:n}var Ns=1e3,Ue=null,ke="1px",Es={width:ke,height:ke,padding:"0",position:"absolute",border:"0",clip:"rect(".concat(ke,", ").concat(ke,", ").concat(ke,", ").concat(ke,")"),overflow:"hidden",whiteSpace:"nowrap",marginTop:"-".concat(ke),pointerEvents:"none"};function Is(){var t=document.createElement("div");return t.setAttribute("role","status"),Object.assign(t.style,Es),document.body.append(t),t}function wr(){return Ue===null&&(Ue=Is()),Ue}var We=null;function un(){We!==null&&clearTimeout(We),We=null}function Rs(t){wr(),un(),We=setTimeout(function(){We=null;var e=wr();e.textContent=t},Ns)}function Ps(){var t;un(),(t=Ue)===null||t===void 0||t.remove(),Ue=null}const Ds=5;function Ms(t){if(!t.length)return;if(t.length===1&&t[0]&&!t[0].includes(" "))return t[0];const e={};for(const n of t){if(!n)continue;const a=n.split(" ");for(const s of a){const i=s.startsWith("_")?s.slice(0,Ds):s;e[i]=s}}let r="";for(const n in e)r+=e[n]+" ";if(r)return r.trimEnd()}var fn={default:"var(--ds-border-selected, #0C66E4)",warning:"var(--ds-border-warning, #E56910)"},zs="var(--ds-border-width-outline, 2px)",$s={top:"horizontal",bottom:"horizontal",left:"vertical",right:"vertical"},Zs={root:"_1e0c1ule _kqswstnw _1pbykb7n _lcxvglyw _bfhkys7w _rfx31ssb _3l8810ly _kzdanqa1 _15m6ys7w _cfu11ld9 _1kt9b3bt _1cs8stnw _13y0usvi _1mp4vjfa _kfgtvjfa"},Vs={horizontal:"_4t3i10ly _1e02fghn _rjxpidpf _z5wtuj5p",vertical:"_1bsb10ly _154ifghn _94n5idpf _1aukuj5p"},Fs={top:"_154ihv0e _1auk70hn",right:"_1xi2hv0e _ooun70hn",bottom:"_94n5hv0e _19wo70hn",left:"_1ltvhv0e _qnec70hn"},Ls={terminal:function(e){var r=e.indent;return"calc(var(--terminal-radius) + ".concat(r,")")},"terminal-no-bleed":function(e){var r=e.indent;return"calc(var(--terminal-diameter) + ".concat(r,")")},"no-terminal":function(e){var r=e.indent;return r}};function Us(t){var e=t.edge,r=t.gap,n=r===void 0?"0px":r,a=t.indent,s=a===void 0?"0px":a,i=t.strokeColor,o=i===void 0?fn.default:i,l=t.strokeWidth,d=l===void 0?zs:l,c=t.type,f=c===void 0?"terminal":c,y=$s[e];return H.createElement("div",{style:{"--stroke-color":o,"--stroke-width":d,"--main-axis-offset":"calc(-0.5 * (".concat(n," + var(--stroke-width)))"),"--line-main-axis-start":Ls[f]({indent:s}),"--terminal-display":f==="no-terminal"?"none":"block","--terminal-diameter":"calc(var(--stroke-width) * 4)","--terminal-radius":"calc(var(--terminal-diameter) / 2)","--terminal-main-axis-start":"calc(-1 * var(--terminal-diameter))","--terminal-cross-axis-offset":"calc(calc(var(--stroke-width) - var(--terminal-diameter)) / 2)"},className:Ms([Zs.root,Vs[y],Fs[e]])})}function Ws(t){var e=t.appearance,r=e===void 0?"default":e,n=t.edge,a=t.gap,s=t.indent,i=t.type;return Y.createElement(Us,{edge:n,gap:a,strokeColor:fn[r],type:i,indent:s})}function ft(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];return function(){e.forEach(function(a){return a()})}}function Gs(t){if(Array.isArray(t))return t}function Bs(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n,a,s,i,o=[],l=!0,d=!1;try{if(s=(r=r.call(t)).next,e===0){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=s.call(r)).done)&&(o.push(n.value),o.length!==e);l=!0);}catch(c){d=!0,a=c}finally{try{if(!l&&r.return!=null&&(i=r.return(),Object(i)!==i))return}finally{if(d)throw a}}return o}}function Ft(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function pn(t,e){if(t){if(typeof t=="string")return Ft(t,e);var r={}.toString.call(t).slice(8,-1);return r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set"?Array.from(t):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Ft(t,e):void 0}}function Hs(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function qt(t,e){return Gs(t)||Bs(t,e)||pn(t,e)||Hs()}var bt={},Ve={},_r;function hn(){if(_r)return Ve;_r=1,Object.defineProperty(Ve,"__esModule",{value:!0}),Ve.bind=void 0;function t(e,r){var n=r.type,a=r.listener,s=r.options;return e.addEventListener(n,a,s),function(){e.removeEventListener(n,a,s)}}return Ve.bind=t,Ve}var Se={},kr;function qs(){if(kr)return Se;kr=1;var t=Se&&Se.__assign||function(){return t=Object.assign||function(s){for(var i,o=1,l=arguments.length;o<l;o++){i=arguments[o];for(var d in i)Object.prototype.hasOwnProperty.call(i,d)&&(s[d]=i[d])}return s},t.apply(this,arguments)};Object.defineProperty(Se,"__esModule",{value:!0}),Se.bindAll=void 0;var e=hn();function r(s){if(!(typeof s>"u"))return typeof s=="boolean"?{capture:s}:s}function n(s,i){if(i==null)return s;var o=t(t({},s),{options:t(t({},r(i)),r(s.options))});return o}function a(s,i,o){var l=i.map(function(d){var c=n(d,o);return(0,e.bind)(s,c)});return function(){l.forEach(function(c){return c()})}}return Se.bindAll=a,Se}var Sr;function Ks(){return Sr||(Sr=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.bindAll=t.bind=void 0;var e=hn();Object.defineProperty(t,"bind",{enumerable:!0,get:function(){return e.bind}});var r=qs();Object.defineProperty(t,"bindAll",{enumerable:!0,get:function(){return r.bindAll}})}(bt)),bt}var Me=Ks(),mn="data-pdnd-honey-pot";function gn(t){return t instanceof Element&&t.hasAttribute(mn)}function vn(t){var e=document.elementsFromPoint(t.x,t.y),r=qt(e,2),n=r[0],a=r[1];return n?gn(n)?a??null:n:null}var yn=2147483647;function jr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable})),r.push.apply(r,n)}return r}function Cr(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?jr(Object(r),!0).forEach(function(n){Xe(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):jr(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}var Ye=2,Tr=Ye/2;function Ys(t){return{x:Math.floor(t.x),y:Math.floor(t.y)}}function Xs(t){return{x:t.x-Tr,y:t.y-Tr}}function Js(t){return{x:Math.max(t.x,0),y:Math.max(t.y,0)}}function Qs(t){return{x:Math.min(t.x,window.innerWidth-Ye),y:Math.min(t.y,window.innerHeight-Ye)}}function Or(t){var e=t.client,r=Qs(Js(Xs(Ys(e))));return DOMRect.fromRect({x:r.x,y:r.y,width:Ye,height:Ye})}function Ar(t){var e=t.clientRect;return{left:"".concat(e.left,"px"),top:"".concat(e.top,"px"),width:"".concat(e.width,"px"),height:"".concat(e.height,"px")}}function ei(t){var e=t.client,r=t.clientRect;return e.x>=r.x&&e.x<=r.x+r.width&&e.y>=r.y&&e.y<=r.y+r.height}function ti(t){var e=t.initial,r=document.createElement("div");r.setAttribute(mn,"true");var n=Or({client:e});Object.assign(r.style,Cr(Cr({backgroundColor:"transparent",position:"fixed",padding:0,margin:0,boxSizing:"border-box"},Ar({clientRect:n})),{},{pointerEvents:"auto",zIndex:yn})),document.body.appendChild(r);var a=Me.bind(window,{type:"pointermove",listener:function(i){var o={x:i.clientX,y:i.clientY};n=Or({client:o}),Object.assign(r.style,Ar({clientRect:n}))},options:{capture:!0}});return function(i){var o=i.current;if(a(),ei({client:o,clientRect:n})){r.remove();return}function l(){d(),r.remove()}var d=Me.bindAll(window,[{type:"pointerdown",listener:l},{type:"pointermove",listener:l},{type:"focusin",listener:l},{type:"focusout",listener:l},{type:"dragstart",listener:l},{type:"dragenter",listener:l},{type:"dragover",listener:l}],{capture:!0})}}function ri(){var t=null;function e(){return t=null,Me.bind(window,{type:"pointermove",listener:function(a){t={x:a.clientX,y:a.clientY}},options:{capture:!0}})}function r(){var n=null;return function(s){var i=s.eventName,o=s.payload;if(i==="onDragStart"){var l=o.location.initial.input,d=t??{x:l.clientX,y:l.clientY};n=ti({initial:d})}if(i==="onDrop"){var c,f=o.location.current.input;(c=n)===null||c===void 0||c({current:{x:f.clientX,y:f.clientY}}),n=null,t=null}}}return{bindEvents:e,getOnPostDispatch:r}}function ni(t){if(Array.isArray(t))return Ft(t)}function ai(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function si(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function bn(t){return ni(t)||ai(t)||pn(t)||si()}function Ce(t){var e=null;return function(){if(!e){for(var n=arguments.length,a=new Array(n),s=0;s<n;s++)a[s]=arguments[s];var i=t.apply(this,a);e={result:i}}return e.result}}var ii=Ce(function(){return navigator.userAgent.includes("Firefox")}),Je=Ce(function(){var e=navigator,r=e.userAgent;return r.includes("AppleWebKit")&&!r.includes("Chrome")}),Lt={isLeavingWindow:Symbol("leaving"),isEnteringWindow:Symbol("entering")};function oi(t){var e=t.dragLeave;return Je()?e.hasOwnProperty(Lt.isLeavingWindow):!1}(function(){if(typeof window>"u"||!Je())return;function e(){return{enterCount:0,isOverWindow:!1}}var r=e();function n(){r=e()}Me.bindAll(window,[{type:"dragstart",listener:function(){r.enterCount=0,r.isOverWindow=!0}},{type:"drop",listener:n},{type:"dragend",listener:n},{type:"dragenter",listener:function(s){!r.isOverWindow&&r.enterCount===0&&(s[Lt.isEnteringWindow]=!0),r.isOverWindow=!0,r.enterCount++}},{type:"dragleave",listener:function(s){r.enterCount--,r.isOverWindow&&r.enterCount===0&&(s[Lt.isLeavingWindow]=!0,r.isOverWindow=!1)}}],{capture:!0})})();function li(t){return"nodeName"in t}function ci(t){return li(t)&&t.ownerDocument!==document}function di(t){var e=t.dragLeave,r=e.type,n=e.relatedTarget;return r!=="dragleave"?!1:Je()?oi({dragLeave:e}):n==null?!0:ii()?ci(n):n instanceof HTMLIFrameElement}function ui(t){var e=t.onDragEnd;return[{type:"pointermove",listener:function(){var r=0;return function(){if(r<20){r++;return}e()}}()},{type:"pointerdown",listener:e}]}function Ge(t){return{altKey:t.altKey,button:t.button,buttons:t.buttons,ctrlKey:t.ctrlKey,metaKey:t.metaKey,shiftKey:t.shiftKey,clientX:t.clientX,clientY:t.clientY,pageX:t.pageX,pageY:t.pageY}}var fi=function(e){var r=[],n=null,a=function(){for(var i=arguments.length,o=new Array(i),l=0;l<i;l++)o[l]=arguments[l];r=o,!n&&(n=requestAnimationFrame(function(){n=null,e.apply(void 0,r)}))};return a.cancel=function(){n&&(cancelAnimationFrame(n),n=null)},a},xt=fi(function(t){return t()}),nt=function(){var t=null;function e(n){var a=requestAnimationFrame(function(){t=null,n()});t={frameId:a,fn:n}}function r(){t&&(cancelAnimationFrame(t.frameId),t.fn(),t=null)}return{schedule:e,flush:r}}();function pi(t){var e=t.source,r=t.initial,n=t.dispatchEvent,a={dropTargets:[]};function s(o){n(o),a={dropTargets:o.payload.location.current.dropTargets}}var i={start:function(l){var d=l.nativeSetDragImage,c={current:r,previous:a,initial:r};s({eventName:"onGenerateDragPreview",payload:{source:e,location:c,nativeSetDragImage:d}}),nt.schedule(function(){s({eventName:"onDragStart",payload:{source:e,location:c}})})},dragUpdate:function(l){var d=l.current;nt.flush(),xt.cancel(),s({eventName:"onDropTargetChange",payload:{source:e,location:{initial:r,previous:a,current:d}}})},drag:function(l){var d=l.current;xt(function(){nt.flush();var c={initial:r,previous:a,current:d};s({eventName:"onDrag",payload:{source:e,location:c}})})},drop:function(l){var d=l.current,c=l.updatedSourcePayload;nt.flush(),xt.cancel(),s({eventName:"onDrop",payload:{source:c??e,location:{current:d,previous:a,initial:r}}})}};return i}var Ut={isActive:!1};function xn(){return!Ut.isActive}function hi(t){return t.dataTransfer?t.dataTransfer.setDragImage.bind(t.dataTransfer):null}function mi(t){var e=t.current,r=t.next;if(e.length!==r.length)return!0;for(var n=0;n<e.length;n++)if(e[n].element!==r[n].element)return!0;return!1}function gi(t){var e=t.event,r=t.dragType,n=t.getDropTargetsOver,a=t.dispatchEvent;if(!xn())return;var s=vi({event:e,dragType:r,getDropTargetsOver:n});Ut.isActive=!0;var i={current:s};wt({event:e,current:s.dropTargets});var o=pi({source:r.payload,dispatchEvent:a,initial:s});function l(g){var h=mi({current:i.current.dropTargets,next:g.dropTargets});i.current=g,h&&o.dragUpdate({current:i.current})}function d(g){var h=Ge(g),j=gn(g.target)?vn({x:h.clientX,y:h.clientY}):g.target,E=n({target:j,input:h,source:r.payload,current:i.current.dropTargets});E.length&&(g.preventDefault(),wt({event:g,current:E})),l({dropTargets:E,input:h})}function c(){i.current.dropTargets.length&&l({dropTargets:[],input:i.current.input}),o.drop({current:i.current,updatedSourcePayload:null}),f()}function f(){Ut.isActive=!1,y()}var y=Me.bindAll(window,[{type:"dragover",listener:function(h){d(h),o.drag({current:i.current})}},{type:"dragenter",listener:d},{type:"dragleave",listener:function(h){di({dragLeave:h})&&(l({input:i.current.input,dropTargets:[]}),r.startedFrom==="external"&&c())}},{type:"drop",listener:function(h){if(i.current={dropTargets:i.current.dropTargets,input:Ge(h)},!i.current.dropTargets.length){c();return}h.preventDefault(),wt({event:h,current:i.current.dropTargets}),o.drop({current:i.current,updatedSourcePayload:r.type==="external"?r.getDropPayload(h):null}),f()}},{type:"dragend",listener:function(h){i.current={dropTargets:i.current.dropTargets,input:Ge(h)},c()}}].concat(bn(ui({onDragEnd:c}))),{capture:!0});o.start({nativeSetDragImage:hi(e)})}function wt(t){var e,r=t.event,n=t.current,a=(e=n[0])===null||e===void 0?void 0:e.dropEffect;a!=null&&r.dataTransfer&&(r.dataTransfer.dropEffect=a)}function vi(t){var e=t.event,r=t.dragType,n=t.getDropTargetsOver,a=Ge(e);if(r.startedFrom==="external")return{input:a,dropTargets:[]};var s=n({input:a,source:r.payload,target:e.target,current:[]});return{input:a,dropTargets:s}}var Nr={canStart:xn,start:gi},Wt=new Map;function yi(t){var e=t.typeKey,r=t.mount,n=Wt.get(e);if(n)return n.usageCount++,n;var a={typeKey:e,unmount:r(),usageCount:1};return Wt.set(e,a),a}function bi(t){var e=yi(t);return function(){e.usageCount--,!(e.usageCount>0)&&(e.unmount(),Wt.delete(t.typeKey))}}function wn(t,e){var r=e.attribute,n=e.value;return t.setAttribute(r,n),function(){return t.removeAttribute(r)}}function Er(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable})),r.push.apply(r,n)}return r}function he(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Er(Object(r),!0).forEach(function(n){Xe(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Er(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function _t(t,e){var r=typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=xi(t))||e){r&&(t=r);var n=0,a=function(){};return{s:a,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(d){throw d},f:a}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var s,i=!0,o=!1;return{s:function(){r=r.call(t)},n:function(){var d=r.next();return i=d.done,d},e:function(d){o=!0,s=d},f:function(){try{i||r.return==null||r.return()}finally{if(o)throw s}}}}function xi(t,e){if(t){if(typeof t=="string")return Ir(t,e);var r={}.toString.call(t).slice(8,-1);return r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set"?Array.from(t):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Ir(t,e):void 0}}function Ir(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function kt(t){return t.slice(0).reverse()}function wi(t){var e=t.typeKey,r=t.defaultDropEffect,n=new WeakMap,a="data-drop-target-for-".concat(e),s="[".concat(a,"]");function i(g){return n.set(g.element,g),function(){return n.delete(g.element)}}function o(g){var h=ft(wn(g.element,{attribute:a,value:"true"}),i(g));return Ce(h)}function l(g){var h,j,E,M,N=g.source,F=g.target,D=g.input,U=g.result,m=U===void 0?[]:U;if(F==null)return m;if(!(F instanceof Element))return F instanceof Node?l({source:N,target:F.parentElement,input:D,result:m}):m;var W=F.closest(s);if(W==null)return m;var G=n.get(W);if(G==null)return m;var K={input:D,source:N,element:G.element};if(G.canDrop&&!G.canDrop(K))return l({source:N,target:G.element.parentElement,input:D,result:m});var q=(h=(j=G.getData)===null||j===void 0?void 0:j.call(G,K))!==null&&h!==void 0?h:{},ee=(E=(M=G.getDropEffect)===null||M===void 0?void 0:M.call(G,K))!==null&&E!==void 0?E:r,A={data:q,element:G.element,dropEffect:ee,isActiveDueToStickiness:!1};return l({source:N,target:G.element.parentElement,input:D,result:[].concat(bn(m),[A])})}function d(g){var h=g.eventName,j=g.payload,E=_t(j.location.current.dropTargets),M;try{for(E.s();!(M=E.n()).done;){var N,F=M.value,D=n.get(F.element),U=he(he({},j),{},{self:F});D==null||(N=D[h])===null||N===void 0||N.call(D,U)}}catch(m){E.e(m)}finally{E.f()}}var c={onGenerateDragPreview:d,onDrag:d,onDragStart:d,onDrop:d,onDropTargetChange:function(h){var j=h.payload,E=new Set(j.location.current.dropTargets.map(function(w){return w.element})),M=new Set,N=_t(j.location.previous.dropTargets),F;try{for(N.s();!(F=N.n()).done;){var D,U=F.value;M.add(U.element);var m=n.get(U.element),W=E.has(U.element),G=he(he({},j),{},{self:U});if(m==null||(D=m.onDropTargetChange)===null||D===void 0||D.call(m,G),!W){var K;m==null||(K=m.onDragLeave)===null||K===void 0||K.call(m,G)}}}catch(w){N.e(w)}finally{N.f()}var q=_t(j.location.current.dropTargets),ee;try{for(q.s();!(ee=q.n()).done;){var A,_,x=ee.value;if(!M.has(x.element)){var I=he(he({},j),{},{self:x}),v=n.get(x.element);v==null||(A=v.onDropTargetChange)===null||A===void 0||A.call(v,I),v==null||(_=v.onDragEnter)===null||_===void 0||_.call(v,I)}}}catch(w){q.e(w)}finally{q.f()}}};function f(g){c[g.eventName](g)}function y(g){var h=g.source,j=g.target,E=g.input,M=g.current,N=l({source:h,target:j,input:E});if(N.length>=M.length)return N;for(var F=kt(M),D=kt(N),U=[],m=0;m<F.length;m++){var W,G=F[m],K=D[m];if(K!=null){U.push(K);continue}var q=U[m-1],ee=F[m-1];if(q?.element!==ee?.element)break;var A=n.get(G.element);if(!A)break;var _={input:E,source:h,element:A.element};if(A.canDrop&&!A.canDrop(_)||!((W=A.getIsSticky)!==null&&W!==void 0&&W.call(A,_)))break;U.push(he(he({},G),{},{isActiveDueToStickiness:!0}))}return kt(U)}return{dropTargetForConsumers:o,getIsOver:y,dispatchEvent:f}}function _i(t,e){var r=typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=ki(t))||e){r&&(t=r);var n=0,a=function(){};return{s:a,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(d){throw d},f:a}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var s,i=!0,o=!1;return{s:function(){r=r.call(t)},n:function(){var d=r.next();return i=d.done,d},e:function(d){o=!0,s=d},f:function(){try{i||r.return==null||r.return()}finally{if(o)throw s}}}}function ki(t,e){if(t){if(typeof t=="string")return Rr(t,e);var r={}.toString.call(t).slice(8,-1);return r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set"?Array.from(t):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Rr(t,e):void 0}}function Rr(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function Pr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable})),r.push.apply(r,n)}return r}function Si(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Pr(Object(r),!0).forEach(function(n){Xe(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Pr(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function ji(){var t=new Set,e=null;function r(s){e&&(!s.canMonitor||s.canMonitor(e.canMonitorArgs))&&e.active.add(s)}function n(s){var i=Si({},s);t.add(i),r(i);function o(){t.delete(i),e&&e.active.delete(i)}return Ce(o)}function a(s){var i=s.eventName,o=s.payload;if(i==="onGenerateDragPreview"){e={canMonitorArgs:{initial:o.location.initial,source:o.source},active:new Set};var l=_i(t),d;try{for(l.s();!(d=l.n()).done;){var c=d.value;r(c)}}catch(E){l.e(E)}finally{l.f()}}if(e){for(var f=Array.from(e.active),y=0,g=f;y<g.length;y++){var h=g[y];if(e.active.has(h)){var j;(j=h[i])===null||j===void 0||j.call(h,o)}}i==="onDrop"&&(e.active.clear(),e=null)}}return{dispatchEvent:a,monitorForConsumers:n}}function Ci(t){var e=t.typeKey,r=t.mount,n=t.dispatchEventToSource,a=t.onPostDispatch,s=t.defaultDropEffect,i=ji(),o=wi({typeKey:e,defaultDropEffect:s});function l(f){n?.(f),o.dispatchEvent(f),i.dispatchEvent(f),a?.(f)}function d(f){var y=f.event,g=f.dragType;Nr.start({event:y,dragType:g,getDropTargetsOver:o.getIsOver,dispatchEvent:l})}function c(){function f(){var y={canStart:Nr.canStart,start:d};return r(y)}return bi({typeKey:e,mount:f})}return{registerUsage:c,dropTarget:o.dropTargetForConsumers,monitor:i.monitorForConsumers}}var Ti=Ce(function(){return navigator.userAgent.toLocaleLowerCase().includes("android")}),Oi="pdnd:android-fallback",Dr="text/plain",Ai="text/uri-list",Ni="application/vnd.pdnd",ut=new WeakMap;function Ei(t){return ut.set(t.element,t),function(){ut.delete(t.element)}}var Mr=ri(),Kt=Ci({typeKey:"element",defaultDropEffect:"move",mount:function(e){return ft(Mr.bindEvents(),Me.bind(document,{type:"dragstart",listener:function(n){var a,s,i,o,l,d;if(e.canStart(n)&&!n.defaultPrevented&&n.dataTransfer){var c=n.target;if(!(c instanceof HTMLElement))return null;var f=ut.get(c);if(!f)return null;var y=Ge(n),g={element:f.element,dragHandle:(a=f.dragHandle)!==null&&a!==void 0?a:null,input:y};if(f.canDrag&&!f.canDrag(g))return n.preventDefault(),null;if(f.dragHandle){var h=vn({x:y.clientX,y:y.clientY});if(!f.dragHandle.contains(h))return n.preventDefault(),null}var j=(s=(i=f.getInitialDataForExternal)===null||i===void 0?void 0:i.call(f,g))!==null&&s!==void 0?s:null;if(j)for(var E=0,M=Object.entries(j);E<M.length;E++){var N=qt(M[E],2),F=N[0],D=N[1];n.dataTransfer.setData(F,D??"")}Ti()&&!n.dataTransfer.types.includes(Dr)&&!n.dataTransfer.types.includes(Ai)&&n.dataTransfer.setData(Dr,Oi),n.dataTransfer.setData(Ni,"");var U={element:f.element,dragHandle:(o=f.dragHandle)!==null&&o!==void 0?o:null,data:(l=(d=f.getInitialData)===null||d===void 0?void 0:d.call(f,g))!==null&&l!==void 0?l:{}},m={type:"element",payload:U,startedFrom:"internal"};e.start({event:n,dragType:m})}}}))},dispatchEventToSource:function(e){var r,n,a=e.eventName,s=e.payload;(r=ut.get(s.source.element))===null||r===void 0||(n=r[a])===null||n===void 0||n.call(r,s)},onPostDispatch:Mr.getOnPostDispatch()}),Ii=Kt.dropTarget,_n=Kt.monitor;function Ri(t){var e=ft(Kt.registerUsage(),Ei(t),wn(t.element,{attribute:"draggable",value:"true"}));return Ce(e)}var Pi=Ce(function(){return Je()&&"ontouchend"in document});function Di(t){return function(e){var r=e.container;Pi()||Object.assign(r.style,{borderInlineStart:"".concat(t.x," solid transparent"),borderTop:"".concat(t.y," solid transparent")});var n=window.getComputedStyle(r);if(n.direction==="rtl"){var a=r.getBoundingClientRect();return{x:a.width,y:0}}return{x:0,y:0}}}function Mi(){return{x:0,y:0}}function zi(t){var e=t.render,r=t.nativeSetDragImage,n=t.getOffset,a=n===void 0?Mi:n,s=document.createElement("div");Object.assign(s.style,{position:"fixed",top:0,left:0,zIndex:yn,pointerEvents:"none"}),document.body.append(s);var i=e({container:s});queueMicrotask(function(){var d=a({container:s});if(Je()){var c=s.getBoundingClientRect();if(c.width===0)return;s.style.left="-".concat(c.width-1e-4,"px")}r?.(s,d.x,d.y)});function o(){l(),i?.(),document.body.removeChild(s)}var l=_n({onDragStart:o,onDrop:o})}function $i(t){var e=t.list,r=t.startIndex,n=t.finishIndex;if(r===-1||n===-1)return Array.from(e);var a=Array.from(e),s=a.splice(r,1),i=qt(s,1),o=i[0];return a.splice(n,0,o),a}const kn=Y.createContext(null);function Zi(){const t=Y.useContext(kn);return at(t!==null),t}const Sn=Symbol("item");function Vi({item:t,index:e,instanceId:r}){return{[Sn]:!0,item:t,index:e,instanceId:r}}function st(t){return t[Sn]===!0}const zr={type:"idle"},$r={type:"dragging"};function Fi({item:t,index:e,column:r}){const{registerItem:n,instanceId:a}=Zi(),s=Y.useRef(null),[i,o]=Y.useState(null),l=Y.useRef(null),[d,c]=Y.useState(zr);return Y.useEffect(()=>{const f=s.current,y=l.current;at(f),at(y);const g=Vi({item:t,index:e,instanceId:a});return ft(n({itemId:t.id,element:f}),Ri({element:y,getInitialData:()=>g,onGenerateDragPreview({nativeSetDragImage:h}){zi({nativeSetDragImage:h,getOffset:Di({x:"10px",y:"10px"}),render({container:j}){return c({type:"preview",container:j}),()=>c($r)}})},onDragStart(){c($r)},onDrop(){c(zr)}}),Ii({element:f,canDrop({source:h}){return st(h.data)&&h.data.instanceId===a},getData({input:h}){return Os(g,{element:f,input:h,allowedEdges:["top","bottom"]})},onDrag({self:h,source:j}){if(j.element===f){o(null);return}const M=dn(h.data),N=j.data.index;at(typeof N=="number");const F=e===N-1,D=e===N+1;if(F&&M==="bottom"||D&&M==="top"){o(null);return}o(M)},onDragLeave(){o(null)},onDrop(){o(null)}}))},[a,t,e,n]),u.jsxs(Y.Fragment,{children:[u.jsxs("div",{ref:s,className:"relative border-b border-transparent",children:[u.jsxs("div",{className:te("relative flex items-center justify-between gap-2",d.type==="dragging"&&"opacity-50"),children:[u.jsxs("div",{className:"flex items-center gap-2",children:[u.jsx(Ur,{checked:r?.getIsVisible(),onCheckedChange:()=>r?.toggleVisibility()}),u.jsx("span",{children:t.label})]}),u.jsx(ce,{"aria-hidden":"true",tabIndex:-1,variant:"ghost",className:"-mr-1 px-0 py-1",ref:l,"aria-label":`Reorder ${t.label}`,children:u.jsx(zn,{className:"size-5 text-gray-400 dark:text-gray-600"})})]}),i&&u.jsx(Ws,{edge:i,gap:"1px"})]}),d.type==="preview"&&Bn.createPortal(u.jsx("div",{children:t.label}),d.container)]})}function Li(){const t=new Map;function e({itemId:n,element:a}){return t.set(n,a),function(){t.delete(n)}}function r(n){return t.get(n)??null}return{register:e,getElement:r}}function Ui({table:t}){const e=t.getAllColumns().map(c=>({id:c.id,label:c.columnDef.meta?.displayName??c.id})),[{items:r,lastCardMoved:n},a]=Y.useState({items:e,lastCardMoved:null}),[s]=Y.useState(Li),[i]=Y.useState(()=>Symbol("instance-id"));Y.useEffect(()=>{t.setColumnOrder(r.map(c=>c.id))},[r]);const o=Y.useCallback(({startIndex:c,indexOfTarget:f,closestEdgeOfTarget:y})=>{const g=As({startIndex:c,closestEdgeOfTarget:y,indexOfTarget:f,axis:"vertical"});g!==c&&a(h=>{const j=h.items[c];return j?{items:$i({list:h.items,startIndex:c,finishIndex:g}),lastCardMoved:{item:j,previousIndex:c,currentIndex:g,numberOfItems:h.items.length}}:h})},[]);Y.useEffect(()=>_n({canMonitor({source:c}){return st(c.data)&&c.data.instanceId===i},onDrop({location:c,source:f}){const y=c.current.dropTargets[0];if(!y)return;const g=f.data,h=y.data;if(!st(g)||!st(h))return;const j=r.findIndex(M=>M.id===h.item.id);if(j<0)return;const E=dn(h);o({startIndex:g.index,indexOfTarget:j,closestEdgeOfTarget:E})}}),[i,r,o]),Y.useEffect(()=>{if(n===null)return;const{item:c,previousIndex:f,currentIndex:y,numberOfItems:g}=n,h=s.getElement(c.id);h&&Ss(h),Rs(`You've moved ${c.label} from position ${f+1} to position ${y+1} of ${g}.`)},[n,s]),Y.useEffect(()=>function(){Ps()},[]);const l=Y.useCallback(()=>r.length,[r.length]),d=Y.useMemo(()=>({registerItem:s.register,reorderItem:o,instanceId:i,getListLength:l}),[s.register,o,i,l]);return u.jsx("div",{children:u.jsx("div",{className:"flex justify-center",children:u.jsxs(Zr,{children:[u.jsx(Vr,{asChild:!0,children:u.jsxs(ce,{variant:"secondary",className:te("ml-auto hidden gap-x-2 px-2 py-1.5 text-sm sm:text-xs lg:flex"),children:[u.jsx(Mn,{className:"size-4","aria-hidden":"true"}),"View"]})}),u.jsxs(Lr,{align:"end",sideOffset:7,className:"z-50 w-fit space-y-2",children:[u.jsx(St,{className:"font-medium",children:"Display properties"}),u.jsx(kn.Provider,{value:d,children:u.jsx("div",{className:"flex flex-col",children:r.map((c,f)=>{const y=t.getColumn(c.id);return y?u.jsx("div",{className:te(!y.getCanHide()&&"hidden"),children:u.jsx(Fi,{column:y,item:c,index:f})},y.id):null})})})]})]})})})}function Wi({table:t}){const e=t.getState().columnFilters.length>0,[r,n]=H.useState(""),a=ws(i=>{t.getColumn("owner")?.setFilterValue(i)},300),s=i=>{const o=i.target.value;n(o),a(o)};return u.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-2 sm:gap-x-6",children:[u.jsxs("div",{className:"flex w-full flex-col gap-2 sm:w-fit sm:flex-row sm:items-center",children:[t.getColumn("status")?.getIsVisible()&&u.jsx(yt,{column:t.getColumn("status"),title:"Status",options:ys,type:"select"}),t.getColumn("region")?.getIsVisible()&&u.jsx(yt,{column:t.getColumn("region"),title:"Region",options:bs,type:"checkbox"}),t.getColumn("costs")?.getIsVisible()&&u.jsx(yt,{column:t.getColumn("costs"),title:"Costs",type:"number",options:xs,formatter:i=>In.currency({number:Number(i)})}),t.getColumn("owner")?.getIsVisible()&&u.jsx(an,{type:"search",placeholder:"Search by owner...",value:r,onChange:s,className:"w-full sm:max-w-[250px] sm:[&>input]:h-[30px]"}),e&&u.jsx(ce,{variant:"ghost",onClick:()=>t.resetColumnFilters(),className:"border border-gray-200 px-2 font-semibold text-indigo-600 sm:border-none sm:py-1 dark:border-gray-800 dark:text-indigo-500",children:"Clear filters"})]}),u.jsxs("div",{className:"flex items-center gap-2",children:[u.jsxs(ce,{variant:"secondary",className:"hidden gap-x-2 px-2 py-1.5 text-sm sm:text-xs lg:flex",children:[u.jsx($n,{className:"size-4 shrink-0","aria-hidden":"true"}),"Export"]}),u.jsx(Ui,{table:t})]})]})}function Gi({table:t,pageSize:e,totalCount:r,onPageSizeChange:n}){const a=[{icon:Zn,onClick:()=>t.setPageIndex(0),disabled:!t.getCanPreviousPage(),srText:"First page",mobileView:"hidden sm:block"},{icon:Vn,onClick:()=>t.previousPage(),disabled:!t.getCanPreviousPage(),srText:"Previous page",mobileView:""},{icon:Fn,onClick:()=>t.nextPage(),disabled:!t.getCanNextPage(),srText:"Next page",mobileView:""},{icon:Ln,onClick:()=>t.setPageIndex(t.getPageCount()-1),disabled:!t.getCanNextPage(),srText:"Last page",mobileView:"hidden sm:block"}],s=r??t.getFilteredRowModel().rows.length,o=t.getState().pagination.pageIndex*e+1,l=Math.min(s,o+e-1),d=[10,25,50,100,"All"],c=f=>{const y=f==="All"?s:parseInt(f,10);t.setPageSize(y),n&&n(y)};return u.jsxs("div",{className:"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4",children:[u.jsxs("div",{className:"flex items-center gap-2",children:[u.jsx("span",{className:"text-sm text-gray-500",children:"Items per page:"}),u.jsxs(jt,{value:e.toString(),onValueChange:c,children:[u.jsx(Ct,{className:"h-8 w-[80px]",children:u.jsx(Tt,{placeholder:e.toString()})}),u.jsx(Ot,{children:d.map(f=>u.jsx(At,{value:f.toString(),children:f},f))})]})]}),u.jsxs("div",{className:"flex items-center justify-between w-full sm:w-auto",children:[u.jsxs("div",{className:"text-sm tabular-nums text-gray-500",children:[t.getFilteredSelectedRowModel().rows.length," of ",s," row(s) selected."]}),u.jsxs("div",{className:"flex items-center gap-x-6 lg:gap-x-8",children:[u.jsxs("p",{className:"hidden text-sm tabular-nums text-gray-500 sm:block",children:["Showing"," ",u.jsxs("span",{className:"font-medium text-gray-900 dark:text-gray-50",children:[o,"-",l]})," ","of"," ",u.jsx("span",{className:"font-medium text-gray-900 dark:text-gray-50",children:s})]}),u.jsx("div",{className:"flex items-center gap-x-1.5",children:a.map((f,y)=>u.jsxs(ce,{variant:"secondary",className:te(f.mobileView,"p-1.5"),onClick:()=>{f.onClick(),t.resetRowSelection()},disabled:f.disabled,children:[u.jsx("span",{className:"sr-only",children:f.srText}),u.jsx(f.icon,{className:"size-4 shrink-0","aria-hidden":"true"})]},y))})]})]})]})}function to({columns:t,data:e,totalCount:r,isLoading:n,manualPagination:a=!1,manualSorting:s=!1,pageSize:i=20,onPaginationChange:o,onSortingChange:l,sortingState:d,onSearch:c,searchValue:f="",customFilterbar:y,hideDefaultFilterbar:g=!1,onRefresh:h,title:j,actionButton:E,enableRowSelection:M=!0}){const[N,F]=H.useState({}),[D,U]=H.useState({pageIndex:0,pageSize:i}),[m,W]=H.useState(d??[]),[G,K]=H.useState(!1);H.useEffect(()=>{d&&W(d)},[d]);const q=H.useCallback(w=>{const T=typeof w=="function"?w(D):w;U(T),o&&o(T)},[o,D]),ee=H.useCallback(w=>{const T=typeof w=="function"?w(m):w;W(T),l&&l(T)},[l,m]),A=H.useCallback(w=>{const T={pageIndex:0,pageSize:w};U(T),o&&o(T)},[o]),_=H.useCallback(()=>{h&&!n&&!G&&(K(!0),h(),setTimeout(()=>{K(!1)},1e3))},[h,n,G]),x=H.useMemo(()=>M?t:t.filter(w=>w.id!=="select"),[t,M]),I=H.useMemo(()=>x.map(w=>w.enableSorting===!1?w:{...w,header:T=>{const z=T.column,B=z.getIsSorted(),se=typeof w.header=="string"?w.header:w.header?ht(w.header,T):null,J=u.jsxs(u.Fragment,{children:[se,B&&u.jsx("span",{className:"inline-flex items-center",children:B==="asc"?u.jsx(Wn,{className:"w-3.5 h-3.5"}):u.jsx(Gn,{className:"w-3.5 h-3.5"})})]});return w.enableSorting!==!1?u.jsx("button",{type:"button",onClick:()=>z.toggleSorting(),className:te("inline-flex items-center gap-1 hover:text-primary",B?"text-primary":""),children:J}):J}}),[x]),v=qn({data:e,columns:I,state:{rowSelection:N,pagination:D,sorting:m},pageCount:r?Math.ceil(r/D.pageSize):-1,enableRowSelection:M,enableSorting:!0,manualSorting:s,getFilteredRowModel:Jn(),getPaginationRowModel:Xn(),getSortedRowModel:Yn(),onRowSelectionChange:F,onSortingChange:ee,getCoreRowModel:Kn(),onPaginationChange:q,manualPagination:a});return u.jsx(u.Fragment,{children:u.jsxs("div",{className:"space-y-3",children:[j&&u.jsxs("div",{className:"flex items-center justify-between mb-4",children:[u.jsx("h2",{className:"text-xl font-semibold",children:j}),E?.content]}),u.jsxs("div",{className:"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2",children:[u.jsx("div",{className:"flex-1 w-full",children:y?u.jsx(y,{table:v,onSearch:c,searchValue:f}):u.jsxs(u.Fragment,{children:[c&&u.jsx(Hn,{onUpdate:c,value:f}),!g&&u.jsx(Wi,{table:v})]})}),u.jsxs("div",{className:"flex items-center gap-2",children:[!j&&E&&u.jsxs(ce,{variant:"primary",size:"sm",className:"flex items-center gap-1 px-3 py-2",onClick:E.onClick,children:[E.content??u.jsx(Fr,{className:"h-4 w-4"}),u.jsx("span",{children:E.label})]}),h&&u.jsxs(ce,{variant:"secondary",size:"sm",className:"flex items-center gap-1",onClick:_,disabled:n??G,children:[u.jsx(Un,{className:te("h-4 w-4",G&&"animate-spin")}),u.jsx("span",{className:"hidden sm:inline",children:"Refresh"})]})]})]}),u.jsx("div",{className:"relative overflow-hidden overflow-x-auto",children:u.jsxs(jn,{children:[u.jsx(Cn,{children:v.getHeaderGroups().map(w=>u.jsx(Qe,{className:"border-y border-gray-200 dark:border-gray-800",children:w.headers.map(T=>u.jsx(Tn,{className:te("whitespace-nowrap py-1 text-sm sm:text-xs",T.column.columnDef.meta?.className),children:T.isPlaceholder?null:ht(T.column.columnDef.header,T.getContext())},T.id))},w.id))}),u.jsx(On,{children:n?u.jsx(Qe,{children:u.jsx(pt,{colSpan:x.length,className:"h-24 text-center",children:"Loading..."})}):v.getRowModel().rows?.length?v.getRowModel().rows.map(w=>u.jsx(Qe,{onClick:()=>M&&w.toggleSelected(!w.getIsSelected()),className:te("group",M?"select-none hover:bg-gray-50 dark:hover:bg-gray-900":""),children:w.getVisibleCells().map((T,z)=>u.jsxs(pt,{className:te(w.getIsSelected()?"bg-gray-50 dark:bg-gray-900":"","relative whitespace-nowrap py-1 text-gray-600 first:w-10 dark:text-gray-400",T.column.columnDef.meta?.className),children:[z===0&&w.getIsSelected()&&u.jsx("div",{className:"absolute inset-y-0 left-0 w-0.5 bg-indigo-600 dark:bg-indigo-500"}),ht(T.column.columnDef.cell,T.getContext())]},T.id))},w.id)):u.jsx(Qe,{children:u.jsx(pt,{colSpan:x.length,className:"h-24 text-center",children:"No results."})})})]})}),u.jsx(Gi,{table:v,pageSize:D.pageSize,totalCount:r,onPageSizeChange:A})]})})}export{to as D,Ui as V};
//# sourceMappingURL=DataTable-BL4SJdnU.js.map
