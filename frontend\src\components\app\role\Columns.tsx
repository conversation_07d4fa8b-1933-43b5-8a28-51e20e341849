'use client'

import { type IdentityRoleDto, type IdentityUserUpdateDto } from '@/client'
import { type ColumnDef } from '@tanstack/react-table'
import { Actions } from './Actions'
import { Checkbox } from '@/components/ui/checkbox'
import { DataTableColumnHeader } from '@/components/data-table/DataTableColumnHeader'
import { YesNoBadge } from '@/components/ui/YesNoBadge'

// Type for the callback function to handle user actions
type UserActionCallback = (userId: string, userDto: IdentityUserUpdateDto, dialogType: 'edit' | 'permission' | 'delete') => void

// Function to create columns with the action callback
export const getColumns = (
  handleUserAction: UserActionCallback
): ColumnDef<IdentityRoleDto>[] => {
  return [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected()
              ? true
              : table.getIsSomeRowsSelected()
                ? "indeterminate"
                : false
          }
          onCheckedChange={() => table.toggleAllPageRowsSelected()}
          className="translate-y-0.5"
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={() => row.toggleSelected()}
          className="translate-y-0.5"
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: true,
      meta: {
        displayName: "Select",
      },
    },
    {
      accessorKey: "name",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Name" />
      ),
      enableSorting: true,
      enableHiding: true,
      cell: (info) => info.getValue(),
      meta: {
        className: "text-left",
        displayName: "Name",
      },
    },
    {
      accessorKey: "isDefault",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Is Default" />
      ),
      enableSorting: true,
      enableHiding: true,
      cell: (info) => <YesNoBadge value={info.getValue() as boolean} />,
      meta: {
        className: "text-left",
        displayName: "Is Default",
      },
    },
    {
      accessorKey: "isPublic",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Is Public" />
      ),
      enableSorting: true,
      enableHiding: true,
      cell: (info) => <YesNoBadge value={info.getValue() as boolean} />,
      meta: {
        className: "text-left",
        displayName: "Is Public",
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: (info) => (
        <Actions
          userId={info.row.original.id!}
          userDto={info.row.original as unknown as IdentityUserUpdateDto}
          onAction={handleUserAction}
          variant="dropdown"
        />
      ),
      enableSorting: false,
      enableHiding: true,
      meta: {
        className: "text-right",
        displayName: "Action",
      },
    }
  ];
}
