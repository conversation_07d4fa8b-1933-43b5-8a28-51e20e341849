{"version": 3, "file": "TogglePermission-LBMxXwtr.js", "sources": ["../../../../../frontend/src/lib/hooks/usePermissions.ts", "../../../../../frontend/src/components/app/permission/PermissionToggle.tsx", "../../../../../frontend/src/components/app/permission/usePermissionChanges.ts", "../../../../../frontend/src/components/app/permission/TogglePermission.tsx"], "sourcesContent": ["import { type GetPermissionListResultDto, getApiPermissionManagementPermissions } from '@/client'\r\nimport { type UseQueryResult, useQuery } from '@tanstack/react-query'\r\nimport { QueryNames } from './QueryConstants'\r\n\r\n/**\r\n * Custom hook to fetch permissions based on provider name and provider key.\r\n *\r\n * @param providerName - The name of the provider.\r\n * @param providerKey - The key of the provider.\r\n * @returns A `UseQueryResult` containing the permission list result.\r\n */\r\nexport const usePermissions = (\r\n  providerName: string | undefined,\r\n  providerKey: string | undefined\r\n): UseQueryResult<GetPermissionListResultDto, unknown> => {\r\n  return useQuery({\r\n    queryKey: [QueryNames.GetPermissions, providerName, providerKey],\r\n    queryFn: async () => {\r\n      const { data } = await getApiPermissionManagementPermissions({\r\n        query: { providerName, providerKey },\r\n      })\r\n      return data!\r\n    },\r\n  })\r\n}\r\n", "import clsx from 'clsx'\r\nimport { memo, useCallback } from 'react'\r\nimport { Checkbox } from '@/components/ui/checkbox'\r\n\r\nexport type Management = 'identity' | 'tenant' | 'setting' | 'feature'\r\nexport type PermissionTracker = {\r\n  name: string\r\n  isGranted: boolean\r\n}\r\n\r\ntype PermissionProps = {\r\n  name: string\r\n  id: string\r\n  isGranted: boolean\r\n  onUpdate?: () => void\r\n  className?: string\r\n  disabled: boolean\r\n}\r\n\r\nfunction PermissionToggle({ name, id, onUpdate, className, isGranted, disabled = false }: PermissionProps) {\r\n  const onChangeEvent = useCallback(() => {\r\n    onUpdate?.()\r\n  }, [onUpdate])\r\n\r\n  return (\r\n    <div className={clsx('flex items-center space-x-2 pb-2', className)}>\r\n      <Checkbox id={id} onCheckedChange={onChangeEvent} checked={isGranted} disabled={disabled} />\r\n      <label htmlFor={id} className=\"text-sm font-medium leading-none\">\r\n        {name}\r\n      </label>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport const Permission = memo(PermissionToggle)\r\n", "import { type PermissionGrantInfoDto } from '@/client'\r\nimport { Permissions } from '@/lib/utils'\r\nimport { useCallback, useEffect, useState } from 'react'\r\n\r\n/**\r\n * Hook to manage permission changes for different types of entities.\r\n *\r\n * @param {UsePermissionsChangesProps} props - The properties for the hook.\r\n * @returns {Object} - An object containing the state and handlers for permission changes.\r\n */\r\nexport type UsePermissionsChangesProps = {\r\n  permissions: PermissionGrantInfoDto[]\r\n  type: 'identity' | 'tenant' | 'feature' | 'setting' | 'identityServer'\r\n}\r\n\r\n/**\r\n * Helper function to update the permission data based on the selected permission.\r\n *\r\n * @param {PermissionGrantInfoDto[]} data - The current permission data.\r\n * @param {PermissionGrantInfoDto} selectedData - The selected permission data.\r\n * @param {string} permission - The permission to be updated.\r\n * @param {Function} setData - The function to update the permission data state.\r\n */\r\nconst helper = (\r\n  data: PermissionGrantInfoDto[],\r\n  selectedData: PermissionGrantInfoDto,\r\n  permission: string,\r\n  setData: (data: PermissionGrantInfoDto[]) => void\r\n) => {\r\n  const parent = data.find((f) => !f.parentName && f.name === permission)\r\n  const children = data.filter((f) => f.parentName === permission)\r\n\r\n  if (selectedData.parentName === permission && parent) {\r\n    if (selectedData.isGranted) {\r\n      selectedData.isGranted = false\r\n      parent.isGranted = false\r\n    } else {\r\n      selectedData.isGranted = true\r\n    }\r\n    // If all the children got granted then updated the parent as well.\r\n    if (!parent.isGranted) {\r\n      const hasChildrenSelected = children.every((c) => c.isGranted)\r\n      if (hasChildrenSelected) {\r\n        parent.isGranted = true\r\n      }\r\n    }\r\n    setData([...data])\r\n    return false\r\n  }\r\n\r\n  if (!selectedData.parentName && selectedData.name === permission) {\r\n    if (parent?.isGranted) {\r\n      parent.isGranted = false\r\n      children.forEach((c) => (c.isGranted = false))\r\n    } else if (parent && !parent.isGranted) {\r\n      parent.isGranted = true\r\n      children.forEach((c) => (c.isGranted = true))\r\n    }\r\n    setData([...data])\r\n  }\r\n}\r\n\r\n// Helper function to detect permission type from permissions array\r\nconst detectPermissionType = (permissions: PermissionGrantInfoDto[]): string => {\r\n  // Check if any permission is related to IdentityProvider\r\n  if (permissions.some((p) => p.name?.startsWith('IdentityServer.'))) {\r\n    return 'identityServer'\r\n  }\r\n  // Check for other permission types\r\n  if (permissions.some((p) => p.name?.startsWith('AbpIdentity.'))) {\r\n    return 'identity'\r\n  }\r\n  if (permissions.some((p) => p.name?.startsWith('AbpTenantManagement.'))) {\r\n    return 'tenant'\r\n  }\r\n  if (permissions.some((p) => p.name?.startsWith('FeatureManagement.'))) {\r\n    return 'feature'\r\n  }\r\n  if (permissions.some((p) => p.name?.startsWith('SettingManagement.'))) {\r\n    return 'setting'\r\n  }\r\n  // if (permissions.some((p) => p.name?.startsWith('WismaApp.'))) {\r\n  //   return 'wismaApp'\r\n  // }\r\n  return ''\r\n}\r\n\r\n// Normalize permission type string to handle various format inputs\r\nconst normalizePermissionType = (type: string): string => {\r\n  if (!type) return ''\r\n\r\n  // Convert to lowercase and remove spaces\r\n  const normalized = type.toLowerCase().replace(/\\s/g, '')\r\n\r\n  // Handle special cases\r\n  if (normalized.includes('identityserver') || normalized.includes('permission:identityserver')) {\r\n    return 'identityserver'\r\n  }\r\n  if (normalized.includes('identity') && !normalized.includes('identityserver')) {\r\n    return 'identity'\r\n  }\r\n  if (normalized.includes('tenant')) {\r\n    return 'tenant'\r\n  }\r\n  // if (normalized.includes('wismaApp')) {\r\n  //   return 'wismaApp'\r\n  // }\r\n  if (normalized.includes('feature')) {\r\n    return 'feature'\r\n  }\r\n  if (normalized.includes('setting')) {\r\n    return 'setting'\r\n  }\r\n\r\n  return normalized\r\n}\r\n\r\nexport const usePermissionsChanges = ({\r\n  permissions,\r\n  type: providedType,\r\n}: UsePermissionsChangesProps) => {\r\n  // Flag determine to enable/disable the selected permissions to a user.\r\n  const [hasAllSelected, setHasAllSelected] = useState(false)\r\n  const [data, setData] = useState<PermissionGrantInfoDto[]>(permissions)\r\n\r\n  // Auto-detect type if needed and normalize\r\n  const detectedType = detectPermissionType(permissions)\r\n  const normalizedType = normalizePermissionType(providedType || detectedType)\r\n\r\n  /**\r\n   * Handler for changes in the current permission.\r\n   *\r\n   * @param {number} idx - The index of the selected permission.\r\n   */\r\n  const onCurrentPermissionChanges = useCallback(\r\n    (idx: number) => {\r\n      const selectedData = data[idx]\r\n\r\n      // If selectedData is undefined, don't proceed\r\n      if (!selectedData) return;\r\n\r\n      // wait for all the events to get done, then check.\r\n      setTimeout(() => {\r\n        const allSelected = data.every((d) => d.isGranted)\r\n        setHasAllSelected(allSelected)\r\n      }, 0)\r\n\r\n      // Check which type we're dealing with based on the normalized type\r\n      if (normalizedType === 'identity') {\r\n        helper(data, selectedData, Permissions.ROLES, setData)\r\n        helper(data, selectedData, Permissions.USERS, setData)\r\n      } else if (normalizedType === 'tenant') {\r\n        helper(data, selectedData, Permissions.TENANTS, setData)\r\n      } else if (normalizedType === 'feature') {\r\n        helper(data, selectedData, Permissions.MANAGE_HOST_FEATURES, setData)\r\n      } else if (normalizedType === 'setting') {\r\n        helper(data, selectedData, Permissions.SETTINGS, setData)\r\n      } else if (normalizedType === 'identityprovider') {\r\n        helper(data, selectedData, Permissions.IDENTITY_PROVIDER_CLAIMS, setData)\r\n        helper(data, selectedData, Permissions.IDENTITY_PROVIDER_CLAIM_TYPES, setData)\r\n        helper(data, selectedData, Permissions.IDENTITY_PROVIDER_OPENIDDICT_APPLICATIONS, setData)\r\n        helper(data, selectedData, Permissions.IDENTITY_PROVIDER_OPENIDDICT_SCOPES, setData)\r\n        helper(data, selectedData, Permissions.IDENTITY_PROVIDER_OPENIDDICT_RESOURCES, setData)\r\n      } else {\r\n        // Toggle the permission directly as a fallback\r\n        console.warn('Unknown permission type:', providedType)\r\n        selectedData.isGranted = !selectedData.isGranted\r\n        setData([...data])\r\n      }\r\n    },\r\n    // Only depend on data and normalizedType, not permissions\r\n    [data, normalizedType]\r\n  )\r\n\r\n  /**\r\n   * Handler to update the state when all permissions are selected or deselected.\r\n   */\r\n  const onHasAllSelectedUpdate = useCallback(() => {\r\n    setHasAllSelected((prevAllSelected) => {\r\n      const newState = !prevAllSelected\r\n\r\n      // First update root permissions\r\n      const rootPermissions = data.filter((p) => !p.parentName)\r\n      rootPermissions.forEach((root) => {\r\n        root.isGranted = newState\r\n\r\n        // Then update all children of this root\r\n        const children = data.filter((p) => p.parentName === root.name)\r\n        children.forEach((child) => {\r\n          child.isGranted = newState\r\n        })\r\n      })\r\n\r\n      // Update any permissions that might not follow parent-child pattern\r\n      data.forEach((permission) => {\r\n        if (!permission.parentName && !rootPermissions.includes(permission)) {\r\n          permission.isGranted = newState\r\n        }\r\n      })\r\n\r\n      // Update the local state with the new data\r\n      setData([...data])\r\n\r\n      return newState\r\n    })\r\n  }, [data])\r\n\r\n  // Initialize data when permissions change\r\n  useEffect(() => {\r\n    setData(permissions)\r\n  }, [permissions])\r\n\r\n  // Update hasAllSelected when data changes\r\n  useEffect(() => {\r\n    setHasAllSelected(data.every((d) => d.isGranted))\r\n  }, [data])\r\n\r\n  return {\r\n    hasAllSelected,\r\n    data,\r\n    onCurrentPermissionChanges,\r\n    onHasAllSelectedUpdate,\r\n  }\r\n}", "import { type PermissionGrantInfoDto } from '@/client'\r\nimport { Permission } from './PermissionToggle'\r\n\r\nimport clsx from 'clsx'\r\nimport * as React from 'react'\r\nimport { useEffect, useRef } from 'react'\r\nimport { Button } from '@/components/ui/button'\r\nimport { DialogFooter } from '@/components/ui/dialog'\r\nimport { usePermissionsChanges, type UsePermissionsChangesProps } from './usePermissionChanges'\r\n\r\ntype TogglePermissionProps = UsePermissionsChangesProps & {\r\n  hideSelectAll?: boolean\r\n  hideSave?: boolean\r\n  onSelectedUpdate?: (permissionDto: PermissionGrantInfoDto) => void\r\n  disabled?: boolean\r\n  onCancelEvent?: () => void\r\n}\r\n/**\r\n * TogglePermission component allows toggling permissions with optional \"Select All\" and \"Save\" functionalities.\r\n *\r\n * @param {TogglePermissionProps} props - The properties for the TogglePermission component.\r\n * @param {PermissionGrantInfoDto[]} props.permissions - The list of permissions to be displayed and toggled.\r\n * @param {string} props.type - The type of permissions being handled.\r\n * @param {boolean} [props.hideSelectAll] - Flag to hide the \"Select All\" option.\r\n * @param {boolean} [props.hideSave] - Flag to hide the \"Save\" button.\r\n * @param {Function} [props.onSelectedUpdate] - Callback function to handle updates when a permission is selected.\r\n * @param {boolean} [props.disabled] - Flag to disable the permission toggles.\r\n * @param {Function} [props.onCancelEvent] - Callback function to handle the cancel event.\r\n *\r\n * @returns {React.ReactElement} The rendered TogglePermission component.\r\n */\r\nexport const TogglePermission = React.memo(function TogglePermission({\r\n  permissions,\r\n  type,\r\n  hideSelectAll,\r\n  hideSave,\r\n  onSelectedUpdate,\r\n  disabled,\r\n  onCancelEvent,\r\n}: TogglePermissionProps) {\r\n  // Use a stable reference to permissions to prevent unnecessary re-renders\r\n  const permissionsRef = useRef(permissions);\r\n\r\n  // Only update the reference if the permissions array changes significantly\r\n  useEffect(() => {\r\n    if (permissions.length !== permissionsRef.current.length) {\r\n      permissionsRef.current = permissions;\r\n    }\r\n  }, [permissions]);\r\n\r\n  const { hasAllSelected, onCurrentPermissionChanges, data, onHasAllSelectedUpdate } =\r\n    usePermissionsChanges({ permissions: permissionsRef.current, type })\r\n\r\n  // No need for this effect as usePermissionsChanges already handles this logic\r\n\r\n  return (\r\n    <>\r\n      {!hideSelectAll && (\r\n        <Permission\r\n          name=\"Select All\"\r\n          isGranted={hasAllSelected}\r\n          disabled={disabled ?? false}\r\n          id=\"select_all\"\r\n          onUpdate={onHasAllSelectedUpdate}\r\n        />\r\n      )}\r\n      {data?.map((dto: PermissionGrantInfoDto, idx) => (\r\n        <div key={idx}>\r\n          <Permission\r\n            name={dto.displayName!}\r\n            isGranted={dto.isGranted!}\r\n            id={dto.displayName!.toLocaleLowerCase().concat(dto.parentName!)}\r\n            onUpdate={() => {\r\n              onCurrentPermissionChanges(idx)\r\n              if (onSelectedUpdate) {\r\n                onSelectedUpdate(dto)\r\n              }\r\n            }}\r\n            className={clsx('ml-5', {\r\n              'pl-5': dto.parentName,\r\n            })}\r\n            disabled={disabled ?? false}\r\n          />\r\n        </div>\r\n      ))}\r\n      {!hideSave && (\r\n        <DialogFooter>\r\n          {onCancelEvent && (\r\n            <Button\r\n              onClick={(e: { preventDefault: () => void }) => {\r\n                e.preventDefault()\r\n                onCancelEvent()\r\n              }}\r\n            >\r\n              Cancel\r\n            </Button>\r\n          )}\r\n\r\n          <Button type=\"submit\">Save</Button>\r\n        </DialogFooter>\r\n      )}\r\n    </>\r\n  )\r\n})\r\n"], "names": ["usePermissions", "providerName", "providerKey", "useQuery", "QueryNames", "data", "getApiPermissionManagementPermissions", "PermissionToggle", "name", "id", "onUpdate", "className", "isGranted", "disabled", "onChangeEvent", "useCallback", "clsx", "jsx", "Checkbox", "Permission", "memo", "helper", "selectedData", "permission", "setData", "parent", "f", "children", "c", "detectPermissionType", "permissions", "p", "normalizePermissionType", "type", "normalized", "usePermissionsChanges", "providedType", "hasAllSelected", "setHasAllSelected", "useState", "detectedType", "normalizedType", "onCurrentPermissionChanges", "idx", "allSelected", "d", "Permissions", "onHasAllSelectedUpdate", "prevAllSelected", "newState", "rootPermissions", "root", "child", "useEffect", "TogglePermission", "React.memo", "hideSelectAll", "hideSave", "onSelectedUpdate", "onCancelEvent", "permissionsRef", "useRef", "jsxs", "Fragment", "dto", "<PERSON><PERSON><PERSON><PERSON>er", "<PERSON><PERSON>", "e"], "mappings": "sNAWa,MAAAA,EAAiB,CAC5BC,EACAC,IAEOC,EAAS,CACd,SAAU,CAACC,EAAW,eAAgBH,EAAcC,CAAW,EAC/D,QAAS,SAAY,CACnB,KAAM,CAAE,KAAAG,GAAS,MAAMC,EAAsC,CAC3D,MAAO,CAAE,aAAAL,EAAc,YAAAC,CAAY,CAAA,CACpC,EACM,OAAAG,CAAA,CACT,CACD,ECJH,SAASE,EAAiB,CAAE,KAAAC,EAAM,GAAAC,EAAI,SAAAC,EAAU,UAAAC,EAAW,UAAAC,EAAW,SAAAC,EAAW,IAA0B,CACnG,MAAAC,EAAgBC,EAAAA,YAAY,IAAM,CAC3BL,IAAA,CAAA,EACV,CAACA,CAAQ,CAAC,EAEb,cACG,MAAI,CAAA,UAAWM,EAAK,mCAAoCL,CAAS,EAChE,SAAA,CAAAM,MAACC,GAAS,GAAAT,EAAQ,gBAAiBK,EAAe,QAASF,EAAW,SAAAC,EAAoB,QACzF,QAAM,CAAA,QAASJ,EAAI,UAAU,mCAC3B,SACHD,CAAA,CAAA,CAAA,EACF,CAEJ,CAEa,MAAAW,EAAaC,OAAKb,CAAgB,ECXzCc,EAAS,CACbhB,EACAiB,EACAC,EACAC,IACG,CACG,MAAAC,EAASpB,EAAK,KAAMqB,GAAM,CAACA,EAAE,YAAcA,EAAE,OAASH,CAAU,EAChEI,EAAWtB,EAAK,OAAQqB,GAAMA,EAAE,aAAeH,CAAU,EAE3D,GAAAD,EAAa,aAAeC,GAAcE,EAC5C,OAAIH,EAAa,WACfA,EAAa,UAAY,GACzBG,EAAO,UAAY,IAEnBH,EAAa,UAAY,GAGtBG,EAAO,WACkBE,EAAS,MAAO,GAAM,EAAE,SAAS,IAE3DF,EAAO,UAAY,IAGfD,EAAA,CAAC,GAAGnB,CAAI,CAAC,EACV,GAGL,CAACiB,EAAa,YAAcA,EAAa,OAASC,IAChDE,GAAQ,WACVA,EAAO,UAAY,GACnBE,EAAS,QAASC,GAAOA,EAAE,UAAY,EAAM,GACpCH,GAAU,CAACA,EAAO,YAC3BA,EAAO,UAAY,GACnBE,EAAS,QAASC,GAAOA,EAAE,UAAY,EAAK,GAEtCJ,EAAA,CAAC,GAAGnB,CAAI,CAAC,EAErB,EAGMwB,EAAwBC,GAExBA,EAAY,KAAMC,GAAMA,EAAE,MAAM,WAAW,iBAAiB,CAAC,EACxD,iBAGLD,EAAY,KAAMC,GAAMA,EAAE,MAAM,WAAW,cAAc,CAAC,EACrD,WAELD,EAAY,KAAMC,GAAMA,EAAE,MAAM,WAAW,sBAAsB,CAAC,EAC7D,SAELD,EAAY,KAAMC,GAAMA,EAAE,MAAM,WAAW,oBAAoB,CAAC,EAC3D,UAELD,EAAY,KAAMC,GAAMA,EAAE,MAAM,WAAW,oBAAoB,CAAC,EAC3D,UAKF,GAIHC,EAA2BC,GAAyB,CACpD,GAAA,CAACA,EAAa,MAAA,GAGlB,MAAMC,EAAaD,EAAK,YAAc,EAAA,QAAQ,MAAO,EAAE,EAGvD,OAAIC,EAAW,SAAS,gBAAgB,GAAKA,EAAW,SAAS,2BAA2B,EACnF,iBAELA,EAAW,SAAS,UAAU,GAAK,CAACA,EAAW,SAAS,gBAAgB,EACnE,WAELA,EAAW,SAAS,QAAQ,EACvB,SAKLA,EAAW,SAAS,SAAS,EACxB,UAELA,EAAW,SAAS,SAAS,EACxB,UAGFA,CACT,EAEaC,EAAwB,CAAC,CACpC,YAAAL,EACA,KAAMM,CACR,IAAkC,CAEhC,KAAM,CAACC,EAAgBC,CAAiB,EAAIC,EAAAA,SAAS,EAAK,EACpD,CAAClC,EAAMmB,CAAO,EAAIe,EAAAA,SAAmCT,CAAW,EAGhEU,EAAeX,EAAqBC,CAAW,EAC/CW,EAAiBT,EAAwBI,GAAgBI,CAAY,EAOrEE,EAA6B3B,EAAA,YAChC4B,GAAgB,CACT,MAAArB,EAAejB,EAAKsC,CAAG,EAGxBrB,IAGL,WAAW,IAAM,CACf,MAAMsB,EAAcvC,EAAK,MAAOwC,GAAMA,EAAE,SAAS,EACjDP,EAAkBM,CAAW,GAC5B,CAAC,EAGAH,IAAmB,YACrBpB,EAAOhB,EAAMiB,EAAcwB,EAAY,MAAOtB,CAAO,EACrDH,EAAOhB,EAAMiB,EAAcwB,EAAY,MAAOtB,CAAO,GAC5CiB,IAAmB,SAC5BpB,EAAOhB,EAAMiB,EAAcwB,EAAY,QAAStB,CAAO,EAC9CiB,IAAmB,UAC5BpB,EAAOhB,EAAMiB,EAAcwB,EAAY,qBAAsBtB,CAAO,EAC3DiB,IAAmB,UAC5BpB,EAAOhB,EAAMiB,EAAcwB,EAAY,SAAUtB,CAAO,EAC/CiB,IAAmB,oBAC5BpB,EAAOhB,EAAMiB,EAAcwB,EAAY,yBAA0BtB,CAAO,EACxEH,EAAOhB,EAAMiB,EAAcwB,EAAY,8BAA+BtB,CAAO,EAC7EH,EAAOhB,EAAMiB,EAAcwB,EAAY,0CAA2CtB,CAAO,EACzFH,EAAOhB,EAAMiB,EAAcwB,EAAY,oCAAqCtB,CAAO,EACnFH,EAAOhB,EAAMiB,EAAcwB,EAAY,uCAAwCtB,CAAO,IAIzEF,EAAA,UAAY,CAACA,EAAa,UAC/BE,EAAA,CAAC,GAAGnB,CAAI,CAAC,GAErB,EAEA,CAACA,EAAMoC,CAAc,CACvB,EAKMM,EAAyBhC,EAAAA,YAAY,IAAM,CAC/CuB,EAAmBU,GAAoB,CACrC,MAAMC,EAAW,CAACD,EAGZE,EAAkB7C,EAAK,OAAQ0B,GAAM,CAACA,EAAE,UAAU,EACxC,OAAAmB,EAAA,QAASC,GAAS,CAChCA,EAAK,UAAYF,EAGA5C,EAAK,OAAQ0B,GAAMA,EAAE,aAAeoB,EAAK,IAAI,EACrD,QAASC,GAAU,CAC1BA,EAAM,UAAYH,CAAA,CACnB,CAAA,CACF,EAGI5C,EAAA,QAASkB,GAAe,CACvB,CAACA,EAAW,YAAc,CAAC2B,EAAgB,SAAS3B,CAAU,IAChEA,EAAW,UAAY0B,EACzB,CACD,EAGOzB,EAAA,CAAC,GAAGnB,CAAI,CAAC,EAEV4C,CAAA,CACR,CAAA,EACA,CAAC5C,CAAI,CAAC,EAGTgD,OAAAA,EAAAA,UAAU,IAAM,CACd7B,EAAQM,CAAW,CAAA,EAClB,CAACA,CAAW,CAAC,EAGhBuB,EAAAA,UAAU,IAAM,CACdf,EAAkBjC,EAAK,MAAOwC,GAAMA,EAAE,SAAS,CAAC,CAAA,EAC/C,CAACxC,CAAI,CAAC,EAEF,CACL,eAAAgC,EACA,KAAAhC,EACA,2BAAAqC,EACA,uBAAAK,CACF,CACF,EChMaO,EAAmBC,EAAAA,KAAW,SAA0B,CACnE,YAAAzB,EACA,KAAAG,EACA,cAAAuB,EACA,SAAAC,EACA,iBAAAC,EACA,SAAA7C,EACA,cAAA8C,CACF,EAA0B,CAElB,MAAAC,EAAiBC,SAAO/B,CAAW,EAGzCuB,EAAAA,UAAU,IAAM,CACVvB,EAAY,SAAW8B,EAAe,QAAQ,SAChDA,EAAe,QAAU9B,EAC3B,EACC,CAACA,CAAW,CAAC,EAEhB,KAAM,CAAE,eAAAO,EAAgB,2BAAAK,EAA4B,KAAArC,EAAM,uBAAA0C,CAAuB,EAC/EZ,EAAsB,CAAE,YAAayB,EAAe,QAAS,KAAA3B,CAAA,CAAM,EAIrE,OAEK6B,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAA,CAACP,GACAvC,EAAA,IAACE,EAAA,CACC,KAAK,aACL,UAAWkB,EACX,SAAUxB,GAAY,GACtB,GAAG,aACH,SAAUkC,CAAA,CACZ,EAED1C,GAAM,IAAI,CAAC2D,EAA6BrB,UACtC,MACC,CAAA,SAAA1B,EAAA,IAACE,EAAA,CACC,KAAM6C,EAAI,YACV,UAAWA,EAAI,UACf,GAAIA,EAAI,YAAa,kBAAoB,EAAA,OAAOA,EAAI,UAAW,EAC/D,SAAU,IAAM,CACdtB,EAA2BC,CAAG,EAC1Be,GACFA,EAAiBM,CAAG,CAExB,EACA,UAAWhD,EAAK,OAAQ,CACtB,OAAQgD,EAAI,UAAA,CACb,EACD,SAAUnD,GAAY,EAAA,CAAA,CACxB,EAfQ8B,CAgBV,CACD,EACA,CAACc,GACAK,EAAAA,KAACG,EACE,CAAA,SAAA,CACCN,GAAA1C,EAAA,IAACiD,EAAA,CACC,QAAUC,GAAsC,CAC9CA,EAAE,eAAe,EACHR,EAAA,CAChB,EACD,SAAA,QAAA,CAED,EAGD1C,EAAA,IAAAiD,EAAA,CAAO,KAAK,SAAS,SAAI,MAAA,CAAA,CAAA,CAC5B,CAAA,CAAA,EAEJ,CAEJ,CAAC"}