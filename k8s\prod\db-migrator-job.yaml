﻿apiVersion: batch/v1
kind: Job
metadata:
  name: imip-idjas-db-migrator-${CI_COMMIT_SHA}
  namespace: imip-idjas-prod
spec:
  # Increased backoffLimit to allow for retries
  backoffLimit: 2
  # Increased activeDeadlineSeconds to give more time for completion
  activeDeadlineSeconds: 600
  template:
    metadata:
      labels:
        app: imip-idjas-db-migrator
        commit: "${CI_COMMIT_SHA}"
    spec:
      # Allow scheduling on any available node for better resource utilization
      # Comment out nodeSelector to use any available node
      # nodeSelector:
      #   kubernetes.io/hostname: imdevapp18
      # Add host aliases to resolve DNS names to specific IP addresses
      hostAliases:
        - ip: "**********"
          hostnames:
            - "identity.imip.co.id"
            - "api-identity.imip.co.id"
      volumes:
        - name: certificate-volume
          secret:
            secretName: imip-idjas-certificate
      # Explicitly specify the image pull secrets
      imagePullSecrets:
        - name: gitlab-registry-credentials
      containers:
        - name: imip-idjas-db-migrator
          image: ${CI_REGISTRY_IMAGE}/db-migrator:${CI_COMMIT_SHA}
          # Add resource requests and limits for better scheduling
          resources:
            requests:
              cpu: "200m"
              memory: "256Mi"
            limits:
              cpu: "500m"
              memory: "512Mi"
          volumeMounts:
            - name: certificate-volume
              mountPath: /app/certs
              readOnly: true
          envFrom:
            - configMapRef:
                name: imip-idjas-config
            - secretRef:
                name: imip-idjas-secrets
      restartPolicy: Never
