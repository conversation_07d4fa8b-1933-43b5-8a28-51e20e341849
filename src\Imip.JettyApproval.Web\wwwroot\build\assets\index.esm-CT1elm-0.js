import{b as P,r as pe}from"./vendor-CrSBzUoz.js";var de=e=>e.type==="checkbox",re=e=>e instanceof Date,R=e=>e==null;const rt=e=>typeof e=="object";var D=e=>!R(e)&&!Array.isArray(e)&&rt(e)&&!re(e),Ft=e=>D(e)&&e.target?de(e.target)?e.target.checked:e.target.value:e,At=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,xt=(e,s)=>e.has(At(s)),wt=e=>{const s=e.constructor&&e.constructor.prototype;return D(s)&&s.hasOwnProperty("isPrototypeOf")},Oe=typeof window<"u"&&typeof window.HTMLElement<"u"&&typeof document<"u";function L(e){let s;const r=Array.isArray(e),a=typeof FileList<"u"?e instanceof FileList:!1;if(e instanceof Date)s=new Date(e);else if(e instanceof Set)s=new Set(e);else if(!(Oe&&(e instanceof Blob||a))&&(r||D(e)))if(s=r?[]:{},!r&&!wt(e))s=e;else for(const u in e)e.hasOwnProperty(u)&&(s[u]=L(e[u]));else return e;return s}var Ve=e=>Array.isArray(e)?e.filter(Boolean):[],S=e=>e===void 0,y=(e,s,r)=>{if(!s||!D(e))return r;const a=Ve(s.split(/[,[\].]+?/)).reduce((u,n)=>R(u)?u:u[n],e);return S(a)||a===e?S(e[s])?r:e[s]:a},z=e=>typeof e=="boolean",Te=e=>/^\w*$/.test(e),st=e=>Ve(e.replace(/["|']|\]/g,"").split(/\.|\[/)),x=(e,s,r)=>{let a=-1;const u=Te(s)?[s]:st(s),n=u.length,c=n-1;for(;++a<n;){const h=u[a];let M=r;if(a!==c){const H=e[h];M=D(H)||Array.isArray(H)?H:isNaN(+u[a+1])?{}:[]}if(h==="__proto__"||h==="constructor"||h==="prototype")return;e[h]=M,e=e[h]}};const $e={BLUR:"blur",FOCUS_OUT:"focusout"},q={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},J={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"};P.createContext(null);var mt=(e,s,r,a=!0)=>{const u={defaultValues:s._defaultValues};for(const n in e)Object.defineProperty(u,n,{get:()=>{const c=n;return s._proxyFormState[c]!==q.all&&(s._proxyFormState[c]=!a||q.all),e[c]}});return u};const Dt=typeof window<"u"?pe.useLayoutEffect:pe.useEffect;var Y=e=>typeof e=="string",Et=(e,s,r,a,u)=>Y(e)?(a&&s.watch.add(e),y(r,e,u)):Array.isArray(e)?e.map(n=>(a&&s.watch.add(n),y(r,n))):(a&&(s.watchAll=!0),r),St=(e,s,r,a,u)=>s?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[a]:u||!0}}:{},oe=e=>Array.isArray(e)?e:[e],Ke=()=>{let e=[];return{get observers(){return e},next:u=>{for(const n of e)n.next&&n.next(u)},subscribe:u=>(e.push(u),{unsubscribe:()=>{e=e.filter(n=>n!==u)}}),unsubscribe:()=>{e=[]}}},ke=e=>R(e)||!rt(e);function ee(e,s){if(ke(e)||ke(s))return e===s;if(re(e)&&re(s))return e.getTime()===s.getTime();const r=Object.keys(e),a=Object.keys(s);if(r.length!==a.length)return!1;for(const u of r){const n=e[u];if(!a.includes(u))return!1;if(u!=="ref"){const c=s[u];if(re(n)&&re(c)||D(n)&&D(c)||Array.isArray(n)&&Array.isArray(c)?!ee(n,c):n!==c)return!1}}return!0}var N=e=>D(e)&&!Object.keys(e).length,Le=e=>e.type==="file",W=e=>typeof e=="function",ve=e=>{if(!Oe)return!1;const s=e?e.ownerDocument:0;return e instanceof(s&&s.defaultView?s.defaultView.HTMLElement:HTMLElement)},it=e=>e.type==="select-multiple",Ce=e=>e.type==="radio",kt=e=>Ce(e)||de(e),Se=e=>ve(e)&&e.isConnected;function Ot(e,s){const r=s.slice(0,-1).length;let a=0;for(;a<r;)e=S(e)?a++:e[s[a++]];return e}function Tt(e){for(const s in e)if(e.hasOwnProperty(s)&&!S(e[s]))return!1;return!0}function T(e,s){const r=Array.isArray(s)?s:Te(s)?[s]:st(s),a=r.length===1?e:Ot(e,r),u=r.length-1,n=r[u];return a&&delete a[n],u!==0&&(D(a)&&N(a)||Array.isArray(a)&&Tt(a))&&T(e,r.slice(0,-1)),e}var at=e=>{for(const s in e)if(W(e[s]))return!0;return!1};function be(e,s={}){const r=Array.isArray(e);if(D(e)||r)for(const a in e)Array.isArray(e[a])||D(e[a])&&!at(e[a])?(s[a]=Array.isArray(e[a])?[]:{},be(e[a],s[a])):R(e[a])||(s[a]=!0);return s}function lt(e,s,r){const a=Array.isArray(e);if(D(e)||a)for(const u in e)Array.isArray(e[u])||D(e[u])&&!at(e[u])?S(s)||ke(r[u])?r[u]=Array.isArray(e[u])?be(e[u],[]):{...be(e[u])}:lt(e[u],R(s)?{}:s[u],r[u]):r[u]=!ee(e[u],s[u]);return r}var ne=(e,s)=>lt(e,s,be(s));const ze={value:!1,isValid:!1},Ye={value:!0,isValid:!0};var nt=e=>{if(Array.isArray(e)){if(e.length>1){const s=e.filter(r=>r&&r.checked&&!r.disabled).map(r=>r.value);return{value:s,isValid:!!s.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!S(e[0].attributes.value)?S(e[0].value)||e[0].value===""?Ye:{value:e[0].value,isValid:!0}:Ye:ze}return ze},ut=(e,{valueAsNumber:s,valueAsDate:r,setValueAs:a})=>S(e)?e:s?e===""?NaN:e&&+e:r&&Y(e)?new Date(e):a?a(e):e;const je={isValid:!1,value:null};var ot=e=>Array.isArray(e)?e.reduce((s,r)=>r&&r.checked&&!r.disabled?{isValid:!0,value:r.value}:s,je):je;function Je(e){const s=e.ref;return Le(s)?s.files:Ce(s)?ot(e.refs).value:it(s)?[...s.selectedOptions].map(({value:r})=>r):de(s)?nt(e.refs).value:ut(S(s.value)?e.ref.value:s.value,e)}var Lt=(e,s,r,a)=>{const u={};for(const n of e){const c=y(s,n);c&&x(u,n,c._f)}return{criteriaMode:r,names:[...e],fields:u,shouldUseNativeValidation:a}},_e=e=>e instanceof RegExp,ue=e=>S(e)?e:_e(e)?e.source:D(e)?_e(e.value)?e.value.source:e.value:e,Qe=e=>({isOnSubmit:!e||e===q.onSubmit,isOnBlur:e===q.onBlur,isOnChange:e===q.onChange,isOnAll:e===q.all,isOnTouch:e===q.onTouched});const Xe="AsyncFunction";var Ct=e=>!!e&&!!e.validate&&!!(W(e.validate)&&e.validate.constructor.name===Xe||D(e.validate)&&Object.values(e.validate).find(s=>s.constructor.name===Xe)),Rt=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),Ze=(e,s,r)=>!r&&(s.watchAll||s.watch.has(e)||[...s.watch].some(a=>e.startsWith(a)&&/^\.\w+/.test(e.slice(a.length))));const fe=(e,s,r,a)=>{for(const u of r||Object.keys(e)){const n=y(e,u);if(n){const{_f:c,...h}=n;if(c){if(c.refs&&c.refs[0]&&s(c.refs[0],u)&&!a)return!0;if(c.ref&&s(c.ref,c.name)&&!a)return!0;if(fe(h,s))break}else if(D(h)&&fe(h,s))break}}};function Ge(e,s,r){const a=y(e,r);if(a||Te(r))return{error:a,name:r};const u=r.split(".");for(;u.length;){const n=u.join("."),c=y(s,n),h=y(e,n);if(c&&!Array.isArray(c)&&r!==n)return{name:r};if(h&&h.type)return{name:n,error:h};if(h&&h.root&&h.root.type)return{name:`${n}.root`,error:h.root};u.pop()}return{name:r}}var Mt=(e,s,r,a)=>{r(e);const{name:u,...n}=e;return N(n)||Object.keys(n).length>=Object.keys(s).length||Object.keys(n).find(c=>s[c]===(!a||q.all))},Ut=(e,s,r)=>!e||!s||e===s||oe(e).some(a=>a&&(r?a===s:a.startsWith(s)||s.startsWith(a))),Nt=(e,s,r,a,u)=>u.isOnAll?!1:!r&&u.isOnTouch?!(s||e):(r?a.isOnBlur:u.isOnBlur)?!e:(r?a.isOnChange:u.isOnChange)?e:!0,Bt=(e,s)=>!Ve(y(e,s)).length&&T(e,s),It=(e,s,r)=>{const a=oe(y(e,r));return x(a,"root",s[r]),x(e,r,a),e},ge=e=>Y(e);function et(e,s,r="validate"){if(ge(e)||Array.isArray(e)&&e.every(ge)||z(e)&&!e)return{type:r,message:ge(e)?e:"",ref:s}}var ie=e=>D(e)&&!_e(e)?e:{value:e,message:""},tt=async(e,s,r,a,u,n)=>{const{ref:c,refs:h,required:M,maxLength:H,minLength:w,min:E,max:v,pattern:ae,validate:Q,name:k,valueAsNumber:X,mount:Fe}=e._f,_=y(r,k);if(!Fe||s.has(k))return{};const j=h?h[0]:c,p=b=>{u&&j.reportValidity&&(j.setCustomValidity(z(b)?"":b||""),j.reportValidity())},O={},ce=Ce(c),Z=de(c),Ae=ce||Z,I=(X||Le(c))&&S(c.value)&&S(_)||ve(c)&&c.value===""||_===""||Array.isArray(_)&&!_.length,te=St.bind(null,k,a,O),$=(b,F,m,C=J.maxLength,U=J.minLength)=>{const K=b?F:m;O[k]={type:b?C:U,message:K,ref:c,...te(b?C:U,K)}};if(n?!Array.isArray(_)||!_.length:M&&(!Ae&&(I||R(_))||z(_)&&!_||Z&&!nt(h).isValid||ce&&!ot(h).isValid)){const{value:b,message:F}=ge(M)?{value:!!M,message:M}:ie(M);if(b&&(O[k]={type:J.required,message:F,ref:j,...te(J.required,F)},!a))return p(F),O}if(!I&&(!R(E)||!R(v))){let b,F;const m=ie(v),C=ie(E);if(!R(_)&&!isNaN(_)){const U=c.valueAsNumber||_&&+_;R(m.value)||(b=U>m.value),R(C.value)||(F=U<C.value)}else{const U=c.valueAsDate||new Date(_),K=ye=>new Date(new Date().toDateString()+" "+ye),le=c.type=="time",se=c.type=="week";Y(m.value)&&_&&(b=le?K(_)>K(m.value):se?_>m.value:U>new Date(m.value)),Y(C.value)&&_&&(F=le?K(_)<K(C.value):se?_<C.value:U<new Date(C.value))}if((b||F)&&($(!!b,m.message,C.message,J.max,J.min),!a))return p(O[k].message),O}if((H||w)&&!I&&(Y(_)||n&&Array.isArray(_))){const b=ie(H),F=ie(w),m=!R(b.value)&&_.length>+b.value,C=!R(F.value)&&_.length<+F.value;if((m||C)&&($(m,b.message,F.message),!a))return p(O[k].message),O}if(ae&&!I&&Y(_)){const{value:b,message:F}=ie(ae);if(_e(b)&&!_.match(b)&&(O[k]={type:J.pattern,message:F,ref:c,...te(J.pattern,F)},!a))return p(F),O}if(Q){if(W(Q)){const b=await Q(_,r),F=et(b,j);if(F&&(O[k]={...F,...te(J.validate,F.message)},!a))return p(F.message),O}else if(D(Q)){let b={};for(const F in Q){if(!N(b)&&!a)break;const m=et(await Q[F](_,r),j,F);m&&(b={...m,...te(F,m.message)},p(m.message),a&&(O[k]=b))}if(!N(b)&&(O[k]={ref:j,...b},!a))return O}}return p(!0),O};const Pt={mode:q.onSubmit,reValidateMode:q.onChange,shouldFocusError:!0};function qt(e={}){let s={...Pt,...e},r={submitCount:0,isDirty:!1,isReady:!1,isLoading:W(s.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:s.errors||{},disabled:s.disabled||!1};const a={};let u=D(s.defaultValues)||D(s.values)?L(s.defaultValues||s.values)||{}:{},n=s.shouldUnregister?{}:L(u),c={action:!1,mount:!1,watch:!1},h={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},M,H=0;const w={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let E={...w};const v={array:Ke(),state:Ke()},ae=s.criteriaMode===q.all,Q=t=>i=>{clearTimeout(H),H=setTimeout(t,i)},k=async t=>{if(!s.disabled&&(w.isValid||E.isValid||t)){const i=s.resolver?N((await Z()).errors):await I(a,!0);i!==r.isValid&&v.state.next({isValid:i})}},X=(t,i)=>{!s.disabled&&(w.isValidating||w.validatingFields||E.isValidating||E.validatingFields)&&((t||Array.from(h.mount)).forEach(l=>{l&&(i?x(r.validatingFields,l,i):T(r.validatingFields,l))}),v.state.next({validatingFields:r.validatingFields,isValidating:!N(r.validatingFields)}))},Fe=(t,i=[],l,d,f=!0,o=!0)=>{if(d&&l&&!s.disabled){if(c.action=!0,o&&Array.isArray(y(a,t))){const g=l(y(a,t),d.argA,d.argB);f&&x(a,t,g)}if(o&&Array.isArray(y(r.errors,t))){const g=l(y(r.errors,t),d.argA,d.argB);f&&x(r.errors,t,g),Bt(r.errors,t)}if((w.touchedFields||E.touchedFields)&&o&&Array.isArray(y(r.touchedFields,t))){const g=l(y(r.touchedFields,t),d.argA,d.argB);f&&x(r.touchedFields,t,g)}(w.dirtyFields||E.dirtyFields)&&(r.dirtyFields=ne(u,n)),v.state.next({name:t,isDirty:$(t,i),dirtyFields:r.dirtyFields,errors:r.errors,isValid:r.isValid})}else x(n,t,i)},_=(t,i)=>{x(r.errors,t,i),v.state.next({errors:r.errors})},j=t=>{r.errors=t,v.state.next({errors:r.errors,isValid:!1})},p=(t,i,l,d)=>{const f=y(a,t);if(f){const o=y(n,t,S(l)?y(u,t):l);S(o)||d&&d.defaultChecked||i?x(n,t,i?o:Je(f._f)):m(t,o),c.mount&&k()}},O=(t,i,l,d,f)=>{let o=!1,g=!1;const V={name:t};if(!s.disabled){if(!l||d){(w.isDirty||E.isDirty)&&(g=r.isDirty,r.isDirty=V.isDirty=$(),o=g!==V.isDirty);const A=ee(y(u,t),i);g=!!y(r.dirtyFields,t),A?T(r.dirtyFields,t):x(r.dirtyFields,t,!0),V.dirtyFields=r.dirtyFields,o=o||(w.dirtyFields||E.dirtyFields)&&g!==!A}if(l){const A=y(r.touchedFields,t);A||(x(r.touchedFields,t,l),V.touchedFields=r.touchedFields,o=o||(w.touchedFields||E.touchedFields)&&A!==l)}o&&f&&v.state.next(V)}return o?V:{}},ce=(t,i,l,d)=>{const f=y(r.errors,t),o=(w.isValid||E.isValid)&&z(i)&&r.isValid!==i;if(s.delayError&&l?(M=Q(()=>_(t,l)),M(s.delayError)):(clearTimeout(H),M=null,l?x(r.errors,t,l):T(r.errors,t)),(l?!ee(f,l):f)||!N(d)||o){const g={...d,...o&&z(i)?{isValid:i}:{},errors:r.errors,name:t};r={...r,...g},v.state.next(g)}},Z=async t=>{X(t,!0);const i=await s.resolver(n,s.context,Lt(t||h.mount,a,s.criteriaMode,s.shouldUseNativeValidation));return X(t),i},Ae=async t=>{const{errors:i}=await Z(t);if(t)for(const l of t){const d=y(i,l);d?x(r.errors,l,d):T(r.errors,l)}else r.errors=i;return i},I=async(t,i,l={valid:!0})=>{for(const d in t){const f=t[d];if(f){const{_f:o,...g}=f;if(o){const V=h.array.has(o.name),A=f._f&&Ct(f._f);A&&w.validatingFields&&X([d],!0);const B=await tt(f,h.disabled,n,ae,s.shouldUseNativeValidation&&!i,V);if(A&&w.validatingFields&&X([d]),B[o.name]&&(l.valid=!1,i))break;!i&&(y(B,o.name)?V?It(r.errors,B,o.name):x(r.errors,o.name,B[o.name]):T(r.errors,o.name))}!N(g)&&await I(g,i,l)}}return l.valid},te=()=>{for(const t of h.unMount){const i=y(a,t);i&&(i._f.refs?i._f.refs.every(l=>!Se(l)):!Se(i._f.ref))&&xe(t)}h.unMount=new Set},$=(t,i)=>!s.disabled&&(t&&i&&x(n,t,i),!ee(ye(),u)),b=(t,i,l)=>Et(t,h,{...c.mount?n:S(i)?u:Y(t)?{[t]:i}:i},l,i),F=t=>Ve(y(c.mount?n:u,t,s.shouldUnregister?y(u,t,[]):[])),m=(t,i,l={})=>{const d=y(a,t);let f=i;if(d){const o=d._f;o&&(!o.disabled&&x(n,t,ut(i,o)),f=ve(o.ref)&&R(i)?"":i,it(o.ref)?[...o.ref.options].forEach(g=>g.selected=f.includes(g.value)):o.refs?de(o.ref)?o.refs.forEach(g=>{(!g.defaultChecked||!g.disabled)&&(Array.isArray(f)?g.checked=!!f.find(V=>V===g.value):g.checked=f===g.value||!!f)}):o.refs.forEach(g=>g.checked=g.value===f):Le(o.ref)?o.ref.value="":(o.ref.value=f,o.ref.type||v.state.next({name:t,values:L(n)})))}(l.shouldDirty||l.shouldTouch)&&O(t,f,l.shouldTouch,l.shouldDirty,!0),l.shouldValidate&&se(t)},C=(t,i,l)=>{for(const d in i){if(!i.hasOwnProperty(d))return;const f=i[d],o=t+"."+d,g=y(a,o);(h.array.has(t)||D(f)||g&&!g._f)&&!re(f)?C(o,f,l):m(o,f,l)}},U=(t,i,l={})=>{const d=y(a,t),f=h.array.has(t),o=L(i);x(n,t,o),f?(v.array.next({name:t,values:L(n)}),(w.isDirty||w.dirtyFields||E.isDirty||E.dirtyFields)&&l.shouldDirty&&v.state.next({name:t,dirtyFields:ne(u,n),isDirty:$(t,o)})):d&&!d._f&&!R(o)?C(t,o,l):m(t,o,l),Ze(t,h)&&v.state.next({...r}),v.state.next({name:c.mount?t:void 0,values:L(n)})},K=async t=>{c.mount=!0;const i=t.target;let l=i.name,d=!0;const f=y(a,l),o=A=>{d=Number.isNaN(A)||re(A)&&isNaN(A.getTime())||ee(A,y(n,l,A))},g=Qe(s.mode),V=Qe(s.reValidateMode);if(f){let A,B;const he=i.type?Je(f._f):Ft(t),G=t.type===$e.BLUR||t.type===$e.FOCUS_OUT,bt=!Rt(f._f)&&!s.resolver&&!y(r.errors,l)&&!f._f.deps||Nt(G,y(r.touchedFields,l),r.isSubmitted,V,g),De=Ze(l,h,G);x(n,l,he),G?(f._f.onBlur&&f._f.onBlur(t),M&&M(0)):f._f.onChange&&f._f.onChange(t);const Ee=O(l,he,G),_t=!N(Ee)||De;if(!G&&v.state.next({name:l,type:t.type,values:L(n)}),bt)return(w.isValid||E.isValid)&&(s.mode==="onBlur"?G&&k():G||k()),_t&&v.state.next({name:l,...De?{}:Ee});if(!G&&De&&v.state.next({...r}),s.resolver){const{errors:We}=await Z([l]);if(o(he),d){const Vt=Ge(r.errors,a,l),He=Ge(We,a,Vt.name||l);A=He.error,l=He.name,B=N(We)}}else X([l],!0),A=(await tt(f,h.disabled,n,ae,s.shouldUseNativeValidation))[l],X([l]),o(he),d&&(A?B=!1:(w.isValid||E.isValid)&&(B=await I(a,!0)));d&&(f._f.deps&&se(f._f.deps),ce(l,B,A,Ee))}},le=(t,i)=>{if(y(r.errors,i)&&t.focus)return t.focus(),1},se=async(t,i={})=>{let l,d;const f=oe(t);if(s.resolver){const o=await Ae(S(t)?t:f);l=N(o),d=t?!f.some(g=>y(o,g)):l}else t?(d=(await Promise.all(f.map(async o=>{const g=y(a,o);return await I(g&&g._f?{[o]:g}:g)}))).every(Boolean),!(!d&&!r.isValid)&&k()):d=l=await I(a);return v.state.next({...!Y(t)||(w.isValid||E.isValid)&&l!==r.isValid?{}:{name:t},...s.resolver||!t?{isValid:l}:{},errors:r.errors}),i.shouldFocus&&!d&&fe(a,le,t?f:h.mount),d},ye=t=>{const i={...c.mount?n:u};return S(t)?i:Y(t)?y(i,t):t.map(l=>y(i,l))},Re=(t,i)=>({invalid:!!y((i||r).errors,t),isDirty:!!y((i||r).dirtyFields,t),error:y((i||r).errors,t),isValidating:!!y(r.validatingFields,t),isTouched:!!y((i||r).touchedFields,t)}),ft=t=>{t&&oe(t).forEach(i=>T(r.errors,i)),v.state.next({errors:t?r.errors:{}})},Me=(t,i,l)=>{const d=(y(a,t,{_f:{}})._f||{}).ref,f=y(r.errors,t)||{},{ref:o,message:g,type:V,...A}=f;x(r.errors,t,{...A,...i,ref:d}),v.state.next({name:t,errors:r.errors,isValid:!1}),l&&l.shouldFocus&&d&&d.focus&&d.focus()},dt=(t,i)=>W(t)?v.state.subscribe({next:l=>t(b(void 0,i),l)}):b(t,i,!0),Ue=t=>v.state.subscribe({next:i=>{Ut(t.name,i.name,t.exact)&&Mt(i,t.formState||w,vt,t.reRenderRoot)&&t.callback({values:{...n},...r,...i})}}).unsubscribe,ct=t=>(c.mount=!0,E={...E,...t.formState},Ue({...t,formState:E})),xe=(t,i={})=>{for(const l of t?oe(t):h.mount)h.mount.delete(l),h.array.delete(l),i.keepValue||(T(a,l),T(n,l)),!i.keepError&&T(r.errors,l),!i.keepDirty&&T(r.dirtyFields,l),!i.keepTouched&&T(r.touchedFields,l),!i.keepIsValidating&&T(r.validatingFields,l),!s.shouldUnregister&&!i.keepDefaultValue&&T(u,l);v.state.next({values:L(n)}),v.state.next({...r,...i.keepDirty?{isDirty:$()}:{}}),!i.keepIsValid&&k()},Ne=({disabled:t,name:i})=>{(z(t)&&c.mount||t||h.disabled.has(i))&&(t?h.disabled.add(i):h.disabled.delete(i))},we=(t,i={})=>{let l=y(a,t);const d=z(i.disabled)||z(s.disabled);return x(a,t,{...l||{},_f:{...l&&l._f?l._f:{ref:{name:t}},name:t,mount:!0,...i}}),h.mount.add(t),l?Ne({disabled:z(i.disabled)?i.disabled:s.disabled,name:t}):p(t,!0,i.value),{...d?{disabled:i.disabled||s.disabled}:{},...s.progressive?{required:!!i.required,min:ue(i.min),max:ue(i.max),minLength:ue(i.minLength),maxLength:ue(i.maxLength),pattern:ue(i.pattern)}:{},name:t,onChange:K,onBlur:K,ref:f=>{if(f){we(t,i),l=y(a,t);const o=S(f.value)&&f.querySelectorAll&&f.querySelectorAll("input,select,textarea")[0]||f,g=kt(o),V=l._f.refs||[];if(g?V.find(A=>A===o):o===l._f.ref)return;x(a,t,{_f:{...l._f,...g?{refs:[...V.filter(Se),o,...Array.isArray(y(u,t))?[{}]:[]],ref:{type:o.type,name:t}}:{ref:o}}}),p(t,!1,void 0,o)}else l=y(a,t,{}),l._f&&(l._f.mount=!1),(s.shouldUnregister||i.shouldUnregister)&&!(xt(h.array,t)&&c.action)&&h.unMount.add(t)}}},me=()=>s.shouldFocusError&&fe(a,le,h.mount),yt=t=>{z(t)&&(v.state.next({disabled:t}),fe(a,(i,l)=>{const d=y(a,l);d&&(i.disabled=d._f.disabled||t,Array.isArray(d._f.refs)&&d._f.refs.forEach(f=>{f.disabled=d._f.disabled||t}))},0,!1))},Be=(t,i)=>async l=>{let d;l&&(l.preventDefault&&l.preventDefault(),l.persist&&l.persist());let f=L(n);if(v.state.next({isSubmitting:!0}),s.resolver){const{errors:o,values:g}=await Z();r.errors=o,f=g}else await I(a);if(h.disabled.size)for(const o of h.disabled)x(f,o,void 0);if(T(r.errors,"root"),N(r.errors)){v.state.next({errors:{}});try{await t(f,l)}catch(o){d=o}}else i&&await i({...r.errors},l),me(),setTimeout(me);if(v.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:N(r.errors)&&!d,submitCount:r.submitCount+1,errors:r.errors}),d)throw d},ht=(t,i={})=>{y(a,t)&&(S(i.defaultValue)?U(t,L(y(u,t))):(U(t,i.defaultValue),x(u,t,L(i.defaultValue))),i.keepTouched||T(r.touchedFields,t),i.keepDirty||(T(r.dirtyFields,t),r.isDirty=i.defaultValue?$(t,L(y(u,t))):$()),i.keepError||(T(r.errors,t),w.isValid&&k()),v.state.next({...r}))},Ie=(t,i={})=>{const l=t?L(t):u,d=L(l),f=N(t),o=f?u:d;if(i.keepDefaultValues||(u=l),!i.keepValues){if(i.keepDirtyValues){const g=new Set([...h.mount,...Object.keys(ne(u,n))]);for(const V of Array.from(g))y(r.dirtyFields,V)?x(o,V,y(n,V)):U(V,y(o,V))}else{if(Oe&&S(t))for(const g of h.mount){const V=y(a,g);if(V&&V._f){const A=Array.isArray(V._f.refs)?V._f.refs[0]:V._f.ref;if(ve(A)){const B=A.closest("form");if(B){B.reset();break}}}}for(const g of h.mount)U(g,y(o,g))}n=L(o),v.array.next({values:{...o}}),v.state.next({values:{...o}})}h={mount:i.keepDirtyValues?h.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},c.mount=!w.isValid||!!i.keepIsValid||!!i.keepDirtyValues,c.watch=!!s.shouldUnregister,v.state.next({submitCount:i.keepSubmitCount?r.submitCount:0,isDirty:f?!1:i.keepDirty?r.isDirty:!!(i.keepDefaultValues&&!ee(t,u)),isSubmitted:i.keepIsSubmitted?r.isSubmitted:!1,dirtyFields:f?{}:i.keepDirtyValues?i.keepDefaultValues&&n?ne(u,n):r.dirtyFields:i.keepDefaultValues&&t?ne(u,t):i.keepDirty?r.dirtyFields:{},touchedFields:i.keepTouched?r.touchedFields:{},errors:i.keepErrors?r.errors:{},isSubmitSuccessful:i.keepIsSubmitSuccessful?r.isSubmitSuccessful:!1,isSubmitting:!1})},Pe=(t,i)=>Ie(W(t)?t(n):t,i),gt=(t,i={})=>{const l=y(a,t),d=l&&l._f;if(d){const f=d.refs?d.refs[0]:d.ref;f.focus&&(f.focus(),i.shouldSelect&&W(f.select)&&f.select())}},vt=t=>{r={...r,...t}},qe={control:{register:we,unregister:xe,getFieldState:Re,handleSubmit:Be,setError:Me,_subscribe:Ue,_runSchema:Z,_focusError:me,_getWatch:b,_getDirty:$,_setValid:k,_setFieldArray:Fe,_setDisabledField:Ne,_setErrors:j,_getFieldArray:F,_reset:Ie,_resetDefaultValues:()=>W(s.defaultValues)&&s.defaultValues().then(t=>{Pe(t,s.resetOptions),v.state.next({isLoading:!1})}),_removeUnmounted:te,_disableForm:yt,_subjects:v,_proxyFormState:w,get _fields(){return a},get _formValues(){return n},get _state(){return c},set _state(t){c=t},get _defaultValues(){return u},get _names(){return h},set _names(t){h=t},get _formState(){return r},get _options(){return s},set _options(t){s={...s,...t}}},subscribe:ct,trigger:se,register:we,handleSubmit:Be,watch:dt,setValue:U,getValues:ye,reset:Pe,resetField:ht,clearErrors:ft,unregister:xe,setError:Me,setFocus:gt,getFieldState:Re};return{...qe,formControl:qe}}function pt(e={}){const s=P.useRef(void 0),r=P.useRef(void 0),[a,u]=P.useState({isDirty:!1,isValidating:!1,isLoading:W(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:W(e.defaultValues)?void 0:e.defaultValues});s.current||(s.current={...e.formControl?e.formControl:qt(e),formState:a},e.formControl&&e.defaultValues&&!W(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions));const n=s.current.control;return n._options=e,Dt(()=>{const c=n._subscribe({formState:n._proxyFormState,callback:()=>u({...n._formState}),reRenderRoot:!0});return u(h=>({...h,isReady:!0})),n._formState.isReady=!0,c},[n]),P.useEffect(()=>n._disableForm(e.disabled),[n,e.disabled]),P.useEffect(()=>{e.mode&&(n._options.mode=e.mode),e.reValidateMode&&(n._options.reValidateMode=e.reValidateMode)},[n,e.mode,e.reValidateMode]),P.useEffect(()=>{e.errors&&(n._setErrors(e.errors),n._focusError())},[n,e.errors]),P.useEffect(()=>{e.shouldUnregister&&n._subjects.state.next({values:n._getWatch()})},[n,e.shouldUnregister]),P.useEffect(()=>{if(n._proxyFormState.isDirty){const c=n._getDirty();c!==a.isDirty&&n._subjects.state.next({isDirty:c})}},[n,a.isDirty]),P.useEffect(()=>{e.values&&!ee(e.values,r.current)?(n._reset(e.values,n._options.resetOptions),r.current=e.values,u(c=>({...c}))):n._resetDefaultValues()},[n,e.values]),P.useEffect(()=>{n._state.mount||(n._setValid(),n._state.mount=!0),n._state.watch&&(n._state.watch=!1,n._subjects.state.next({...n._formState})),n._removeUnmounted()}),s.current.formState=mt(a,n),s.current}export{pt as u};
//# sourceMappingURL=index.esm-CT1elm-0.js.map
