using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace Imip.JettyApproval.Approvals.ApprovalDelegations;

/// <summary>
/// Repository interface for ApprovalDelegation entity
/// </summary>
public interface IApprovalDelegationRepository : IRepository<ApprovalDelegation, Guid>
{
    /// <summary>
    /// Gets delegations by approver ID
    /// </summary>
    Task<List<ApprovalDelegation>> GetByApproverIdAsync(Guid approverId);

    /// <summary>
    /// Gets delegations by substitute ID
    /// </summary>
    Task<List<ApprovalDelegation>> GetBySubstituteIdAsync(Guid substituteId);

    /// <summary>
    /// Gets active delegations
    /// </summary>
    Task<List<ApprovalDelegation>> GetActiveDelegationsAsync();

    /// <summary>
    /// Gets delegations by date range
    /// </summary>
    Task<List<ApprovalDelegation>> GetByDateRangeAsync(DateTime startDate, DateTime endDate);
}