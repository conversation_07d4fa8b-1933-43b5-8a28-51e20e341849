import{r as m,j as e}from"./vendor-CrSBzUoz.js";import{A as S,I as A,D as p,e as u,B as c,f as w,g as T,h as M,i as R,j as L}from"./app-layout-CNB1Wtrx.js";import{C as O,a as V,b as z,c as H}from"./card-BAJCNJxm.js";import{C}from"./checkbox-CayrCcBd.js";import{T as k,a as B,b,c as x,d as E,e as j}from"./table-CAbNlII1.js";import{I}from"./IconChevronDown-D4jBeGMo.js";import"./radix-DaY-mnHi.js";import"./App-CGHLK9xH.js";const d=Array(10).fill(null).map((o,l)=>({id:`hist_${l+1}`,vesselName:`MV. GRAND LINE V. 00${l+1}`,arrivalDate:"2025-04-15",departureDate:"2025-04-16",itemName:"COAL 50MT",requestBy:"USER2",status:l%2===0?"Approved":"Rejected"}));function U(){const[o,l]=m.useState(""),[i,h]=m.useState(new Set),[r,N]=m.useState(new Set(Object.keys(d[0]))),n=d.filter(s=>Object.values(s).some(a=>a.toString().toLowerCase().includes(o.toLowerCase()))),f=s=>{h(s?new Set(n.map(a=>a.id)):new Set)},g=(s,a)=>{const t=new Set(i);a?t.add(s):t.delete(s),h(t)},v=(s,a)=>{const t=new Set(r);a?t.add(s):t.delete(s),N(t)},D=s=>{};return e.jsx(S,{children:e.jsx("div",{className:"container mx-auto p-4",children:e.jsxs(O,{children:[e.jsx(V,{children:e.jsx(z,{className:"text-2xl font-bold",children:"Approval History"})}),e.jsxs(H,{children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx(A,{placeholder:"Filter lines...",value:o,onChange:s=>l(s.target.value),className:"max-w-sm"}),e.jsxs(p,{children:[e.jsx(u,{asChild:!0,children:e.jsxs(c,{variant:"outline",className:"ml-auto",children:["Columns ",e.jsx(I,{className:"ml-2 h-4 w-4"})]})}),e.jsx(w,{align:"end",children:Object.keys(d[0]).map(s=>e.jsx(T,{className:"capitalize",checked:r.has(s),onCheckedChange:a=>v(s,a===!0),children:s.replace(/([A-Z])/g," $1").trim()},s))})]})]}),e.jsx("div",{className:"rounded-md border",children:e.jsxs(k,{children:[e.jsx(B,{children:e.jsxs(b,{children:[e.jsx(x,{className:"w-[30px]",children:e.jsx(C,{checked:i.size===n.length&&n.length>0,onCheckedChange:s=>f(s===!0)})}),Object.keys(d[0]).map(s=>r.has(s)&&s!=="id"&&e.jsx(x,{className:"capitalize",children:s.replace(/([A-Z])/g," $1").trim()},s)),e.jsx(x,{className:"text-right",children:"Actions"})]})}),e.jsx(E,{children:n.map(s=>e.jsxs(b,{children:[e.jsx(j,{children:e.jsx(C,{checked:i.has(s.id),onCheckedChange:a=>g(s.id,a===!0)})}),Object.entries(s).map(([a,t])=>r.has(a)&&a!=="id"&&e.jsx(j,{children:t},a)),e.jsx(j,{className:"text-right",children:e.jsxs(p,{children:[e.jsx(u,{asChild:!0,children:e.jsxs(c,{variant:"ghost",className:"h-8 w-8 p-0",children:[e.jsx("span",{className:"sr-only",children:"Open menu"}),e.jsx(M,{className:"h-4 w-4"})]})}),e.jsxs(w,{align:"end",children:[e.jsx(R,{children:"Actions"}),e.jsx(L,{onClick:()=>D(s.id),children:"View Details"})]})]})})]},s.id))})]})}),e.jsxs("div",{className:"flex items-center justify-between mt-4",children:[e.jsxs("div",{className:"text-sm text-gray-500",children:[i.size," of ",n.length," row(s) selected."]}),e.jsxs("div",{className:"space-x-2",children:[e.jsx(c,{variant:"outline",size:"sm",children:"Previous"}),e.jsx(c,{variant:"outline",size:"sm",children:"Next"})]})]})]})]})})})}export{U as default};
//# sourceMappingURL=page-DPqb7IgJ.js.map
