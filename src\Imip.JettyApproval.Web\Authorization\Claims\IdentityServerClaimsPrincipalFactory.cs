using Imip.JettyApproval.Web.Services.Interfaces;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Security.Claims;
using System.Text.Json;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Security.Claims;
using Volo.Abp.Users;

namespace Imip.JettyApproval.Web.Authorization.Claims;

public class IdentityServerClaimsPrincipalFactory : AbpClaimsPrincipalFactory, ITransientDependency
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IConfiguration _configuration;
    private readonly ILogger<IdentityServerClaimsPrincipalFactory> _logger;
    private readonly ITokenService _tokenService;

    public IdentityServerClaimsPrincipalFactory(
        IServiceProvider serviceProvider,
        Microsoft.Extensions.Options.IOptions<AbpClaimsPrincipalFactoryOptions> options,
        IHttpClientFactory httpClientFactory,
        IConfiguration configuration,
        ITokenService tokenService,
        ILogger<IdentityServerClaimsPrincipalFactory> logger)
        : base(serviceProvider, options)
    {
        _httpClientFactory = httpClientFactory;
        _configuration = configuration;
        _logger = logger;
        _tokenService = tokenService;
    }

    public override async Task<ClaimsPrincipal> CreateAsync(ClaimsPrincipal? principal)
    {
        var claimsPrincipal = await base.CreateAsync(principal);

        // If the principal is not authenticated, return as is
        if (claimsPrincipal?.Identity?.IsAuthenticated != true)
        {
            return claimsPrincipal;
        }

        try
        {
            // Get the access token from the current request
            var accessToken = await _tokenService.GetAccessTokenAsync();
            if (string.IsNullOrEmpty(accessToken))
            {
                _logger.LogDebug("No access token found, returning base claims principal");
                return claimsPrincipal;
            }

            // Get current user information from Identity Server
            var userInfo = await GetCurrentUserFromIdentityServerAsync(accessToken);
            if (userInfo != null)
            {
                // Update the claims principal with Identity Server user information
                return UpdateClaimsPrincipalWithIdentityServerUser(claimsPrincipal, userInfo);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting current user from Identity Server");
        }

        return claimsPrincipal;
    }

    private async Task<IdentityServerUserInfo?> GetCurrentUserFromIdentityServerAsync(string accessToken)
    {
        try
        {
            var identityServerUrl = _configuration["AuthServer:Authority"];
            var userInfoEndpoint = $"{identityServerUrl}/api/abp/application-configuration";

            var client = _httpClientFactory.CreateClient("IdentityServer");
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
            client.Timeout = TimeSpan.FromSeconds(30);

            var response = await client.GetAsync(userInfoEndpoint);
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                using var document = JsonDocument.Parse(content);

                // Extract user information from the application configuration
                if (document.RootElement.TryGetProperty("currentUser", out var currentUserElement))
                {
                    var userInfo = new IdentityServerUserInfo();

                    if (currentUserElement.TryGetProperty("id", out var idElement))
                    {
                        userInfo.Id = idElement.GetString();
                    }

                    if (currentUserElement.TryGetProperty("userName", out var userNameElement))
                    {
                        userInfo.UserName = userNameElement.GetString();
                    }

                    if (currentUserElement.TryGetProperty("email", out var emailElement))
                    {
                        userInfo.Email = emailElement.GetString();
                    }

                    if (currentUserElement.TryGetProperty("name", out var nameElement))
                    {
                        userInfo.Name = nameElement.GetString();
                    }

                    if (currentUserElement.TryGetProperty("surname", out var surnameElement))
                    {
                        userInfo.Surname = surnameElement.GetString();
                    }

                    if (currentUserElement.TryGetProperty("tenantId", out var tenantIdElement))
                    {
                        userInfo.TenantId = tenantIdElement.GetString();
                    }

                    // Extract roles
                    if (currentUserElement.TryGetProperty("roles", out var rolesElement))
                    {
                        userInfo.Roles = rolesElement.EnumerateArray()
                            .Select(r => r.GetString())
                            .Where(r => !string.IsNullOrEmpty(r))
                            .ToList();

                        _logger.LogInformation("Found {Count} roles from Identity Server currentUser: [{Roles}]",
                            userInfo.Roles.Count, string.Join(", ", userInfo.Roles));
                    }
                    else
                    {
                        _logger.LogWarning("No roles property found in Identity Server currentUser response");
                        userInfo.Roles = new List<string>();
                    }

                    _logger.LogDebug("Retrieved user info from Identity Server: {UserName}", userInfo.UserName);
                    return userInfo;
                }
            }
            else
            {
                _logger.LogWarning("Failed to get user info from Identity Server. Status: {Status}", response.StatusCode);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting current user from Identity Server");
        }

        return null;
    }

    private ClaimsPrincipal UpdateClaimsPrincipalWithIdentityServerUser(ClaimsPrincipal claimsPrincipal, IdentityServerUserInfo userInfo)
    {
        if (claimsPrincipal.Identity is not ClaimsIdentity identity)
        {
            return claimsPrincipal;
        }

        // Log original claims for debugging
        _logger.LogDebug("Original claims before Identity Server update:");
        foreach (var claim in identity.Claims)
        {
            _logger.LogDebug("Original claim: {Type} = {Value}", claim.Type, claim.Value);
        }

        // Remove existing ABP claims to avoid duplicates
        var existingAbpClaims = identity.Claims
            .Where(c => c.Type == AbpClaimTypes.UserId ||
                       c.Type == AbpClaimTypes.UserName ||
                       c.Type == AbpClaimTypes.Email ||
                       c.Type == AbpClaimTypes.Name ||
                       c.Type == AbpClaimTypes.SurName ||
                       c.Type == AbpClaimTypes.TenantId)
            .ToList();

        foreach (var claim in existingAbpClaims)
        {
            identity.RemoveClaim(claim);
        }

        // Add Identity Server user information as ABP claims
        if (!string.IsNullOrEmpty(userInfo.Id))
        {
            identity.AddClaim(new Claim(AbpClaimTypes.UserId, userInfo.Id));
        }

        if (!string.IsNullOrEmpty(userInfo.UserName))
        {
            identity.AddClaim(new Claim(AbpClaimTypes.UserName, userInfo.UserName));
        }

        if (!string.IsNullOrEmpty(userInfo.Email))
        {
            identity.AddClaim(new Claim(AbpClaimTypes.Email, userInfo.Email));
        }

        // IMPORTANT: Use the original given_name claim if available, otherwise use Identity Server data
        var originalGivenName = identity.Claims.FirstOrDefault(c => c.Type == "given_name")?.Value;
        if (!string.IsNullOrEmpty(originalGivenName))
        {
            _logger.LogDebug("Using original given_name claim: {GivenName}", originalGivenName);
            identity.AddClaim(new Claim(AbpClaimTypes.Name, originalGivenName));
        }
        else if (!string.IsNullOrEmpty(userInfo.Name))
        {
            _logger.LogDebug("Using Identity Server name: {Name}", userInfo.Name);
            identity.AddClaim(new Claim(AbpClaimTypes.Name, userInfo.Name));
        }

        if (!string.IsNullOrEmpty(userInfo.Surname))
        {
            identity.AddClaim(new Claim(AbpClaimTypes.SurName, userInfo.Surname));
        }

        if (!string.IsNullOrEmpty(userInfo.TenantId))
        {
            identity.AddClaim(new Claim(AbpClaimTypes.TenantId, userInfo.TenantId));
        }

        // Add roles from Identity Server
        if (userInfo.Roles != null && userInfo.Roles.Count > 0)
        {
            _logger.LogInformation("Adding {Count} roles to claims principal: [{Roles}]",
                userInfo.Roles.Count, string.Join(", ", userInfo.Roles));

            foreach (var role in userInfo.Roles)
            {
                if (!identity.HasClaim(c => c.Type == ClaimTypes.Role && c.Value == role))
                {
                    identity.AddClaim(new Claim(ClaimTypes.Role, role));
                    _logger.LogDebug("Added role claim: {Role}", role);
                }
                else
                {
                    _logger.LogDebug("Role claim already exists: {Role}", role);
                }
            }
        }
        else
        {
            _logger.LogWarning("No roles to add to claims principal from Identity Server");
        }

        // Log final claims for debugging
        _logger.LogDebug("Final claims after Identity Server update:");
        foreach (var claim in identity.Claims)
        {
            _logger.LogDebug("Final claim: {Type} = {Value}", claim.Type, claim.Value);
        }

        _logger.LogDebug("Updated claims principal with Identity Server user: {UserName}", userInfo.UserName);
        return claimsPrincipal;
    }
}

public class IdentityServerUserInfo
{
    public string? Id { get; set; }
    public string? UserName { get; set; }
    public string? Email { get; set; }
    public string? Name { get; set; }
    public string? Surname { get; set; }
    public string? TenantId { get; set; }
    public List<string>? Roles { get; set; }
}