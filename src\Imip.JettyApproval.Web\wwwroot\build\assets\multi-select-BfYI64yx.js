import{r as a,j as g}from"./vendor-CrSBzUoz.js";import{B as je}from"./badge-BtBZs1VC.js";import{n as ge,M as F,B as Ne,X as me,N as Ie}from"./app-layout-CNB1Wtrx.js";import{u as B,P,R as Re,a as Me,O as $e,C as De,c as G}from"./radix-DaY-mnHi.js";import{P as Ae,b as Le,c as Pe}from"./popover-7NwOVASC.js";import{S as qe}from"./scroll-area-5IehhMpa.js";/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fe=[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]],Oe=ge("chevrons-up-down",Fe);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Te=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],Ke=ge("search",Te);function kt(e,n,t){if(!t||typeof t!="object"||!("operator"in t)||!(s=>typeof s=="object"&&s!==null&&"operator"in s&&"value"in s)(t))return!0;const o=e.getValue(n),{operator:i,value:d}=t;if(o==null)return i==="IsNull"?!0:i==="IsNotNull"?!1:i==="IsEmpty";let f="";if(typeof o=="object"&&o!==null)try{f=JSON.stringify(o).toLowerCase()}catch{f=`[${Object.prototype.toString.call(o)}]`}else typeof o=="number"||typeof o=="boolean"||typeof o=="string"?f=String(o).toLowerCase():f=`[${typeof o}]`;const c=d?String(d).toLowerCase():"";switch(i){case"Equals":return f===c;case"NotEquals":return f!==c;case"Contains":return f.includes(c);case"StartsWith":return f.startsWith(c);case"EndsWith":return f.endsWith(c);case"GreaterThan":return Number(o)>Number(d);case"GreaterThanOrEqual":return Number(o)>=Number(d);case"LessThan":return Number(o)<Number(d);case"LessThanOrEqual":return Number(o)<=Number(d);case"IsEmpty":return f==="";case"IsNotEmpty":return f!=="";case"IsNull":return o==null;case"IsNotNull":return o!=null;default:return!0}}var pe=1,Ve=.9,We=.8,Be=.17,re=.1,ne=.999,ze=.9999,_e=.99,Ge=/[\\\/_+.#"@\[\(\{&]/,Ye=/[\\\/_+.#"@\[\(\{&]/g,Ue=/[\s-]/,be=/[\s-]/g;function ae(e,n,t,v,o,i,d){if(i===n.length)return o===e.length?pe:_e;var f=`${o},${i}`;if(d[f]!==void 0)return d[f];for(var c=v.charAt(i),s=t.indexOf(c,o),b=0,x,E,C,R;s>=0;)x=ae(e,n,t,v,s+1,i+1,d),x>b&&(s===o?x*=pe:Ge.test(e.charAt(s-1))?(x*=We,C=e.slice(o,s-1).match(Ye),C&&o>0&&(x*=Math.pow(ne,C.length))):Ue.test(e.charAt(s-1))?(x*=Ve,R=e.slice(o,s-1).match(be),R&&o>0&&(x*=Math.pow(ne,R.length))):(x*=Be,o>0&&(x*=Math.pow(ne,s-o))),e.charAt(s)!==n.charAt(i)&&(x*=ze)),(x<re&&t.charAt(s-1)===v.charAt(i+1)||v.charAt(i+1)===v.charAt(i)&&t.charAt(s-1)!==v.charAt(i))&&(E=ae(e,n,t,v,s+1,i+2,d),E*re>x&&(x=E*re)),x>b&&(b=x),s=t.indexOf(c,s+1);return d[f]=b,b}function ve(e){return e.toLowerCase().replace(be," ")}function Xe(e,n,t){return e=t&&t.length>0?`${e+" "+t.join(" ")}`:e,ae(e,n,ve(e),ve(n),0,0,{})}var _='[cmdk-group=""]',le='[cmdk-group-items=""]',He='[cmdk-group-heading=""]',xe='[cmdk-item=""]',he=`${xe}:not([aria-disabled="true"])`,oe="cmdk-item-select",V="data-value",Je=(e,n,t)=>Xe(e,n,t),we=a.createContext(void 0),Y=()=>a.useContext(we),ye=a.createContext(void 0),se=()=>a.useContext(ye),ke=a.createContext(void 0),Ee=a.forwardRef((e,n)=>{let t=W(()=>{var r,m;return{search:"",value:(m=(r=e.value)!=null?r:e.defaultValue)!=null?m:"",selectedItemId:void 0,filtered:{count:0,items:new Map,groups:new Set}}}),v=W(()=>new Set),o=W(()=>new Map),i=W(()=>new Map),d=W(()=>new Set),f=Ce(e),{label:c,children:s,value:b,onValueChange:x,filter:E,shouldFilter:C,loop:R,disablePointerSelection:j=!1,vimBindings:I=!0,...M}=e,A=B(),X=B(),T=B(),$=a.useRef(null),k=ut();O(()=>{if(b!==void 0){let r=b.trim();t.current.value=r,l.emit()}},[b]),O(()=>{k(6,ue)},[]);let l=a.useMemo(()=>({subscribe:r=>(d.current.add(r),()=>d.current.delete(r)),snapshot:()=>t.current,setState:(r,m,h)=>{var u,w,y,N;if(!Object.is(t.current[r],m)){if(t.current[r]=m,r==="search")Z(),q(),k(1,Q);else if(r==="value"){if(document.activeElement.hasAttribute("cmdk-input")||document.activeElement.hasAttribute("cmdk-root")){let S=document.getElementById(T);S?S.focus():(u=document.getElementById(A))==null||u.focus()}if(k(7,()=>{var S;t.current.selectedItemId=(S=K())==null?void 0:S.id,l.emit()}),h||k(5,ue),((w=f.current)==null?void 0:w.value)!==void 0){let S=m??"";(N=(y=f.current).onValueChange)==null||N.call(y,S);return}}l.emit()}},emit:()=>{d.current.forEach(r=>r())}}),[]),p=a.useMemo(()=>({value:(r,m,h)=>{var u;m!==((u=i.current.get(r))==null?void 0:u.value)&&(i.current.set(r,{value:m,keywords:h}),t.current.filtered.items.set(r,D(m,h)),k(2,()=>{q(),l.emit()}))},item:(r,m)=>(v.current.add(r),m&&(o.current.has(m)?o.current.get(m).add(r):o.current.set(m,new Set([r]))),k(3,()=>{Z(),q(),t.current.value||Q(),l.emit()}),()=>{i.current.delete(r),v.current.delete(r),t.current.filtered.items.delete(r);let h=K();k(4,()=>{Z(),h?.getAttribute("id")===r&&Q(),l.emit()})}),group:r=>(o.current.has(r)||o.current.set(r,new Set),()=>{i.current.delete(r),o.current.delete(r)}),filter:()=>f.current.shouldFilter,label:c||e["aria-label"],getDisablePointerSelection:()=>f.current.disablePointerSelection,listId:A,inputId:T,labelId:X,listInnerRef:$}),[]);function D(r,m){var h,u;let w=(u=(h=f.current)==null?void 0:h.filter)!=null?u:Je;return r?w(r,t.current.search,m):0}function q(){if(!t.current.search||f.current.shouldFilter===!1)return;let r=t.current.filtered.items,m=[];t.current.filtered.groups.forEach(u=>{let w=o.current.get(u),y=0;w.forEach(N=>{let S=r.get(N);y=Math.max(S,y)}),m.push([u,y])});let h=$.current;z().sort((u,w)=>{var y,N;let S=u.getAttribute("id"),H=w.getAttribute("id");return((y=r.get(H))!=null?y:0)-((N=r.get(S))!=null?N:0)}).forEach(u=>{let w=u.closest(le);w?w.appendChild(u.parentElement===w?u:u.closest(`${le} > *`)):h.appendChild(u.parentElement===h?u:u.closest(`${le} > *`))}),m.sort((u,w)=>w[1]-u[1]).forEach(u=>{var w;let y=(w=$.current)==null?void 0:w.querySelector(`${_}[${V}="${encodeURIComponent(u[0])}"]`);y?.parentElement.appendChild(y)})}function Q(){let r=z().find(h=>h.getAttribute("aria-disabled")!=="true"),m=r?.getAttribute(V);l.setState("value",m||void 0)}function Z(){var r,m,h,u;if(!t.current.search||f.current.shouldFilter===!1){t.current.filtered.count=v.current.size;return}t.current.filtered.groups=new Set;let w=0;for(let y of v.current){let N=(m=(r=i.current.get(y))==null?void 0:r.value)!=null?m:"",S=(u=(h=i.current.get(y))==null?void 0:h.keywords)!=null?u:[],H=D(N,S);t.current.filtered.items.set(y,H),H>0&&w++}for(let[y,N]of o.current)for(let S of N)if(t.current.filtered.items.get(S)>0){t.current.filtered.groups.add(y);break}t.current.filtered.count=w}function ue(){var r,m,h;let u=K();u&&(((r=u.parentElement)==null?void 0:r.firstChild)===u&&((h=(m=u.closest(_))==null?void 0:m.querySelector(He))==null||h.scrollIntoView({block:"nearest"})),u.scrollIntoView({block:"nearest"}))}function K(){var r;return(r=$.current)==null?void 0:r.querySelector(`${xe}[aria-selected="true"]`)}function z(){var r;return Array.from(((r=$.current)==null?void 0:r.querySelectorAll(he))||[])}function ee(r){let m=z()[r];m&&l.setState("value",m.getAttribute(V))}function te(r){var m;let h=K(),u=z(),w=u.findIndex(N=>N===h),y=u[w+r];(m=f.current)!=null&&m.loop&&(y=w+r<0?u[u.length-1]:w+r===u.length?u[0]:u[w+r]),y&&l.setState("value",y.getAttribute(V))}function ie(r){let m=K(),h=m?.closest(_),u;for(;h&&!u;)h=r>0?ot(h,_):st(h,_),u=h?.querySelector(he);u?l.setState("value",u.getAttribute(V)):te(r)}let ce=()=>ee(z().length-1),de=r=>{r.preventDefault(),r.metaKey?ce():r.altKey?ie(1):te(1)},fe=r=>{r.preventDefault(),r.metaKey?ee(0):r.altKey?ie(-1):te(-1)};return a.createElement(P.div,{ref:n,tabIndex:-1,...M,"cmdk-root":"",onKeyDown:r=>{var m;(m=M.onKeyDown)==null||m.call(M,r);let h=r.nativeEvent.isComposing||r.keyCode===229;if(!(r.defaultPrevented||h))switch(r.key){case"n":case"j":{I&&r.ctrlKey&&de(r);break}case"ArrowDown":{de(r);break}case"p":case"k":{I&&r.ctrlKey&&fe(r);break}case"ArrowUp":{fe(r);break}case"Home":{r.preventDefault(),ee(0);break}case"End":{r.preventDefault(),ce();break}case"Enter":{r.preventDefault();let u=K();if(u){let w=new Event(oe);u.dispatchEvent(w)}}}}},a.createElement("label",{"cmdk-label":"",htmlFor:p.inputId,id:p.labelId,style:ct},c),J(e,r=>a.createElement(ye.Provider,{value:l},a.createElement(we.Provider,{value:p},r))))}),Qe=a.forwardRef((e,n)=>{var t,v;let o=B(),i=a.useRef(null),d=a.useContext(ke),f=Y(),c=Ce(e),s=(v=(t=c.current)==null?void 0:t.forceMount)!=null?v:d?.forceMount;O(()=>{if(!s)return f.item(o,d?.id)},[s]);let b=Se(o,i,[e.value,e.children,i],e.keywords),x=se(),E=L(k=>k.value&&k.value===b.current),C=L(k=>s||f.filter()===!1?!0:k.search?k.filtered.items.get(o)>0:!0);a.useEffect(()=>{let k=i.current;if(!(!k||e.disabled))return k.addEventListener(oe,R),()=>k.removeEventListener(oe,R)},[C,e.onSelect,e.disabled]);function R(){var k,l;j(),(l=(k=c.current).onSelect)==null||l.call(k,b.current)}function j(){x.setState("value",b.current,!0)}if(!C)return null;let{disabled:I,value:M,onSelect:A,forceMount:X,keywords:T,...$}=e;return a.createElement(P.div,{ref:G(i,n),...$,id:o,"cmdk-item":"",role:"option","aria-disabled":!!I,"aria-selected":!!E,"data-disabled":!!I,"data-selected":!!E,onPointerMove:I||f.getDisablePointerSelection()?void 0:j,onClick:I?void 0:R},e.children)}),Ze=a.forwardRef((e,n)=>{let{heading:t,children:v,forceMount:o,...i}=e,d=B(),f=a.useRef(null),c=a.useRef(null),s=B(),b=Y(),x=L(C=>o||b.filter()===!1?!0:C.search?C.filtered.groups.has(d):!0);O(()=>b.group(d),[]),Se(d,f,[e.value,e.heading,c]);let E=a.useMemo(()=>({id:d,forceMount:o}),[o]);return a.createElement(P.div,{ref:G(f,n),...i,"cmdk-group":"",role:"presentation",hidden:x?void 0:!0},t&&a.createElement("div",{ref:c,"cmdk-group-heading":"","aria-hidden":!0,id:s},t),J(e,C=>a.createElement("div",{"cmdk-group-items":"",role:"group","aria-labelledby":t?s:void 0},a.createElement(ke.Provider,{value:E},C))))}),et=a.forwardRef((e,n)=>{let{alwaysRender:t,...v}=e,o=a.useRef(null),i=L(d=>!d.search);return!t&&!i?null:a.createElement(P.div,{ref:G(o,n),...v,"cmdk-separator":"",role:"separator"})}),tt=a.forwardRef((e,n)=>{let{onValueChange:t,...v}=e,o=e.value!=null,i=se(),d=L(s=>s.search),f=L(s=>s.selectedItemId),c=Y();return a.useEffect(()=>{e.value!=null&&i.setState("search",e.value)},[e.value]),a.createElement(P.input,{ref:n,...v,"cmdk-input":"",autoComplete:"off",autoCorrect:"off",spellCheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":c.listId,"aria-labelledby":c.labelId,"aria-activedescendant":f,id:c.inputId,type:"text",value:o?e.value:d,onChange:s=>{o||i.setState("search",s.target.value),t?.(s.target.value)}})}),rt=a.forwardRef((e,n)=>{let{children:t,label:v="Suggestions",...o}=e,i=a.useRef(null),d=a.useRef(null),f=L(s=>s.selectedItemId),c=Y();return a.useEffect(()=>{if(d.current&&i.current){let s=d.current,b=i.current,x,E=new ResizeObserver(()=>{x=requestAnimationFrame(()=>{let C=s.offsetHeight;b.style.setProperty("--cmdk-list-height",C.toFixed(1)+"px")})});return E.observe(s),()=>{cancelAnimationFrame(x),E.unobserve(s)}}},[]),a.createElement(P.div,{ref:G(i,n),...o,"cmdk-list":"",role:"listbox",tabIndex:-1,"aria-activedescendant":f,"aria-label":v,id:c.listId},J(e,s=>a.createElement("div",{ref:G(d,c.listInnerRef),"cmdk-list-sizer":""},s)))}),nt=a.forwardRef((e,n)=>{let{open:t,onOpenChange:v,overlayClassName:o,contentClassName:i,container:d,...f}=e;return a.createElement(Re,{open:t,onOpenChange:v},a.createElement(Me,{container:d},a.createElement($e,{"cmdk-overlay":"",className:o}),a.createElement(De,{"aria-label":e.label,"cmdk-dialog":"",className:i},a.createElement(Ee,{ref:n,...f}))))}),lt=a.forwardRef((e,n)=>L(t=>t.filtered.count===0)?a.createElement(P.div,{ref:n,...e,"cmdk-empty":"",role:"presentation"}):null),at=a.forwardRef((e,n)=>{let{progress:t,children:v,label:o="Loading...",...i}=e;return a.createElement(P.div,{ref:n,...i,"cmdk-loading":"",role:"progressbar","aria-valuenow":t,"aria-valuemin":0,"aria-valuemax":100,"aria-label":o},J(e,d=>a.createElement("div",{"aria-hidden":!0},d)))}),U=Object.assign(Ee,{List:rt,Item:Qe,Input:tt,Group:Ze,Separator:et,Dialog:nt,Empty:lt,Loading:at});function ot(e,n){let t=e.nextElementSibling;for(;t;){if(t.matches(n))return t;t=t.nextElementSibling}}function st(e,n){let t=e.previousElementSibling;for(;t;){if(t.matches(n))return t;t=t.previousElementSibling}}function Ce(e){let n=a.useRef(e);return O(()=>{n.current=e}),n}var O=typeof window>"u"?a.useEffect:a.useLayoutEffect;function W(e){let n=a.useRef();return n.current===void 0&&(n.current=e()),n}function L(e){let n=se(),t=()=>e(n.snapshot());return a.useSyncExternalStore(n.subscribe,t,t)}function Se(e,n,t,v=[]){let o=a.useRef(),i=Y();return O(()=>{var d;let f=(()=>{var s;for(let b of t){if(typeof b=="string")return b.trim();if(typeof b=="object"&&"current"in b)return b.current?(s=b.current.textContent)==null?void 0:s.trim():o.current}})(),c=v.map(s=>s.trim());i.value(e,f,c),(d=n.current)==null||d.setAttribute(V,f),o.current=f}),o}var ut=()=>{let[e,n]=a.useState(),t=W(()=>new Map);return O(()=>{t.current.forEach(v=>v()),t.current=new Map},[e]),(v,o)=>{t.current.set(v,o),n({})}};function it(e){let n=e.type;return typeof n=="function"?n(e.props):"render"in n?n.render(e.props):e}function J({asChild:e,children:n},t){return e&&a.isValidElement(n)?a.cloneElement(it(n),{ref:n.ref},t(n.props.children)):t(n)}var ct={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"};function dt({className:e,...n}){return g.jsx(U,{"data-slot":"command",className:F("bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-md",e),...n})}function ft({className:e,...n}){return g.jsxs("div",{"data-slot":"command-input-wrapper",className:"flex h-9 items-center gap-2 border-b px-3",children:[g.jsx(Ke,{className:"size-4 shrink-0 opacity-50"}),g.jsx(U.Input,{"data-slot":"command-input",className:F("placeholder:text-muted-foreground flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-hidden disabled:cursor-not-allowed disabled:opacity-50",e),...n})]})}function mt({...e}){return g.jsx(U.Empty,{"data-slot":"command-empty",className:"py-6 text-center text-sm",...e})}function pt({className:e,...n}){return g.jsx(U.Group,{"data-slot":"command-group",className:F("text-foreground [&_[cmdk-group-heading]]:text-muted-foreground overflow-hidden p-1 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium",e),...n})}function vt({className:e,...n}){return g.jsx(U.Item,{"data-slot":"command-item",className:F("data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n})}const Et=({options:e=[],value:n=[],onChange:t,placeholder:v="Select options",className:o,disabled:i=!1,maxHeight:d=300,mode:f="multiple",name:c,register:s,valueAsNumber:b=!1})=>{const[x,E]=a.useState(!1),[C,R]=a.useState(""),j=a.useRef(null),I=a.useCallback(l=>{if(j.current){const p=j.current.querySelector("[data-radix-scroll-area-viewport]");p&&(l.preventDefault(),p.scrollTop+=l.deltaY)}},[]);a.useEffect(()=>{const l=j.current;if(x&&l)return l.addEventListener("wheel",I,{passive:!1}),()=>{l.removeEventListener("wheel",I)}},[x,I]);const M=f==="single",A=a.useMemo(()=>e.filter(l=>n.includes(l.value)).map(l=>({value:l.value,label:l.label})),[e,n]);a.useEffect(()=>{if(s&&c){const l=n.length>0?M?b?Number(n[0]):n[0]:n:void 0;if(s(c),l!==void 0){const p={target:{name:c,value:l}};s(c).onChange(p)}}},[s,c,M,b]);const X=a.useCallback(l=>{if(M){if(t([l]),s&&c){const p={target:{name:c,value:b?Number(l):l}};s(c).onChange(p)}E(!1)}else{const p=n.includes(l)?n.filter(D=>D!==l):[...n,l];if(t(p),s&&c){const D={target:{name:c,value:p}};s(c).onChange(D)}}},[n,t,M,E,s,c,b]),T=a.useCallback((l,p)=>{p?.preventDefault(),p?.stopPropagation();const D=n.filter(q=>q!==l);if(t(D),s&&c){const q={target:{name:c,value:D.length>0?D:void 0}};s(c).onChange(q)}},[n,t,s,c]),$=a.useCallback(l=>{if(l?.preventDefault(),l?.stopPropagation(),t([]),s&&c){const p={target:{name:c,value:void 0}};s(c).onChange(p)}},[t,s,c]),k=()=>{if(A.length===0)return g.jsx("span",{children:v});if(M&&A.length>0){const l=A[0]?.label??"";return g.jsx("span",{className:"text-foreground",children:l})}return g.jsx("div",{className:"flex flex-wrap gap-1 w-full",children:A.map(l=>g.jsxs(je,{variant:"secondary",className:"mr-1 mb-1 max-w-full overflow-hidden text-ellipsis whitespace-nowrap",children:[g.jsx("span",{className:"truncate",children:l.label}),g.jsx("span",{className:"ml-1 rounded-full outline-none hover:bg-muted cursor-pointer inline-flex items-center flex-shrink-0",onKeyDown:p=>{p.key==="Enter"&&T(l.value)},onMouseDown:p=>{p.preventDefault(),p.stopPropagation()},onClick:p=>T(l.value,p),role:"button",tabIndex:0,"aria-label":`Remove ${l.label}`,children:g.jsx(me,{className:"h-3 w-3"})})]},l.value))})};return g.jsxs(Ae,{open:x,onOpenChange:E,children:[g.jsx(Le,{asChild:!0,children:g.jsxs(Ne,{variant:"outline",role:"combobox","aria-expanded":x,className:F("w-full justify-between min-h-6 h-auto py-2",!n.length&&"text-muted-foreground",o),onClick:()=>E(!x),disabled:i,children:[g.jsx("div",{className:"flex flex-wrap gap-1 items-center w-full mr-2",children:k()}),g.jsxs("div",{className:"flex items-center flex-shrink-0",children:[A.length>0&&g.jsx("span",{className:"mr-1 rounded-full outline-none hover:bg-muted p-0.5 cursor-pointer inline-flex items-center",onKeyDown:l=>{l.key==="Enter"&&$()},onMouseDown:l=>{l.preventDefault(),l.stopPropagation()},onClick:l=>$(l),role:"button",tabIndex:0,"aria-label":"Clear all selections",children:g.jsx(me,{className:"h-4 w-4"})}),g.jsx(Oe,{className:"h-4 w-4 shrink-0 opacity-50"})]})]})}),g.jsx(Pe,{className:"p-0 w-[var(--radix-popover-trigger-width)] overflow-hidden",align:"start",sideOffset:5,onWheel:l=>{if(j.current){const p=j.current.querySelector("[data-radix-scroll-area-viewport]");p&&(p.scrollTop+=l.deltaY)}},children:g.jsxs(dt,{shouldFilter:!1,className:"max-h-full",onWheel:l=>{if(j.current){const p=j.current.querySelector("[data-radix-scroll-area-viewport]");p&&(p.scrollTop+=l.deltaY)}},children:[g.jsx(ft,{placeholder:"Search...",value:C,onValueChange:R,className:"h-9"}),g.jsx(mt,{children:"No options found"}),g.jsx(qe,{className:"overflow-hidden h-full custom-scrollbar",style:{height:`${d-40}px`,maxHeight:`${d-40}px`},ref:j,children:g.jsx(pt,{onWheel:l=>{if(j.current){const p=j.current.querySelector("[data-radix-scroll-area-viewport]");p&&(p.scrollTop+=l.deltaY)}},children:e.filter(l=>l.label.toLowerCase().includes(C.toLowerCase())).map(l=>{const p=n.includes(l.value);return g.jsxs(vt,{value:l.value,onSelect:()=>X(l.value),className:F("flex items-center gap-2",p?"bg-muted":""),children:[g.jsx("div",{className:F("flex h-4 w-4 items-center justify-center rounded-sm border",p?"bg-primary border-primary text-primary-foreground":"opacity-50"),children:p&&g.jsx(Ie,{className:"h-3 w-3"})}),g.jsx("span",{children:l.label})]},l.value)})})})]})})]})};export{Et as M,kt as c};
//# sourceMappingURL=multi-select-BfYI64yx.js.map
