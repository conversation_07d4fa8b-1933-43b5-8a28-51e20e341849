{"version": 3, "file": "dnd-DoiScGU0.js", "sources": ["../../../../../frontend/node_modules/.pnpm/@dnd-kit+utilities@3.2.2_react@19.1.0/node_modules/@dnd-kit/utilities/dist/utilities.esm.js", "../../../../../frontend/node_modules/.pnpm/@dnd-kit+accessibility@3.1.1_react@19.1.0/node_modules/@dnd-kit/accessibility/dist/accessibility.esm.js", "../../../../../frontend/node_modules/.pnpm/@dnd-kit+core@6.3.1_react-d_48e15fdc53b9c3dc1f051ea52fd46531/node_modules/@dnd-kit/core/dist/core.esm.js"], "sourcesContent": ["import { useMemo, useLayoutEffect, useEffect, useRef, useCallback } from 'react';\n\nfunction useCombinedRefs() {\n  for (var _len = arguments.length, refs = new Array(_len), _key = 0; _key < _len; _key++) {\n    refs[_key] = arguments[_key];\n  }\n\n  return useMemo(() => node => {\n    refs.forEach(ref => ref(node));\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  refs);\n}\n\n// https://github.com/facebook/react/blob/master/packages/shared/ExecutionEnvironment.js\nconst canUseDOM = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined';\n\nfunction isWindow(element) {\n  const elementString = Object.prototype.toString.call(element);\n  return elementString === '[object Window]' || // In Electron context the Window object serializes to [object global]\n  elementString === '[object global]';\n}\n\nfunction isNode(node) {\n  return 'nodeType' in node;\n}\n\nfunction getWindow(target) {\n  var _target$ownerDocument, _target$ownerDocument2;\n\n  if (!target) {\n    return window;\n  }\n\n  if (isWindow(target)) {\n    return target;\n  }\n\n  if (!isNode(target)) {\n    return window;\n  }\n\n  return (_target$ownerDocument = (_target$ownerDocument2 = target.ownerDocument) == null ? void 0 : _target$ownerDocument2.defaultView) != null ? _target$ownerDocument : window;\n}\n\nfunction isDocument(node) {\n  const {\n    Document\n  } = getWindow(node);\n  return node instanceof Document;\n}\n\nfunction isHTMLElement(node) {\n  if (isWindow(node)) {\n    return false;\n  }\n\n  return node instanceof getWindow(node).HTMLElement;\n}\n\nfunction isSVGElement(node) {\n  return node instanceof getWindow(node).SVGElement;\n}\n\nfunction getOwnerDocument(target) {\n  if (!target) {\n    return document;\n  }\n\n  if (isWindow(target)) {\n    return target.document;\n  }\n\n  if (!isNode(target)) {\n    return document;\n  }\n\n  if (isDocument(target)) {\n    return target;\n  }\n\n  if (isHTMLElement(target) || isSVGElement(target)) {\n    return target.ownerDocument;\n  }\n\n  return document;\n}\n\n/**\r\n * A hook that resolves to useEffect on the server and useLayoutEffect on the client\r\n * @param callback {function} Callback function that is invoked when the dependencies of the hook change\r\n */\n\nconst useIsomorphicLayoutEffect = canUseDOM ? useLayoutEffect : useEffect;\n\nfunction useEvent(handler) {\n  const handlerRef = useRef(handler);\n  useIsomorphicLayoutEffect(() => {\n    handlerRef.current = handler;\n  });\n  return useCallback(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    return handlerRef.current == null ? void 0 : handlerRef.current(...args);\n  }, []);\n}\n\nfunction useInterval() {\n  const intervalRef = useRef(null);\n  const set = useCallback((listener, duration) => {\n    intervalRef.current = setInterval(listener, duration);\n  }, []);\n  const clear = useCallback(() => {\n    if (intervalRef.current !== null) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n  }, []);\n  return [set, clear];\n}\n\nfunction useLatestValue(value, dependencies) {\n  if (dependencies === void 0) {\n    dependencies = [value];\n  }\n\n  const valueRef = useRef(value);\n  useIsomorphicLayoutEffect(() => {\n    if (valueRef.current !== value) {\n      valueRef.current = value;\n    }\n  }, dependencies);\n  return valueRef;\n}\n\nfunction useLazyMemo(callback, dependencies) {\n  const valueRef = useRef();\n  return useMemo(() => {\n    const newValue = callback(valueRef.current);\n    valueRef.current = newValue;\n    return newValue;\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [...dependencies]);\n}\n\nfunction useNodeRef(onChange) {\n  const onChangeHandler = useEvent(onChange);\n  const node = useRef(null);\n  const setNodeRef = useCallback(element => {\n    if (element !== node.current) {\n      onChangeHandler == null ? void 0 : onChangeHandler(element, node.current);\n    }\n\n    node.current = element;\n  }, //eslint-disable-next-line\n  []);\n  return [node, setNodeRef];\n}\n\nfunction usePrevious(value) {\n  const ref = useRef();\n  useEffect(() => {\n    ref.current = value;\n  }, [value]);\n  return ref.current;\n}\n\nlet ids = {};\nfunction useUniqueId(prefix, value) {\n  return useMemo(() => {\n    if (value) {\n      return value;\n    }\n\n    const id = ids[prefix] == null ? 0 : ids[prefix] + 1;\n    ids[prefix] = id;\n    return prefix + \"-\" + id;\n  }, [prefix, value]);\n}\n\nfunction createAdjustmentFn(modifier) {\n  return function (object) {\n    for (var _len = arguments.length, adjustments = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      adjustments[_key - 1] = arguments[_key];\n    }\n\n    return adjustments.reduce((accumulator, adjustment) => {\n      const entries = Object.entries(adjustment);\n\n      for (const [key, valueAdjustment] of entries) {\n        const value = accumulator[key];\n\n        if (value != null) {\n          accumulator[key] = value + modifier * valueAdjustment;\n        }\n      }\n\n      return accumulator;\n    }, { ...object\n    });\n  };\n}\n\nconst add = /*#__PURE__*/createAdjustmentFn(1);\nconst subtract = /*#__PURE__*/createAdjustmentFn(-1);\n\nfunction hasViewportRelativeCoordinates(event) {\n  return 'clientX' in event && 'clientY' in event;\n}\n\nfunction isKeyboardEvent(event) {\n  if (!event) {\n    return false;\n  }\n\n  const {\n    KeyboardEvent\n  } = getWindow(event.target);\n  return KeyboardEvent && event instanceof KeyboardEvent;\n}\n\nfunction isTouchEvent(event) {\n  if (!event) {\n    return false;\n  }\n\n  const {\n    TouchEvent\n  } = getWindow(event.target);\n  return TouchEvent && event instanceof TouchEvent;\n}\n\n/**\r\n * Returns the normalized x and y coordinates for mouse and touch events.\r\n */\n\nfunction getEventCoordinates(event) {\n  if (isTouchEvent(event)) {\n    if (event.touches && event.touches.length) {\n      const {\n        clientX: x,\n        clientY: y\n      } = event.touches[0];\n      return {\n        x,\n        y\n      };\n    } else if (event.changedTouches && event.changedTouches.length) {\n      const {\n        clientX: x,\n        clientY: y\n      } = event.changedTouches[0];\n      return {\n        x,\n        y\n      };\n    }\n  }\n\n  if (hasViewportRelativeCoordinates(event)) {\n    return {\n      x: event.clientX,\n      y: event.clientY\n    };\n  }\n\n  return null;\n}\n\nconst CSS = /*#__PURE__*/Object.freeze({\n  Translate: {\n    toString(transform) {\n      if (!transform) {\n        return;\n      }\n\n      const {\n        x,\n        y\n      } = transform;\n      return \"translate3d(\" + (x ? Math.round(x) : 0) + \"px, \" + (y ? Math.round(y) : 0) + \"px, 0)\";\n    }\n\n  },\n  Scale: {\n    toString(transform) {\n      if (!transform) {\n        return;\n      }\n\n      const {\n        scaleX,\n        scaleY\n      } = transform;\n      return \"scaleX(\" + scaleX + \") scaleY(\" + scaleY + \")\";\n    }\n\n  },\n  Transform: {\n    toString(transform) {\n      if (!transform) {\n        return;\n      }\n\n      return [CSS.Translate.toString(transform), CSS.Scale.toString(transform)].join(' ');\n    }\n\n  },\n  Transition: {\n    toString(_ref) {\n      let {\n        property,\n        duration,\n        easing\n      } = _ref;\n      return property + \" \" + duration + \"ms \" + easing;\n    }\n\n  }\n});\n\nconst SELECTOR = 'a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]';\nfunction findFirstFocusableNode(element) {\n  if (element.matches(SELECTOR)) {\n    return element;\n  }\n\n  return element.querySelector(SELECTOR);\n}\n\nexport { CSS, add, canUseDOM, findFirstFocusableNode, getEventCoordinates, getOwnerDocument, getWindow, hasViewportRelativeCoordinates, isDocument, isHTMLElement, isKeyboardEvent, isNode, isSVGElement, isTouchEvent, isWindow, subtract, useCombinedRefs, useEvent, useInterval, useIsomorphicLayoutEffect, useLatestValue, useLazyMemo, useNodeRef, usePrevious, useUniqueId };\n//# sourceMappingURL=utilities.esm.js.map\n", "import React, { useState, useCallback } from 'react';\n\nconst hiddenStyles = {\n  display: 'none'\n};\nfunction HiddenText(_ref) {\n  let {\n    id,\n    value\n  } = _ref;\n  return React.createElement(\"div\", {\n    id: id,\n    style: hiddenStyles\n  }, value);\n}\n\nfunction LiveRegion(_ref) {\n  let {\n    id,\n    announcement,\n    ariaLiveType = \"assertive\"\n  } = _ref;\n  // Hide element visually but keep it readable by screen readers\n  const visuallyHidden = {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    width: 1,\n    height: 1,\n    margin: -1,\n    border: 0,\n    padding: 0,\n    overflow: 'hidden',\n    clip: 'rect(0 0 0 0)',\n    clipPath: 'inset(100%)',\n    whiteSpace: 'nowrap'\n  };\n  return React.createElement(\"div\", {\n    id: id,\n    style: visuallyHidden,\n    role: \"status\",\n    \"aria-live\": ariaLiveType,\n    \"aria-atomic\": true\n  }, announcement);\n}\n\nfunction useAnnouncement() {\n  const [announcement, setAnnouncement] = useState('');\n  const announce = useCallback(value => {\n    if (value != null) {\n      setAnnouncement(value);\n    }\n  }, []);\n  return {\n    announce,\n    announcement\n  };\n}\n\nexport { HiddenText, LiveRegion, useAnnouncement };\n//# sourceMappingURL=accessibility.esm.js.map\n", "import React, { createContext, useContext, useEffect, useState, useCallback, useMemo, useRef, memo, useReducer, cloneElement, forwardRef } from 'react';\nimport { createPortal, unstable_batchedUpdates } from 'react-dom';\nimport { useUniqueId, getEventCoordinates, getWindow, isDocument, isHTMLElement, isSVGElement, canUseDOM, isWindow, isNode, getOwnerDocument, add, isKeyboardEvent, subtract, useLazyMemo, useInterval, usePrevious, useLatestValue, useEvent, useIsomorphicLayoutEffect, useNodeRef, findFirstFocusableNode, CSS } from '@dnd-kit/utilities';\nimport { useAnnouncement, HiddenText, LiveRegion } from '@dnd-kit/accessibility';\n\nconst DndMonitorContext = /*#__PURE__*/createContext(null);\n\nfunction useDndMonitor(listener) {\n  const registerListener = useContext(DndMonitorContext);\n  useEffect(() => {\n    if (!registerListener) {\n      throw new Error('useDndMonitor must be used within a children of <DndContext>');\n    }\n\n    const unsubscribe = registerListener(listener);\n    return unsubscribe;\n  }, [listener, registerListener]);\n}\n\nfunction useDndMonitorProvider() {\n  const [listeners] = useState(() => new Set());\n  const registerListener = useCallback(listener => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  }, [listeners]);\n  const dispatch = useCallback(_ref => {\n    let {\n      type,\n      event\n    } = _ref;\n    listeners.forEach(listener => {\n      var _listener$type;\n\n      return (_listener$type = listener[type]) == null ? void 0 : _listener$type.call(listener, event);\n    });\n  }, [listeners]);\n  return [dispatch, registerListener];\n}\n\nconst defaultScreenReaderInstructions = {\n  draggable: \"\\n    To pick up a draggable item, press the space bar.\\n    While dragging, use the arrow keys to move the item.\\n    Press space again to drop the item in its new position, or press escape to cancel.\\n  \"\n};\nconst defaultAnnouncements = {\n  onDragStart(_ref) {\n    let {\n      active\n    } = _ref;\n    return \"Picked up draggable item \" + active.id + \".\";\n  },\n\n  onDragOver(_ref2) {\n    let {\n      active,\n      over\n    } = _ref2;\n\n    if (over) {\n      return \"Draggable item \" + active.id + \" was moved over droppable area \" + over.id + \".\";\n    }\n\n    return \"Draggable item \" + active.id + \" is no longer over a droppable area.\";\n  },\n\n  onDragEnd(_ref3) {\n    let {\n      active,\n      over\n    } = _ref3;\n\n    if (over) {\n      return \"Draggable item \" + active.id + \" was dropped over droppable area \" + over.id;\n    }\n\n    return \"Draggable item \" + active.id + \" was dropped.\";\n  },\n\n  onDragCancel(_ref4) {\n    let {\n      active\n    } = _ref4;\n    return \"Dragging was cancelled. Draggable item \" + active.id + \" was dropped.\";\n  }\n\n};\n\nfunction Accessibility(_ref) {\n  let {\n    announcements = defaultAnnouncements,\n    container,\n    hiddenTextDescribedById,\n    screenReaderInstructions = defaultScreenReaderInstructions\n  } = _ref;\n  const {\n    announce,\n    announcement\n  } = useAnnouncement();\n  const liveRegionId = useUniqueId(\"DndLiveRegion\");\n  const [mounted, setMounted] = useState(false);\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n  useDndMonitor(useMemo(() => ({\n    onDragStart(_ref2) {\n      let {\n        active\n      } = _ref2;\n      announce(announcements.onDragStart({\n        active\n      }));\n    },\n\n    onDragMove(_ref3) {\n      let {\n        active,\n        over\n      } = _ref3;\n\n      if (announcements.onDragMove) {\n        announce(announcements.onDragMove({\n          active,\n          over\n        }));\n      }\n    },\n\n    onDragOver(_ref4) {\n      let {\n        active,\n        over\n      } = _ref4;\n      announce(announcements.onDragOver({\n        active,\n        over\n      }));\n    },\n\n    onDragEnd(_ref5) {\n      let {\n        active,\n        over\n      } = _ref5;\n      announce(announcements.onDragEnd({\n        active,\n        over\n      }));\n    },\n\n    onDragCancel(_ref6) {\n      let {\n        active,\n        over\n      } = _ref6;\n      announce(announcements.onDragCancel({\n        active,\n        over\n      }));\n    }\n\n  }), [announce, announcements]));\n\n  if (!mounted) {\n    return null;\n  }\n\n  const markup = React.createElement(React.Fragment, null, React.createElement(HiddenText, {\n    id: hiddenTextDescribedById,\n    value: screenReaderInstructions.draggable\n  }), React.createElement(LiveRegion, {\n    id: liveRegionId,\n    announcement: announcement\n  }));\n  return container ? createPortal(markup, container) : markup;\n}\n\nvar Action;\n\n(function (Action) {\n  Action[\"DragStart\"] = \"dragStart\";\n  Action[\"DragMove\"] = \"dragMove\";\n  Action[\"DragEnd\"] = \"dragEnd\";\n  Action[\"DragCancel\"] = \"dragCancel\";\n  Action[\"DragOver\"] = \"dragOver\";\n  Action[\"RegisterDroppable\"] = \"registerDroppable\";\n  Action[\"SetDroppableDisabled\"] = \"setDroppableDisabled\";\n  Action[\"UnregisterDroppable\"] = \"unregisterDroppable\";\n})(Action || (Action = {}));\n\nfunction noop() {}\n\nfunction useSensor(sensor, options) {\n  return useMemo(() => ({\n    sensor,\n    options: options != null ? options : {}\n  }), // eslint-disable-next-line react-hooks/exhaustive-deps\n  [sensor, options]);\n}\n\nfunction useSensors() {\n  for (var _len = arguments.length, sensors = new Array(_len), _key = 0; _key < _len; _key++) {\n    sensors[_key] = arguments[_key];\n  }\n\n  return useMemo(() => [...sensors].filter(sensor => sensor != null), // eslint-disable-next-line react-hooks/exhaustive-deps\n  [...sensors]);\n}\n\nconst defaultCoordinates = /*#__PURE__*/Object.freeze({\n  x: 0,\n  y: 0\n});\n\n/**\r\n * Returns the distance between two points\r\n */\nfunction distanceBetween(p1, p2) {\n  return Math.sqrt(Math.pow(p1.x - p2.x, 2) + Math.pow(p1.y - p2.y, 2));\n}\n\nfunction getRelativeTransformOrigin(event, rect) {\n  const eventCoordinates = getEventCoordinates(event);\n\n  if (!eventCoordinates) {\n    return '0 0';\n  }\n\n  const transformOrigin = {\n    x: (eventCoordinates.x - rect.left) / rect.width * 100,\n    y: (eventCoordinates.y - rect.top) / rect.height * 100\n  };\n  return transformOrigin.x + \"% \" + transformOrigin.y + \"%\";\n}\n\n/**\r\n * Sort collisions from smallest to greatest value\r\n */\nfunction sortCollisionsAsc(_ref, _ref2) {\n  let {\n    data: {\n      value: a\n    }\n  } = _ref;\n  let {\n    data: {\n      value: b\n    }\n  } = _ref2;\n  return a - b;\n}\n/**\r\n * Sort collisions from greatest to smallest value\r\n */\n\nfunction sortCollisionsDesc(_ref3, _ref4) {\n  let {\n    data: {\n      value: a\n    }\n  } = _ref3;\n  let {\n    data: {\n      value: b\n    }\n  } = _ref4;\n  return b - a;\n}\n/**\r\n * Returns the coordinates of the corners of a given rectangle:\r\n * [TopLeft {x, y}, TopRight {x, y}, BottomLeft {x, y}, BottomRight {x, y}]\r\n */\n\nfunction cornersOfRectangle(_ref5) {\n  let {\n    left,\n    top,\n    height,\n    width\n  } = _ref5;\n  return [{\n    x: left,\n    y: top\n  }, {\n    x: left + width,\n    y: top\n  }, {\n    x: left,\n    y: top + height\n  }, {\n    x: left + width,\n    y: top + height\n  }];\n}\nfunction getFirstCollision(collisions, property) {\n  if (!collisions || collisions.length === 0) {\n    return null;\n  }\n\n  const [firstCollision] = collisions;\n  return property ? firstCollision[property] : firstCollision;\n}\n\n/**\r\n * Returns the coordinates of the center of a given ClientRect\r\n */\n\nfunction centerOfRectangle(rect, left, top) {\n  if (left === void 0) {\n    left = rect.left;\n  }\n\n  if (top === void 0) {\n    top = rect.top;\n  }\n\n  return {\n    x: left + rect.width * 0.5,\n    y: top + rect.height * 0.5\n  };\n}\n/**\r\n * Returns the closest rectangles from an array of rectangles to the center of a given\r\n * rectangle.\r\n */\n\n\nconst closestCenter = _ref => {\n  let {\n    collisionRect,\n    droppableRects,\n    droppableContainers\n  } = _ref;\n  const centerRect = centerOfRectangle(collisionRect, collisionRect.left, collisionRect.top);\n  const collisions = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {\n      id\n    } = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const distBetween = distanceBetween(centerOfRectangle(rect), centerRect);\n      collisions.push({\n        id,\n        data: {\n          droppableContainer,\n          value: distBetween\n        }\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n\n/**\r\n * Returns the closest rectangles from an array of rectangles to the corners of\r\n * another rectangle.\r\n */\n\nconst closestCorners = _ref => {\n  let {\n    collisionRect,\n    droppableRects,\n    droppableContainers\n  } = _ref;\n  const corners = cornersOfRectangle(collisionRect);\n  const collisions = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {\n      id\n    } = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const rectCorners = cornersOfRectangle(rect);\n      const distances = corners.reduce((accumulator, corner, index) => {\n        return accumulator + distanceBetween(rectCorners[index], corner);\n      }, 0);\n      const effectiveDistance = Number((distances / 4).toFixed(4));\n      collisions.push({\n        id,\n        data: {\n          droppableContainer,\n          value: effectiveDistance\n        }\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n\n/**\r\n * Returns the intersecting rectangle area between two rectangles\r\n */\n\nfunction getIntersectionRatio(entry, target) {\n  const top = Math.max(target.top, entry.top);\n  const left = Math.max(target.left, entry.left);\n  const right = Math.min(target.left + target.width, entry.left + entry.width);\n  const bottom = Math.min(target.top + target.height, entry.top + entry.height);\n  const width = right - left;\n  const height = bottom - top;\n\n  if (left < right && top < bottom) {\n    const targetArea = target.width * target.height;\n    const entryArea = entry.width * entry.height;\n    const intersectionArea = width * height;\n    const intersectionRatio = intersectionArea / (targetArea + entryArea - intersectionArea);\n    return Number(intersectionRatio.toFixed(4));\n  } // Rectangles do not overlap, or overlap has an area of zero (edge/corner overlap)\n\n\n  return 0;\n}\n/**\r\n * Returns the rectangles that has the greatest intersection area with a given\r\n * rectangle in an array of rectangles.\r\n */\n\nconst rectIntersection = _ref => {\n  let {\n    collisionRect,\n    droppableRects,\n    droppableContainers\n  } = _ref;\n  const collisions = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {\n      id\n    } = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const intersectionRatio = getIntersectionRatio(rect, collisionRect);\n\n      if (intersectionRatio > 0) {\n        collisions.push({\n          id,\n          data: {\n            droppableContainer,\n            value: intersectionRatio\n          }\n        });\n      }\n    }\n  }\n\n  return collisions.sort(sortCollisionsDesc);\n};\n\n/**\r\n * Check if a given point is contained within a bounding rectangle\r\n */\n\nfunction isPointWithinRect(point, rect) {\n  const {\n    top,\n    left,\n    bottom,\n    right\n  } = rect;\n  return top <= point.y && point.y <= bottom && left <= point.x && point.x <= right;\n}\n/**\r\n * Returns the rectangles that the pointer is hovering over\r\n */\n\n\nconst pointerWithin = _ref => {\n  let {\n    droppableContainers,\n    droppableRects,\n    pointerCoordinates\n  } = _ref;\n\n  if (!pointerCoordinates) {\n    return [];\n  }\n\n  const collisions = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {\n      id\n    } = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect && isPointWithinRect(pointerCoordinates, rect)) {\n      /* There may be more than a single rectangle intersecting\r\n       * with the pointer coordinates. In order to sort the\r\n       * colliding rectangles, we measure the distance between\r\n       * the pointer and the corners of the intersecting rectangle\r\n       */\n      const corners = cornersOfRectangle(rect);\n      const distances = corners.reduce((accumulator, corner) => {\n        return accumulator + distanceBetween(pointerCoordinates, corner);\n      }, 0);\n      const effectiveDistance = Number((distances / 4).toFixed(4));\n      collisions.push({\n        id,\n        data: {\n          droppableContainer,\n          value: effectiveDistance\n        }\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n\nfunction adjustScale(transform, rect1, rect2) {\n  return { ...transform,\n    scaleX: rect1 && rect2 ? rect1.width / rect2.width : 1,\n    scaleY: rect1 && rect2 ? rect1.height / rect2.height : 1\n  };\n}\n\nfunction getRectDelta(rect1, rect2) {\n  return rect1 && rect2 ? {\n    x: rect1.left - rect2.left,\n    y: rect1.top - rect2.top\n  } : defaultCoordinates;\n}\n\nfunction createRectAdjustmentFn(modifier) {\n  return function adjustClientRect(rect) {\n    for (var _len = arguments.length, adjustments = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      adjustments[_key - 1] = arguments[_key];\n    }\n\n    return adjustments.reduce((acc, adjustment) => ({ ...acc,\n      top: acc.top + modifier * adjustment.y,\n      bottom: acc.bottom + modifier * adjustment.y,\n      left: acc.left + modifier * adjustment.x,\n      right: acc.right + modifier * adjustment.x\n    }), { ...rect\n    });\n  };\n}\nconst getAdjustedRect = /*#__PURE__*/createRectAdjustmentFn(1);\n\nfunction parseTransform(transform) {\n  if (transform.startsWith('matrix3d(')) {\n    const transformArray = transform.slice(9, -1).split(/, /);\n    return {\n      x: +transformArray[12],\n      y: +transformArray[13],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[5]\n    };\n  } else if (transform.startsWith('matrix(')) {\n    const transformArray = transform.slice(7, -1).split(/, /);\n    return {\n      x: +transformArray[4],\n      y: +transformArray[5],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[3]\n    };\n  }\n\n  return null;\n}\n\nfunction inverseTransform(rect, transform, transformOrigin) {\n  const parsedTransform = parseTransform(transform);\n\n  if (!parsedTransform) {\n    return rect;\n  }\n\n  const {\n    scaleX,\n    scaleY,\n    x: translateX,\n    y: translateY\n  } = parsedTransform;\n  const x = rect.left - translateX - (1 - scaleX) * parseFloat(transformOrigin);\n  const y = rect.top - translateY - (1 - scaleY) * parseFloat(transformOrigin.slice(transformOrigin.indexOf(' ') + 1));\n  const w = scaleX ? rect.width / scaleX : rect.width;\n  const h = scaleY ? rect.height / scaleY : rect.height;\n  return {\n    width: w,\n    height: h,\n    top: y,\n    right: x + w,\n    bottom: y + h,\n    left: x\n  };\n}\n\nconst defaultOptions = {\n  ignoreTransform: false\n};\n/**\r\n * Returns the bounding client rect of an element relative to the viewport.\r\n */\n\nfunction getClientRect(element, options) {\n  if (options === void 0) {\n    options = defaultOptions;\n  }\n\n  let rect = element.getBoundingClientRect();\n\n  if (options.ignoreTransform) {\n    const {\n      transform,\n      transformOrigin\n    } = getWindow(element).getComputedStyle(element);\n\n    if (transform) {\n      rect = inverseTransform(rect, transform, transformOrigin);\n    }\n  }\n\n  const {\n    top,\n    left,\n    width,\n    height,\n    bottom,\n    right\n  } = rect;\n  return {\n    top,\n    left,\n    width,\n    height,\n    bottom,\n    right\n  };\n}\n/**\r\n * Returns the bounding client rect of an element relative to the viewport.\r\n *\r\n * @remarks\r\n * The ClientRect returned by this method does not take into account transforms\r\n * applied to the element it measures.\r\n *\r\n */\n\nfunction getTransformAgnosticClientRect(element) {\n  return getClientRect(element, {\n    ignoreTransform: true\n  });\n}\n\nfunction getWindowClientRect(element) {\n  const width = element.innerWidth;\n  const height = element.innerHeight;\n  return {\n    top: 0,\n    left: 0,\n    right: width,\n    bottom: height,\n    width,\n    height\n  };\n}\n\nfunction isFixed(node, computedStyle) {\n  if (computedStyle === void 0) {\n    computedStyle = getWindow(node).getComputedStyle(node);\n  }\n\n  return computedStyle.position === 'fixed';\n}\n\nfunction isScrollable(element, computedStyle) {\n  if (computedStyle === void 0) {\n    computedStyle = getWindow(element).getComputedStyle(element);\n  }\n\n  const overflowRegex = /(auto|scroll|overlay)/;\n  const properties = ['overflow', 'overflowX', 'overflowY'];\n  return properties.some(property => {\n    const value = computedStyle[property];\n    return typeof value === 'string' ? overflowRegex.test(value) : false;\n  });\n}\n\nfunction getScrollableAncestors(element, limit) {\n  const scrollParents = [];\n\n  function findScrollableAncestors(node) {\n    if (limit != null && scrollParents.length >= limit) {\n      return scrollParents;\n    }\n\n    if (!node) {\n      return scrollParents;\n    }\n\n    if (isDocument(node) && node.scrollingElement != null && !scrollParents.includes(node.scrollingElement)) {\n      scrollParents.push(node.scrollingElement);\n      return scrollParents;\n    }\n\n    if (!isHTMLElement(node) || isSVGElement(node)) {\n      return scrollParents;\n    }\n\n    if (scrollParents.includes(node)) {\n      return scrollParents;\n    }\n\n    const computedStyle = getWindow(element).getComputedStyle(node);\n\n    if (node !== element) {\n      if (isScrollable(node, computedStyle)) {\n        scrollParents.push(node);\n      }\n    }\n\n    if (isFixed(node, computedStyle)) {\n      return scrollParents;\n    }\n\n    return findScrollableAncestors(node.parentNode);\n  }\n\n  if (!element) {\n    return scrollParents;\n  }\n\n  return findScrollableAncestors(element);\n}\nfunction getFirstScrollableAncestor(node) {\n  const [firstScrollableAncestor] = getScrollableAncestors(node, 1);\n  return firstScrollableAncestor != null ? firstScrollableAncestor : null;\n}\n\nfunction getScrollableElement(element) {\n  if (!canUseDOM || !element) {\n    return null;\n  }\n\n  if (isWindow(element)) {\n    return element;\n  }\n\n  if (!isNode(element)) {\n    return null;\n  }\n\n  if (isDocument(element) || element === getOwnerDocument(element).scrollingElement) {\n    return window;\n  }\n\n  if (isHTMLElement(element)) {\n    return element;\n  }\n\n  return null;\n}\n\nfunction getScrollXCoordinate(element) {\n  if (isWindow(element)) {\n    return element.scrollX;\n  }\n\n  return element.scrollLeft;\n}\nfunction getScrollYCoordinate(element) {\n  if (isWindow(element)) {\n    return element.scrollY;\n  }\n\n  return element.scrollTop;\n}\nfunction getScrollCoordinates(element) {\n  return {\n    x: getScrollXCoordinate(element),\n    y: getScrollYCoordinate(element)\n  };\n}\n\nvar Direction;\n\n(function (Direction) {\n  Direction[Direction[\"Forward\"] = 1] = \"Forward\";\n  Direction[Direction[\"Backward\"] = -1] = \"Backward\";\n})(Direction || (Direction = {}));\n\nfunction isDocumentScrollingElement(element) {\n  if (!canUseDOM || !element) {\n    return false;\n  }\n\n  return element === document.scrollingElement;\n}\n\nfunction getScrollPosition(scrollingContainer) {\n  const minScroll = {\n    x: 0,\n    y: 0\n  };\n  const dimensions = isDocumentScrollingElement(scrollingContainer) ? {\n    height: window.innerHeight,\n    width: window.innerWidth\n  } : {\n    height: scrollingContainer.clientHeight,\n    width: scrollingContainer.clientWidth\n  };\n  const maxScroll = {\n    x: scrollingContainer.scrollWidth - dimensions.width,\n    y: scrollingContainer.scrollHeight - dimensions.height\n  };\n  const isTop = scrollingContainer.scrollTop <= minScroll.y;\n  const isLeft = scrollingContainer.scrollLeft <= minScroll.x;\n  const isBottom = scrollingContainer.scrollTop >= maxScroll.y;\n  const isRight = scrollingContainer.scrollLeft >= maxScroll.x;\n  return {\n    isTop,\n    isLeft,\n    isBottom,\n    isRight,\n    maxScroll,\n    minScroll\n  };\n}\n\nconst defaultThreshold = {\n  x: 0.2,\n  y: 0.2\n};\nfunction getScrollDirectionAndSpeed(scrollContainer, scrollContainerRect, _ref, acceleration, thresholdPercentage) {\n  let {\n    top,\n    left,\n    right,\n    bottom\n  } = _ref;\n\n  if (acceleration === void 0) {\n    acceleration = 10;\n  }\n\n  if (thresholdPercentage === void 0) {\n    thresholdPercentage = defaultThreshold;\n  }\n\n  const {\n    isTop,\n    isBottom,\n    isLeft,\n    isRight\n  } = getScrollPosition(scrollContainer);\n  const direction = {\n    x: 0,\n    y: 0\n  };\n  const speed = {\n    x: 0,\n    y: 0\n  };\n  const threshold = {\n    height: scrollContainerRect.height * thresholdPercentage.y,\n    width: scrollContainerRect.width * thresholdPercentage.x\n  };\n\n  if (!isTop && top <= scrollContainerRect.top + threshold.height) {\n    // Scroll Up\n    direction.y = Direction.Backward;\n    speed.y = acceleration * Math.abs((scrollContainerRect.top + threshold.height - top) / threshold.height);\n  } else if (!isBottom && bottom >= scrollContainerRect.bottom - threshold.height) {\n    // Scroll Down\n    direction.y = Direction.Forward;\n    speed.y = acceleration * Math.abs((scrollContainerRect.bottom - threshold.height - bottom) / threshold.height);\n  }\n\n  if (!isRight && right >= scrollContainerRect.right - threshold.width) {\n    // Scroll Right\n    direction.x = Direction.Forward;\n    speed.x = acceleration * Math.abs((scrollContainerRect.right - threshold.width - right) / threshold.width);\n  } else if (!isLeft && left <= scrollContainerRect.left + threshold.width) {\n    // Scroll Left\n    direction.x = Direction.Backward;\n    speed.x = acceleration * Math.abs((scrollContainerRect.left + threshold.width - left) / threshold.width);\n  }\n\n  return {\n    direction,\n    speed\n  };\n}\n\nfunction getScrollElementRect(element) {\n  if (element === document.scrollingElement) {\n    const {\n      innerWidth,\n      innerHeight\n    } = window;\n    return {\n      top: 0,\n      left: 0,\n      right: innerWidth,\n      bottom: innerHeight,\n      width: innerWidth,\n      height: innerHeight\n    };\n  }\n\n  const {\n    top,\n    left,\n    right,\n    bottom\n  } = element.getBoundingClientRect();\n  return {\n    top,\n    left,\n    right,\n    bottom,\n    width: element.clientWidth,\n    height: element.clientHeight\n  };\n}\n\nfunction getScrollOffsets(scrollableAncestors) {\n  return scrollableAncestors.reduce((acc, node) => {\n    return add(acc, getScrollCoordinates(node));\n  }, defaultCoordinates);\n}\nfunction getScrollXOffset(scrollableAncestors) {\n  return scrollableAncestors.reduce((acc, node) => {\n    return acc + getScrollXCoordinate(node);\n  }, 0);\n}\nfunction getScrollYOffset(scrollableAncestors) {\n  return scrollableAncestors.reduce((acc, node) => {\n    return acc + getScrollYCoordinate(node);\n  }, 0);\n}\n\nfunction scrollIntoViewIfNeeded(element, measure) {\n  if (measure === void 0) {\n    measure = getClientRect;\n  }\n\n  if (!element) {\n    return;\n  }\n\n  const {\n    top,\n    left,\n    bottom,\n    right\n  } = measure(element);\n  const firstScrollableAncestor = getFirstScrollableAncestor(element);\n\n  if (!firstScrollableAncestor) {\n    return;\n  }\n\n  if (bottom <= 0 || right <= 0 || top >= window.innerHeight || left >= window.innerWidth) {\n    element.scrollIntoView({\n      block: 'center',\n      inline: 'center'\n    });\n  }\n}\n\nconst properties = [['x', ['left', 'right'], getScrollXOffset], ['y', ['top', 'bottom'], getScrollYOffset]];\nclass Rect {\n  constructor(rect, element) {\n    this.rect = void 0;\n    this.width = void 0;\n    this.height = void 0;\n    this.top = void 0;\n    this.bottom = void 0;\n    this.right = void 0;\n    this.left = void 0;\n    const scrollableAncestors = getScrollableAncestors(element);\n    const scrollOffsets = getScrollOffsets(scrollableAncestors);\n    this.rect = { ...rect\n    };\n    this.width = rect.width;\n    this.height = rect.height;\n\n    for (const [axis, keys, getScrollOffset] of properties) {\n      for (const key of keys) {\n        Object.defineProperty(this, key, {\n          get: () => {\n            const currentOffsets = getScrollOffset(scrollableAncestors);\n            const scrollOffsetsDeltla = scrollOffsets[axis] - currentOffsets;\n            return this.rect[key] + scrollOffsetsDeltla;\n          },\n          enumerable: true\n        });\n      }\n    }\n\n    Object.defineProperty(this, 'rect', {\n      enumerable: false\n    });\n  }\n\n}\n\nclass Listeners {\n  constructor(target) {\n    this.target = void 0;\n    this.listeners = [];\n\n    this.removeAll = () => {\n      this.listeners.forEach(listener => {\n        var _this$target;\n\n        return (_this$target = this.target) == null ? void 0 : _this$target.removeEventListener(...listener);\n      });\n    };\n\n    this.target = target;\n  }\n\n  add(eventName, handler, options) {\n    var _this$target2;\n\n    (_this$target2 = this.target) == null ? void 0 : _this$target2.addEventListener(eventName, handler, options);\n    this.listeners.push([eventName, handler, options]);\n  }\n\n}\n\nfunction getEventListenerTarget(target) {\n  // If the `event.target` element is removed from the document events will still be targeted\n  // at it, and hence won't always bubble up to the window or document anymore.\n  // If there is any risk of an element being removed while it is being dragged,\n  // the best practice is to attach the event listeners directly to the target.\n  // https://developer.mozilla.org/en-US/docs/Web/API/EventTarget\n  const {\n    EventTarget\n  } = getWindow(target);\n  return target instanceof EventTarget ? target : getOwnerDocument(target);\n}\n\nfunction hasExceededDistance(delta, measurement) {\n  const dx = Math.abs(delta.x);\n  const dy = Math.abs(delta.y);\n\n  if (typeof measurement === 'number') {\n    return Math.sqrt(dx ** 2 + dy ** 2) > measurement;\n  }\n\n  if ('x' in measurement && 'y' in measurement) {\n    return dx > measurement.x && dy > measurement.y;\n  }\n\n  if ('x' in measurement) {\n    return dx > measurement.x;\n  }\n\n  if ('y' in measurement) {\n    return dy > measurement.y;\n  }\n\n  return false;\n}\n\nvar EventName;\n\n(function (EventName) {\n  EventName[\"Click\"] = \"click\";\n  EventName[\"DragStart\"] = \"dragstart\";\n  EventName[\"Keydown\"] = \"keydown\";\n  EventName[\"ContextMenu\"] = \"contextmenu\";\n  EventName[\"Resize\"] = \"resize\";\n  EventName[\"SelectionChange\"] = \"selectionchange\";\n  EventName[\"VisibilityChange\"] = \"visibilitychange\";\n})(EventName || (EventName = {}));\n\nfunction preventDefault(event) {\n  event.preventDefault();\n}\nfunction stopPropagation(event) {\n  event.stopPropagation();\n}\n\nvar KeyboardCode;\n\n(function (KeyboardCode) {\n  KeyboardCode[\"Space\"] = \"Space\";\n  KeyboardCode[\"Down\"] = \"ArrowDown\";\n  KeyboardCode[\"Right\"] = \"ArrowRight\";\n  KeyboardCode[\"Left\"] = \"ArrowLeft\";\n  KeyboardCode[\"Up\"] = \"ArrowUp\";\n  KeyboardCode[\"Esc\"] = \"Escape\";\n  KeyboardCode[\"Enter\"] = \"Enter\";\n  KeyboardCode[\"Tab\"] = \"Tab\";\n})(KeyboardCode || (KeyboardCode = {}));\n\nconst defaultKeyboardCodes = {\n  start: [KeyboardCode.Space, KeyboardCode.Enter],\n  cancel: [KeyboardCode.Esc],\n  end: [KeyboardCode.Space, KeyboardCode.Enter, KeyboardCode.Tab]\n};\nconst defaultKeyboardCoordinateGetter = (event, _ref) => {\n  let {\n    currentCoordinates\n  } = _ref;\n\n  switch (event.code) {\n    case KeyboardCode.Right:\n      return { ...currentCoordinates,\n        x: currentCoordinates.x + 25\n      };\n\n    case KeyboardCode.Left:\n      return { ...currentCoordinates,\n        x: currentCoordinates.x - 25\n      };\n\n    case KeyboardCode.Down:\n      return { ...currentCoordinates,\n        y: currentCoordinates.y + 25\n      };\n\n    case KeyboardCode.Up:\n      return { ...currentCoordinates,\n        y: currentCoordinates.y - 25\n      };\n  }\n\n  return undefined;\n};\n\nclass KeyboardSensor {\n  constructor(props) {\n    this.props = void 0;\n    this.autoScrollEnabled = false;\n    this.referenceCoordinates = void 0;\n    this.listeners = void 0;\n    this.windowListeners = void 0;\n    this.props = props;\n    const {\n      event: {\n        target\n      }\n    } = props;\n    this.props = props;\n    this.listeners = new Listeners(getOwnerDocument(target));\n    this.windowListeners = new Listeners(getWindow(target));\n    this.handleKeyDown = this.handleKeyDown.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n    this.attach();\n  }\n\n  attach() {\n    this.handleStart();\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n    setTimeout(() => this.listeners.add(EventName.Keydown, this.handleKeyDown));\n  }\n\n  handleStart() {\n    const {\n      activeNode,\n      onStart\n    } = this.props;\n    const node = activeNode.node.current;\n\n    if (node) {\n      scrollIntoViewIfNeeded(node);\n    }\n\n    onStart(defaultCoordinates);\n  }\n\n  handleKeyDown(event) {\n    if (isKeyboardEvent(event)) {\n      const {\n        active,\n        context,\n        options\n      } = this.props;\n      const {\n        keyboardCodes = defaultKeyboardCodes,\n        coordinateGetter = defaultKeyboardCoordinateGetter,\n        scrollBehavior = 'smooth'\n      } = options;\n      const {\n        code\n      } = event;\n\n      if (keyboardCodes.end.includes(code)) {\n        this.handleEnd(event);\n        return;\n      }\n\n      if (keyboardCodes.cancel.includes(code)) {\n        this.handleCancel(event);\n        return;\n      }\n\n      const {\n        collisionRect\n      } = context.current;\n      const currentCoordinates = collisionRect ? {\n        x: collisionRect.left,\n        y: collisionRect.top\n      } : defaultCoordinates;\n\n      if (!this.referenceCoordinates) {\n        this.referenceCoordinates = currentCoordinates;\n      }\n\n      const newCoordinates = coordinateGetter(event, {\n        active,\n        context: context.current,\n        currentCoordinates\n      });\n\n      if (newCoordinates) {\n        const coordinatesDelta = subtract(newCoordinates, currentCoordinates);\n        const scrollDelta = {\n          x: 0,\n          y: 0\n        };\n        const {\n          scrollableAncestors\n        } = context.current;\n\n        for (const scrollContainer of scrollableAncestors) {\n          const direction = event.code;\n          const {\n            isTop,\n            isRight,\n            isLeft,\n            isBottom,\n            maxScroll,\n            minScroll\n          } = getScrollPosition(scrollContainer);\n          const scrollElementRect = getScrollElementRect(scrollContainer);\n          const clampedCoordinates = {\n            x: Math.min(direction === KeyboardCode.Right ? scrollElementRect.right - scrollElementRect.width / 2 : scrollElementRect.right, Math.max(direction === KeyboardCode.Right ? scrollElementRect.left : scrollElementRect.left + scrollElementRect.width / 2, newCoordinates.x)),\n            y: Math.min(direction === KeyboardCode.Down ? scrollElementRect.bottom - scrollElementRect.height / 2 : scrollElementRect.bottom, Math.max(direction === KeyboardCode.Down ? scrollElementRect.top : scrollElementRect.top + scrollElementRect.height / 2, newCoordinates.y))\n          };\n          const canScrollX = direction === KeyboardCode.Right && !isRight || direction === KeyboardCode.Left && !isLeft;\n          const canScrollY = direction === KeyboardCode.Down && !isBottom || direction === KeyboardCode.Up && !isTop;\n\n          if (canScrollX && clampedCoordinates.x !== newCoordinates.x) {\n            const newScrollCoordinates = scrollContainer.scrollLeft + coordinatesDelta.x;\n            const canScrollToNewCoordinates = direction === KeyboardCode.Right && newScrollCoordinates <= maxScroll.x || direction === KeyboardCode.Left && newScrollCoordinates >= minScroll.x;\n\n            if (canScrollToNewCoordinates && !coordinatesDelta.y) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                left: newScrollCoordinates,\n                behavior: scrollBehavior\n              });\n              return;\n            }\n\n            if (canScrollToNewCoordinates) {\n              scrollDelta.x = scrollContainer.scrollLeft - newScrollCoordinates;\n            } else {\n              scrollDelta.x = direction === KeyboardCode.Right ? scrollContainer.scrollLeft - maxScroll.x : scrollContainer.scrollLeft - minScroll.x;\n            }\n\n            if (scrollDelta.x) {\n              scrollContainer.scrollBy({\n                left: -scrollDelta.x,\n                behavior: scrollBehavior\n              });\n            }\n\n            break;\n          } else if (canScrollY && clampedCoordinates.y !== newCoordinates.y) {\n            const newScrollCoordinates = scrollContainer.scrollTop + coordinatesDelta.y;\n            const canScrollToNewCoordinates = direction === KeyboardCode.Down && newScrollCoordinates <= maxScroll.y || direction === KeyboardCode.Up && newScrollCoordinates >= minScroll.y;\n\n            if (canScrollToNewCoordinates && !coordinatesDelta.x) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                top: newScrollCoordinates,\n                behavior: scrollBehavior\n              });\n              return;\n            }\n\n            if (canScrollToNewCoordinates) {\n              scrollDelta.y = scrollContainer.scrollTop - newScrollCoordinates;\n            } else {\n              scrollDelta.y = direction === KeyboardCode.Down ? scrollContainer.scrollTop - maxScroll.y : scrollContainer.scrollTop - minScroll.y;\n            }\n\n            if (scrollDelta.y) {\n              scrollContainer.scrollBy({\n                top: -scrollDelta.y,\n                behavior: scrollBehavior\n              });\n            }\n\n            break;\n          }\n        }\n\n        this.handleMove(event, add(subtract(newCoordinates, this.referenceCoordinates), scrollDelta));\n      }\n    }\n  }\n\n  handleMove(event, coordinates) {\n    const {\n      onMove\n    } = this.props;\n    event.preventDefault();\n    onMove(coordinates);\n  }\n\n  handleEnd(event) {\n    const {\n      onEnd\n    } = this.props;\n    event.preventDefault();\n    this.detach();\n    onEnd();\n  }\n\n  handleCancel(event) {\n    const {\n      onCancel\n    } = this.props;\n    event.preventDefault();\n    this.detach();\n    onCancel();\n  }\n\n  detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll();\n  }\n\n}\nKeyboardSensor.activators = [{\n  eventName: 'onKeyDown',\n  handler: (event, _ref, _ref2) => {\n    let {\n      keyboardCodes = defaultKeyboardCodes,\n      onActivation\n    } = _ref;\n    let {\n      active\n    } = _ref2;\n    const {\n      code\n    } = event.nativeEvent;\n\n    if (keyboardCodes.start.includes(code)) {\n      const activator = active.activatorNode.current;\n\n      if (activator && event.target !== activator) {\n        return false;\n      }\n\n      event.preventDefault();\n      onActivation == null ? void 0 : onActivation({\n        event: event.nativeEvent\n      });\n      return true;\n    }\n\n    return false;\n  }\n}];\n\nfunction isDistanceConstraint(constraint) {\n  return Boolean(constraint && 'distance' in constraint);\n}\n\nfunction isDelayConstraint(constraint) {\n  return Boolean(constraint && 'delay' in constraint);\n}\n\nclass AbstractPointerSensor {\n  constructor(props, events, listenerTarget) {\n    var _getEventCoordinates;\n\n    if (listenerTarget === void 0) {\n      listenerTarget = getEventListenerTarget(props.event.target);\n    }\n\n    this.props = void 0;\n    this.events = void 0;\n    this.autoScrollEnabled = true;\n    this.document = void 0;\n    this.activated = false;\n    this.initialCoordinates = void 0;\n    this.timeoutId = null;\n    this.listeners = void 0;\n    this.documentListeners = void 0;\n    this.windowListeners = void 0;\n    this.props = props;\n    this.events = events;\n    const {\n      event\n    } = props;\n    const {\n      target\n    } = event;\n    this.props = props;\n    this.events = events;\n    this.document = getOwnerDocument(target);\n    this.documentListeners = new Listeners(this.document);\n    this.listeners = new Listeners(listenerTarget);\n    this.windowListeners = new Listeners(getWindow(target));\n    this.initialCoordinates = (_getEventCoordinates = getEventCoordinates(event)) != null ? _getEventCoordinates : defaultCoordinates;\n    this.handleStart = this.handleStart.bind(this);\n    this.handleMove = this.handleMove.bind(this);\n    this.handleEnd = this.handleEnd.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n    this.handleKeydown = this.handleKeydown.bind(this);\n    this.removeTextSelection = this.removeTextSelection.bind(this);\n    this.attach();\n  }\n\n  attach() {\n    const {\n      events,\n      props: {\n        options: {\n          activationConstraint,\n          bypassActivationConstraint\n        }\n      }\n    } = this;\n    this.listeners.add(events.move.name, this.handleMove, {\n      passive: false\n    });\n    this.listeners.add(events.end.name, this.handleEnd);\n\n    if (events.cancel) {\n      this.listeners.add(events.cancel.name, this.handleCancel);\n    }\n\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.DragStart, preventDefault);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n    this.windowListeners.add(EventName.ContextMenu, preventDefault);\n    this.documentListeners.add(EventName.Keydown, this.handleKeydown);\n\n    if (activationConstraint) {\n      if (bypassActivationConstraint != null && bypassActivationConstraint({\n        event: this.props.event,\n        activeNode: this.props.activeNode,\n        options: this.props.options\n      })) {\n        return this.handleStart();\n      }\n\n      if (isDelayConstraint(activationConstraint)) {\n        this.timeoutId = setTimeout(this.handleStart, activationConstraint.delay);\n        this.handlePending(activationConstraint);\n        return;\n      }\n\n      if (isDistanceConstraint(activationConstraint)) {\n        this.handlePending(activationConstraint);\n        return;\n      }\n    }\n\n    this.handleStart();\n  }\n\n  detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll(); // Wait until the next event loop before removing document listeners\n    // This is necessary because we listen for `click` and `selection` events on the document\n\n    setTimeout(this.documentListeners.removeAll, 50);\n\n    if (this.timeoutId !== null) {\n      clearTimeout(this.timeoutId);\n      this.timeoutId = null;\n    }\n  }\n\n  handlePending(constraint, offset) {\n    const {\n      active,\n      onPending\n    } = this.props;\n    onPending(active, constraint, this.initialCoordinates, offset);\n  }\n\n  handleStart() {\n    const {\n      initialCoordinates\n    } = this;\n    const {\n      onStart\n    } = this.props;\n\n    if (initialCoordinates) {\n      this.activated = true; // Stop propagation of click events once activation constraints are met\n\n      this.documentListeners.add(EventName.Click, stopPropagation, {\n        capture: true\n      }); // Remove any text selection from the document\n\n      this.removeTextSelection(); // Prevent further text selection while dragging\n\n      this.documentListeners.add(EventName.SelectionChange, this.removeTextSelection);\n      onStart(initialCoordinates);\n    }\n  }\n\n  handleMove(event) {\n    var _getEventCoordinates2;\n\n    const {\n      activated,\n      initialCoordinates,\n      props\n    } = this;\n    const {\n      onMove,\n      options: {\n        activationConstraint\n      }\n    } = props;\n\n    if (!initialCoordinates) {\n      return;\n    }\n\n    const coordinates = (_getEventCoordinates2 = getEventCoordinates(event)) != null ? _getEventCoordinates2 : defaultCoordinates;\n    const delta = subtract(initialCoordinates, coordinates); // Constraint validation\n\n    if (!activated && activationConstraint) {\n      if (isDistanceConstraint(activationConstraint)) {\n        if (activationConstraint.tolerance != null && hasExceededDistance(delta, activationConstraint.tolerance)) {\n          return this.handleCancel();\n        }\n\n        if (hasExceededDistance(delta, activationConstraint.distance)) {\n          return this.handleStart();\n        }\n      }\n\n      if (isDelayConstraint(activationConstraint)) {\n        if (hasExceededDistance(delta, activationConstraint.tolerance)) {\n          return this.handleCancel();\n        }\n      }\n\n      this.handlePending(activationConstraint, delta);\n      return;\n    }\n\n    if (event.cancelable) {\n      event.preventDefault();\n    }\n\n    onMove(coordinates);\n  }\n\n  handleEnd() {\n    const {\n      onAbort,\n      onEnd\n    } = this.props;\n    this.detach();\n\n    if (!this.activated) {\n      onAbort(this.props.active);\n    }\n\n    onEnd();\n  }\n\n  handleCancel() {\n    const {\n      onAbort,\n      onCancel\n    } = this.props;\n    this.detach();\n\n    if (!this.activated) {\n      onAbort(this.props.active);\n    }\n\n    onCancel();\n  }\n\n  handleKeydown(event) {\n    if (event.code === KeyboardCode.Esc) {\n      this.handleCancel();\n    }\n  }\n\n  removeTextSelection() {\n    var _this$document$getSel;\n\n    (_this$document$getSel = this.document.getSelection()) == null ? void 0 : _this$document$getSel.removeAllRanges();\n  }\n\n}\n\nconst events = {\n  cancel: {\n    name: 'pointercancel'\n  },\n  move: {\n    name: 'pointermove'\n  },\n  end: {\n    name: 'pointerup'\n  }\n};\nclass PointerSensor extends AbstractPointerSensor {\n  constructor(props) {\n    const {\n      event\n    } = props; // Pointer events stop firing if the target is unmounted while dragging\n    // Therefore we attach listeners to the owner document instead\n\n    const listenerTarget = getOwnerDocument(event.target);\n    super(props, events, listenerTarget);\n  }\n\n}\nPointerSensor.activators = [{\n  eventName: 'onPointerDown',\n  handler: (_ref, _ref2) => {\n    let {\n      nativeEvent: event\n    } = _ref;\n    let {\n      onActivation\n    } = _ref2;\n\n    if (!event.isPrimary || event.button !== 0) {\n      return false;\n    }\n\n    onActivation == null ? void 0 : onActivation({\n      event\n    });\n    return true;\n  }\n}];\n\nconst events$1 = {\n  move: {\n    name: 'mousemove'\n  },\n  end: {\n    name: 'mouseup'\n  }\n};\nvar MouseButton;\n\n(function (MouseButton) {\n  MouseButton[MouseButton[\"RightClick\"] = 2] = \"RightClick\";\n})(MouseButton || (MouseButton = {}));\n\nclass MouseSensor extends AbstractPointerSensor {\n  constructor(props) {\n    super(props, events$1, getOwnerDocument(props.event.target));\n  }\n\n}\nMouseSensor.activators = [{\n  eventName: 'onMouseDown',\n  handler: (_ref, _ref2) => {\n    let {\n      nativeEvent: event\n    } = _ref;\n    let {\n      onActivation\n    } = _ref2;\n\n    if (event.button === MouseButton.RightClick) {\n      return false;\n    }\n\n    onActivation == null ? void 0 : onActivation({\n      event\n    });\n    return true;\n  }\n}];\n\nconst events$2 = {\n  cancel: {\n    name: 'touchcancel'\n  },\n  move: {\n    name: 'touchmove'\n  },\n  end: {\n    name: 'touchend'\n  }\n};\nclass TouchSensor extends AbstractPointerSensor {\n  constructor(props) {\n    super(props, events$2);\n  }\n\n  static setup() {\n    // Adding a non-capture and non-passive `touchmove` listener in order\n    // to force `event.preventDefault()` calls to work in dynamically added\n    // touchmove event handlers. This is required for iOS Safari.\n    window.addEventListener(events$2.move.name, noop, {\n      capture: false,\n      passive: false\n    });\n    return function teardown() {\n      window.removeEventListener(events$2.move.name, noop);\n    }; // We create a new handler because the teardown function of another sensor\n    // could remove our event listener if we use a referentially equal listener.\n\n    function noop() {}\n  }\n\n}\nTouchSensor.activators = [{\n  eventName: 'onTouchStart',\n  handler: (_ref, _ref2) => {\n    let {\n      nativeEvent: event\n    } = _ref;\n    let {\n      onActivation\n    } = _ref2;\n    const {\n      touches\n    } = event;\n\n    if (touches.length > 1) {\n      return false;\n    }\n\n    onActivation == null ? void 0 : onActivation({\n      event\n    });\n    return true;\n  }\n}];\n\nvar AutoScrollActivator;\n\n(function (AutoScrollActivator) {\n  AutoScrollActivator[AutoScrollActivator[\"Pointer\"] = 0] = \"Pointer\";\n  AutoScrollActivator[AutoScrollActivator[\"DraggableRect\"] = 1] = \"DraggableRect\";\n})(AutoScrollActivator || (AutoScrollActivator = {}));\n\nvar TraversalOrder;\n\n(function (TraversalOrder) {\n  TraversalOrder[TraversalOrder[\"TreeOrder\"] = 0] = \"TreeOrder\";\n  TraversalOrder[TraversalOrder[\"ReversedTreeOrder\"] = 1] = \"ReversedTreeOrder\";\n})(TraversalOrder || (TraversalOrder = {}));\n\nfunction useAutoScroller(_ref) {\n  let {\n    acceleration,\n    activator = AutoScrollActivator.Pointer,\n    canScroll,\n    draggingRect,\n    enabled,\n    interval = 5,\n    order = TraversalOrder.TreeOrder,\n    pointerCoordinates,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    delta,\n    threshold\n  } = _ref;\n  const scrollIntent = useScrollIntent({\n    delta,\n    disabled: !enabled\n  });\n  const [setAutoScrollInterval, clearAutoScrollInterval] = useInterval();\n  const scrollSpeed = useRef({\n    x: 0,\n    y: 0\n  });\n  const scrollDirection = useRef({\n    x: 0,\n    y: 0\n  });\n  const rect = useMemo(() => {\n    switch (activator) {\n      case AutoScrollActivator.Pointer:\n        return pointerCoordinates ? {\n          top: pointerCoordinates.y,\n          bottom: pointerCoordinates.y,\n          left: pointerCoordinates.x,\n          right: pointerCoordinates.x\n        } : null;\n\n      case AutoScrollActivator.DraggableRect:\n        return draggingRect;\n    }\n  }, [activator, draggingRect, pointerCoordinates]);\n  const scrollContainerRef = useRef(null);\n  const autoScroll = useCallback(() => {\n    const scrollContainer = scrollContainerRef.current;\n\n    if (!scrollContainer) {\n      return;\n    }\n\n    const scrollLeft = scrollSpeed.current.x * scrollDirection.current.x;\n    const scrollTop = scrollSpeed.current.y * scrollDirection.current.y;\n    scrollContainer.scrollBy(scrollLeft, scrollTop);\n  }, []);\n  const sortedScrollableAncestors = useMemo(() => order === TraversalOrder.TreeOrder ? [...scrollableAncestors].reverse() : scrollableAncestors, [order, scrollableAncestors]);\n  useEffect(() => {\n    if (!enabled || !scrollableAncestors.length || !rect) {\n      clearAutoScrollInterval();\n      return;\n    }\n\n    for (const scrollContainer of sortedScrollableAncestors) {\n      if ((canScroll == null ? void 0 : canScroll(scrollContainer)) === false) {\n        continue;\n      }\n\n      const index = scrollableAncestors.indexOf(scrollContainer);\n      const scrollContainerRect = scrollableAncestorRects[index];\n\n      if (!scrollContainerRect) {\n        continue;\n      }\n\n      const {\n        direction,\n        speed\n      } = getScrollDirectionAndSpeed(scrollContainer, scrollContainerRect, rect, acceleration, threshold);\n\n      for (const axis of ['x', 'y']) {\n        if (!scrollIntent[axis][direction[axis]]) {\n          speed[axis] = 0;\n          direction[axis] = 0;\n        }\n      }\n\n      if (speed.x > 0 || speed.y > 0) {\n        clearAutoScrollInterval();\n        scrollContainerRef.current = scrollContainer;\n        setAutoScrollInterval(autoScroll, interval);\n        scrollSpeed.current = speed;\n        scrollDirection.current = direction;\n        return;\n      }\n    }\n\n    scrollSpeed.current = {\n      x: 0,\n      y: 0\n    };\n    scrollDirection.current = {\n      x: 0,\n      y: 0\n    };\n    clearAutoScrollInterval();\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [acceleration, autoScroll, canScroll, clearAutoScrollInterval, enabled, interval, // eslint-disable-next-line react-hooks/exhaustive-deps\n  JSON.stringify(rect), // eslint-disable-next-line react-hooks/exhaustive-deps\n  JSON.stringify(scrollIntent), setAutoScrollInterval, scrollableAncestors, sortedScrollableAncestors, scrollableAncestorRects, // eslint-disable-next-line react-hooks/exhaustive-deps\n  JSON.stringify(threshold)]);\n}\nconst defaultScrollIntent = {\n  x: {\n    [Direction.Backward]: false,\n    [Direction.Forward]: false\n  },\n  y: {\n    [Direction.Backward]: false,\n    [Direction.Forward]: false\n  }\n};\n\nfunction useScrollIntent(_ref2) {\n  let {\n    delta,\n    disabled\n  } = _ref2;\n  const previousDelta = usePrevious(delta);\n  return useLazyMemo(previousIntent => {\n    if (disabled || !previousDelta || !previousIntent) {\n      // Reset scroll intent tracking when auto-scrolling is disabled\n      return defaultScrollIntent;\n    }\n\n    const direction = {\n      x: Math.sign(delta.x - previousDelta.x),\n      y: Math.sign(delta.y - previousDelta.y)\n    }; // Keep track of the user intent to scroll in each direction for both axis\n\n    return {\n      x: {\n        [Direction.Backward]: previousIntent.x[Direction.Backward] || direction.x === -1,\n        [Direction.Forward]: previousIntent.x[Direction.Forward] || direction.x === 1\n      },\n      y: {\n        [Direction.Backward]: previousIntent.y[Direction.Backward] || direction.y === -1,\n        [Direction.Forward]: previousIntent.y[Direction.Forward] || direction.y === 1\n      }\n    };\n  }, [disabled, delta, previousDelta]);\n}\n\nfunction useCachedNode(draggableNodes, id) {\n  const draggableNode = id != null ? draggableNodes.get(id) : undefined;\n  const node = draggableNode ? draggableNode.node.current : null;\n  return useLazyMemo(cachedNode => {\n    var _ref;\n\n    if (id == null) {\n      return null;\n    } // In some cases, the draggable node can unmount while dragging\n    // This is the case for virtualized lists. In those situations,\n    // we fall back to the last known value for that node.\n\n\n    return (_ref = node != null ? node : cachedNode) != null ? _ref : null;\n  }, [node, id]);\n}\n\nfunction useCombineActivators(sensors, getSyntheticHandler) {\n  return useMemo(() => sensors.reduce((accumulator, sensor) => {\n    const {\n      sensor: Sensor\n    } = sensor;\n    const sensorActivators = Sensor.activators.map(activator => ({\n      eventName: activator.eventName,\n      handler: getSyntheticHandler(activator.handler, sensor)\n    }));\n    return [...accumulator, ...sensorActivators];\n  }, []), [sensors, getSyntheticHandler]);\n}\n\nvar MeasuringStrategy;\n\n(function (MeasuringStrategy) {\n  MeasuringStrategy[MeasuringStrategy[\"Always\"] = 0] = \"Always\";\n  MeasuringStrategy[MeasuringStrategy[\"BeforeDragging\"] = 1] = \"BeforeDragging\";\n  MeasuringStrategy[MeasuringStrategy[\"WhileDragging\"] = 2] = \"WhileDragging\";\n})(MeasuringStrategy || (MeasuringStrategy = {}));\n\nvar MeasuringFrequency;\n\n(function (MeasuringFrequency) {\n  MeasuringFrequency[\"Optimized\"] = \"optimized\";\n})(MeasuringFrequency || (MeasuringFrequency = {}));\n\nconst defaultValue = /*#__PURE__*/new Map();\nfunction useDroppableMeasuring(containers, _ref) {\n  let {\n    dragging,\n    dependencies,\n    config\n  } = _ref;\n  const [queue, setQueue] = useState(null);\n  const {\n    frequency,\n    measure,\n    strategy\n  } = config;\n  const containersRef = useRef(containers);\n  const disabled = isDisabled();\n  const disabledRef = useLatestValue(disabled);\n  const measureDroppableContainers = useCallback(function (ids) {\n    if (ids === void 0) {\n      ids = [];\n    }\n\n    if (disabledRef.current) {\n      return;\n    }\n\n    setQueue(value => {\n      if (value === null) {\n        return ids;\n      }\n\n      return value.concat(ids.filter(id => !value.includes(id)));\n    });\n  }, [disabledRef]);\n  const timeoutId = useRef(null);\n  const droppableRects = useLazyMemo(previousValue => {\n    if (disabled && !dragging) {\n      return defaultValue;\n    }\n\n    if (!previousValue || previousValue === defaultValue || containersRef.current !== containers || queue != null) {\n      const map = new Map();\n\n      for (let container of containers) {\n        if (!container) {\n          continue;\n        }\n\n        if (queue && queue.length > 0 && !queue.includes(container.id) && container.rect.current) {\n          // This container does not need to be re-measured\n          map.set(container.id, container.rect.current);\n          continue;\n        }\n\n        const node = container.node.current;\n        const rect = node ? new Rect(measure(node), node) : null;\n        container.rect.current = rect;\n\n        if (rect) {\n          map.set(container.id, rect);\n        }\n      }\n\n      return map;\n    }\n\n    return previousValue;\n  }, [containers, queue, dragging, disabled, measure]);\n  useEffect(() => {\n    containersRef.current = containers;\n  }, [containers]);\n  useEffect(() => {\n    if (disabled) {\n      return;\n    }\n\n    measureDroppableContainers();\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [dragging, disabled]);\n  useEffect(() => {\n    if (queue && queue.length > 0) {\n      setQueue(null);\n    }\n  }, //eslint-disable-next-line react-hooks/exhaustive-deps\n  [JSON.stringify(queue)]);\n  useEffect(() => {\n    if (disabled || typeof frequency !== 'number' || timeoutId.current !== null) {\n      return;\n    }\n\n    timeoutId.current = setTimeout(() => {\n      measureDroppableContainers();\n      timeoutId.current = null;\n    }, frequency);\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [frequency, disabled, measureDroppableContainers, ...dependencies]);\n  return {\n    droppableRects,\n    measureDroppableContainers,\n    measuringScheduled: queue != null\n  };\n\n  function isDisabled() {\n    switch (strategy) {\n      case MeasuringStrategy.Always:\n        return false;\n\n      case MeasuringStrategy.BeforeDragging:\n        return dragging;\n\n      default:\n        return !dragging;\n    }\n  }\n}\n\nfunction useInitialValue(value, computeFn) {\n  return useLazyMemo(previousValue => {\n    if (!value) {\n      return null;\n    }\n\n    if (previousValue) {\n      return previousValue;\n    }\n\n    return typeof computeFn === 'function' ? computeFn(value) : value;\n  }, [computeFn, value]);\n}\n\nfunction useInitialRect(node, measure) {\n  return useInitialValue(node, measure);\n}\n\n/**\r\n * Returns a new MutationObserver instance.\r\n * If `MutationObserver` is undefined in the execution environment, returns `undefined`.\r\n */\n\nfunction useMutationObserver(_ref) {\n  let {\n    callback,\n    disabled\n  } = _ref;\n  const handleMutations = useEvent(callback);\n  const mutationObserver = useMemo(() => {\n    if (disabled || typeof window === 'undefined' || typeof window.MutationObserver === 'undefined') {\n      return undefined;\n    }\n\n    const {\n      MutationObserver\n    } = window;\n    return new MutationObserver(handleMutations);\n  }, [handleMutations, disabled]);\n  useEffect(() => {\n    return () => mutationObserver == null ? void 0 : mutationObserver.disconnect();\n  }, [mutationObserver]);\n  return mutationObserver;\n}\n\n/**\r\n * Returns a new ResizeObserver instance bound to the `onResize` callback.\r\n * If `ResizeObserver` is undefined in the execution environment, returns `undefined`.\r\n */\n\nfunction useResizeObserver(_ref) {\n  let {\n    callback,\n    disabled\n  } = _ref;\n  const handleResize = useEvent(callback);\n  const resizeObserver = useMemo(() => {\n    if (disabled || typeof window === 'undefined' || typeof window.ResizeObserver === 'undefined') {\n      return undefined;\n    }\n\n    const {\n      ResizeObserver\n    } = window;\n    return new ResizeObserver(handleResize);\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [disabled]);\n  useEffect(() => {\n    return () => resizeObserver == null ? void 0 : resizeObserver.disconnect();\n  }, [resizeObserver]);\n  return resizeObserver;\n}\n\nfunction defaultMeasure(element) {\n  return new Rect(getClientRect(element), element);\n}\n\nfunction useRect(element, measure, fallbackRect) {\n  if (measure === void 0) {\n    measure = defaultMeasure;\n  }\n\n  const [rect, setRect] = useState(null);\n\n  function measureRect() {\n    setRect(currentRect => {\n      if (!element) {\n        return null;\n      }\n\n      if (element.isConnected === false) {\n        var _ref;\n\n        // Fall back to last rect we measured if the element is\n        // no longer connected to the DOM.\n        return (_ref = currentRect != null ? currentRect : fallbackRect) != null ? _ref : null;\n      }\n\n      const newRect = measure(element);\n\n      if (JSON.stringify(currentRect) === JSON.stringify(newRect)) {\n        return currentRect;\n      }\n\n      return newRect;\n    });\n  }\n\n  const mutationObserver = useMutationObserver({\n    callback(records) {\n      if (!element) {\n        return;\n      }\n\n      for (const record of records) {\n        const {\n          type,\n          target\n        } = record;\n\n        if (type === 'childList' && target instanceof HTMLElement && target.contains(element)) {\n          measureRect();\n          break;\n        }\n      }\n    }\n\n  });\n  const resizeObserver = useResizeObserver({\n    callback: measureRect\n  });\n  useIsomorphicLayoutEffect(() => {\n    measureRect();\n\n    if (element) {\n      resizeObserver == null ? void 0 : resizeObserver.observe(element);\n      mutationObserver == null ? void 0 : mutationObserver.observe(document.body, {\n        childList: true,\n        subtree: true\n      });\n    } else {\n      resizeObserver == null ? void 0 : resizeObserver.disconnect();\n      mutationObserver == null ? void 0 : mutationObserver.disconnect();\n    }\n  }, [element]);\n  return rect;\n}\n\nfunction useRectDelta(rect) {\n  const initialRect = useInitialValue(rect);\n  return getRectDelta(rect, initialRect);\n}\n\nconst defaultValue$1 = [];\nfunction useScrollableAncestors(node) {\n  const previousNode = useRef(node);\n  const ancestors = useLazyMemo(previousValue => {\n    if (!node) {\n      return defaultValue$1;\n    }\n\n    if (previousValue && previousValue !== defaultValue$1 && node && previousNode.current && node.parentNode === previousNode.current.parentNode) {\n      return previousValue;\n    }\n\n    return getScrollableAncestors(node);\n  }, [node]);\n  useEffect(() => {\n    previousNode.current = node;\n  }, [node]);\n  return ancestors;\n}\n\nfunction useScrollOffsets(elements) {\n  const [scrollCoordinates, setScrollCoordinates] = useState(null);\n  const prevElements = useRef(elements); // To-do: Throttle the handleScroll callback\n\n  const handleScroll = useCallback(event => {\n    const scrollingElement = getScrollableElement(event.target);\n\n    if (!scrollingElement) {\n      return;\n    }\n\n    setScrollCoordinates(scrollCoordinates => {\n      if (!scrollCoordinates) {\n        return null;\n      }\n\n      scrollCoordinates.set(scrollingElement, getScrollCoordinates(scrollingElement));\n      return new Map(scrollCoordinates);\n    });\n  }, []);\n  useEffect(() => {\n    const previousElements = prevElements.current;\n\n    if (elements !== previousElements) {\n      cleanup(previousElements);\n      const entries = elements.map(element => {\n        const scrollableElement = getScrollableElement(element);\n\n        if (scrollableElement) {\n          scrollableElement.addEventListener('scroll', handleScroll, {\n            passive: true\n          });\n          return [scrollableElement, getScrollCoordinates(scrollableElement)];\n        }\n\n        return null;\n      }).filter(entry => entry != null);\n      setScrollCoordinates(entries.length ? new Map(entries) : null);\n      prevElements.current = elements;\n    }\n\n    return () => {\n      cleanup(elements);\n      cleanup(previousElements);\n    };\n\n    function cleanup(elements) {\n      elements.forEach(element => {\n        const scrollableElement = getScrollableElement(element);\n        scrollableElement == null ? void 0 : scrollableElement.removeEventListener('scroll', handleScroll);\n      });\n    }\n  }, [handleScroll, elements]);\n  return useMemo(() => {\n    if (elements.length) {\n      return scrollCoordinates ? Array.from(scrollCoordinates.values()).reduce((acc, coordinates) => add(acc, coordinates), defaultCoordinates) : getScrollOffsets(elements);\n    }\n\n    return defaultCoordinates;\n  }, [elements, scrollCoordinates]);\n}\n\nfunction useScrollOffsetsDelta(scrollOffsets, dependencies) {\n  if (dependencies === void 0) {\n    dependencies = [];\n  }\n\n  const initialScrollOffsets = useRef(null);\n  useEffect(() => {\n    initialScrollOffsets.current = null;\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  dependencies);\n  useEffect(() => {\n    const hasScrollOffsets = scrollOffsets !== defaultCoordinates;\n\n    if (hasScrollOffsets && !initialScrollOffsets.current) {\n      initialScrollOffsets.current = scrollOffsets;\n    }\n\n    if (!hasScrollOffsets && initialScrollOffsets.current) {\n      initialScrollOffsets.current = null;\n    }\n  }, [scrollOffsets]);\n  return initialScrollOffsets.current ? subtract(scrollOffsets, initialScrollOffsets.current) : defaultCoordinates;\n}\n\nfunction useSensorSetup(sensors) {\n  useEffect(() => {\n    if (!canUseDOM) {\n      return;\n    }\n\n    const teardownFns = sensors.map(_ref => {\n      let {\n        sensor\n      } = _ref;\n      return sensor.setup == null ? void 0 : sensor.setup();\n    });\n    return () => {\n      for (const teardown of teardownFns) {\n        teardown == null ? void 0 : teardown();\n      }\n    };\n  }, // TO-DO: Sensors length could theoretically change which would not be a valid dependency\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  sensors.map(_ref2 => {\n    let {\n      sensor\n    } = _ref2;\n    return sensor;\n  }));\n}\n\nfunction useSyntheticListeners(listeners, id) {\n  return useMemo(() => {\n    return listeners.reduce((acc, _ref) => {\n      let {\n        eventName,\n        handler\n      } = _ref;\n\n      acc[eventName] = event => {\n        handler(event, id);\n      };\n\n      return acc;\n    }, {});\n  }, [listeners, id]);\n}\n\nfunction useWindowRect(element) {\n  return useMemo(() => element ? getWindowClientRect(element) : null, [element]);\n}\n\nconst defaultValue$2 = [];\nfunction useRects(elements, measure) {\n  if (measure === void 0) {\n    measure = getClientRect;\n  }\n\n  const [firstElement] = elements;\n  const windowRect = useWindowRect(firstElement ? getWindow(firstElement) : null);\n  const [rects, setRects] = useState(defaultValue$2);\n\n  function measureRects() {\n    setRects(() => {\n      if (!elements.length) {\n        return defaultValue$2;\n      }\n\n      return elements.map(element => isDocumentScrollingElement(element) ? windowRect : new Rect(measure(element), element));\n    });\n  }\n\n  const resizeObserver = useResizeObserver({\n    callback: measureRects\n  });\n  useIsomorphicLayoutEffect(() => {\n    resizeObserver == null ? void 0 : resizeObserver.disconnect();\n    measureRects();\n    elements.forEach(element => resizeObserver == null ? void 0 : resizeObserver.observe(element));\n  }, [elements]);\n  return rects;\n}\n\nfunction getMeasurableNode(node) {\n  if (!node) {\n    return null;\n  }\n\n  if (node.children.length > 1) {\n    return node;\n  }\n\n  const firstChild = node.children[0];\n  return isHTMLElement(firstChild) ? firstChild : node;\n}\n\nfunction useDragOverlayMeasuring(_ref) {\n  let {\n    measure\n  } = _ref;\n  const [rect, setRect] = useState(null);\n  const handleResize = useCallback(entries => {\n    for (const {\n      target\n    } of entries) {\n      if (isHTMLElement(target)) {\n        setRect(rect => {\n          const newRect = measure(target);\n          return rect ? { ...rect,\n            width: newRect.width,\n            height: newRect.height\n          } : newRect;\n        });\n        break;\n      }\n    }\n  }, [measure]);\n  const resizeObserver = useResizeObserver({\n    callback: handleResize\n  });\n  const handleNodeChange = useCallback(element => {\n    const node = getMeasurableNode(element);\n    resizeObserver == null ? void 0 : resizeObserver.disconnect();\n\n    if (node) {\n      resizeObserver == null ? void 0 : resizeObserver.observe(node);\n    }\n\n    setRect(node ? measure(node) : null);\n  }, [measure, resizeObserver]);\n  const [nodeRef, setRef] = useNodeRef(handleNodeChange);\n  return useMemo(() => ({\n    nodeRef,\n    rect,\n    setRef\n  }), [rect, nodeRef, setRef]);\n}\n\nconst defaultSensors = [{\n  sensor: PointerSensor,\n  options: {}\n}, {\n  sensor: KeyboardSensor,\n  options: {}\n}];\nconst defaultData = {\n  current: {}\n};\nconst defaultMeasuringConfiguration = {\n  draggable: {\n    measure: getTransformAgnosticClientRect\n  },\n  droppable: {\n    measure: getTransformAgnosticClientRect,\n    strategy: MeasuringStrategy.WhileDragging,\n    frequency: MeasuringFrequency.Optimized\n  },\n  dragOverlay: {\n    measure: getClientRect\n  }\n};\n\nclass DroppableContainersMap extends Map {\n  get(id) {\n    var _super$get;\n\n    return id != null ? (_super$get = super.get(id)) != null ? _super$get : undefined : undefined;\n  }\n\n  toArray() {\n    return Array.from(this.values());\n  }\n\n  getEnabled() {\n    return this.toArray().filter(_ref => {\n      let {\n        disabled\n      } = _ref;\n      return !disabled;\n    });\n  }\n\n  getNodeFor(id) {\n    var _this$get$node$curren, _this$get;\n\n    return (_this$get$node$curren = (_this$get = this.get(id)) == null ? void 0 : _this$get.node.current) != null ? _this$get$node$curren : undefined;\n  }\n\n}\n\nconst defaultPublicContext = {\n  activatorEvent: null,\n  active: null,\n  activeNode: null,\n  activeNodeRect: null,\n  collisions: null,\n  containerNodeRect: null,\n  draggableNodes: /*#__PURE__*/new Map(),\n  droppableRects: /*#__PURE__*/new Map(),\n  droppableContainers: /*#__PURE__*/new DroppableContainersMap(),\n  over: null,\n  dragOverlay: {\n    nodeRef: {\n      current: null\n    },\n    rect: null,\n    setRef: noop\n  },\n  scrollableAncestors: [],\n  scrollableAncestorRects: [],\n  measuringConfiguration: defaultMeasuringConfiguration,\n  measureDroppableContainers: noop,\n  windowRect: null,\n  measuringScheduled: false\n};\nconst defaultInternalContext = {\n  activatorEvent: null,\n  activators: [],\n  active: null,\n  activeNodeRect: null,\n  ariaDescribedById: {\n    draggable: ''\n  },\n  dispatch: noop,\n  draggableNodes: /*#__PURE__*/new Map(),\n  over: null,\n  measureDroppableContainers: noop\n};\nconst InternalContext = /*#__PURE__*/createContext(defaultInternalContext);\nconst PublicContext = /*#__PURE__*/createContext(defaultPublicContext);\n\nfunction getInitialState() {\n  return {\n    draggable: {\n      active: null,\n      initialCoordinates: {\n        x: 0,\n        y: 0\n      },\n      nodes: new Map(),\n      translate: {\n        x: 0,\n        y: 0\n      }\n    },\n    droppable: {\n      containers: new DroppableContainersMap()\n    }\n  };\n}\nfunction reducer(state, action) {\n  switch (action.type) {\n    case Action.DragStart:\n      return { ...state,\n        draggable: { ...state.draggable,\n          initialCoordinates: action.initialCoordinates,\n          active: action.active\n        }\n      };\n\n    case Action.DragMove:\n      if (state.draggable.active == null) {\n        return state;\n      }\n\n      return { ...state,\n        draggable: { ...state.draggable,\n          translate: {\n            x: action.coordinates.x - state.draggable.initialCoordinates.x,\n            y: action.coordinates.y - state.draggable.initialCoordinates.y\n          }\n        }\n      };\n\n    case Action.DragEnd:\n    case Action.DragCancel:\n      return { ...state,\n        draggable: { ...state.draggable,\n          active: null,\n          initialCoordinates: {\n            x: 0,\n            y: 0\n          },\n          translate: {\n            x: 0,\n            y: 0\n          }\n        }\n      };\n\n    case Action.RegisterDroppable:\n      {\n        const {\n          element\n        } = action;\n        const {\n          id\n        } = element;\n        const containers = new DroppableContainersMap(state.droppable.containers);\n        containers.set(id, element);\n        return { ...state,\n          droppable: { ...state.droppable,\n            containers\n          }\n        };\n      }\n\n    case Action.SetDroppableDisabled:\n      {\n        const {\n          id,\n          key,\n          disabled\n        } = action;\n        const element = state.droppable.containers.get(id);\n\n        if (!element || key !== element.key) {\n          return state;\n        }\n\n        const containers = new DroppableContainersMap(state.droppable.containers);\n        containers.set(id, { ...element,\n          disabled\n        });\n        return { ...state,\n          droppable: { ...state.droppable,\n            containers\n          }\n        };\n      }\n\n    case Action.UnregisterDroppable:\n      {\n        const {\n          id,\n          key\n        } = action;\n        const element = state.droppable.containers.get(id);\n\n        if (!element || key !== element.key) {\n          return state;\n        }\n\n        const containers = new DroppableContainersMap(state.droppable.containers);\n        containers.delete(id);\n        return { ...state,\n          droppable: { ...state.droppable,\n            containers\n          }\n        };\n      }\n\n    default:\n      {\n        return state;\n      }\n  }\n}\n\nfunction RestoreFocus(_ref) {\n  let {\n    disabled\n  } = _ref;\n  const {\n    active,\n    activatorEvent,\n    draggableNodes\n  } = useContext(InternalContext);\n  const previousActivatorEvent = usePrevious(activatorEvent);\n  const previousActiveId = usePrevious(active == null ? void 0 : active.id); // Restore keyboard focus on the activator node\n\n  useEffect(() => {\n    if (disabled) {\n      return;\n    }\n\n    if (!activatorEvent && previousActivatorEvent && previousActiveId != null) {\n      if (!isKeyboardEvent(previousActivatorEvent)) {\n        return;\n      }\n\n      if (document.activeElement === previousActivatorEvent.target) {\n        // No need to restore focus\n        return;\n      }\n\n      const draggableNode = draggableNodes.get(previousActiveId);\n\n      if (!draggableNode) {\n        return;\n      }\n\n      const {\n        activatorNode,\n        node\n      } = draggableNode;\n\n      if (!activatorNode.current && !node.current) {\n        return;\n      }\n\n      requestAnimationFrame(() => {\n        for (const element of [activatorNode.current, node.current]) {\n          if (!element) {\n            continue;\n          }\n\n          const focusableNode = findFirstFocusableNode(element);\n\n          if (focusableNode) {\n            focusableNode.focus();\n            break;\n          }\n        }\n      });\n    }\n  }, [activatorEvent, disabled, draggableNodes, previousActiveId, previousActivatorEvent]);\n  return null;\n}\n\nfunction applyModifiers(modifiers, _ref) {\n  let {\n    transform,\n    ...args\n  } = _ref;\n  return modifiers != null && modifiers.length ? modifiers.reduce((accumulator, modifier) => {\n    return modifier({\n      transform: accumulator,\n      ...args\n    });\n  }, transform) : transform;\n}\n\nfunction useMeasuringConfiguration(config) {\n  return useMemo(() => ({\n    draggable: { ...defaultMeasuringConfiguration.draggable,\n      ...(config == null ? void 0 : config.draggable)\n    },\n    droppable: { ...defaultMeasuringConfiguration.droppable,\n      ...(config == null ? void 0 : config.droppable)\n    },\n    dragOverlay: { ...defaultMeasuringConfiguration.dragOverlay,\n      ...(config == null ? void 0 : config.dragOverlay)\n    }\n  }), // eslint-disable-next-line react-hooks/exhaustive-deps\n  [config == null ? void 0 : config.draggable, config == null ? void 0 : config.droppable, config == null ? void 0 : config.dragOverlay]);\n}\n\nfunction useLayoutShiftScrollCompensation(_ref) {\n  let {\n    activeNode,\n    measure,\n    initialRect,\n    config = true\n  } = _ref;\n  const initialized = useRef(false);\n  const {\n    x,\n    y\n  } = typeof config === 'boolean' ? {\n    x: config,\n    y: config\n  } : config;\n  useIsomorphicLayoutEffect(() => {\n    const disabled = !x && !y;\n\n    if (disabled || !activeNode) {\n      initialized.current = false;\n      return;\n    }\n\n    if (initialized.current || !initialRect) {\n      // Return early if layout shift scroll compensation was already attempted\n      // or if there is no initialRect to compare to.\n      return;\n    } // Get the most up to date node ref for the active draggable\n\n\n    const node = activeNode == null ? void 0 : activeNode.node.current;\n\n    if (!node || node.isConnected === false) {\n      // Return early if there is no attached node ref or if the node is\n      // disconnected from the document.\n      return;\n    }\n\n    const rect = measure(node);\n    const rectDelta = getRectDelta(rect, initialRect);\n\n    if (!x) {\n      rectDelta.x = 0;\n    }\n\n    if (!y) {\n      rectDelta.y = 0;\n    } // Only perform layout shift scroll compensation once\n\n\n    initialized.current = true;\n\n    if (Math.abs(rectDelta.x) > 0 || Math.abs(rectDelta.y) > 0) {\n      const firstScrollableAncestor = getFirstScrollableAncestor(node);\n\n      if (firstScrollableAncestor) {\n        firstScrollableAncestor.scrollBy({\n          top: rectDelta.y,\n          left: rectDelta.x\n        });\n      }\n    }\n  }, [activeNode, x, y, initialRect, measure]);\n}\n\nconst ActiveDraggableContext = /*#__PURE__*/createContext({ ...defaultCoordinates,\n  scaleX: 1,\n  scaleY: 1\n});\nvar Status;\n\n(function (Status) {\n  Status[Status[\"Uninitialized\"] = 0] = \"Uninitialized\";\n  Status[Status[\"Initializing\"] = 1] = \"Initializing\";\n  Status[Status[\"Initialized\"] = 2] = \"Initialized\";\n})(Status || (Status = {}));\n\nconst DndContext = /*#__PURE__*/memo(function DndContext(_ref) {\n  var _sensorContext$curren, _dragOverlay$nodeRef$, _dragOverlay$rect, _over$rect;\n\n  let {\n    id,\n    accessibility,\n    autoScroll = true,\n    children,\n    sensors = defaultSensors,\n    collisionDetection = rectIntersection,\n    measuring,\n    modifiers,\n    ...props\n  } = _ref;\n  const store = useReducer(reducer, undefined, getInitialState);\n  const [state, dispatch] = store;\n  const [dispatchMonitorEvent, registerMonitorListener] = useDndMonitorProvider();\n  const [status, setStatus] = useState(Status.Uninitialized);\n  const isInitialized = status === Status.Initialized;\n  const {\n    draggable: {\n      active: activeId,\n      nodes: draggableNodes,\n      translate\n    },\n    droppable: {\n      containers: droppableContainers\n    }\n  } = state;\n  const node = activeId != null ? draggableNodes.get(activeId) : null;\n  const activeRects = useRef({\n    initial: null,\n    translated: null\n  });\n  const active = useMemo(() => {\n    var _node$data;\n\n    return activeId != null ? {\n      id: activeId,\n      // It's possible for the active node to unmount while dragging\n      data: (_node$data = node == null ? void 0 : node.data) != null ? _node$data : defaultData,\n      rect: activeRects\n    } : null;\n  }, [activeId, node]);\n  const activeRef = useRef(null);\n  const [activeSensor, setActiveSensor] = useState(null);\n  const [activatorEvent, setActivatorEvent] = useState(null);\n  const latestProps = useLatestValue(props, Object.values(props));\n  const draggableDescribedById = useUniqueId(\"DndDescribedBy\", id);\n  const enabledDroppableContainers = useMemo(() => droppableContainers.getEnabled(), [droppableContainers]);\n  const measuringConfiguration = useMeasuringConfiguration(measuring);\n  const {\n    droppableRects,\n    measureDroppableContainers,\n    measuringScheduled\n  } = useDroppableMeasuring(enabledDroppableContainers, {\n    dragging: isInitialized,\n    dependencies: [translate.x, translate.y],\n    config: measuringConfiguration.droppable\n  });\n  const activeNode = useCachedNode(draggableNodes, activeId);\n  const activationCoordinates = useMemo(() => activatorEvent ? getEventCoordinates(activatorEvent) : null, [activatorEvent]);\n  const autoScrollOptions = getAutoScrollerOptions();\n  const initialActiveNodeRect = useInitialRect(activeNode, measuringConfiguration.draggable.measure);\n  useLayoutShiftScrollCompensation({\n    activeNode: activeId != null ? draggableNodes.get(activeId) : null,\n    config: autoScrollOptions.layoutShiftCompensation,\n    initialRect: initialActiveNodeRect,\n    measure: measuringConfiguration.draggable.measure\n  });\n  const activeNodeRect = useRect(activeNode, measuringConfiguration.draggable.measure, initialActiveNodeRect);\n  const containerNodeRect = useRect(activeNode ? activeNode.parentElement : null);\n  const sensorContext = useRef({\n    activatorEvent: null,\n    active: null,\n    activeNode,\n    collisionRect: null,\n    collisions: null,\n    droppableRects,\n    draggableNodes,\n    draggingNode: null,\n    draggingNodeRect: null,\n    droppableContainers,\n    over: null,\n    scrollableAncestors: [],\n    scrollAdjustedTranslate: null\n  });\n  const overNode = droppableContainers.getNodeFor((_sensorContext$curren = sensorContext.current.over) == null ? void 0 : _sensorContext$curren.id);\n  const dragOverlay = useDragOverlayMeasuring({\n    measure: measuringConfiguration.dragOverlay.measure\n  }); // Use the rect of the drag overlay if it is mounted\n\n  const draggingNode = (_dragOverlay$nodeRef$ = dragOverlay.nodeRef.current) != null ? _dragOverlay$nodeRef$ : activeNode;\n  const draggingNodeRect = isInitialized ? (_dragOverlay$rect = dragOverlay.rect) != null ? _dragOverlay$rect : activeNodeRect : null;\n  const usesDragOverlay = Boolean(dragOverlay.nodeRef.current && dragOverlay.rect); // The delta between the previous and new position of the draggable node\n  // is only relevant when there is no drag overlay\n\n  const nodeRectDelta = useRectDelta(usesDragOverlay ? null : activeNodeRect); // Get the window rect of the dragging node\n\n  const windowRect = useWindowRect(draggingNode ? getWindow(draggingNode) : null); // Get scrollable ancestors of the dragging node\n\n  const scrollableAncestors = useScrollableAncestors(isInitialized ? overNode != null ? overNode : activeNode : null);\n  const scrollableAncestorRects = useRects(scrollableAncestors); // Apply modifiers\n\n  const modifiedTranslate = applyModifiers(modifiers, {\n    transform: {\n      x: translate.x - nodeRectDelta.x,\n      y: translate.y - nodeRectDelta.y,\n      scaleX: 1,\n      scaleY: 1\n    },\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggingNodeRect,\n    over: sensorContext.current.over,\n    overlayNodeRect: dragOverlay.rect,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    windowRect\n  });\n  const pointerCoordinates = activationCoordinates ? add(activationCoordinates, translate) : null;\n  const scrollOffsets = useScrollOffsets(scrollableAncestors); // Represents the scroll delta since dragging was initiated\n\n  const scrollAdjustment = useScrollOffsetsDelta(scrollOffsets); // Represents the scroll delta since the last time the active node rect was measured\n\n  const activeNodeScrollDelta = useScrollOffsetsDelta(scrollOffsets, [activeNodeRect]);\n  const scrollAdjustedTranslate = add(modifiedTranslate, scrollAdjustment);\n  const collisionRect = draggingNodeRect ? getAdjustedRect(draggingNodeRect, modifiedTranslate) : null;\n  const collisions = active && collisionRect ? collisionDetection({\n    active,\n    collisionRect,\n    droppableRects,\n    droppableContainers: enabledDroppableContainers,\n    pointerCoordinates\n  }) : null;\n  const overId = getFirstCollision(collisions, 'id');\n  const [over, setOver] = useState(null); // When there is no drag overlay used, we need to account for the\n  // window scroll delta\n\n  const appliedTranslate = usesDragOverlay ? modifiedTranslate : add(modifiedTranslate, activeNodeScrollDelta);\n  const transform = adjustScale(appliedTranslate, (_over$rect = over == null ? void 0 : over.rect) != null ? _over$rect : null, activeNodeRect);\n  const activeSensorRef = useRef(null);\n  const instantiateSensor = useCallback((event, _ref2) => {\n    let {\n      sensor: Sensor,\n      options\n    } = _ref2;\n\n    if (activeRef.current == null) {\n      return;\n    }\n\n    const activeNode = draggableNodes.get(activeRef.current);\n\n    if (!activeNode) {\n      return;\n    }\n\n    const activatorEvent = event.nativeEvent;\n    const sensorInstance = new Sensor({\n      active: activeRef.current,\n      activeNode,\n      event: activatorEvent,\n      options,\n      // Sensors need to be instantiated with refs for arguments that change over time\n      // otherwise they are frozen in time with the stale arguments\n      context: sensorContext,\n\n      onAbort(id) {\n        const draggableNode = draggableNodes.get(id);\n\n        if (!draggableNode) {\n          return;\n        }\n\n        const {\n          onDragAbort\n        } = latestProps.current;\n        const event = {\n          id\n        };\n        onDragAbort == null ? void 0 : onDragAbort(event);\n        dispatchMonitorEvent({\n          type: 'onDragAbort',\n          event\n        });\n      },\n\n      onPending(id, constraint, initialCoordinates, offset) {\n        const draggableNode = draggableNodes.get(id);\n\n        if (!draggableNode) {\n          return;\n        }\n\n        const {\n          onDragPending\n        } = latestProps.current;\n        const event = {\n          id,\n          constraint,\n          initialCoordinates,\n          offset\n        };\n        onDragPending == null ? void 0 : onDragPending(event);\n        dispatchMonitorEvent({\n          type: 'onDragPending',\n          event\n        });\n      },\n\n      onStart(initialCoordinates) {\n        const id = activeRef.current;\n\n        if (id == null) {\n          return;\n        }\n\n        const draggableNode = draggableNodes.get(id);\n\n        if (!draggableNode) {\n          return;\n        }\n\n        const {\n          onDragStart\n        } = latestProps.current;\n        const event = {\n          activatorEvent,\n          active: {\n            id,\n            data: draggableNode.data,\n            rect: activeRects\n          }\n        };\n        unstable_batchedUpdates(() => {\n          onDragStart == null ? void 0 : onDragStart(event);\n          setStatus(Status.Initializing);\n          dispatch({\n            type: Action.DragStart,\n            initialCoordinates,\n            active: id\n          });\n          dispatchMonitorEvent({\n            type: 'onDragStart',\n            event\n          });\n          setActiveSensor(activeSensorRef.current);\n          setActivatorEvent(activatorEvent);\n        });\n      },\n\n      onMove(coordinates) {\n        dispatch({\n          type: Action.DragMove,\n          coordinates\n        });\n      },\n\n      onEnd: createHandler(Action.DragEnd),\n      onCancel: createHandler(Action.DragCancel)\n    });\n    activeSensorRef.current = sensorInstance;\n\n    function createHandler(type) {\n      return async function handler() {\n        const {\n          active,\n          collisions,\n          over,\n          scrollAdjustedTranslate\n        } = sensorContext.current;\n        let event = null;\n\n        if (active && scrollAdjustedTranslate) {\n          const {\n            cancelDrop\n          } = latestProps.current;\n          event = {\n            activatorEvent,\n            active: active,\n            collisions,\n            delta: scrollAdjustedTranslate,\n            over\n          };\n\n          if (type === Action.DragEnd && typeof cancelDrop === 'function') {\n            const shouldCancel = await Promise.resolve(cancelDrop(event));\n\n            if (shouldCancel) {\n              type = Action.DragCancel;\n            }\n          }\n        }\n\n        activeRef.current = null;\n        unstable_batchedUpdates(() => {\n          dispatch({\n            type\n          });\n          setStatus(Status.Uninitialized);\n          setOver(null);\n          setActiveSensor(null);\n          setActivatorEvent(null);\n          activeSensorRef.current = null;\n          const eventName = type === Action.DragEnd ? 'onDragEnd' : 'onDragCancel';\n\n          if (event) {\n            const handler = latestProps.current[eventName];\n            handler == null ? void 0 : handler(event);\n            dispatchMonitorEvent({\n              type: eventName,\n              event\n            });\n          }\n        });\n      };\n    }\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [draggableNodes]);\n  const bindActivatorToSensorInstantiator = useCallback((handler, sensor) => {\n    return (event, active) => {\n      const nativeEvent = event.nativeEvent;\n      const activeDraggableNode = draggableNodes.get(active);\n\n      if ( // Another sensor is already instantiating\n      activeRef.current !== null || // No active draggable\n      !activeDraggableNode || // Event has already been captured\n      nativeEvent.dndKit || nativeEvent.defaultPrevented) {\n        return;\n      }\n\n      const activationContext = {\n        active: activeDraggableNode\n      };\n      const shouldActivate = handler(event, sensor.options, activationContext);\n\n      if (shouldActivate === true) {\n        nativeEvent.dndKit = {\n          capturedBy: sensor.sensor\n        };\n        activeRef.current = active;\n        instantiateSensor(event, sensor);\n      }\n    };\n  }, [draggableNodes, instantiateSensor]);\n  const activators = useCombineActivators(sensors, bindActivatorToSensorInstantiator);\n  useSensorSetup(sensors);\n  useIsomorphicLayoutEffect(() => {\n    if (activeNodeRect && status === Status.Initializing) {\n      setStatus(Status.Initialized);\n    }\n  }, [activeNodeRect, status]);\n  useEffect(() => {\n    const {\n      onDragMove\n    } = latestProps.current;\n    const {\n      active,\n      activatorEvent,\n      collisions,\n      over\n    } = sensorContext.current;\n\n    if (!active || !activatorEvent) {\n      return;\n    }\n\n    const event = {\n      active,\n      activatorEvent,\n      collisions,\n      delta: {\n        x: scrollAdjustedTranslate.x,\n        y: scrollAdjustedTranslate.y\n      },\n      over\n    };\n    unstable_batchedUpdates(() => {\n      onDragMove == null ? void 0 : onDragMove(event);\n      dispatchMonitorEvent({\n        type: 'onDragMove',\n        event\n      });\n    });\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [scrollAdjustedTranslate.x, scrollAdjustedTranslate.y]);\n  useEffect(() => {\n    const {\n      active,\n      activatorEvent,\n      collisions,\n      droppableContainers,\n      scrollAdjustedTranslate\n    } = sensorContext.current;\n\n    if (!active || activeRef.current == null || !activatorEvent || !scrollAdjustedTranslate) {\n      return;\n    }\n\n    const {\n      onDragOver\n    } = latestProps.current;\n    const overContainer = droppableContainers.get(overId);\n    const over = overContainer && overContainer.rect.current ? {\n      id: overContainer.id,\n      rect: overContainer.rect.current,\n      data: overContainer.data,\n      disabled: overContainer.disabled\n    } : null;\n    const event = {\n      active,\n      activatorEvent,\n      collisions,\n      delta: {\n        x: scrollAdjustedTranslate.x,\n        y: scrollAdjustedTranslate.y\n      },\n      over\n    };\n    unstable_batchedUpdates(() => {\n      setOver(over);\n      onDragOver == null ? void 0 : onDragOver(event);\n      dispatchMonitorEvent({\n        type: 'onDragOver',\n        event\n      });\n    });\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [overId]);\n  useIsomorphicLayoutEffect(() => {\n    sensorContext.current = {\n      activatorEvent,\n      active,\n      activeNode,\n      collisionRect,\n      collisions,\n      droppableRects,\n      draggableNodes,\n      draggingNode,\n      draggingNodeRect,\n      droppableContainers,\n      over,\n      scrollableAncestors,\n      scrollAdjustedTranslate\n    };\n    activeRects.current = {\n      initial: draggingNodeRect,\n      translated: collisionRect\n    };\n  }, [active, activeNode, collisions, collisionRect, draggableNodes, draggingNode, draggingNodeRect, droppableRects, droppableContainers, over, scrollableAncestors, scrollAdjustedTranslate]);\n  useAutoScroller({ ...autoScrollOptions,\n    delta: translate,\n    draggingRect: collisionRect,\n    pointerCoordinates,\n    scrollableAncestors,\n    scrollableAncestorRects\n  });\n  const publicContext = useMemo(() => {\n    const context = {\n      active,\n      activeNode,\n      activeNodeRect,\n      activatorEvent,\n      collisions,\n      containerNodeRect,\n      dragOverlay,\n      draggableNodes,\n      droppableContainers,\n      droppableRects,\n      over,\n      measureDroppableContainers,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      measuringConfiguration,\n      measuringScheduled,\n      windowRect\n    };\n    return context;\n  }, [active, activeNode, activeNodeRect, activatorEvent, collisions, containerNodeRect, dragOverlay, draggableNodes, droppableContainers, droppableRects, over, measureDroppableContainers, scrollableAncestors, scrollableAncestorRects, measuringConfiguration, measuringScheduled, windowRect]);\n  const internalContext = useMemo(() => {\n    const context = {\n      activatorEvent,\n      activators,\n      active,\n      activeNodeRect,\n      ariaDescribedById: {\n        draggable: draggableDescribedById\n      },\n      dispatch,\n      draggableNodes,\n      over,\n      measureDroppableContainers\n    };\n    return context;\n  }, [activatorEvent, activators, active, activeNodeRect, dispatch, draggableDescribedById, draggableNodes, over, measureDroppableContainers]);\n  return React.createElement(DndMonitorContext.Provider, {\n    value: registerMonitorListener\n  }, React.createElement(InternalContext.Provider, {\n    value: internalContext\n  }, React.createElement(PublicContext.Provider, {\n    value: publicContext\n  }, React.createElement(ActiveDraggableContext.Provider, {\n    value: transform\n  }, children)), React.createElement(RestoreFocus, {\n    disabled: (accessibility == null ? void 0 : accessibility.restoreFocus) === false\n  })), React.createElement(Accessibility, { ...accessibility,\n    hiddenTextDescribedById: draggableDescribedById\n  }));\n\n  function getAutoScrollerOptions() {\n    const activeSensorDisablesAutoscroll = (activeSensor == null ? void 0 : activeSensor.autoScrollEnabled) === false;\n    const autoScrollGloballyDisabled = typeof autoScroll === 'object' ? autoScroll.enabled === false : autoScroll === false;\n    const enabled = isInitialized && !activeSensorDisablesAutoscroll && !autoScrollGloballyDisabled;\n\n    if (typeof autoScroll === 'object') {\n      return { ...autoScroll,\n        enabled\n      };\n    }\n\n    return {\n      enabled\n    };\n  }\n});\n\nconst NullContext = /*#__PURE__*/createContext(null);\nconst defaultRole = 'button';\nconst ID_PREFIX = 'Draggable';\nfunction useDraggable(_ref) {\n  let {\n    id,\n    data,\n    disabled = false,\n    attributes\n  } = _ref;\n  const key = useUniqueId(ID_PREFIX);\n  const {\n    activators,\n    activatorEvent,\n    active,\n    activeNodeRect,\n    ariaDescribedById,\n    draggableNodes,\n    over\n  } = useContext(InternalContext);\n  const {\n    role = defaultRole,\n    roleDescription = 'draggable',\n    tabIndex = 0\n  } = attributes != null ? attributes : {};\n  const isDragging = (active == null ? void 0 : active.id) === id;\n  const transform = useContext(isDragging ? ActiveDraggableContext : NullContext);\n  const [node, setNodeRef] = useNodeRef();\n  const [activatorNode, setActivatorNodeRef] = useNodeRef();\n  const listeners = useSyntheticListeners(activators, id);\n  const dataRef = useLatestValue(data);\n  useIsomorphicLayoutEffect(() => {\n    draggableNodes.set(id, {\n      id,\n      key,\n      node,\n      activatorNode,\n      data: dataRef\n    });\n    return () => {\n      const node = draggableNodes.get(id);\n\n      if (node && node.key === key) {\n        draggableNodes.delete(id);\n      }\n    };\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [draggableNodes, id]);\n  const memoizedAttributes = useMemo(() => ({\n    role,\n    tabIndex,\n    'aria-disabled': disabled,\n    'aria-pressed': isDragging && role === defaultRole ? true : undefined,\n    'aria-roledescription': roleDescription,\n    'aria-describedby': ariaDescribedById.draggable\n  }), [disabled, role, tabIndex, isDragging, roleDescription, ariaDescribedById.draggable]);\n  return {\n    active,\n    activatorEvent,\n    activeNodeRect,\n    attributes: memoizedAttributes,\n    isDragging,\n    listeners: disabled ? undefined : listeners,\n    node,\n    over,\n    setNodeRef,\n    setActivatorNodeRef,\n    transform\n  };\n}\n\nfunction useDndContext() {\n  return useContext(PublicContext);\n}\n\nconst ID_PREFIX$1 = 'Droppable';\nconst defaultResizeObserverConfig = {\n  timeout: 25\n};\nfunction useDroppable(_ref) {\n  let {\n    data,\n    disabled = false,\n    id,\n    resizeObserverConfig\n  } = _ref;\n  const key = useUniqueId(ID_PREFIX$1);\n  const {\n    active,\n    dispatch,\n    over,\n    measureDroppableContainers\n  } = useContext(InternalContext);\n  const previous = useRef({\n    disabled\n  });\n  const resizeObserverConnected = useRef(false);\n  const rect = useRef(null);\n  const callbackId = useRef(null);\n  const {\n    disabled: resizeObserverDisabled,\n    updateMeasurementsFor,\n    timeout: resizeObserverTimeout\n  } = { ...defaultResizeObserverConfig,\n    ...resizeObserverConfig\n  };\n  const ids = useLatestValue(updateMeasurementsFor != null ? updateMeasurementsFor : id);\n  const handleResize = useCallback(() => {\n    if (!resizeObserverConnected.current) {\n      // ResizeObserver invokes the `handleResize` callback as soon as `observe` is called,\n      // assuming the element is rendered and displayed.\n      resizeObserverConnected.current = true;\n      return;\n    }\n\n    if (callbackId.current != null) {\n      clearTimeout(callbackId.current);\n    }\n\n    callbackId.current = setTimeout(() => {\n      measureDroppableContainers(Array.isArray(ids.current) ? ids.current : [ids.current]);\n      callbackId.current = null;\n    }, resizeObserverTimeout);\n  }, //eslint-disable-next-line react-hooks/exhaustive-deps\n  [resizeObserverTimeout]);\n  const resizeObserver = useResizeObserver({\n    callback: handleResize,\n    disabled: resizeObserverDisabled || !active\n  });\n  const handleNodeChange = useCallback((newElement, previousElement) => {\n    if (!resizeObserver) {\n      return;\n    }\n\n    if (previousElement) {\n      resizeObserver.unobserve(previousElement);\n      resizeObserverConnected.current = false;\n    }\n\n    if (newElement) {\n      resizeObserver.observe(newElement);\n    }\n  }, [resizeObserver]);\n  const [nodeRef, setNodeRef] = useNodeRef(handleNodeChange);\n  const dataRef = useLatestValue(data);\n  useEffect(() => {\n    if (!resizeObserver || !nodeRef.current) {\n      return;\n    }\n\n    resizeObserver.disconnect();\n    resizeObserverConnected.current = false;\n    resizeObserver.observe(nodeRef.current);\n  }, [nodeRef, resizeObserver]);\n  useEffect(() => {\n    dispatch({\n      type: Action.RegisterDroppable,\n      element: {\n        id,\n        key,\n        disabled,\n        node: nodeRef,\n        rect,\n        data: dataRef\n      }\n    });\n    return () => dispatch({\n      type: Action.UnregisterDroppable,\n      key,\n      id\n    });\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [id]);\n  useEffect(() => {\n    if (disabled !== previous.current.disabled) {\n      dispatch({\n        type: Action.SetDroppableDisabled,\n        id,\n        key,\n        disabled\n      });\n      previous.current.disabled = disabled;\n    }\n  }, [id, key, disabled, dispatch]);\n  return {\n    active,\n    rect,\n    isOver: (over == null ? void 0 : over.id) === id,\n    node: nodeRef,\n    over,\n    setNodeRef\n  };\n}\n\nfunction AnimationManager(_ref) {\n  let {\n    animation,\n    children\n  } = _ref;\n  const [clonedChildren, setClonedChildren] = useState(null);\n  const [element, setElement] = useState(null);\n  const previousChildren = usePrevious(children);\n\n  if (!children && !clonedChildren && previousChildren) {\n    setClonedChildren(previousChildren);\n  }\n\n  useIsomorphicLayoutEffect(() => {\n    if (!element) {\n      return;\n    }\n\n    const key = clonedChildren == null ? void 0 : clonedChildren.key;\n    const id = clonedChildren == null ? void 0 : clonedChildren.props.id;\n\n    if (key == null || id == null) {\n      setClonedChildren(null);\n      return;\n    }\n\n    Promise.resolve(animation(id, element)).then(() => {\n      setClonedChildren(null);\n    });\n  }, [animation, clonedChildren, element]);\n  return React.createElement(React.Fragment, null, children, clonedChildren ? cloneElement(clonedChildren, {\n    ref: setElement\n  }) : null);\n}\n\nconst defaultTransform = {\n  x: 0,\n  y: 0,\n  scaleX: 1,\n  scaleY: 1\n};\nfunction NullifiedContextProvider(_ref) {\n  let {\n    children\n  } = _ref;\n  return React.createElement(InternalContext.Provider, {\n    value: defaultInternalContext\n  }, React.createElement(ActiveDraggableContext.Provider, {\n    value: defaultTransform\n  }, children));\n}\n\nconst baseStyles = {\n  position: 'fixed',\n  touchAction: 'none'\n};\n\nconst defaultTransition = activatorEvent => {\n  const isKeyboardActivator = isKeyboardEvent(activatorEvent);\n  return isKeyboardActivator ? 'transform 250ms ease' : undefined;\n};\n\nconst PositionedOverlay = /*#__PURE__*/forwardRef((_ref, ref) => {\n  let {\n    as,\n    activatorEvent,\n    adjustScale,\n    children,\n    className,\n    rect,\n    style,\n    transform,\n    transition = defaultTransition\n  } = _ref;\n\n  if (!rect) {\n    return null;\n  }\n\n  const scaleAdjustedTransform = adjustScale ? transform : { ...transform,\n    scaleX: 1,\n    scaleY: 1\n  };\n  const styles = { ...baseStyles,\n    width: rect.width,\n    height: rect.height,\n    top: rect.top,\n    left: rect.left,\n    transform: CSS.Transform.toString(scaleAdjustedTransform),\n    transformOrigin: adjustScale && activatorEvent ? getRelativeTransformOrigin(activatorEvent, rect) : undefined,\n    transition: typeof transition === 'function' ? transition(activatorEvent) : transition,\n    ...style\n  };\n  return React.createElement(as, {\n    className,\n    style: styles,\n    ref\n  }, children);\n});\n\nconst defaultDropAnimationSideEffects = options => _ref => {\n  let {\n    active,\n    dragOverlay\n  } = _ref;\n  const originalStyles = {};\n  const {\n    styles,\n    className\n  } = options;\n\n  if (styles != null && styles.active) {\n    for (const [key, value] of Object.entries(styles.active)) {\n      if (value === undefined) {\n        continue;\n      }\n\n      originalStyles[key] = active.node.style.getPropertyValue(key);\n      active.node.style.setProperty(key, value);\n    }\n  }\n\n  if (styles != null && styles.dragOverlay) {\n    for (const [key, value] of Object.entries(styles.dragOverlay)) {\n      if (value === undefined) {\n        continue;\n      }\n\n      dragOverlay.node.style.setProperty(key, value);\n    }\n  }\n\n  if (className != null && className.active) {\n    active.node.classList.add(className.active);\n  }\n\n  if (className != null && className.dragOverlay) {\n    dragOverlay.node.classList.add(className.dragOverlay);\n  }\n\n  return function cleanup() {\n    for (const [key, value] of Object.entries(originalStyles)) {\n      active.node.style.setProperty(key, value);\n    }\n\n    if (className != null && className.active) {\n      active.node.classList.remove(className.active);\n    }\n  };\n};\n\nconst defaultKeyframeResolver = _ref2 => {\n  let {\n    transform: {\n      initial,\n      final\n    }\n  } = _ref2;\n  return [{\n    transform: CSS.Transform.toString(initial)\n  }, {\n    transform: CSS.Transform.toString(final)\n  }];\n};\n\nconst defaultDropAnimationConfiguration = {\n  duration: 250,\n  easing: 'ease',\n  keyframes: defaultKeyframeResolver,\n  sideEffects: /*#__PURE__*/defaultDropAnimationSideEffects({\n    styles: {\n      active: {\n        opacity: '0'\n      }\n    }\n  })\n};\nfunction useDropAnimation(_ref3) {\n  let {\n    config,\n    draggableNodes,\n    droppableContainers,\n    measuringConfiguration\n  } = _ref3;\n  return useEvent((id, node) => {\n    if (config === null) {\n      return;\n    }\n\n    const activeDraggable = draggableNodes.get(id);\n\n    if (!activeDraggable) {\n      return;\n    }\n\n    const activeNode = activeDraggable.node.current;\n\n    if (!activeNode) {\n      return;\n    }\n\n    const measurableNode = getMeasurableNode(node);\n\n    if (!measurableNode) {\n      return;\n    }\n\n    const {\n      transform\n    } = getWindow(node).getComputedStyle(node);\n    const parsedTransform = parseTransform(transform);\n\n    if (!parsedTransform) {\n      return;\n    }\n\n    const animation = typeof config === 'function' ? config : createDefaultDropAnimation(config);\n    scrollIntoViewIfNeeded(activeNode, measuringConfiguration.draggable.measure);\n    return animation({\n      active: {\n        id,\n        data: activeDraggable.data,\n        node: activeNode,\n        rect: measuringConfiguration.draggable.measure(activeNode)\n      },\n      draggableNodes,\n      dragOverlay: {\n        node,\n        rect: measuringConfiguration.dragOverlay.measure(measurableNode)\n      },\n      droppableContainers,\n      measuringConfiguration,\n      transform: parsedTransform\n    });\n  });\n}\n\nfunction createDefaultDropAnimation(options) {\n  const {\n    duration,\n    easing,\n    sideEffects,\n    keyframes\n  } = { ...defaultDropAnimationConfiguration,\n    ...options\n  };\n  return _ref4 => {\n    let {\n      active,\n      dragOverlay,\n      transform,\n      ...rest\n    } = _ref4;\n\n    if (!duration) {\n      // Do not animate if animation duration is zero.\n      return;\n    }\n\n    const delta = {\n      x: dragOverlay.rect.left - active.rect.left,\n      y: dragOverlay.rect.top - active.rect.top\n    };\n    const scale = {\n      scaleX: transform.scaleX !== 1 ? active.rect.width * transform.scaleX / dragOverlay.rect.width : 1,\n      scaleY: transform.scaleY !== 1 ? active.rect.height * transform.scaleY / dragOverlay.rect.height : 1\n    };\n    const finalTransform = {\n      x: transform.x - delta.x,\n      y: transform.y - delta.y,\n      ...scale\n    };\n    const animationKeyframes = keyframes({ ...rest,\n      active,\n      dragOverlay,\n      transform: {\n        initial: transform,\n        final: finalTransform\n      }\n    });\n    const [firstKeyframe] = animationKeyframes;\n    const lastKeyframe = animationKeyframes[animationKeyframes.length - 1];\n\n    if (JSON.stringify(firstKeyframe) === JSON.stringify(lastKeyframe)) {\n      // The start and end keyframes are the same, infer that there is no animation needed.\n      return;\n    }\n\n    const cleanup = sideEffects == null ? void 0 : sideEffects({\n      active,\n      dragOverlay,\n      ...rest\n    });\n    const animation = dragOverlay.node.animate(animationKeyframes, {\n      duration,\n      easing,\n      fill: 'forwards'\n    });\n    return new Promise(resolve => {\n      animation.onfinish = () => {\n        cleanup == null ? void 0 : cleanup();\n        resolve();\n      };\n    });\n  };\n}\n\nlet key = 0;\nfunction useKey(id) {\n  return useMemo(() => {\n    if (id == null) {\n      return;\n    }\n\n    key++;\n    return key;\n  }, [id]);\n}\n\nconst DragOverlay = /*#__PURE__*/React.memo(_ref => {\n  let {\n    adjustScale = false,\n    children,\n    dropAnimation: dropAnimationConfig,\n    style,\n    transition,\n    modifiers,\n    wrapperElement = 'div',\n    className,\n    zIndex = 999\n  } = _ref;\n  const {\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggableNodes,\n    droppableContainers,\n    dragOverlay,\n    over,\n    measuringConfiguration,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    windowRect\n  } = useDndContext();\n  const transform = useContext(ActiveDraggableContext);\n  const key = useKey(active == null ? void 0 : active.id);\n  const modifiedTransform = applyModifiers(modifiers, {\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggingNodeRect: dragOverlay.rect,\n    over,\n    overlayNodeRect: dragOverlay.rect,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    transform,\n    windowRect\n  });\n  const initialRect = useInitialValue(activeNodeRect);\n  const dropAnimation = useDropAnimation({\n    config: dropAnimationConfig,\n    draggableNodes,\n    droppableContainers,\n    measuringConfiguration\n  }); // We need to wait for the active node to be measured before connecting the drag overlay ref\n  // otherwise collisions can be computed against a mispositioned drag overlay\n\n  const ref = initialRect ? dragOverlay.setRef : undefined;\n  return React.createElement(NullifiedContextProvider, null, React.createElement(AnimationManager, {\n    animation: dropAnimation\n  }, active && key ? React.createElement(PositionedOverlay, {\n    key: key,\n    id: active.id,\n    ref: ref,\n    as: wrapperElement,\n    activatorEvent: activatorEvent,\n    adjustScale: adjustScale,\n    className: className,\n    transition: transition,\n    rect: initialRect,\n    style: {\n      zIndex,\n      ...style\n    },\n    transform: modifiedTransform\n  }, children) : null));\n});\n\nexport { AutoScrollActivator, DndContext, DragOverlay, KeyboardCode, KeyboardSensor, MeasuringFrequency, MeasuringStrategy, MouseSensor, PointerSensor, TouchSensor, TraversalOrder, applyModifiers, closestCenter, closestCorners, defaultAnnouncements, defaultCoordinates, defaultDropAnimationConfiguration as defaultDropAnimation, defaultDropAnimationSideEffects, defaultKeyboardCoordinateGetter, defaultScreenReaderInstructions, getClientRect, getFirstCollision, getScrollableAncestors, pointerWithin, rectIntersection, useDndContext, useDndMonitor, useDraggable, useDroppable, useSensor, useSensors };\n//# sourceMappingURL=core.esm.js.map\n"], "names": ["canUseDOM", "isWindow", "element", "elementString", "isNode", "node", "getWindow", "target", "_target$ownerDocument", "_target$ownerDocument2", "isDocument", "Document", "isHTMLElement", "isSVGElement", "getOwnerDocument", "useIsomorphicLayoutEffect", "useLayoutEffect", "useEffect", "useEvent", "handler", "handler<PERSON>ef", "useRef", "useCallback", "_len", "args", "_key", "useInterval", "intervalRef", "set", "listener", "duration", "clear", "useLatestValue", "value", "dependencies", "valueRef", "useLazyMemo", "callback", "useMemo", "newValue", "useNodeRef", "onChange", "onChangeHandler", "setNodeRef", "usePrevious", "ref", "ids", "useUniqueId", "prefix", "id", "createAdjustmentFn", "modifier", "object", "adjustments", "accumulator", "adjustment", "entries", "key", "valueAdjustment", "add", "subtract", "hasViewportRelativeCoordinates", "event", "isKeyboardEvent", "KeyboardEvent", "isTouchEvent", "TouchEvent", "getEventCoordinates", "x", "y", "CSS", "transform", "scaleX", "scaleY", "_ref", "property", "easing", "SELECTOR", "findFirstFocusableNode", "hiddenStyles", "HiddenText", "React", "LiveRegion", "announcement", "ariaLiveType", "visuallyHidden", "useAnnouncement", "setAnnouncement", "useState", "DndMonitorContext", "createContext", "useDndMonitor", "registerListener", "useContext", "useDndMonitorProvider", "listeners", "type", "_listener$type", "defaultScreenReaderInstructions", "defaultAnnouncements", "active", "_ref2", "over", "_ref3", "_ref4", "Accessibility", "announcements", "container", "hiddenTextDescribedById", "screenReaderInstructions", "announce", "liveRegionId", "mounted", "setMounted", "_ref5", "_ref6", "markup", "createPortal", "Action", "noop", "useSensor", "sensor", "options", "useSensors", "sensors", "defaultCoordinates", "getRelativeTransformOrigin", "rect", "eventCoordinates", "transform<PERSON><PERSON>in", "sortCollisionsDesc", "a", "b", "getFirstCollision", "collisions", "firstCollision", "getIntersectionRatio", "entry", "top", "left", "right", "bottom", "width", "height", "targetArea", "entryArea", "intersectionArea", "intersectionRatio", "rectIntersection", "collisionRect", "droppableRects", "droppableContainers", "droppableContainer", "adjustScale", "rect1", "rect2", "getRectDelta", "createRectAdjustmentFn", "acc", "getAdjustedRect", "parseTransform", "transformArray", "inverseTransform", "parsedTransform", "translateX", "translateY", "w", "h", "defaultOptions", "getClientRect", "getTransformAgnosticClientRect", "getWindowClientRect", "isFixed", "computedStyle", "isScrollable", "overflowRegex", "getScrollableAncestors", "limit", "scrollParents", "findScrollableAncestors", "getFirstScrollableAncestor", "firstScrollableAncestor", "getScrollableElement", "getScrollXCoordinate", "getScrollYCoordinate", "getScrollCoordinates", "Direction", "isDocumentScrollingElement", "getScrollPosition", "scrollingContainer", "minScroll", "dimensions", "maxScroll", "isTop", "isLeft", "isBottom", "isRight", "defaultThreshold", "getScrollDirectionAndSpeed", "scrollContainer", "scrollContainerRect", "acceleration", "thresholdPercentage", "direction", "speed", "threshold", "getScrollElementRect", "innerWidth", "innerHeight", "getScrollOffsets", "scrollableAncestors", "getScrollXOffset", "getScrollYOffset", "scrollIntoViewIfNeeded", "measure", "properties", "Rect", "scrollOffsets", "axis", "keys", "getScrollOffset", "currentOffsets", "scrollOffsetsDeltla", "Listeners", "_this$target", "eventName", "_this$target2", "getEventListenerTarget", "EventTarget", "hasExceededDistance", "delta", "measurement", "dx", "dy", "EventName", "preventDefault", "stopPropagation", "KeyboardCode", "defaultKeyboardCodes", "defaultKeyboardCoordinateGetter", "currentCoordinates", "KeyboardSensor", "props", "activeNode", "onStart", "context", "keyboardCodes", "coordinateGetter", "scroll<PERSON>eh<PERSON>or", "code", "newCoordinates", "coordinates<PERSON><PERSON><PERSON>", "scrollDelta", "scrollElementRect", "clampedCoordinates", "canScrollX", "canScrollY", "newScrollCoordinates", "canScrollToNewCoordinates", "coordinates", "onMove", "onEnd", "onCancel", "onActivation", "activator", "isDistanceConstraint", "constraint", "isDelayConstraint", "AbstractPointerSensor", "events", "<PERSON><PERSON><PERSON><PERSON>", "_getEventCoordinates", "activationConstraint", "bypassActivationConstraint", "offset", "onPending", "initialCoordinates", "_getEventCoordinates2", "activated", "onAbort", "_this$document$getSel", "PointerSensor", "events$1", "MouseB<PERSON>on", "MouseSensor", "events$2", "TouchSensor", "touches", "AutoScrollActivator", "TraversalOrder", "useAutoScroller", "canScroll", "draggingRect", "enabled", "interval", "order", "pointerCoordinates", "scrollableAncestorRects", "scrollIntent", "useScrollIntent", "setAutoScrollInterval", "clearAutoScrollInterval", "scrollSpeed", "scrollDirection", "scrollContainerRef", "autoScroll", "scrollLeft", "scrollTop", "sortedScrollableAncestors", "index", "defaultScrollIntent", "disabled", "previousDel<PERSON>", "previousIntent", "useCachedNode", "draggableNodes", "draggableNode", "cachedNode", "useCombineActivators", "getSyntheticHandler", "Sensor", "sensorActivators", "MeasuringStrategy", "MeasuringFrequency", "defaultValue", "useDroppableMeasuring", "containers", "dragging", "config", "queue", "setQueue", "frequency", "strategy", "containersRef", "isDisabled", "disabledRef", "measureDroppableContainers", "timeoutId", "previousValue", "map", "useInitialValue", "computeFn", "useInitialRect", "useMutationObserver", "handleMutations", "mutationObserver", "MutationObserver", "useResizeObserver", "handleResize", "resizeObserver", "ResizeObserver", "defaultMeasure", "useRect", "fallbackRect", "setRect", "measureRect", "currentRect", "newRect", "records", "record", "useRectDelta", "initialRect", "defaultValue$1", "useScrollableAncestors", "previousNode", "ancestors", "useScrollOffsets", "elements", "scrollCoordinates", "setScrollCoordinates", "prevElements", "handleScroll", "scrollingElement", "previousElements", "cleanup", "scrollableElement", "useScrollOffsetsDelta", "initialScrollOffsets", "hasScrollOffsets", "useSensorSetup", "teardownFns", "teardown", "useSyntheticListeners", "useWindowRect", "defaultValue$2", "useRects", "firstElement", "windowRect", "rects", "setRects", "measureRects", "getMeasurableNode", "<PERSON><PERSON><PERSON><PERSON>", "useDragOverlayMeasuring", "handleNodeChange", "nodeRef", "setRef", "defaultSensors", "defaultData", "defaultMeasuringConfiguration", "DroppableContainersMap", "_super$get", "_this$get$node$curren", "_this$get", "defaultPublicContext", "defaultInternalContext", "InternalContext", "PublicContext", "getInitialState", "reducer", "state", "action", "RestoreFocus", "activatorEvent", "previousActivatorEvent", "previousActiveId", "activatorNode", "focusableNode", "applyModifiers", "modifiers", "useMeasuringConfiguration", "useLayoutShiftScrollCompensation", "initialized", "rectD<PERSON><PERSON>", "ActiveDraggableContext", "Status", "DndContext", "memo", "_sensorContext$curren", "_dragOverlay$nodeRef$", "_dragOverlay$rect", "_over$rect", "accessibility", "children", "collisionDetection", "measuring", "store", "useReducer", "dispatch", "dispatchMonitorEvent", "registerMonitorListener", "status", "setStatus", "isInitialized", "activeId", "translate", "activeRects", "_node$data", "activeRef", "activeSensor", "setActiveSensor", "setActivatorEvent", "latestProps", "draggableDescribedById", "enabledDroppableContainers", "measuringConfiguration", "measuringScheduled", "activationCoordinates", "autoScrollOptions", "getAutoScrollerOptions", "initialActiveNodeRect", "activeNodeRect", "containerNodeRect", "sensorContext", "overNode", "dragOverlay", "draggingNode", "draggingNodeRect", "usesDragOverlay", "nodeRectDelta", "modifiedTranslate", "scrollAdjustment", "activeNodeScrollDelta", "scrollAdjustedTranslate", "overId", "setOver", "appliedTranslate", "activeSensorRef", "instantiateSensor", "sensorInstance", "onDragAbort", "onDragPending", "onDragStart", "unstable_batchedUpdates", "createHandler", "cancelDrop", "bindActivatorToSensorInstantiator", "nativeEvent", "activeDraggableNode", "activationContext", "activators", "onDragMove", "onDragOver", "over<PERSON><PERSON><PERSON>", "publicContext", "internalContext", "activeSensorDisablesAutoscroll", "autoScrollGloballyDisabled", "NullContext", "defaultRole", "ID_PREFIX", "useDraggable", "data", "attributes", "ariaDescribedById", "role", "roleDescription", "tabIndex", "isDragging", "setActivatorNodeRef", "dataRef", "memoizedAttributes", "useDndContext", "ID_PREFIX$1", "defaultResizeObserverConfig", "useDroppable", "resizeObserverConfig", "previous", "resizeObserverConnected", "callbackId", "resizeObserverDisabled", "updateMeasurementsFor", "resizeObserverTimeout", "newElement", "previousElement", "AnimationManager", "animation", "cloned<PERSON><PERSON><PERSON><PERSON>", "setClonedChildren", "setElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cloneElement", "defaultTransform", "NullifiedContextProvider", "baseStyles", "defaultTransition", "PositionedOverlay", "forwardRef", "as", "className", "style", "transition", "scaleAdjustedTransform", "styles", "defaultDropAnimationSideEffects", "originalStyles", "defaultKeyframeResolver", "initial", "final", "defaultDropAnimationConfiguration", "useDropAnimation", "activeDraggable", "measurableNode", "createDefaultDropAnimation", "sideEffects", "keyframes", "rest", "scale", "finalTransform", "animationKeyframes", "firstKeyframe", "lastKeyframe", "resolve", "useKey", "DragOverlay", "dropAnimationConfig", "wrapperElement", "zIndex", "modifiedTransform", "dropAnimation"], "mappings": "yFAcA,MAAMA,GAAY,OAAO,OAAW,KAAe,OAAO,OAAO,SAAa,KAAe,OAAO,OAAO,SAAS,cAAkB,IAEtI,SAASC,GAASC,EAAS,CACzB,MAAMC,EAAgB,OAAO,UAAU,SAAS,KAAKD,CAAO,EAC5D,OAAOC,IAAkB,mBACzBA,IAAkB,iBACpB,CAEA,SAASC,GAAOC,EAAM,CACpB,MAAO,aAAcA,CACvB,CAEA,SAASC,EAAUC,EAAQ,CACzB,IAAIC,EAAuBC,EAE3B,OAAKF,EAIDN,GAASM,CAAM,EACVA,EAGJH,GAAOG,CAAM,IAIVC,GAAyBC,EAAyBF,EAAO,gBAAkB,KAAO,OAASE,EAAuB,cAAgB,KAAOD,EAHxI,OARA,MAYX,CAEA,SAASE,GAAWL,EAAM,CACxB,KAAM,CACJ,SAAAM,CACJ,EAAML,EAAUD,CAAI,EAClB,OAAOA,aAAgBM,CACzB,CAEA,SAASC,GAAcP,EAAM,CAC3B,OAAIJ,GAASI,CAAI,EACR,GAGFA,aAAgBC,EAAUD,CAAI,EAAE,WACzC,CAEA,SAASQ,GAAaR,EAAM,CAC1B,OAAOA,aAAgBC,EAAUD,CAAI,EAAE,UACzC,CAEA,SAASS,GAAiBP,EAAQ,CAChC,OAAKA,EAIDN,GAASM,CAAM,EACVA,EAAO,SAGXH,GAAOG,CAAM,EAIdG,GAAWH,CAAM,EACZA,EAGLK,GAAcL,CAAM,GAAKM,GAAaN,CAAM,EACvCA,EAAO,cAGT,SAXE,SARA,QAoBX,CAOA,MAAMQ,EAA4Bf,GAAYgB,EAAAA,gBAAkBC,EAAS,UAEzE,SAASC,GAASC,EAAS,CACzB,MAAMC,EAAaC,EAAM,OAACF,CAAO,EACjC,OAAAJ,EAA0B,IAAM,CAC9BK,EAAW,QAAUD,CACzB,CAAG,EACMG,EAAW,YAAC,UAAY,CAC7B,QAASC,EAAO,UAAU,OAAQC,EAAO,IAAI,MAAMD,CAAI,EAAGE,EAAO,EAAGA,EAAOF,EAAME,IAC/ED,EAAKC,CAAI,EAAI,UAAUA,CAAI,EAG7B,OAAOL,EAAW,SAAW,KAAO,OAASA,EAAW,QAAQ,GAAGI,CAAI,CACxE,EAAE,EAAE,CACP,CAEA,SAASE,IAAc,CACrB,MAAMC,EAAcN,EAAM,OAAC,IAAI,EACzBO,EAAMN,EAAAA,YAAY,CAACO,EAAUC,IAAa,CAC9CH,EAAY,QAAU,YAAYE,EAAUC,CAAQ,CACrD,EAAE,EAAE,EACCC,EAAQT,EAAAA,YAAY,IAAM,CAC1BK,EAAY,UAAY,OAC1B,cAAcA,EAAY,OAAO,EACjCA,EAAY,QAAU,KAEzB,EAAE,EAAE,EACL,MAAO,CAACC,EAAKG,CAAK,CACpB,CAEA,SAASC,GAAeC,EAAOC,EAAc,CACvCA,IAAiB,SACnBA,EAAe,CAACD,CAAK,GAGvB,MAAME,EAAWd,EAAM,OAACY,CAAK,EAC7B,OAAAlB,EAA0B,IAAM,CAC1BoB,EAAS,UAAYF,IACvBE,EAAS,QAAUF,EAEtB,EAAEC,CAAY,EACRC,CACT,CAEA,SAASC,GAAYC,EAAUH,EAAc,CAC3C,MAAMC,EAAWd,EAAAA,OAAQ,EACzB,OAAOiB,EAAO,QAAC,IAAM,CACnB,MAAMC,EAAWF,EAASF,EAAS,OAAO,EAC1C,OAAAA,EAAS,QAAUI,EACZA,CACR,EACD,CAAC,GAAGL,CAAY,CAAC,CACnB,CAEA,SAASM,GAAWC,EAAU,CAC5B,MAAMC,EAAkBxB,GAASuB,CAAQ,EACnCpC,EAAOgB,EAAM,OAAC,IAAI,EAClBsB,EAAarB,EAAW,YAACpB,GAAW,CACpCA,IAAYG,EAAK,SACgBqC,IAAgBxC,EAASG,EAAK,OAAO,EAG1EA,EAAK,QAAUH,CAChB,EACD,EAAE,EACF,MAAO,CAACG,EAAMsC,CAAU,CAC1B,CAEA,SAASC,GAAYX,EAAO,CAC1B,MAAMY,EAAMxB,EAAAA,OAAQ,EACpBJ,OAAAA,EAAAA,UAAU,IAAM,CACd4B,EAAI,QAAUZ,CAClB,EAAK,CAACA,CAAK,CAAC,EACHY,EAAI,OACb,CAEA,IAAIC,GAAM,CAAE,EACZ,SAASC,GAAYC,EAAQf,EAAO,CAClC,OAAOK,EAAO,QAAC,IAAM,CACnB,GAAIL,EACF,OAAOA,EAGT,MAAMgB,EAAKH,GAAIE,CAAM,GAAK,KAAO,EAAIF,GAAIE,CAAM,EAAI,EACnD,OAAAF,GAAIE,CAAM,EAAIC,EACPD,EAAS,IAAMC,CAC1B,EAAK,CAACD,EAAQf,CAAK,CAAC,CACpB,CAEA,SAASiB,GAAmBC,EAAU,CACpC,OAAO,SAAUC,EAAQ,CACvB,QAAS7B,EAAO,UAAU,OAAQ8B,EAAc,IAAI,MAAM9B,EAAO,EAAIA,EAAO,EAAI,CAAC,EAAGE,EAAO,EAAGA,EAAOF,EAAME,IACzG4B,EAAY5B,EAAO,CAAC,EAAI,UAAUA,CAAI,EAGxC,OAAO4B,EAAY,OAAO,CAACC,EAAaC,IAAe,CACrD,MAAMC,EAAU,OAAO,QAAQD,CAAU,EAEzC,SAAW,CAACE,EAAKC,CAAe,IAAKF,EAAS,CAC5C,MAAMvB,EAAQqB,EAAYG,CAAG,EAEzBxB,GAAS,OACXqB,EAAYG,CAAG,EAAIxB,EAAQkB,EAAWO,EAEhD,CAEM,OAAOJ,CACR,EAAE,CAAE,GAAGF,CACZ,CAAK,CACF,CACH,CAEA,MAAMO,GAAmBT,GAAmB,CAAC,EACvCU,GAAwBV,GAAmB,EAAE,EAEnD,SAASW,GAA+BC,EAAO,CAC7C,MAAO,YAAaA,GAAS,YAAaA,CAC5C,CAEA,SAASC,GAAgBD,EAAO,CAC9B,GAAI,CAACA,EACH,MAAO,GAGT,KAAM,CACJ,cAAAE,CACJ,EAAM1D,EAAUwD,EAAM,MAAM,EAC1B,OAAOE,GAAiBF,aAAiBE,CAC3C,CAEA,SAASC,GAAaH,EAAO,CAC3B,GAAI,CAACA,EACH,MAAO,GAGT,KAAM,CACJ,WAAAI,CACJ,EAAM5D,EAAUwD,EAAM,MAAM,EAC1B,OAAOI,GAAcJ,aAAiBI,CACxC,CAMA,SAASC,GAAoBL,EAAO,CAClC,GAAIG,GAAaH,CAAK,GACpB,GAAIA,EAAM,SAAWA,EAAM,QAAQ,OAAQ,CACzC,KAAM,CACJ,QAASM,EACT,QAASC,CACjB,EAAUP,EAAM,QAAQ,CAAC,EACnB,MAAO,CACL,EAAAM,EACA,EAAAC,CACD,CACF,SAAUP,EAAM,gBAAkBA,EAAM,eAAe,OAAQ,CAC9D,KAAM,CACJ,QAASM,EACT,QAASC,CACjB,EAAUP,EAAM,eAAe,CAAC,EAC1B,MAAO,CACL,EAAAM,EACA,EAAAC,CACD,CACP,EAGE,OAAIR,GAA+BC,CAAK,EAC/B,CACL,EAAGA,EAAM,QACT,EAAGA,EAAM,OACV,EAGI,IACT,CAEK,MAACQ,GAAmB,OAAO,OAAO,CACrC,UAAW,CACT,SAASC,EAAW,CAClB,GAAI,CAACA,EACH,OAGF,KAAM,CACJ,EAAAH,EACA,EAAAC,CACR,EAAUE,EACJ,MAAO,gBAAkBH,EAAI,KAAK,MAAMA,CAAC,EAAI,GAAK,QAAUC,EAAI,KAAK,MAAMA,CAAC,EAAI,GAAK,QAC3F,CAEG,EACD,MAAO,CACL,SAASE,EAAW,CAClB,GAAI,CAACA,EACH,OAGF,KAAM,CACJ,OAAAC,EACA,OAAAC,CACR,EAAUF,EACJ,MAAO,UAAYC,EAAS,YAAcC,EAAS,GACzD,CAEG,EACD,UAAW,CACT,SAASF,EAAW,CAClB,GAAKA,EAIL,MAAO,CAACD,GAAI,UAAU,SAASC,CAAS,EAAGD,GAAI,MAAM,SAASC,CAAS,CAAC,EAAE,KAAK,GAAG,CACxF,CAEG,EACD,WAAY,CACV,SAASG,EAAM,CACb,GAAI,CACF,SAAAC,EACA,SAAA7C,EACA,OAAA8C,CACR,EAAUF,EACJ,OAAOC,EAAW,IAAM7C,EAAW,MAAQ8C,CACjD,CAEA,CACA,CAAC,EAEKC,GAAW,yIACjB,SAASC,GAAuB5E,EAAS,CACvC,OAAIA,EAAQ,QAAQ2E,EAAQ,EACnB3E,EAGFA,EAAQ,cAAc2E,EAAQ,CACvC,CCvUA,MAAME,GAAe,CACnB,QAAS,MACX,EACA,SAASC,GAAWN,EAAM,CACxB,GAAI,CACF,GAAAzB,EACA,MAAAhB,CACJ,EAAMyC,EACJ,OAAOO,EAAM,cAAc,MAAO,CAChC,GAAIhC,EACJ,MAAO8B,EACR,EAAE9C,CAAK,CACV,CAEA,SAASiD,GAAWR,EAAM,CACxB,GAAI,CACF,GAAAzB,EACA,aAAAkC,EACA,aAAAC,EAAe,WACnB,EAAMV,EAEJ,MAAMW,EAAiB,CACrB,SAAU,QACV,IAAK,EACL,KAAM,EACN,MAAO,EACP,OAAQ,EACR,OAAQ,GACR,OAAQ,EACR,QAAS,EACT,SAAU,SACV,KAAM,gBACN,SAAU,cACV,WAAY,QACb,EACD,OAAOJ,EAAM,cAAc,MAAO,CAChC,GAAIhC,EACJ,MAAOoC,EACP,KAAM,SACN,YAAaD,EACb,cAAe,EAChB,EAAED,CAAY,CACjB,CAEA,SAASG,IAAkB,CACzB,KAAM,CAACH,EAAcI,CAAe,EAAIC,EAAAA,SAAS,EAAE,EAMnD,MAAO,CACL,SANelE,EAAW,YAACW,GAAS,CAChCA,GAAS,MACXsD,EAAgBtD,CAAK,CAExB,EAAE,EAAE,EAGH,aAAAkD,CACD,CACH,CCpDA,MAAMM,GAAiCC,EAAa,cAAC,IAAI,EAEzD,SAASC,GAAc9D,EAAU,CAC/B,MAAM+D,EAAmBC,EAAU,WAACJ,EAAiB,EACrDxE,EAAAA,UAAU,IAAM,CACd,GAAI,CAAC2E,EACH,MAAM,IAAI,MAAM,8DAA8D,EAIhF,OADoBA,EAAiB/D,CAAQ,CAEjD,EAAK,CAACA,EAAU+D,CAAgB,CAAC,CACjC,CAEA,SAASE,IAAwB,CAC/B,KAAM,CAACC,CAAS,EAAIP,EAAQ,SAAC,IAAM,IAAI,GAAK,EACtCI,EAAmBtE,EAAW,YAACO,IACnCkE,EAAU,IAAIlE,CAAQ,EACf,IAAMkE,EAAU,OAAOlE,CAAQ,GACrC,CAACkE,CAAS,CAAC,EAYd,MAAO,CAXUzE,EAAW,YAACoD,GAAQ,CACnC,GAAI,CACF,KAAAsB,EACA,MAAAlC,CACN,EAAQY,EACJqB,EAAU,QAAQlE,GAAY,CAC5B,IAAIoE,EAEJ,OAAQA,EAAiBpE,EAASmE,CAAI,IAAM,KAAO,OAASC,EAAe,KAAKpE,EAAUiC,CAAK,CACrG,CAAK,CACL,EAAK,CAACiC,CAAS,CAAC,EACIH,CAAgB,CACpC,CAEA,MAAMM,GAAkC,CACtC,UAAW;AAAA;AAAA;AAAA;AAAA,GACb,EACMC,GAAuB,CAC3B,YAAYzB,EAAM,CAChB,GAAI,CACF,OAAA0B,CACN,EAAQ1B,EACJ,MAAO,4BAA8B0B,EAAO,GAAK,GAClD,EAED,WAAWC,EAAO,CAChB,GAAI,CACF,OAAAD,EACA,KAAAE,CACN,EAAQD,EAEJ,OAAIC,EACK,kBAAoBF,EAAO,GAAK,kCAAoCE,EAAK,GAAK,IAGhF,kBAAoBF,EAAO,GAAK,sCACxC,EAED,UAAUG,EAAO,CACf,GAAI,CACF,OAAAH,EACA,KAAAE,CACN,EAAQC,EAEJ,OAAID,EACK,kBAAoBF,EAAO,GAAK,oCAAsCE,EAAK,GAG7E,kBAAoBF,EAAO,GAAK,eACxC,EAED,aAAaI,EAAO,CAClB,GAAI,CACF,OAAAJ,CACN,EAAQI,EACJ,MAAO,0CAA4CJ,EAAO,GAAK,eACnE,CAEA,EAEA,SAASK,GAAc/B,EAAM,CAC3B,GAAI,CACF,cAAAgC,EAAgBP,GAChB,UAAAQ,EACA,wBAAAC,EACA,yBAAAC,EAA2BX,EAC/B,EAAMxB,EACJ,KAAM,CACJ,SAAAoC,EACA,aAAA3B,CACD,EAAGG,GAAiB,EACfyB,EAAehE,GAAY,eAAe,EAC1C,CAACiE,EAASC,CAAU,EAAIzB,EAAAA,SAAS,EAAK,EA+D5C,GA9DAvE,EAAAA,UAAU,IAAM,CACdgG,EAAW,EAAI,CAChB,EAAE,EAAE,EACLtB,GAAcrD,EAAO,QAAC,KAAO,CAC3B,YAAY+D,EAAO,CACjB,GAAI,CACF,OAAAD,CACR,EAAUC,EACJS,EAASJ,EAAc,YAAY,CACjC,OAAAN,CACR,CAAO,CAAC,CACH,EAED,WAAWG,EAAO,CAChB,GAAI,CACF,OAAAH,EACA,KAAAE,CACR,EAAUC,EAEAG,EAAc,YAChBI,EAASJ,EAAc,WAAW,CAChC,OAAAN,EACA,KAAAE,CACV,CAAS,CAAC,CAEL,EAED,WAAWE,EAAO,CAChB,GAAI,CACF,OAAAJ,EACA,KAAAE,CACR,EAAUE,EACJM,EAASJ,EAAc,WAAW,CAChC,OAAAN,EACA,KAAAE,CACR,CAAO,CAAC,CACH,EAED,UAAUY,EAAO,CACf,GAAI,CACF,OAAAd,EACA,KAAAE,CACR,EAAUY,EACJJ,EAASJ,EAAc,UAAU,CAC/B,OAAAN,EACA,KAAAE,CACR,CAAO,CAAC,CACH,EAED,aAAaa,EAAO,CAClB,GAAI,CACF,OAAAf,EACA,KAAAE,CACR,EAAUa,EACJL,EAASJ,EAAc,aAAa,CAClC,OAAAN,EACA,KAAAE,CACR,CAAO,CAAC,CACR,CAEG,GAAG,CAACQ,EAAUJ,CAAa,CAAC,CAAC,EAE1B,CAACM,EACH,OAAO,KAGT,MAAMI,EAASnC,EAAM,cAAcA,EAAM,SAAU,KAAMA,EAAM,cAAcD,GAAY,CACvF,GAAI4B,EACJ,MAAOC,EAAyB,SACpC,CAAG,EAAG5B,EAAM,cAAcC,GAAY,CAClC,GAAI6B,EACJ,aAAc5B,CAClB,CAAG,CAAC,EACF,OAAOwB,EAAYU,GAAY,aAACD,EAAQT,CAAS,EAAIS,CACvD,CAEA,IAAIE,GAEH,SAAUA,EAAQ,CACjBA,EAAO,UAAe,YACtBA,EAAO,SAAc,WACrBA,EAAO,QAAa,UACpBA,EAAO,WAAgB,aACvBA,EAAO,SAAc,WACrBA,EAAO,kBAAuB,oBAC9BA,EAAO,qBAA0B,uBACjCA,EAAO,oBAAyB,qBAClC,GAAGA,IAAWA,EAAS,CAAA,EAAG,EAE1B,SAASC,IAAO,CAAA,CAEhB,SAASC,GAAUC,EAAQC,EAAS,CAClC,OAAOpF,EAAO,QAAC,KAAO,CACpB,OAAAmF,EACA,QAASC,GAA4B,CAAA,CACzC,GACE,CAACD,EAAQC,CAAO,CAAC,CACnB,CAEA,SAASC,IAAa,CACpB,QAASpG,EAAO,UAAU,OAAQqG,EAAU,IAAI,MAAMrG,CAAI,EAAGE,EAAO,EAAGA,EAAOF,EAAME,IAClFmG,EAAQnG,CAAI,EAAI,UAAUA,CAAI,EAGhC,OAAOa,EAAO,QAAC,IAAM,CAAC,GAAGsF,CAAO,EAAE,OAAOH,GAAUA,GAAU,IAAI,EACjE,CAAC,GAAGG,CAAO,CAAC,CACd,CAEA,MAAMC,EAAkC,OAAO,OAAO,CACpD,EAAG,EACH,EAAG,CACL,CAAC,EASD,SAASC,GAA2BhE,EAAOiE,EAAM,CAC/C,MAAMC,EAAmB7D,GAAoBL,CAAK,EAElD,GAAI,CAACkE,EACH,MAAO,MAGT,MAAMC,EAAkB,CACtB,GAAID,EAAiB,EAAID,EAAK,MAAQA,EAAK,MAAQ,IACnD,GAAIC,EAAiB,EAAID,EAAK,KAAOA,EAAK,OAAS,GACpD,EACD,OAAOE,EAAgB,EAAI,KAAOA,EAAgB,EAAI,GACxD,CAsBA,SAASC,GAAmB3B,EAAOC,EAAO,CACxC,GAAI,CACF,KAAM,CACJ,MAAO2B,CACb,CACA,EAAM5B,EACA,CACF,KAAM,CACJ,MAAO6B,CACb,CACA,EAAM5B,EACJ,OAAO4B,EAAID,CACb,CA2BA,SAASE,GAAkBC,EAAY3D,EAAU,CAC/C,GAAI,CAAC2D,GAAcA,EAAW,SAAW,EACvC,OAAO,KAGT,KAAM,CAACC,CAAc,EAAID,EACzB,OAAkBC,EAAe5D,CAAQ,CAC3C,CAmGA,SAAS6D,GAAqBC,EAAOlI,EAAQ,CAC3C,MAAMmI,EAAM,KAAK,IAAInI,EAAO,IAAKkI,EAAM,GAAG,EACpCE,EAAO,KAAK,IAAIpI,EAAO,KAAMkI,EAAM,IAAI,EACvCG,EAAQ,KAAK,IAAIrI,EAAO,KAAOA,EAAO,MAAOkI,EAAM,KAAOA,EAAM,KAAK,EACrEI,EAAS,KAAK,IAAItI,EAAO,IAAMA,EAAO,OAAQkI,EAAM,IAAMA,EAAM,MAAM,EACtEK,EAAQF,EAAQD,EAChBI,EAASF,EAASH,EAExB,GAAIC,EAAOC,GAASF,EAAMG,EAAQ,CAChC,MAAMG,EAAazI,EAAO,MAAQA,EAAO,OACnC0I,EAAYR,EAAM,MAAQA,EAAM,OAChCS,EAAmBJ,EAAQC,EAC3BI,EAAoBD,GAAoBF,EAAaC,EAAYC,GACvE,OAAO,OAAOC,EAAkB,QAAQ,CAAC,CAAC,CAC3C,CAGD,MAAO,EACT,CAMA,MAAMC,GAAmB1E,GAAQ,CAC/B,GAAI,CACF,cAAA2E,EACA,eAAAC,EACA,oBAAAC,CACJ,EAAM7E,EACJ,MAAM4D,EAAa,CAAE,EAErB,UAAWkB,KAAsBD,EAAqB,CACpD,KAAM,CACJ,GAAAtG,CACN,EAAQuG,EACEzB,EAAOuB,EAAe,IAAIrG,CAAE,EAElC,GAAI8E,EAAM,CACR,MAAMoB,EAAoBX,GAAqBT,EAAMsB,CAAa,EAE9DF,EAAoB,GACtBb,EAAW,KAAK,CACd,GAAArF,EACA,KAAM,CACJ,mBAAAuG,EACA,MAAOL,CACnB,CACA,CAAS,CAET,CACA,CAEE,OAAOb,EAAW,KAAKJ,EAAkB,CAC3C,EA+DA,SAASuB,GAAYlF,EAAWmF,EAAOC,EAAO,CAC5C,MAAO,CAAE,GAAGpF,EACV,OAAQmF,GAASC,EAAQD,EAAM,MAAQC,EAAM,MAAQ,EACrD,OAAQD,GAASC,EAAQD,EAAM,OAASC,EAAM,OAAS,CACxD,CACH,CAEA,SAASC,GAAaF,EAAOC,EAAO,CAClC,OAAOD,GAASC,EAAQ,CACtB,EAAGD,EAAM,KAAOC,EAAM,KACtB,EAAGD,EAAM,IAAMC,EAAM,GACzB,EAAM9B,CACN,CAEA,SAASgC,GAAuB1G,EAAU,CACxC,OAAO,SAA0B4E,EAAM,CACrC,QAASxG,EAAO,UAAU,OAAQ8B,EAAc,IAAI,MAAM9B,EAAO,EAAIA,EAAO,EAAI,CAAC,EAAGE,EAAO,EAAGA,EAAOF,EAAME,IACzG4B,EAAY5B,EAAO,CAAC,EAAI,UAAUA,CAAI,EAGxC,OAAO4B,EAAY,OAAO,CAACyG,EAAKvG,KAAgB,CAAE,GAAGuG,EACnD,IAAKA,EAAI,IAAM3G,EAAWI,EAAW,EACrC,OAAQuG,EAAI,OAAS3G,EAAWI,EAAW,EAC3C,KAAMuG,EAAI,KAAO3G,EAAWI,EAAW,EACvC,MAAOuG,EAAI,MAAQ3G,EAAWI,EAAW,CAC1C,GAAG,CAAE,GAAGwE,CACb,CAAK,CACF,CACH,CACA,MAAMgC,GAA+BF,GAAuB,CAAC,EAE7D,SAASG,GAAezF,EAAW,CACjC,GAAIA,EAAU,WAAW,WAAW,EAAG,CACrC,MAAM0F,EAAiB1F,EAAU,MAAM,EAAG,EAAE,EAAE,MAAM,IAAI,EACxD,MAAO,CACL,EAAG,CAAC0F,EAAe,EAAE,EACrB,EAAG,CAACA,EAAe,EAAE,EACrB,OAAQ,CAACA,EAAe,CAAC,EACzB,OAAQ,CAACA,EAAe,CAAC,CAC1B,CACF,SAAU1F,EAAU,WAAW,SAAS,EAAG,CAC1C,MAAM0F,EAAiB1F,EAAU,MAAM,EAAG,EAAE,EAAE,MAAM,IAAI,EACxD,MAAO,CACL,EAAG,CAAC0F,EAAe,CAAC,EACpB,EAAG,CAACA,EAAe,CAAC,EACpB,OAAQ,CAACA,EAAe,CAAC,EACzB,OAAQ,CAACA,EAAe,CAAC,CAC1B,CACL,CAEE,OAAO,IACT,CAEA,SAASC,GAAiBnC,EAAMxD,EAAW0D,EAAiB,CAC1D,MAAMkC,EAAkBH,GAAezF,CAAS,EAEhD,GAAI,CAAC4F,EACH,OAAOpC,EAGT,KAAM,CACJ,OAAAvD,EACA,OAAAC,EACA,EAAG2F,EACH,EAAGC,CACP,EAAMF,EACE/F,EAAI2D,EAAK,KAAOqC,GAAc,EAAI5F,GAAU,WAAWyD,CAAe,EACtE5D,EAAI0D,EAAK,IAAMsC,GAAc,EAAI5F,GAAU,WAAWwD,EAAgB,MAAMA,EAAgB,QAAQ,GAAG,EAAI,CAAC,CAAC,EAC7GqC,EAAI9F,EAASuD,EAAK,MAAQvD,EAASuD,EAAK,MACxCwC,EAAI9F,EAASsD,EAAK,OAAStD,EAASsD,EAAK,OAC/C,MAAO,CACL,MAAOuC,EACP,OAAQC,EACR,IAAKlG,EACL,MAAOD,EAAIkG,EACX,OAAQjG,EAAIkG,EACZ,KAAMnG,CACP,CACH,CAEA,MAAMoG,GAAiB,CACrB,gBAAiB,EACnB,EAKA,SAASC,GAAcvK,EAASwH,EAAS,CACnCA,IAAY,SACdA,EAAU8C,IAGZ,IAAIzC,EAAO7H,EAAQ,sBAAuB,EAE1C,GAAIwH,EAAQ,gBAAiB,CAC3B,KAAM,CACJ,UAAAnD,EACA,gBAAA0D,CACD,EAAG3H,EAAUJ,CAAO,EAAE,iBAAiBA,CAAO,EAE3CqE,IACFwD,EAAOmC,GAAiBnC,EAAMxD,EAAW0D,CAAe,EAE9D,CAEE,KAAM,CACJ,IAAAS,EACA,KAAAC,EACA,MAAAG,EACA,OAAAC,EACA,OAAAF,EACA,MAAAD,CACJ,EAAMb,EACJ,MAAO,CACL,IAAAW,EACA,KAAAC,EACA,MAAAG,EACA,OAAAC,EACA,OAAAF,EACA,MAAAD,CACD,CACH,CAUA,SAAS8B,GAA+BxK,EAAS,CAC/C,OAAOuK,GAAcvK,EAAS,CAC5B,gBAAiB,EACrB,CAAG,CACH,CAEA,SAASyK,GAAoBzK,EAAS,CACpC,MAAM4I,EAAQ5I,EAAQ,WAChB6I,EAAS7I,EAAQ,YACvB,MAAO,CACL,IAAK,EACL,KAAM,EACN,MAAO4I,EACP,OAAQC,EACR,MAAAD,EACA,OAAAC,CACD,CACH,CAEA,SAAS6B,GAAQvK,EAAMwK,EAAe,CACpC,OAAIA,IAAkB,SACpBA,EAAgBvK,EAAUD,CAAI,EAAE,iBAAiBA,CAAI,GAGhDwK,EAAc,WAAa,OACpC,CAEA,SAASC,GAAa5K,EAAS2K,EAAe,CACxCA,IAAkB,SACpBA,EAAgBvK,EAAUJ,CAAO,EAAE,iBAAiBA,CAAO,GAG7D,MAAM6K,EAAgB,wBAEtB,MADmB,CAAC,WAAY,YAAa,WAAW,EACtC,KAAKpG,GAAY,CACjC,MAAM1C,EAAQ4I,EAAclG,CAAQ,EACpC,OAAO,OAAO1C,GAAU,SAAW8I,EAAc,KAAK9I,CAAK,EAAI,EACnE,CAAG,CACH,CAEA,SAAS+I,GAAuB9K,EAAS+K,EAAO,CAC9C,MAAMC,EAAgB,CAAE,EAExB,SAASC,EAAwB9K,EAAM,CAKrC,GAJI4K,GAAS,MAAQC,EAAc,QAAUD,GAIzC,CAAC5K,EACH,OAAO6K,EAGT,GAAIxK,GAAWL,CAAI,GAAKA,EAAK,kBAAoB,MAAQ,CAAC6K,EAAc,SAAS7K,EAAK,gBAAgB,EACpG,OAAA6K,EAAc,KAAK7K,EAAK,gBAAgB,EACjC6K,EAOT,GAJI,CAACtK,GAAcP,CAAI,GAAKQ,GAAaR,CAAI,GAIzC6K,EAAc,SAAS7K,CAAI,EAC7B,OAAO6K,EAGT,MAAML,EAAgBvK,EAAUJ,CAAO,EAAE,iBAAiBG,CAAI,EAQ9D,OANIA,IAASH,GACP4K,GAAazK,EAAMwK,CAAa,GAClCK,EAAc,KAAK7K,CAAI,EAIvBuK,GAAQvK,EAAMwK,CAAa,EACtBK,EAGFC,EAAwB9K,EAAK,UAAU,CAClD,CAEE,OAAKH,EAIEiL,EAAwBjL,CAAO,EAH7BgL,CAIX,CACA,SAASE,GAA2B/K,EAAM,CACxC,KAAM,CAACgL,CAAuB,EAAIL,GAAuB3K,EAAM,CAAC,EAChE,OAAOgL,GAA4D,IACrE,CAEA,SAASC,GAAqBpL,EAAS,CACrC,MAAI,CAACF,IAAa,CAACE,EACV,KAGLD,GAASC,CAAO,EACXA,EAGJE,GAAOF,CAAO,EAIfQ,GAAWR,CAAO,GAAKA,IAAYY,GAAiBZ,CAAO,EAAE,iBACxD,OAGLU,GAAcV,CAAO,EAChBA,EAGF,KAXE,IAYX,CAEA,SAASqL,GAAqBrL,EAAS,CACrC,OAAID,GAASC,CAAO,EACXA,EAAQ,QAGVA,EAAQ,UACjB,CACA,SAASsL,GAAqBtL,EAAS,CACrC,OAAID,GAASC,CAAO,EACXA,EAAQ,QAGVA,EAAQ,SACjB,CACA,SAASuL,GAAqBvL,EAAS,CACrC,MAAO,CACL,EAAGqL,GAAqBrL,CAAO,EAC/B,EAAGsL,GAAqBtL,CAAO,CAChC,CACH,CAEA,IAAIwL,GAEH,SAAUA,EAAW,CACpBA,EAAUA,EAAU,QAAa,CAAC,EAAI,UACtCA,EAAUA,EAAU,SAAc,EAAE,EAAI,UAC1C,GAAGA,IAAcA,EAAY,CAAA,EAAG,EAEhC,SAASC,GAA2BzL,EAAS,CAC3C,MAAI,CAACF,IAAa,CAACE,EACV,GAGFA,IAAY,SAAS,gBAC9B,CAEA,SAAS0L,GAAkBC,EAAoB,CAC7C,MAAMC,EAAY,CAChB,EAAG,EACH,EAAG,CACJ,EACKC,EAAaJ,GAA2BE,CAAkB,EAAI,CAClE,OAAQ,OAAO,YACf,MAAO,OAAO,UAClB,EAAM,CACF,OAAQA,EAAmB,aAC3B,MAAOA,EAAmB,WAC3B,EACKG,EAAY,CAChB,EAAGH,EAAmB,YAAcE,EAAW,MAC/C,EAAGF,EAAmB,aAAeE,EAAW,MACjD,EACKE,EAAQJ,EAAmB,WAAaC,EAAU,EAClDI,EAASL,EAAmB,YAAcC,EAAU,EACpDK,EAAWN,EAAmB,WAAaG,EAAU,EACrDI,EAAUP,EAAmB,YAAcG,EAAU,EAC3D,MAAO,CACL,MAAAC,EACA,OAAAC,EACA,SAAAC,EACA,QAAAC,EACA,UAAAJ,EACA,UAAAF,CACD,CACH,CAEA,MAAMO,GAAmB,CACvB,EAAG,GACH,EAAG,EACL,EACA,SAASC,GAA2BC,EAAiBC,EAAqB9H,EAAM+H,EAAcC,EAAqB,CACjH,GAAI,CACF,IAAAhE,EACA,KAAAC,EACA,MAAAC,EACA,OAAAC,CACJ,EAAMnE,EAEA+H,IAAiB,SACnBA,EAAe,IAGbC,IAAwB,SAC1BA,EAAsBL,IAGxB,KAAM,CACJ,MAAAJ,EACA,SAAAE,EACA,OAAAD,EACA,QAAAE,CACJ,EAAMR,GAAkBW,CAAe,EAC/BI,EAAY,CAChB,EAAG,EACH,EAAG,CACJ,EACKC,EAAQ,CACZ,EAAG,EACH,EAAG,CACJ,EACKC,EAAY,CAChB,OAAQL,EAAoB,OAASE,EAAoB,EACzD,MAAOF,EAAoB,MAAQE,EAAoB,CACxD,EAED,MAAI,CAACT,GAASvD,GAAO8D,EAAoB,IAAMK,EAAU,QAEvDF,EAAU,EAAIjB,EAAU,SACxBkB,EAAM,EAAIH,EAAe,KAAK,KAAKD,EAAoB,IAAMK,EAAU,OAASnE,GAAOmE,EAAU,MAAM,GAC9F,CAACV,GAAYtD,GAAU2D,EAAoB,OAASK,EAAU,SAEvEF,EAAU,EAAIjB,EAAU,QACxBkB,EAAM,EAAIH,EAAe,KAAK,KAAKD,EAAoB,OAASK,EAAU,OAAShE,GAAUgE,EAAU,MAAM,GAG3G,CAACT,GAAWxD,GAAS4D,EAAoB,MAAQK,EAAU,OAE7DF,EAAU,EAAIjB,EAAU,QACxBkB,EAAM,EAAIH,EAAe,KAAK,KAAKD,EAAoB,MAAQK,EAAU,MAAQjE,GAASiE,EAAU,KAAK,GAChG,CAACX,GAAUvD,GAAQ6D,EAAoB,KAAOK,EAAU,QAEjEF,EAAU,EAAIjB,EAAU,SACxBkB,EAAM,EAAIH,EAAe,KAAK,KAAKD,EAAoB,KAAOK,EAAU,MAAQlE,GAAQkE,EAAU,KAAK,GAGlG,CACL,UAAAF,EACA,MAAAC,CACD,CACH,CAEA,SAASE,GAAqB5M,EAAS,CACrC,GAAIA,IAAY,SAAS,iBAAkB,CACzC,KAAM,CACJ,WAAA6M,EACA,YAAAC,CACN,EAAQ,OACJ,MAAO,CACL,IAAK,EACL,KAAM,EACN,MAAOD,EACP,OAAQC,EACR,MAAOD,EACP,OAAQC,CACT,CACL,CAEE,KAAM,CACJ,IAAAtE,EACA,KAAAC,EACA,MAAAC,EACA,OAAAC,CACJ,EAAM3I,EAAQ,sBAAuB,EACnC,MAAO,CACL,IAAAwI,EACA,KAAAC,EACA,MAAAC,EACA,OAAAC,EACA,MAAO3I,EAAQ,YACf,OAAQA,EAAQ,YACjB,CACH,CAEA,SAAS+M,GAAiBC,EAAqB,CAC7C,OAAOA,EAAoB,OAAO,CAACpD,EAAKzJ,IAC/BsD,GAAImG,EAAK2B,GAAqBpL,CAAI,CAAC,EACzCwH,CAAkB,CACvB,CACA,SAASsF,GAAiBD,EAAqB,CAC7C,OAAOA,EAAoB,OAAO,CAACpD,EAAKzJ,IAC/ByJ,EAAMyB,GAAqBlL,CAAI,EACrC,CAAC,CACN,CACA,SAAS+M,GAAiBF,EAAqB,CAC7C,OAAOA,EAAoB,OAAO,CAACpD,EAAKzJ,IAC/ByJ,EAAM0B,GAAqBnL,CAAI,EACrC,CAAC,CACN,CAEA,SAASgN,GAAuBnN,EAASoN,EAAS,CAKhD,GAJIA,IAAY,SACdA,EAAU7C,IAGR,CAACvK,EACH,OAGF,KAAM,CACJ,IAAAwI,EACA,KAAAC,EACA,OAAAE,EACA,MAAAD,CACJ,EAAM0E,EAAQpN,CAAO,EACakL,GAA2BlL,CAAO,IAM9D2I,GAAU,GAAKD,GAAS,GAAKF,GAAO,OAAO,aAAeC,GAAQ,OAAO,aAC3EzI,EAAQ,eAAe,CACrB,MAAO,SACP,OAAQ,QACd,CAAK,CAEL,CAEA,MAAMqN,GAAa,CAAC,CAAC,IAAK,CAAC,OAAQ,OAAO,EAAGJ,EAAgB,EAAG,CAAC,IAAK,CAAC,MAAO,QAAQ,EAAGC,EAAgB,CAAC,EAC1G,MAAMI,EAAK,CACT,YAAYzF,EAAM7H,EAAS,CACzB,KAAK,KAAO,OACZ,KAAK,MAAQ,OACb,KAAK,OAAS,OACd,KAAK,IAAM,OACX,KAAK,OAAS,OACd,KAAK,MAAQ,OACb,KAAK,KAAO,OACZ,MAAMgN,EAAsBlC,GAAuB9K,CAAO,EACpDuN,EAAgBR,GAAiBC,CAAmB,EAC1D,KAAK,KAAO,CAAE,GAAGnF,CAChB,EACD,KAAK,MAAQA,EAAK,MAClB,KAAK,OAASA,EAAK,OAEnB,SAAW,CAAC2F,EAAMC,EAAMC,CAAe,IAAKL,GAC1C,UAAW9J,KAAOkK,EAChB,OAAO,eAAe,KAAMlK,EAAK,CAC/B,IAAK,IAAM,CACT,MAAMoK,EAAiBD,EAAgBV,CAAmB,EACpDY,EAAsBL,EAAcC,CAAI,EAAIG,EAClD,OAAO,KAAK,KAAKpK,CAAG,EAAIqK,CACzB,EACD,WAAY,EACtB,CAAS,EAIL,OAAO,eAAe,KAAM,OAAQ,CAClC,WAAY,EAClB,CAAK,CACL,CAEA,CAEA,MAAMC,EAAU,CACd,YAAYxN,EAAQ,CAClB,KAAK,OAAS,OACd,KAAK,UAAY,CAAE,EAEnB,KAAK,UAAY,IAAM,CACrB,KAAK,UAAU,QAAQsB,GAAY,CACjC,IAAImM,EAEJ,OAAQA,EAAe,KAAK,SAAW,KAAO,OAASA,EAAa,oBAAoB,GAAGnM,CAAQ,CAC3G,CAAO,CACF,EAED,KAAK,OAAStB,CAClB,CAEE,IAAI0N,EAAW9M,EAASuG,EAAS,CAC/B,IAAIwG,GAEHA,EAAgB,KAAK,SAAW,MAAgBA,EAAc,iBAAiBD,EAAW9M,EAASuG,CAAO,EAC3G,KAAK,UAAU,KAAK,CAACuG,EAAW9M,EAASuG,CAAO,CAAC,CACrD,CAEA,CAEA,SAASyG,GAAuB5N,EAAQ,CAMtC,KAAM,CACJ,YAAA6N,CACJ,EAAM9N,EAAUC,CAAM,EACpB,OAAOA,aAAkB6N,EAAc7N,EAASO,GAAiBP,CAAM,CACzE,CAEA,SAAS8N,GAAoBC,EAAOC,EAAa,CAC/C,MAAMC,EAAK,KAAK,IAAIF,EAAM,CAAC,EACrBG,EAAK,KAAK,IAAIH,EAAM,CAAC,EAE3B,OAAI,OAAOC,GAAgB,SAClB,KAAK,KAAKC,GAAM,EAAIC,GAAM,CAAC,EAAIF,EAGpC,MAAOA,GAAe,MAAOA,EACxBC,EAAKD,EAAY,GAAKE,EAAKF,EAAY,EAG5C,MAAOA,EACFC,EAAKD,EAAY,EAGtB,MAAOA,EACFE,EAAKF,EAAY,EAGnB,EACT,CAEA,IAAIG,GAEH,SAAUA,EAAW,CACpBA,EAAU,MAAW,QACrBA,EAAU,UAAe,YACzBA,EAAU,QAAa,UACvBA,EAAU,YAAiB,cAC3BA,EAAU,OAAY,SACtBA,EAAU,gBAAqB,kBAC/BA,EAAU,iBAAsB,kBAClC,GAAGA,IAAcA,EAAY,CAAA,EAAG,EAEhC,SAASC,GAAe7K,EAAO,CAC7BA,EAAM,eAAgB,CACxB,CACA,SAAS8K,GAAgB9K,EAAO,CAC9BA,EAAM,gBAAiB,CACzB,CAEA,IAAI+K,GAEH,SAAUA,EAAc,CACvBA,EAAa,MAAW,QACxBA,EAAa,KAAU,YACvBA,EAAa,MAAW,aACxBA,EAAa,KAAU,YACvBA,EAAa,GAAQ,UACrBA,EAAa,IAAS,SACtBA,EAAa,MAAW,QACxBA,EAAa,IAAS,KACxB,GAAGA,IAAiBA,EAAe,CAAA,EAAG,EAEtC,MAAMC,GAAuB,CAC3B,MAAO,CAACD,EAAa,MAAOA,EAAa,KAAK,EAC9C,OAAQ,CAACA,EAAa,GAAG,EACzB,IAAK,CAACA,EAAa,MAAOA,EAAa,MAAOA,EAAa,GAAG,CAChE,EACME,GAAkC,CAACjL,EAAOY,IAAS,CACvD,GAAI,CACF,mBAAAsK,CACJ,EAAMtK,EAEJ,OAAQZ,EAAM,KAAI,CAChB,KAAK+K,EAAa,MAChB,MAAO,CAAE,GAAGG,EACV,EAAGA,EAAmB,EAAI,EAC3B,EAEH,KAAKH,EAAa,KAChB,MAAO,CAAE,GAAGG,EACV,EAAGA,EAAmB,EAAI,EAC3B,EAEH,KAAKH,EAAa,KAChB,MAAO,CAAE,GAAGG,EACV,EAAGA,EAAmB,EAAI,EAC3B,EAEH,KAAKH,EAAa,GAChB,MAAO,CAAE,GAAGG,EACV,EAAGA,EAAmB,EAAI,EAC3B,CACP,CAGA,EAEA,MAAMC,EAAe,CACnB,YAAYC,EAAO,CACjB,KAAK,MAAQ,OACb,KAAK,kBAAoB,GACzB,KAAK,qBAAuB,OAC5B,KAAK,UAAY,OACjB,KAAK,gBAAkB,OACvB,KAAK,MAAQA,EACb,KAAM,CACJ,MAAO,CACL,OAAA3O,CACR,CACA,EAAQ2O,EACJ,KAAK,MAAQA,EACb,KAAK,UAAY,IAAInB,GAAUjN,GAAiBP,CAAM,CAAC,EACvD,KAAK,gBAAkB,IAAIwN,GAAUzN,EAAUC,CAAM,CAAC,EACtD,KAAK,cAAgB,KAAK,cAAc,KAAK,IAAI,EACjD,KAAK,aAAe,KAAK,aAAa,KAAK,IAAI,EAC/C,KAAK,OAAQ,CACjB,CAEE,QAAS,CACP,KAAK,YAAa,EAClB,KAAK,gBAAgB,IAAImO,EAAU,OAAQ,KAAK,YAAY,EAC5D,KAAK,gBAAgB,IAAIA,EAAU,iBAAkB,KAAK,YAAY,EACtE,WAAW,IAAM,KAAK,UAAU,IAAIA,EAAU,QAAS,KAAK,aAAa,CAAC,CAC9E,CAEE,aAAc,CACZ,KAAM,CACJ,WAAAS,EACA,QAAAC,CACD,EAAG,KAAK,MACH/O,EAAO8O,EAAW,KAAK,QAEzB9O,GACFgN,GAAuBhN,CAAI,EAG7B+O,EAAQvH,CAAkB,CAC9B,CAEE,cAAc/D,EAAO,CACnB,GAAIC,GAAgBD,CAAK,EAAG,CAC1B,KAAM,CACJ,OAAAsC,EACA,QAAAiJ,EACA,QAAA3H,CACD,EAAG,KAAK,MACH,CACJ,cAAA4H,EAAgBR,GAChB,iBAAAS,EAAmBR,GACnB,eAAAS,EAAiB,QACzB,EAAU9H,EACE,CACJ,KAAA+H,CACR,EAAU3L,EAEJ,GAAIwL,EAAc,IAAI,SAASG,CAAI,EAAG,CACpC,KAAK,UAAU3L,CAAK,EACpB,MACR,CAEM,GAAIwL,EAAc,OAAO,SAASG,CAAI,EAAG,CACvC,KAAK,aAAa3L,CAAK,EACvB,MACR,CAEM,KAAM,CACJ,cAAAuF,CACD,EAAGgG,EAAQ,QACNL,EAAqB3F,EAAgB,CACzC,EAAGA,EAAc,KACjB,EAAGA,EAAc,GACzB,EAAUxB,EAEC,KAAK,uBACR,KAAK,qBAAuBmH,GAG9B,MAAMU,EAAiBH,EAAiBzL,EAAO,CAC7C,OAAAsC,EACA,QAASiJ,EAAQ,QACjB,mBAAAL,CACR,CAAO,EAED,GAAIU,EAAgB,CAClB,MAAMC,EAAmB/L,GAAS8L,EAAgBV,CAAkB,EAC9DY,EAAc,CAClB,EAAG,EACH,EAAG,CACJ,EACK,CACJ,oBAAA1C,CACD,EAAGmC,EAAQ,QAEZ,UAAW9C,KAAmBW,EAAqB,CACjD,MAAMP,EAAY7I,EAAM,KAClB,CACJ,MAAAmI,EACA,QAAAG,EACA,OAAAF,EACA,SAAAC,EACA,UAAAH,EACA,UAAAF,CACZ,EAAcF,GAAkBW,CAAe,EAC/BsD,EAAoB/C,GAAqBP,CAAe,EACxDuD,EAAqB,CACzB,EAAG,KAAK,IAAInD,IAAckC,EAAa,MAAQgB,EAAkB,MAAQA,EAAkB,MAAQ,EAAIA,EAAkB,MAAO,KAAK,IAAIlD,IAAckC,EAAa,MAAQgB,EAAkB,KAAOA,EAAkB,KAAOA,EAAkB,MAAQ,EAAGH,EAAe,CAAC,CAAC,EAC5Q,EAAG,KAAK,IAAI/C,IAAckC,EAAa,KAAOgB,EAAkB,OAASA,EAAkB,OAAS,EAAIA,EAAkB,OAAQ,KAAK,IAAIlD,IAAckC,EAAa,KAAOgB,EAAkB,IAAMA,EAAkB,IAAMA,EAAkB,OAAS,EAAGH,EAAe,CAAC,CAAC,CAC7Q,EACKK,EAAapD,IAAckC,EAAa,OAAS,CAACzC,GAAWO,IAAckC,EAAa,MAAQ,CAAC3C,EACjG8D,EAAarD,IAAckC,EAAa,MAAQ,CAAC1C,GAAYQ,IAAckC,EAAa,IAAM,CAAC5C,EAErG,GAAI8D,GAAcD,EAAmB,IAAMJ,EAAe,EAAG,CAC3D,MAAMO,EAAuB1D,EAAgB,WAAaoD,EAAiB,EACrEO,EAA4BvD,IAAckC,EAAa,OAASoB,GAAwBjE,EAAU,GAAKW,IAAckC,EAAa,MAAQoB,GAAwBnE,EAAU,EAElL,GAAIoE,GAA6B,CAACP,EAAiB,EAAG,CAGpDpD,EAAgB,SAAS,CACvB,KAAM0D,EACN,SAAUT,CAC1B,CAAe,EACD,MACd,CAEgBU,EACFN,EAAY,EAAIrD,EAAgB,WAAa0D,EAE7CL,EAAY,EAAIjD,IAAckC,EAAa,MAAQtC,EAAgB,WAAaP,EAAU,EAAIO,EAAgB,WAAaT,EAAU,EAGnI8D,EAAY,GACdrD,EAAgB,SAAS,CACvB,KAAM,CAACqD,EAAY,EACnB,SAAUJ,CAC1B,CAAe,EAGH,KACD,SAAUQ,GAAcF,EAAmB,IAAMJ,EAAe,EAAG,CAClE,MAAMO,EAAuB1D,EAAgB,UAAYoD,EAAiB,EACpEO,EAA4BvD,IAAckC,EAAa,MAAQoB,GAAwBjE,EAAU,GAAKW,IAAckC,EAAa,IAAMoB,GAAwBnE,EAAU,EAE/K,GAAIoE,GAA6B,CAACP,EAAiB,EAAG,CAGpDpD,EAAgB,SAAS,CACvB,IAAK0D,EACL,SAAUT,CAC1B,CAAe,EACD,MACd,CAEgBU,EACFN,EAAY,EAAIrD,EAAgB,UAAY0D,EAE5CL,EAAY,EAAIjD,IAAckC,EAAa,KAAOtC,EAAgB,UAAYP,EAAU,EAAIO,EAAgB,UAAYT,EAAU,EAGhI8D,EAAY,GACdrD,EAAgB,SAAS,CACvB,IAAK,CAACqD,EAAY,EAClB,SAAUJ,CAC1B,CAAe,EAGH,KACZ,CACA,CAEQ,KAAK,WAAW1L,EAAOH,GAAIC,GAAS8L,EAAgB,KAAK,oBAAoB,EAAGE,CAAW,CAAC,CACpG,CACA,CACA,CAEE,WAAW9L,EAAOqM,EAAa,CAC7B,KAAM,CACJ,OAAAC,CACD,EAAG,KAAK,MACTtM,EAAM,eAAgB,EACtBsM,EAAOD,CAAW,CACtB,CAEE,UAAUrM,EAAO,CACf,KAAM,CACJ,MAAAuM,CACD,EAAG,KAAK,MACTvM,EAAM,eAAgB,EACtB,KAAK,OAAQ,EACbuM,EAAO,CACX,CAEE,aAAavM,EAAO,CAClB,KAAM,CACJ,SAAAwM,CACD,EAAG,KAAK,MACTxM,EAAM,eAAgB,EACtB,KAAK,OAAQ,EACbwM,EAAU,CACd,CAEE,QAAS,CACP,KAAK,UAAU,UAAW,EAC1B,KAAK,gBAAgB,UAAW,CACpC,CAEA,CACArB,GAAe,WAAa,CAAC,CAC3B,UAAW,YACX,QAAS,CAACnL,EAAOY,EAAM2B,IAAU,CAC/B,GAAI,CACF,cAAAiJ,EAAgBR,GAChB,aAAAyB,CACN,EAAQ7L,EACA,CACF,OAAA0B,CACN,EAAQC,EACJ,KAAM,CACJ,KAAAoJ,CACD,EAAG3L,EAAM,YAEV,GAAIwL,EAAc,MAAM,SAASG,CAAI,EAAG,CACtC,MAAMe,EAAYpK,EAAO,cAAc,QAEvC,OAAIoK,GAAa1M,EAAM,SAAW0M,EACzB,IAGT1M,EAAM,eAAgB,EACUyM,IAAa,CAC3C,MAAOzM,EAAM,WACrB,CAAO,EACM,GACb,CAEI,MAAO,EACX,CACA,CAAC,EAED,SAAS2M,GAAqBC,EAAY,CACxC,MAAO,GAAQA,GAAc,aAAcA,EAC7C,CAEA,SAASC,GAAkBD,EAAY,CACrC,MAAO,GAAQA,GAAc,UAAWA,EAC1C,CAEA,MAAME,EAAsB,CAC1B,YAAY1B,EAAO2B,EAAQC,EAAgB,CACzC,IAAIC,EAEAD,IAAmB,SACrBA,EAAiB3C,GAAuBe,EAAM,MAAM,MAAM,GAG5D,KAAK,MAAQ,OACb,KAAK,OAAS,OACd,KAAK,kBAAoB,GACzB,KAAK,SAAW,OAChB,KAAK,UAAY,GACjB,KAAK,mBAAqB,OAC1B,KAAK,UAAY,KACjB,KAAK,UAAY,OACjB,KAAK,kBAAoB,OACzB,KAAK,gBAAkB,OACvB,KAAK,MAAQA,EACb,KAAK,OAAS2B,EACd,KAAM,CACJ,MAAA/M,CACN,EAAQoL,EACE,CACJ,OAAA3O,CACN,EAAQuD,EACJ,KAAK,MAAQoL,EACb,KAAK,OAAS2B,EACd,KAAK,SAAW/P,GAAiBP,CAAM,EACvC,KAAK,kBAAoB,IAAIwN,GAAU,KAAK,QAAQ,EACpD,KAAK,UAAY,IAAIA,GAAU+C,CAAc,EAC7C,KAAK,gBAAkB,IAAI/C,GAAUzN,EAAUC,CAAM,CAAC,EACtD,KAAK,oBAAsBwQ,EAAuB5M,GAAoBL,CAAK,IAAM,KAAOiN,EAAuBlJ,EAC/G,KAAK,YAAc,KAAK,YAAY,KAAK,IAAI,EAC7C,KAAK,WAAa,KAAK,WAAW,KAAK,IAAI,EAC3C,KAAK,UAAY,KAAK,UAAU,KAAK,IAAI,EACzC,KAAK,aAAe,KAAK,aAAa,KAAK,IAAI,EAC/C,KAAK,cAAgB,KAAK,cAAc,KAAK,IAAI,EACjD,KAAK,oBAAsB,KAAK,oBAAoB,KAAK,IAAI,EAC7D,KAAK,OAAQ,CACjB,CAEE,QAAS,CACP,KAAM,CACJ,OAAAgJ,EACA,MAAO,CACL,QAAS,CACP,qBAAAG,EACA,2BAAAC,CACV,CACA,CACA,EAAQ,KAgBJ,GAfA,KAAK,UAAU,IAAIJ,EAAO,KAAK,KAAM,KAAK,WAAY,CACpD,QAAS,EACf,CAAK,EACD,KAAK,UAAU,IAAIA,EAAO,IAAI,KAAM,KAAK,SAAS,EAE9CA,EAAO,QACT,KAAK,UAAU,IAAIA,EAAO,OAAO,KAAM,KAAK,YAAY,EAG1D,KAAK,gBAAgB,IAAInC,EAAU,OAAQ,KAAK,YAAY,EAC5D,KAAK,gBAAgB,IAAIA,EAAU,UAAWC,EAAc,EAC5D,KAAK,gBAAgB,IAAID,EAAU,iBAAkB,KAAK,YAAY,EACtE,KAAK,gBAAgB,IAAIA,EAAU,YAAaC,EAAc,EAC9D,KAAK,kBAAkB,IAAID,EAAU,QAAS,KAAK,aAAa,EAE5DsC,EAAsB,CACxB,GAAIC,GAA8B,MAAQA,EAA2B,CACnE,MAAO,KAAK,MAAM,MAClB,WAAY,KAAK,MAAM,WACvB,QAAS,KAAK,MAAM,OAC5B,CAAO,EACC,OAAO,KAAK,YAAa,EAG3B,GAAIN,GAAkBK,CAAoB,EAAG,CAC3C,KAAK,UAAY,WAAW,KAAK,YAAaA,EAAqB,KAAK,EACxE,KAAK,cAAcA,CAAoB,EACvC,MACR,CAEM,GAAIP,GAAqBO,CAAoB,EAAG,CAC9C,KAAK,cAAcA,CAAoB,EACvC,MACR,CACA,CAEI,KAAK,YAAa,CACtB,CAEE,QAAS,CACP,KAAK,UAAU,UAAW,EAC1B,KAAK,gBAAgB,YAGrB,WAAW,KAAK,kBAAkB,UAAW,EAAE,EAE3C,KAAK,YAAc,OACrB,aAAa,KAAK,SAAS,EAC3B,KAAK,UAAY,KAEvB,CAEE,cAAcN,EAAYQ,EAAQ,CAChC,KAAM,CACJ,OAAA9K,EACA,UAAA+K,CACD,EAAG,KAAK,MACTA,EAAU/K,EAAQsK,EAAY,KAAK,mBAAoBQ,CAAM,CACjE,CAEE,aAAc,CACZ,KAAM,CACJ,mBAAAE,CACN,EAAQ,KACE,CACJ,QAAAhC,CACD,EAAG,KAAK,MAELgC,IACF,KAAK,UAAY,GAEjB,KAAK,kBAAkB,IAAI1C,EAAU,MAAOE,GAAiB,CAC3D,QAAS,EACjB,CAAO,EAED,KAAK,oBAAmB,EAExB,KAAK,kBAAkB,IAAIF,EAAU,gBAAiB,KAAK,mBAAmB,EAC9EU,EAAQgC,CAAkB,EAEhC,CAEE,WAAWtN,EAAO,CAChB,IAAIuN,EAEJ,KAAM,CACJ,UAAAC,EACA,mBAAAF,EACA,MAAAlC,CACN,EAAQ,KACE,CACJ,OAAAkB,EACA,QAAS,CACP,qBAAAY,CACR,CACA,EAAQ9B,EAEJ,GAAI,CAACkC,EACH,OAGF,MAAMjB,GAAekB,EAAwBlN,GAAoBL,CAAK,IAAM,KAAOuN,EAAwBxJ,EACrGyG,EAAQ1K,GAASwN,EAAoBjB,CAAW,EAEtD,GAAI,CAACmB,GAAaN,EAAsB,CACtC,GAAIP,GAAqBO,CAAoB,EAAG,CAC9C,GAAIA,EAAqB,WAAa,MAAQ3C,GAAoBC,EAAO0C,EAAqB,SAAS,EACrG,OAAO,KAAK,aAAc,EAG5B,GAAI3C,GAAoBC,EAAO0C,EAAqB,QAAQ,EAC1D,OAAO,KAAK,YAAa,CAEnC,CAEM,GAAIL,GAAkBK,CAAoB,GACpC3C,GAAoBC,EAAO0C,EAAqB,SAAS,EAC3D,OAAO,KAAK,aAAc,EAI9B,KAAK,cAAcA,EAAsB1C,CAAK,EAC9C,MACN,CAEQxK,EAAM,YACRA,EAAM,eAAgB,EAGxBsM,EAAOD,CAAW,CACtB,CAEE,WAAY,CACV,KAAM,CACJ,QAAAoB,EACA,MAAAlB,CACD,EAAG,KAAK,MACT,KAAK,OAAQ,EAER,KAAK,WACRkB,EAAQ,KAAK,MAAM,MAAM,EAG3BlB,EAAO,CACX,CAEE,cAAe,CACb,KAAM,CACJ,QAAAkB,EACA,SAAAjB,CACD,EAAG,KAAK,MACT,KAAK,OAAQ,EAER,KAAK,WACRiB,EAAQ,KAAK,MAAM,MAAM,EAG3BjB,EAAU,CACd,CAEE,cAAcxM,EAAO,CACfA,EAAM,OAAS+K,EAAa,KAC9B,KAAK,aAAc,CAEzB,CAEE,qBAAsB,CACpB,IAAI2C,GAEHA,EAAwB,KAAK,SAAS,aAAc,IAAK,MAAgBA,EAAsB,gBAAiB,CACrH,CAEA,CAEA,MAAMX,GAAS,CACb,OAAQ,CACN,KAAM,eACP,EACD,KAAM,CACJ,KAAM,aACP,EACD,IAAK,CACH,KAAM,WACV,CACA,EACA,MAAMY,WAAsBb,EAAsB,CAChD,YAAY1B,EAAO,CACjB,KAAM,CACJ,MAAApL,CACD,EAAGoL,EAGE4B,EAAiBhQ,GAAiBgD,EAAM,MAAM,EACpD,MAAMoL,EAAO2B,GAAQC,CAAc,CACvC,CAEA,CACAW,GAAc,WAAa,CAAC,CAC1B,UAAW,gBACX,QAAS,CAAC/M,EAAM2B,IAAU,CACxB,GAAI,CACF,YAAavC,CACnB,EAAQY,EACA,CACF,aAAA6L,CACN,EAAQlK,EAEJ,MAAI,CAACvC,EAAM,WAAaA,EAAM,SAAW,EAChC,IAGuByM,IAAa,CAC3C,MAAAzM,CACN,CAAK,EACM,GACX,CACA,CAAC,EAED,MAAM4N,GAAW,CACf,KAAM,CACJ,KAAM,WACP,EACD,IAAK,CACH,KAAM,SACV,CACA,EACA,IAAIC,IAEH,SAAUA,EAAa,CACtBA,EAAYA,EAAY,WAAgB,CAAC,EAAI,YAC/C,GAAGA,KAAgBA,GAAc,CAAA,EAAG,EAEpC,MAAMC,WAAoBhB,EAAsB,CAC9C,YAAY1B,EAAO,CACjB,MAAMA,EAAOwC,GAAU5Q,GAAiBoO,EAAM,MAAM,MAAM,CAAC,CAC/D,CAEA,CACA0C,GAAY,WAAa,CAAC,CACxB,UAAW,cACX,QAAS,CAAClN,EAAM2B,IAAU,CACxB,GAAI,CACF,YAAavC,CACnB,EAAQY,EACA,CACF,aAAA6L,CACN,EAAQlK,EAEJ,OAAIvC,EAAM,SAAW6N,GAAY,WACxB,IAGuBpB,IAAa,CAC3C,MAAAzM,CACN,CAAK,EACM,GACX,CACA,CAAC,EAED,MAAM+N,GAAW,CACf,OAAQ,CACN,KAAM,aACP,EACD,KAAM,CACJ,KAAM,WACP,EACD,IAAK,CACH,KAAM,UACV,CACA,EACA,MAAMC,WAAoBlB,EAAsB,CAC9C,YAAY1B,EAAO,CACjB,MAAMA,EAAO2C,EAAQ,CACzB,CAEE,OAAO,OAAQ,CAIb,cAAO,iBAAiBA,GAAS,KAAK,KAAMtK,EAAM,CAChD,QAAS,GACT,QAAS,EACf,CAAK,EACM,UAAoB,CACzB,OAAO,oBAAoBsK,GAAS,KAAK,KAAMtK,CAAI,CACzD,EAGI,SAASA,GAAO,CAAA,CACpB,CAEA,CACAuK,GAAY,WAAa,CAAC,CACxB,UAAW,eACX,QAAS,CAACpN,EAAM2B,IAAU,CACxB,GAAI,CACF,YAAavC,CACnB,EAAQY,EACA,CACF,aAAA6L,CACN,EAAQlK,EACJ,KAAM,CACJ,QAAA0L,CACN,EAAQjO,EAEJ,OAAIiO,EAAQ,OAAS,EACZ,IAGuBxB,IAAa,CAC3C,MAAAzM,CACN,CAAK,EACM,GACX,CACA,CAAC,EAED,IAAIkO,IAEH,SAAUA,EAAqB,CAC9BA,EAAoBA,EAAoB,QAAa,CAAC,EAAI,UAC1DA,EAAoBA,EAAoB,cAAmB,CAAC,EAAI,eAClE,GAAGA,KAAwBA,GAAsB,CAAA,EAAG,EAEpD,IAAIC,IAEH,SAAUA,EAAgB,CACzBA,EAAeA,EAAe,UAAe,CAAC,EAAI,YAClDA,EAAeA,EAAe,kBAAuB,CAAC,EAAI,mBAC5D,GAAGA,KAAmBA,GAAiB,CAAA,EAAG,EAE1C,SAASC,GAAgBxN,EAAM,CAC7B,GAAI,CACF,aAAA+H,EACA,UAAA+D,EAAYwB,GAAoB,QAChC,UAAAG,EACA,aAAAC,EACA,QAAAC,EACA,SAAAC,EAAW,EACX,MAAAC,EAAQN,GAAe,UACvB,mBAAAO,EACA,oBAAAtF,EACA,wBAAAuF,EACA,MAAAnE,EACA,UAAAzB,CACJ,EAAMnI,EACJ,MAAMgO,EAAeC,GAAgB,CACnC,MAAArE,EACA,SAAU,CAAC+D,CACf,CAAG,EACK,CAACO,EAAuBC,CAAuB,EAAInR,GAAa,EAChEoR,EAAczR,EAAAA,OAAO,CACzB,EAAG,EACH,EAAG,CACP,CAAG,EACK0R,EAAkB1R,EAAAA,OAAO,CAC7B,EAAG,EACH,EAAG,CACP,CAAG,EACK0G,EAAOzF,EAAAA,QAAQ,IAAM,CACzB,OAAQkO,EAAS,CACf,KAAKwB,GAAoB,QACvB,OAAOQ,EAAqB,CAC1B,IAAKA,EAAmB,EACxB,OAAQA,EAAmB,EAC3B,KAAMA,EAAmB,EACzB,MAAOA,EAAmB,CACpC,EAAY,KAEN,KAAKR,GAAoB,cACvB,OAAOI,CACf,CACG,EAAE,CAAC5B,EAAW4B,EAAcI,CAAkB,CAAC,EAC1CQ,EAAqB3R,EAAM,OAAC,IAAI,EAChC4R,EAAa3R,EAAAA,YAAY,IAAM,CACnC,MAAMiL,EAAkByG,EAAmB,QAE3C,GAAI,CAACzG,EACH,OAGF,MAAM2G,EAAaJ,EAAY,QAAQ,EAAIC,EAAgB,QAAQ,EAC7DI,EAAYL,EAAY,QAAQ,EAAIC,EAAgB,QAAQ,EAClExG,EAAgB,SAAS2G,EAAYC,CAAS,CAC/C,EAAE,EAAE,EACCC,EAA4B9Q,EAAO,QAAC,IAAMiQ,IAAUN,GAAe,UAAY,CAAC,GAAG/E,CAAmB,EAAE,QAAO,EAAKA,EAAqB,CAACqF,EAAOrF,CAAmB,CAAC,EAC3KjM,EAAAA,UAAU,IAAM,CACd,GAAI,CAACoR,GAAW,CAACnF,EAAoB,QAAU,CAACnF,EAAM,CACpD8K,EAAyB,EACzB,MACN,CAEI,UAAWtG,KAAmB6G,EAA2B,CACvD,GAAkCjB,IAAU5F,CAAe,IAAO,GAChE,SAGF,MAAM8G,EAAQnG,EAAoB,QAAQX,CAAe,EACnDC,EAAsBiG,EAAwBY,CAAK,EAEzD,GAAI,CAAC7G,EACH,SAGF,KAAM,CACJ,UAAAG,EACA,MAAAC,CACR,EAAUN,GAA2BC,EAAiBC,EAAqBzE,EAAM0E,EAAcI,CAAS,EAElG,UAAWa,IAAQ,CAAC,IAAK,GAAG,EACrBgF,EAAahF,CAAI,EAAEf,EAAUe,CAAI,CAAC,IACrCd,EAAMc,CAAI,EAAI,EACdf,EAAUe,CAAI,EAAI,GAItB,GAAId,EAAM,EAAI,GAAKA,EAAM,EAAI,EAAG,CAC9BiG,EAAyB,EACzBG,EAAmB,QAAUzG,EAC7BqG,EAAsBK,EAAYX,CAAQ,EAC1CQ,EAAY,QAAUlG,EACtBmG,EAAgB,QAAUpG,EAC1B,MACR,CACA,CAEImG,EAAY,QAAU,CACpB,EAAG,EACH,EAAG,CACJ,EACDC,EAAgB,QAAU,CACxB,EAAG,EACH,EAAG,CACJ,EACDF,EAAyB,CAC1B,EACD,CAACpG,EAAcwG,EAAYd,EAAWU,EAAyBR,EAASC,EACxE,KAAK,UAAUvK,CAAI,EACnB,KAAK,UAAU2K,CAAY,EAAGE,EAAuB1F,EAAqBkG,EAA2BX,EACrG,KAAK,UAAU5F,CAAS,CAAC,CAAC,CAC5B,CACA,MAAMyG,GAAsB,CAC1B,EAAG,CACD,CAAC5H,EAAU,QAAQ,EAAG,GACtB,CAACA,EAAU,OAAO,EAAG,EACtB,EACD,EAAG,CACD,CAACA,EAAU,QAAQ,EAAG,GACtB,CAACA,EAAU,OAAO,EAAG,EACzB,CACA,EAEA,SAASiH,GAAgBtM,EAAO,CAC9B,GAAI,CACF,MAAAiI,EACA,SAAAiF,CACJ,EAAMlN,EACJ,MAAMmN,EAAgB5Q,GAAY0L,CAAK,EACvC,OAAOlM,GAAYqR,GAAkB,CACnC,GAAIF,GAAY,CAACC,GAAiB,CAACC,EAEjC,OAAOH,GAGT,MAAM3G,EAAY,CAChB,EAAG,KAAK,KAAK2B,EAAM,EAAIkF,EAAc,CAAC,EACtC,EAAG,KAAK,KAAKlF,EAAM,EAAIkF,EAAc,CAAC,CAC5C,EAEI,MAAO,CACL,EAAG,CACD,CAAC9H,EAAU,QAAQ,EAAG+H,EAAe,EAAE/H,EAAU,QAAQ,GAAKiB,EAAU,IAAM,GAC9E,CAACjB,EAAU,OAAO,EAAG+H,EAAe,EAAE/H,EAAU,OAAO,GAAKiB,EAAU,IAAM,CAC7E,EACD,EAAG,CACD,CAACjB,EAAU,QAAQ,EAAG+H,EAAe,EAAE/H,EAAU,QAAQ,GAAKiB,EAAU,IAAM,GAC9E,CAACjB,EAAU,OAAO,EAAG+H,EAAe,EAAE/H,EAAU,OAAO,GAAKiB,EAAU,IAAM,CACpF,CACK,CACF,EAAE,CAAC4G,EAAUjF,EAAOkF,CAAa,CAAC,CACrC,CAEA,SAASE,GAAcC,EAAgB1Q,EAAI,CACzC,MAAM2Q,EAAgB3Q,GAAM,KAAO0Q,EAAe,IAAI1Q,CAAE,EAAI,OACtD5C,EAAOuT,EAAgBA,EAAc,KAAK,QAAU,KAC1D,OAAOxR,GAAYyR,GAAc,CAC/B,IAAInP,EAEJ,OAAIzB,GAAM,KACD,MAMDyB,EAAOrE,GAAsBwT,IAAe,KAAOnP,EAAO,IACtE,EAAK,CAACrE,EAAM4C,CAAE,CAAC,CACf,CAEA,SAAS6Q,GAAqBlM,EAASmM,EAAqB,CAC1D,OAAOzR,EAAAA,QAAQ,IAAMsF,EAAQ,OAAO,CAACtE,EAAamE,IAAW,CAC3D,KAAM,CACJ,OAAQuM,CACd,EAAQvM,EACEwM,EAAmBD,EAAO,WAAW,IAAIxD,IAAc,CAC3D,UAAWA,EAAU,UACrB,QAASuD,EAAoBvD,EAAU,QAAS/I,CAAM,CAC5D,EAAM,EACF,MAAO,CAAC,GAAGnE,EAAa,GAAG2Q,CAAgB,CAC5C,EAAE,EAAE,EAAG,CAACrM,EAASmM,CAAmB,CAAC,CACxC,CAEA,IAAIG,IAEH,SAAUA,EAAmB,CAC5BA,EAAkBA,EAAkB,OAAY,CAAC,EAAI,SACrDA,EAAkBA,EAAkB,eAAoB,CAAC,EAAI,iBAC7DA,EAAkBA,EAAkB,cAAmB,CAAC,EAAI,eAC9D,GAAGA,KAAsBA,GAAoB,CAAA,EAAG,EAEhD,IAAIC,IAEH,SAAUA,EAAoB,CAC7BA,EAAmB,UAAe,WACpC,GAAGA,KAAuBA,GAAqB,CAAA,EAAG,EAElD,MAAMC,GAA4B,IAAI,IACtC,SAASC,GAAsBC,EAAY5P,EAAM,CAC/C,GAAI,CACF,SAAA6P,EACA,aAAArS,EACA,OAAAsS,CACJ,EAAM9P,EACJ,KAAM,CAAC+P,EAAOC,CAAQ,EAAIlP,EAAAA,SAAS,IAAI,EACjC,CACJ,UAAAmP,EACA,QAAArH,EACA,SAAAsH,CACJ,EAAMJ,EACEK,EAAgBxT,EAAM,OAACiT,CAAU,EACjCf,EAAWuB,EAAY,EACvBC,EAAc/S,GAAeuR,CAAQ,EACrCyB,EAA6B1T,cAAY,SAAUwB,EAAK,CACxDA,IAAQ,SACVA,EAAM,CAAE,GAGN,CAAAiS,EAAY,SAIhBL,EAASzS,GACHA,IAAU,KACLa,EAGFb,EAAM,OAAOa,EAAI,OAAOG,GAAM,CAAChB,EAAM,SAASgB,CAAE,CAAC,CAAC,CAC1D,CACL,EAAK,CAAC8R,CAAW,CAAC,EACVE,EAAY5T,EAAM,OAAC,IAAI,EACvBiI,EAAiBlH,GAAY8S,GAAiB,CAClD,GAAI3B,GAAY,CAACgB,EACf,OAAOH,GAGT,GAAI,CAACc,GAAiBA,IAAkBd,IAAgBS,EAAc,UAAYP,GAAcG,GAAS,KAAM,CAC7G,MAAMU,EAAM,IAAI,IAEhB,QAASxO,KAAa2N,EAAY,CAChC,GAAI,CAAC3N,EACH,SAGF,GAAI8N,GAASA,EAAM,OAAS,GAAK,CAACA,EAAM,SAAS9N,EAAU,EAAE,GAAKA,EAAU,KAAK,QAAS,CAExFwO,EAAI,IAAIxO,EAAU,GAAIA,EAAU,KAAK,OAAO,EAC5C,QACV,CAEQ,MAAMtG,EAAOsG,EAAU,KAAK,QACtBoB,EAAO1H,EAAO,IAAImN,GAAKF,EAAQjN,CAAI,EAAGA,CAAI,EAAI,KACpDsG,EAAU,KAAK,QAAUoB,EAErBA,GACFoN,EAAI,IAAIxO,EAAU,GAAIoB,CAAI,CAEpC,CAEM,OAAOoN,CACb,CAEI,OAAOD,CACX,EAAK,CAACZ,EAAYG,EAAOF,EAAUhB,EAAUjG,CAAO,CAAC,EACnDrM,OAAAA,EAAAA,UAAU,IAAM,CACd4T,EAAc,QAAUP,CAC5B,EAAK,CAACA,CAAU,CAAC,EACfrT,EAAAA,UAAU,IAAM,CACVsS,GAIJyB,EAA4B,CAC7B,EACD,CAACT,EAAUhB,CAAQ,CAAC,EACpBtS,EAAAA,UAAU,IAAM,CACVwT,GAASA,EAAM,OAAS,GAC1BC,EAAS,IAAI,CAEhB,EACD,CAAC,KAAK,UAAUD,CAAK,CAAC,CAAC,EACvBxT,EAAAA,UAAU,IAAM,CACVsS,GAAY,OAAOoB,GAAc,UAAYM,EAAU,UAAY,OAIvEA,EAAU,QAAU,WAAW,IAAM,CACnCD,EAA4B,EAC5BC,EAAU,QAAU,IACrB,EAAEN,CAAS,EACb,EACD,CAACA,EAAWpB,EAAUyB,EAA4B,GAAG9S,CAAY,CAAC,EAC3D,CACL,eAAAoH,EACA,2BAAA0L,EACA,mBAAoBP,GAAS,IAC9B,EAED,SAASK,GAAa,CACpB,OAAQF,EAAQ,CACd,KAAKV,GAAkB,OACrB,MAAO,GAET,KAAKA,GAAkB,eACrB,OAAOK,EAET,QACE,MAAO,CAACA,CAChB,CACA,CACA,CAEA,SAASa,GAAgBnT,EAAOoT,EAAW,CACzC,OAAOjT,GAAY8S,GACZjT,EAIDiT,IAIG,OAAOG,GAAc,WAAaA,EAAUpT,CAAK,EAAIA,GAPnD,KAQR,CAACoT,EAAWpT,CAAK,CAAC,CACvB,CAEA,SAASqT,GAAejV,EAAMiN,EAAS,CACrC,OAAO8H,GAAgB/U,EAAMiN,CAAO,CACtC,CAOA,SAASiI,GAAoB7Q,EAAM,CACjC,GAAI,CACF,SAAArC,EACA,SAAAkR,CACJ,EAAM7O,EACJ,MAAM8Q,EAAkBtU,GAASmB,CAAQ,EACnCoT,EAAmBnT,EAAAA,QAAQ,IAAM,CACrC,GAAIiR,GAAY,OAAO,OAAW,KAAe,OAAO,OAAO,iBAAqB,IAClF,OAGF,KAAM,CACJ,iBAAAmC,CACN,EAAQ,OACJ,OAAO,IAAIA,EAAiBF,CAAe,CAC/C,EAAK,CAACA,EAAiBjC,CAAQ,CAAC,EAC9BtS,OAAAA,EAAAA,UAAU,IACD,IAA0CwU,GAAiB,WAAY,EAC7E,CAACA,CAAgB,CAAC,EACdA,CACT,CAOA,SAASE,GAAkBjR,EAAM,CAC/B,GAAI,CACF,SAAArC,EACA,SAAAkR,CACJ,EAAM7O,EACJ,MAAMkR,EAAe1U,GAASmB,CAAQ,EAChCwT,EAAiBvT,EAAAA,QAAQ,IAAM,CACnC,GAAIiR,GAAY,OAAO,OAAW,KAAe,OAAO,OAAO,eAAmB,IAChF,OAGF,KAAM,CACJ,eAAAuC,CACN,EAAQ,OACJ,OAAO,IAAIA,EAAeF,CAAY,CACvC,EACD,CAACrC,CAAQ,CAAC,EACVtS,OAAAA,EAAAA,UAAU,IACD,IAAwC4U,GAAe,WAAY,EACzE,CAACA,CAAc,CAAC,EACZA,CACT,CAEA,SAASE,GAAe7V,EAAS,CAC/B,OAAO,IAAIsN,GAAK/C,GAAcvK,CAAO,EAAGA,CAAO,CACjD,CAEA,SAAS8V,GAAQ9V,EAASoN,EAAS2I,EAAc,CAC3C3I,IAAY,SACdA,EAAUyI,IAGZ,KAAM,CAAChO,EAAMmO,CAAO,EAAI1Q,EAAAA,SAAS,IAAI,EAErC,SAAS2Q,GAAc,CACrBD,EAAQE,GAAe,CACrB,GAAI,CAAClW,EACH,OAAO,KAGT,GAAIA,EAAQ,cAAgB,GAAO,CACjC,IAAIwE,EAIJ,OAAQA,EAAO0R,GAAoCH,IAAiB,KAAOvR,EAAO,IAC1F,CAEM,MAAM2R,EAAU/I,EAAQpN,CAAO,EAE/B,OAAI,KAAK,UAAUkW,CAAW,IAAM,KAAK,UAAUC,CAAO,EACjDD,EAGFC,CACb,CAAK,CACL,CAEE,MAAMZ,EAAmBF,GAAoB,CAC3C,SAASe,EAAS,CAChB,GAAKpW,EAIL,UAAWqW,KAAUD,EAAS,CAC5B,KAAM,CACJ,KAAAtQ,EACA,OAAAzF,CACV,EAAYgW,EAEJ,GAAIvQ,IAAS,aAAezF,aAAkB,aAAeA,EAAO,SAASL,CAAO,EAAG,CACrFiW,EAAa,EACb,KACV,CACA,CACA,CAEA,CAAG,EACKN,EAAiBF,GAAkB,CACvC,SAAUQ,CACd,CAAG,EACD,OAAApV,EAA0B,IAAM,CAC9BoV,EAAa,EAETjW,GACgC2V,GAAe,QAAQ3V,CAAO,EAC5BuV,GAAiB,QAAQ,SAAS,KAAM,CAC1E,UAAW,GACX,QAAS,EACjB,CAAO,IAEiCI,GAAe,WAAY,EACzBJ,GAAiB,WAAY,EAEvE,EAAK,CAACvV,CAAO,CAAC,EACL6H,CACT,CAEA,SAASyO,GAAazO,EAAM,CAC1B,MAAM0O,EAAcrB,GAAgBrN,CAAI,EACxC,OAAO6B,GAAa7B,EAAM0O,CAAW,CACvC,CAEA,MAAMC,GAAiB,CAAE,EACzB,SAASC,GAAuBtW,EAAM,CACpC,MAAMuW,EAAevV,EAAM,OAAChB,CAAI,EAC1BwW,EAAYzU,GAAY8S,GACvB7U,EAID6U,GAAiBA,IAAkBwB,IAAkBrW,GAAQuW,EAAa,SAAWvW,EAAK,aAAeuW,EAAa,QAAQ,WACzH1B,EAGFlK,GAAuB3K,CAAI,EAPzBqW,GAQR,CAACrW,CAAI,CAAC,EACTY,OAAAA,EAAAA,UAAU,IAAM,CACd2V,EAAa,QAAUvW,CAC3B,EAAK,CAACA,CAAI,CAAC,EACFwW,CACT,CAEA,SAASC,GAAiBC,EAAU,CAClC,KAAM,CAACC,EAAmBC,CAAoB,EAAIzR,EAAAA,SAAS,IAAI,EACzD0R,EAAe7V,SAAO0V,CAAQ,EAE9BI,EAAe7V,EAAW,YAACwC,GAAS,CACxC,MAAMsT,EAAmB9L,GAAqBxH,EAAM,MAAM,EAErDsT,GAILH,EAAqBD,GACdA,GAILA,EAAkB,IAAII,EAAkB3L,GAAqB2L,CAAgB,CAAC,EACvE,IAAI,IAAIJ,CAAiB,GAJvB,IAKV,CACF,EAAE,EAAE,EACL/V,OAAAA,EAAAA,UAAU,IAAM,CACd,MAAMoW,EAAmBH,EAAa,QAEtC,GAAIH,IAAaM,EAAkB,CACjCC,EAAQD,CAAgB,EACxB,MAAM7T,EAAUuT,EAAS,IAAI7W,GAAW,CACtC,MAAMqX,EAAoBjM,GAAqBpL,CAAO,EAEtD,OAAIqX,GACFA,EAAkB,iBAAiB,SAAUJ,EAAc,CACzD,QAAS,EACrB,CAAW,EACM,CAACI,EAAmB9L,GAAqB8L,CAAiB,CAAC,GAG7D,IACR,CAAA,EAAE,OAAO9O,GAASA,GAAS,IAAI,EAChCwO,EAAqBzT,EAAQ,OAAS,IAAI,IAAIA,CAAO,EAAI,IAAI,EAC7D0T,EAAa,QAAUH,CAC7B,CAEI,MAAO,IAAM,CACXO,EAAQP,CAAQ,EAChBO,EAAQD,CAAgB,CACzB,EAED,SAASC,EAAQP,EAAU,CACzBA,EAAS,QAAQ7W,GAAW,CAC1B,MAAMqX,EAAoBjM,GAAqBpL,CAAO,EACjBqX,GAAkB,oBAAoB,SAAUJ,CAAY,CACzG,CAAO,CACP,CACA,EAAK,CAACA,EAAcJ,CAAQ,CAAC,EACpBzU,EAAO,QAAC,IACTyU,EAAS,OACJC,EAAoB,MAAM,KAAKA,EAAkB,OAAM,CAAE,EAAE,OAAO,CAAClN,EAAKqG,IAAgBxM,GAAImG,EAAKqG,CAAW,EAAGtI,CAAkB,EAAIoF,GAAiB8J,CAAQ,EAGhKlP,EACN,CAACkP,EAAUC,CAAiB,CAAC,CAClC,CAEA,SAASQ,GAAsB/J,EAAevL,EAAc,CACtDA,IAAiB,SACnBA,EAAe,CAAE,GAGnB,MAAMuV,EAAuBpW,EAAM,OAAC,IAAI,EACxCJ,OAAAA,EAAAA,UAAU,IAAM,CACdwW,EAAqB,QAAU,IAChC,EACDvV,CAAY,EACZjB,EAAAA,UAAU,IAAM,CACd,MAAMyW,EAAmBjK,IAAkB5F,EAEvC6P,GAAoB,CAACD,EAAqB,UAC5CA,EAAqB,QAAUhK,GAG7B,CAACiK,GAAoBD,EAAqB,UAC5CA,EAAqB,QAAU,KAErC,EAAK,CAAChK,CAAa,CAAC,EACXgK,EAAqB,QAAU7T,GAAS6J,EAAegK,EAAqB,OAAO,EAAI5P,CAChG,CAEA,SAAS8P,GAAe/P,EAAS,CAC/B3G,EAAAA,UAAU,IAAM,CACd,GAAI,CAACjB,GACH,OAGF,MAAM4X,EAAchQ,EAAQ,IAAIlD,GAAQ,CACtC,GAAI,CACF,OAAA+C,CACR,EAAU/C,EACJ,OAAO+C,EAAO,OAAS,KAAO,OAASA,EAAO,MAAO,CAC3D,CAAK,EACD,MAAO,IAAM,CACX,UAAWoQ,KAAYD,EACOC,IAAU,CAEzC,CACF,EAEDjQ,EAAQ,IAAIvB,GAAS,CACnB,GAAI,CACF,OAAAoB,CACN,EAAQpB,EACJ,OAAOoB,CACX,CAAG,CAAC,CACJ,CAEA,SAASqQ,GAAsB/R,EAAW9C,EAAI,CAC5C,OAAOX,EAAO,QAAC,IACNyD,EAAU,OAAO,CAAC+D,EAAKpF,IAAS,CACrC,GAAI,CACF,UAAAuJ,EACA,QAAA9M,CACR,EAAUuD,EAEJ,OAAAoF,EAAImE,CAAS,EAAInK,GAAS,CACxB3C,EAAQ2C,EAAOb,CAAE,CAClB,EAEM6G,CACR,EAAE,EAAE,EACJ,CAAC/D,EAAW9C,CAAE,CAAC,CACpB,CAEA,SAAS8U,GAAc7X,EAAS,CAC9B,OAAOoC,EAAO,QAAC,IAAMpC,EAAUyK,GAAoBzK,CAAO,EAAI,KAAM,CAACA,CAAO,CAAC,CAC/E,CAEA,MAAM8X,GAAiB,CAAE,EACzB,SAASC,GAASlB,EAAUzJ,EAAS,CAC/BA,IAAY,SACdA,EAAU7C,IAGZ,KAAM,CAACyN,CAAY,EAAInB,EACjBoB,EAAaJ,GAAcG,EAAe5X,EAAU4X,CAAY,EAAI,IAAI,EACxE,CAACE,EAAOC,CAAQ,EAAI7S,EAAAA,SAASwS,EAAc,EAEjD,SAASM,GAAe,CACtBD,EAAS,IACFtB,EAAS,OAIPA,EAAS,IAAI7W,GAAWyL,GAA2BzL,CAAO,EAAIiY,EAAa,IAAI3K,GAAKF,EAAQpN,CAAO,EAAGA,CAAO,CAAC,EAH5G8X,EAIV,CACL,CAEE,MAAMnC,EAAiBF,GAAkB,CACvC,SAAU2C,CACd,CAAG,EACD,OAAAvX,EAA0B,IAAM,CACI8U,GAAe,WAAY,EAC7DyC,EAAc,EACdvB,EAAS,QAAQ7W,GAA6C2V,GAAe,QAAQ3V,CAAO,CAAC,CACjG,EAAK,CAAC6W,CAAQ,CAAC,EACNqB,CACT,CAEA,SAASG,GAAkBlY,EAAM,CAC/B,GAAI,CAACA,EACH,OAAO,KAGT,GAAIA,EAAK,SAAS,OAAS,EACzB,OAAOA,EAGT,MAAMmY,EAAanY,EAAK,SAAS,CAAC,EAClC,OAAOO,GAAc4X,CAAU,EAAIA,EAAanY,CAClD,CAEA,SAASoY,GAAwB/T,EAAM,CACrC,GAAI,CACF,QAAA4I,CACJ,EAAM5I,EACJ,KAAM,CAACqD,EAAMmO,CAAO,EAAI1Q,EAAAA,SAAS,IAAI,EAC/BoQ,EAAetU,EAAW,YAACkC,GAAW,CAC1C,SAAW,CACT,OAAAjD,CACD,IAAIiD,EACH,GAAI5C,GAAcL,CAAM,EAAG,CACzB2V,EAAQnO,GAAQ,CACd,MAAMsO,EAAU/I,EAAQ/M,CAAM,EAC9B,OAAOwH,EAAO,CAAE,GAAGA,EACjB,MAAOsO,EAAQ,MACf,OAAQA,EAAQ,MAC5B,EAAcA,CACd,CAAS,EACD,KACR,CAEA,EAAK,CAAC/I,CAAO,CAAC,EACNuI,EAAiBF,GAAkB,CACvC,SAAUC,CACd,CAAG,EACK8C,EAAmBpX,EAAW,YAACpB,GAAW,CAC9C,MAAMG,EAAOkY,GAAkBrY,CAAO,EACJ2V,GAAe,WAAY,EAEzDxV,GACgCwV,GAAe,QAAQxV,CAAI,EAG/D6V,EAAQ7V,EAAOiN,EAAQjN,CAAI,EAAI,IAAI,CACvC,EAAK,CAACiN,EAASuI,CAAc,CAAC,EACtB,CAAC8C,EAASC,CAAM,EAAIpW,GAAWkW,CAAgB,EACrD,OAAOpW,EAAO,QAAC,KAAO,CACpB,QAAAqW,EACA,KAAA5Q,EACA,OAAA6Q,CACD,GAAG,CAAC7Q,EAAM4Q,EAASC,CAAM,CAAC,CAC7B,CAEA,MAAMC,GAAiB,CAAC,CACtB,OAAQpH,GACR,QAAS,CAAA,CACX,EAAG,CACD,OAAQxC,GACR,QAAS,CAAA,CACX,CAAC,EACK6J,GAAc,CAClB,QAAS,CAAA,CACX,EACMC,GAAgC,CACpC,UAAW,CACT,QAASrO,EACV,EACD,UAAW,CACT,QAASA,GACT,SAAUwJ,GAAkB,cAC5B,UAAWC,GAAmB,SAC/B,EACD,YAAa,CACX,QAAS1J,EACb,CACA,EAEA,MAAMuO,WAA+B,GAAI,CACvC,IAAI/V,EAAI,CACN,IAAIgW,EAEJ,OAAOhW,GAAM,OAAQgW,EAAa,MAAM,IAAIhW,CAAE,IAAM,KAAOgW,EAAyB,MACxF,CAEE,SAAU,CACR,OAAO,MAAM,KAAK,KAAK,OAAM,CAAE,CACnC,CAEE,YAAa,CACX,OAAO,KAAK,UAAU,OAAOvU,GAAQ,CACnC,GAAI,CACF,SAAA6O,CACR,EAAU7O,EACJ,MAAO,CAAC6O,CACd,CAAK,CACL,CAEE,WAAWtQ,EAAI,CACb,IAAIiW,EAAuBC,EAE3B,OAAQD,GAAyBC,EAAY,KAAK,IAAIlW,CAAE,IAAM,KAAO,OAASkW,EAAU,KAAK,UAAY,KAAOD,EAAwB,MAC5I,CAEA,CAEA,MAAME,GAAuB,CAC3B,eAAgB,KAChB,OAAQ,KACR,WAAY,KACZ,eAAgB,KAChB,WAAY,KACZ,kBAAmB,KACnB,eAA6B,IAAI,IACjC,eAA6B,IAAI,IACjC,oBAAkC,IAAIJ,GACtC,KAAM,KACN,YAAa,CACX,QAAS,CACP,QAAS,IACV,EACD,KAAM,KACN,OAAQzR,EACT,EACD,oBAAqB,CAAE,EACvB,wBAAyB,CAAE,EAC3B,uBAAwBwR,GACxB,2BAA4BxR,GAC5B,WAAY,KACZ,mBAAoB,EACtB,EACM8R,GAAyB,CAC7B,eAAgB,KAChB,WAAY,CAAE,EACd,OAAQ,KACR,eAAgB,KAChB,kBAAmB,CACjB,UAAW,EACZ,EACD,SAAU9R,GACV,eAA6B,IAAI,IACjC,KAAM,KACN,2BAA4BA,EAC9B,EACM+R,GAA+B5T,EAAa,cAAC2T,EAAsB,EACnEE,GAA6B7T,EAAa,cAAC0T,EAAoB,EAErE,SAASI,IAAkB,CACzB,MAAO,CACL,UAAW,CACT,OAAQ,KACR,mBAAoB,CAClB,EAAG,EACH,EAAG,CACJ,EACD,MAAO,IAAI,IACX,UAAW,CACT,EAAG,EACH,EAAG,CACX,CACK,EACD,UAAW,CACT,WAAY,IAAIR,EACtB,CACG,CACH,CACA,SAASS,GAAQC,EAAOC,EAAQ,CAC9B,OAAQA,EAAO,KAAI,CACjB,KAAKrS,EAAO,UACV,MAAO,CAAE,GAAGoS,EACV,UAAW,CAAE,GAAGA,EAAM,UACpB,mBAAoBC,EAAO,mBAC3B,OAAQA,EAAO,MACzB,CACO,EAEH,KAAKrS,EAAO,SACV,OAAIoS,EAAM,UAAU,QAAU,KACrBA,EAGF,CAAE,GAAGA,EACV,UAAW,CAAE,GAAGA,EAAM,UACpB,UAAW,CACT,EAAGC,EAAO,YAAY,EAAID,EAAM,UAAU,mBAAmB,EAC7D,EAAGC,EAAO,YAAY,EAAID,EAAM,UAAU,mBAAmB,CACzE,CACA,CACO,EAEH,KAAKpS,EAAO,QACZ,KAAKA,EAAO,WACV,MAAO,CAAE,GAAGoS,EACV,UAAW,CAAE,GAAGA,EAAM,UACpB,OAAQ,KACR,mBAAoB,CAClB,EAAG,EACH,EAAG,CACJ,EACD,UAAW,CACT,EAAG,EACH,EAAG,CACf,CACA,CACO,EAEH,KAAKpS,EAAO,kBACV,CACE,KAAM,CACJ,QAAApH,CACV,EAAYyZ,EACE,CACJ,GAAA1W,CACV,EAAY/C,EACEoU,EAAa,IAAI0E,GAAuBU,EAAM,UAAU,UAAU,EACxE,OAAApF,EAAW,IAAIrR,EAAI/C,CAAO,EACnB,CAAE,GAAGwZ,EACV,UAAW,CAAE,GAAGA,EAAM,UACpB,WAAApF,CACZ,CACS,CACT,CAEI,KAAKhN,EAAO,qBACV,CACE,KAAM,CACJ,GAAArE,EACA,IAAAQ,EACA,SAAA8P,CACV,EAAYoG,EACEzZ,EAAUwZ,EAAM,UAAU,WAAW,IAAIzW,CAAE,EAEjD,GAAI,CAAC/C,GAAWuD,IAAQvD,EAAQ,IAC9B,OAAOwZ,EAGT,MAAMpF,EAAa,IAAI0E,GAAuBU,EAAM,UAAU,UAAU,EACxE,OAAApF,EAAW,IAAIrR,EAAI,CAAE,GAAG/C,EACtB,SAAAqT,CACV,CAAS,EACM,CAAE,GAAGmG,EACV,UAAW,CAAE,GAAGA,EAAM,UACpB,WAAApF,CACZ,CACS,CACT,CAEI,KAAKhN,EAAO,oBACV,CACE,KAAM,CACJ,GAAArE,EACA,IAAAQ,CACV,EAAYkW,EACEzZ,EAAUwZ,EAAM,UAAU,WAAW,IAAIzW,CAAE,EAEjD,GAAI,CAAC/C,GAAWuD,IAAQvD,EAAQ,IAC9B,OAAOwZ,EAGT,MAAMpF,EAAa,IAAI0E,GAAuBU,EAAM,UAAU,UAAU,EACxE,OAAApF,EAAW,OAAOrR,CAAE,EACb,CAAE,GAAGyW,EACV,UAAW,CAAE,GAAGA,EAAM,UACpB,WAAApF,CACZ,CACS,CACT,CAEI,QAEI,OAAOoF,CAEf,CACA,CAEA,SAASE,GAAalV,EAAM,CAC1B,GAAI,CACF,SAAA6O,CACJ,EAAM7O,EACJ,KAAM,CACJ,OAAA0B,EACA,eAAAyT,EACA,eAAAlG,CACJ,EAAM9N,EAAAA,WAAWyT,EAAe,EACxBQ,EAAyBlX,GAAYiX,CAAc,EACnDE,EAAmBnX,GAAsCwD,GAAO,EAAE,EAExEnF,OAAAA,EAAAA,UAAU,IAAM,CACd,GAAI,CAAAsS,GAIA,CAACsG,GAAkBC,GAA0BC,GAAoB,KAAM,CAKzE,GAJI,CAAChW,GAAgB+V,CAAsB,GAIvC,SAAS,gBAAkBA,EAAuB,OAEpD,OAGF,MAAMlG,EAAgBD,EAAe,IAAIoG,CAAgB,EAEzD,GAAI,CAACnG,EACH,OAGF,KAAM,CACJ,cAAAoG,EACA,KAAA3Z,CACR,EAAUuT,EAEJ,GAAI,CAACoG,EAAc,SAAW,CAAC3Z,EAAK,QAClC,OAGF,sBAAsB,IAAM,CAC1B,UAAWH,IAAW,CAAC8Z,EAAc,QAAS3Z,EAAK,OAAO,EAAG,CAC3D,GAAI,CAACH,EACH,SAGF,MAAM+Z,EAAgBnV,GAAuB5E,CAAO,EAEpD,GAAI+Z,EAAe,CACjBA,EAAc,MAAO,EACrB,KACZ,CACA,CACA,CAAO,CACP,CACA,EAAK,CAACJ,EAAgBtG,EAAUI,EAAgBoG,EAAkBD,CAAsB,CAAC,EAChF,IACT,CAEA,SAASI,GAAeC,EAAWzV,EAAM,CACvC,GAAI,CACF,UAAAH,EACA,GAAG/C,CACP,EAAMkD,EACJ,OAAOyV,GAAa,MAAQA,EAAU,OAASA,EAAU,OAAO,CAAC7W,EAAaH,IACrEA,EAAS,CACd,UAAWG,EACX,GAAG9B,CACT,CAAK,EACA+C,CAAS,EAAIA,CAClB,CAEA,SAAS6V,GAA0B5F,EAAQ,CACzC,OAAOlS,EAAO,QAAC,KAAO,CACpB,UAAW,CAAE,GAAGyW,GAA8B,UAC5C,GAA8BvE,GAAO,SACtC,EACD,UAAW,CAAE,GAAGuE,GAA8B,UAC5C,GAA8BvE,GAAO,SACtC,EACD,YAAa,CAAE,GAAGuE,GAA8B,YAC9C,GAA8BvE,GAAO,WAC3C,CACA,GACE,CAA2BA,GAAO,UAAqCA,GAAO,UAAqCA,GAAO,WAAW,CAAC,CACxI,CAEA,SAAS6F,GAAiC3V,EAAM,CAC9C,GAAI,CACF,WAAAyK,EACA,QAAA7B,EACA,YAAAmJ,EACA,OAAAjC,EAAS,EACb,EAAM9P,EACJ,MAAM4V,EAAcjZ,EAAM,OAAC,EAAK,EAC1B,CACJ,EAAA+C,EACA,EAAAC,CACJ,EAAM,OAAOmQ,GAAW,UAAY,CAChC,EAAGA,EACH,EAAGA,CACP,EAAMA,EACJzT,EAA0B,IAAM,CAG9B,GAFiB,CAACqD,GAAK,CAACC,GAER,CAAC8K,EAAY,CAC3BmL,EAAY,QAAU,GACtB,MACN,CAEI,GAAIA,EAAY,SAAW,CAAC7D,EAG1B,OAIF,MAAMpW,EAAqC8O,GAAW,KAAK,QAE3D,GAAI,CAAC9O,GAAQA,EAAK,cAAgB,GAGhC,OAGF,MAAM0H,EAAOuF,EAAQjN,CAAI,EACnBka,EAAY3Q,GAAa7B,EAAM0O,CAAW,EAahD,GAXKrS,IACHmW,EAAU,EAAI,GAGXlW,IACHkW,EAAU,EAAI,GAIhBD,EAAY,QAAU,GAElB,KAAK,IAAIC,EAAU,CAAC,EAAI,GAAK,KAAK,IAAIA,EAAU,CAAC,EAAI,EAAG,CAC1D,MAAMlP,EAA0BD,GAA2B/K,CAAI,EAE3DgL,GACFA,EAAwB,SAAS,CAC/B,IAAKkP,EAAU,EACf,KAAMA,EAAU,CAC1B,CAAS,CAET,CACA,EAAK,CAACpL,EAAY/K,EAAGC,EAAGoS,EAAanJ,CAAO,CAAC,CAC7C,CAEA,MAAMkN,GAAsC9U,EAAAA,cAAc,CAAE,GAAGmC,EAC7D,OAAQ,EACR,OAAQ,CACV,CAAC,EACD,IAAI4S,IAEH,SAAUA,EAAQ,CACjBA,EAAOA,EAAO,cAAmB,CAAC,EAAI,gBACtCA,EAAOA,EAAO,aAAkB,CAAC,EAAI,eACrCA,EAAOA,EAAO,YAAiB,CAAC,EAAI,aACtC,GAAGA,KAAWA,GAAS,CAAA,EAAG,EAErB,MAACC,GAA0BC,EAAI,KAAC,SAAoBjW,EAAM,CAC7D,IAAIkW,EAAuBC,EAAuBC,EAAmBC,EAErE,GAAI,CACF,GAAA9X,EACA,cAAA+X,EACA,WAAA/H,EAAa,GACb,SAAAgI,EACA,QAAArT,EAAUiR,GACV,mBAAAqC,EAAqB9R,GACrB,UAAA+R,EACA,UAAAhB,EACA,GAAGjL,CACP,EAAMxK,EACJ,MAAM0W,EAAQC,EAAU,WAAC5B,GAAS,OAAWD,EAAe,EACtD,CAACE,EAAO4B,CAAQ,EAAIF,EACpB,CAACG,EAAsBC,CAAuB,EAAI1V,GAAuB,EACzE,CAAC2V,EAAQC,CAAS,EAAIlW,EAAAA,SAASiV,GAAO,aAAa,EACnDkB,EAAgBF,IAAWhB,GAAO,YAClC,CACJ,UAAW,CACT,OAAQmB,EACR,MAAOjI,EACP,UAAAkI,CACD,EACD,UAAW,CACT,WAAYtS,CAClB,CACA,EAAMmQ,EACErZ,EAAOub,GAAY,KAAOjI,EAAe,IAAIiI,CAAQ,EAAI,KACzDE,EAAcza,EAAAA,OAAO,CACzB,QAAS,KACT,WAAY,IAChB,CAAG,EACK+E,EAAS9D,EAAAA,QAAQ,IAAM,CAC3B,IAAIyZ,EAEJ,OAAOH,GAAY,KAAO,CACxB,GAAIA,EAEJ,MAAOG,EAAqC1b,GAAK,OAAS,KAAO0b,EAAajD,GAC9E,KAAMgD,CACZ,EAAQ,IACR,EAAK,CAACF,EAAUvb,CAAI,CAAC,EACb2b,EAAY3a,EAAM,OAAC,IAAI,EACvB,CAAC4a,GAAcC,EAAe,EAAI1W,EAAAA,SAAS,IAAI,EAC/C,CAACqU,EAAgBsC,EAAiB,EAAI3W,EAAAA,SAAS,IAAI,EACnD4W,GAAcpa,GAAekN,EAAO,OAAO,OAAOA,CAAK,CAAC,EACxDmN,GAAyBtZ,GAAY,iBAAkBE,CAAE,EACzDqZ,GAA6Bha,EAAAA,QAAQ,IAAMiH,EAAoB,WAAY,EAAE,CAACA,CAAmB,CAAC,EAClGgT,GAAyBnC,GAA0Be,CAAS,EAC5D,CACJ,eAAA7R,GACA,2BAAA0L,GACA,mBAAAwH,EACJ,EAAMnI,GAAsBiI,GAA4B,CACpD,SAAUX,EACV,aAAc,CAACE,EAAU,EAAGA,EAAU,CAAC,EACvC,OAAQU,GAAuB,SACnC,CAAG,EACKpN,EAAauE,GAAcC,EAAgBiI,CAAQ,EACnDa,GAAwBna,UAAQ,IAAMuX,EAAiB1V,GAAoB0V,CAAc,EAAI,KAAM,CAACA,CAAc,CAAC,EACnH6C,GAAoBC,GAAwB,EAC5CC,GAAwBtH,GAAenG,EAAYoN,GAAuB,UAAU,OAAO,EACjGlC,GAAiC,CAC/B,WAAYuB,GAAY,KAAOjI,EAAe,IAAIiI,CAAQ,EAAI,KAC9D,OAAQc,GAAkB,wBAC1B,YAAaE,GACb,QAASL,GAAuB,UAAU,OAC9C,CAAG,EACD,MAAMM,EAAiB7G,GAAQ7G,EAAYoN,GAAuB,UAAU,QAASK,EAAqB,EACpGE,GAAoB9G,GAAQ7G,EAAaA,EAAW,cAAgB,IAAI,EACxE4N,GAAgB1b,EAAAA,OAAO,CAC3B,eAAgB,KAChB,OAAQ,KACR,WAAA8N,EACA,cAAe,KACf,WAAY,KACZ,eAAA7F,GACA,eAAAqK,EACA,aAAc,KACd,iBAAkB,KAClB,oBAAApK,EACA,KAAM,KACN,oBAAqB,CAAE,EACvB,wBAAyB,IAC7B,CAAG,EACKyT,GAAWzT,EAAoB,YAAYqR,EAAwBmC,GAAc,QAAQ,OAAS,KAAO,OAASnC,EAAsB,EAAE,EAC1IqC,GAAcxE,GAAwB,CAC1C,QAAS8D,GAAuB,YAAY,OAChD,CAAG,EAEKW,IAAgBrC,EAAwBoC,GAAY,QAAQ,UAAY,KAAOpC,EAAwB1L,EACvGgO,GAAmBxB,GAAiBb,EAAoBmC,GAAY,OAAS,KAAOnC,EAAoB+B,EAAiB,KACzHO,GAAkB,GAAQH,GAAY,QAAQ,SAAWA,GAAY,MAGrEI,GAAgB7G,GAAa4G,GAAkB,KAAOP,CAAc,EAEpE1E,GAAaJ,GAAcmF,GAAe5c,EAAU4c,EAAY,EAAI,IAAI,EAExEhQ,EAAsByJ,GAAuBgF,EAAgBqB,IAA8B7N,EAAa,IAAI,EAC5GsD,GAA0BwF,GAAS/K,CAAmB,EAEtDoQ,GAAoBpD,GAAeC,EAAW,CAClD,UAAW,CACT,EAAG0B,EAAU,EAAIwB,GAAc,EAC/B,EAAGxB,EAAU,EAAIwB,GAAc,EAC/B,OAAQ,EACR,OAAQ,CACT,EACD,eAAAxD,EACA,OAAAzT,EACA,eAAAyW,EACA,kBAAAC,GACA,iBAAAK,GACA,KAAMJ,GAAc,QAAQ,KAC5B,gBAAiBE,GAAY,KAC7B,oBAAA/P,EACA,wBAAAuF,GACA,WAAA0F,EACJ,CAAG,EACK3F,GAAqBiK,GAAwB9Y,GAAI8Y,GAAuBZ,CAAS,EAAI,KACrFpO,GAAgBqJ,GAAiB5J,CAAmB,EAEpDqQ,GAAmB/F,GAAsB/J,EAAa,EAEtD+P,GAAwBhG,GAAsB/J,GAAe,CAACoP,CAAc,CAAC,EAC7EY,GAA0B9Z,GAAI2Z,GAAmBC,EAAgB,EACjElU,GAAgB8T,GAAmBpT,GAAgBoT,GAAkBG,EAAiB,EAAI,KAC1FhV,GAAalC,GAAUiD,GAAgB6R,EAAmB,CAC9D,OAAA9U,EACA,cAAAiD,GACA,eAAAC,GACA,oBAAqBgT,GACrB,mBAAA9J,EACD,CAAA,EAAI,KACCkL,GAASrV,GAAkBC,GAAY,IAAI,EAC3C,CAAChC,EAAMqX,EAAO,EAAInY,EAAQ,SAAC,IAAI,EAG/BoY,GAAmBR,GAAkBE,GAAoB3Z,GAAI2Z,GAAmBE,EAAqB,EACrGjZ,GAAYkF,GAAYmU,IAAmB7C,EAAqCzU,GAAK,OAAS,KAAOyU,EAAa,KAAM8B,CAAc,EACtIgB,GAAkBxc,EAAM,OAAC,IAAI,EAC7Byc,GAAoBxc,EAAAA,YAAY,CAACwC,EAAOuC,IAAU,CACtD,GAAI,CACF,OAAQ2N,EACR,QAAAtM,EACN,EAAQrB,EAEJ,GAAI2V,EAAU,SAAW,KACvB,OAGF,MAAM7M,EAAawE,EAAe,IAAIqI,EAAU,OAAO,EAEvD,GAAI,CAAC7M,EACH,OAGF,MAAM0K,EAAiB/V,EAAM,YACvBia,EAAiB,IAAI/J,EAAO,CAChC,OAAQgI,EAAU,QAClB,WAAA7M,EACA,MAAO0K,EACP,QAAAnS,GAGA,QAASqV,GAET,QAAQ9Z,EAAI,CAGV,GAAI,CAFkB0Q,EAAe,IAAI1Q,CAAE,EAGzC,OAGF,KAAM,CACJ,YAAA+a,CACD,EAAG5B,GAAY,QACVtY,EAAQ,CACZ,GAAAb,CACD,EAC8B+a,IAAYla,CAAK,EAChDyX,EAAqB,CACnB,KAAM,cACN,MAAAzX,CACV,CAAS,CACF,EAED,UAAUb,EAAIyN,GAAYU,EAAoBF,EAAQ,CAGpD,GAAI,CAFkByC,EAAe,IAAI1Q,CAAE,EAGzC,OAGF,KAAM,CACJ,cAAAgb,EACD,EAAG7B,GAAY,QACVtY,GAAQ,CACZ,GAAAb,EACA,WAAAyN,GACA,mBAAAU,EACA,OAAAF,CACD,EACgC+M,KAAcna,EAAK,EACpDyX,EAAqB,CACnB,KAAM,gBACN,MAAAzX,EACV,CAAS,CACF,EAED,QAAQsN,EAAoB,CAC1B,MAAMnO,GAAK+Y,EAAU,QAErB,GAAI/Y,IAAM,KACR,OAGF,MAAM2Q,EAAgBD,EAAe,IAAI1Q,EAAE,EAE3C,GAAI,CAAC2Q,EACH,OAGF,KAAM,CACJ,YAAAsK,CACD,EAAG9B,GAAY,QACVtY,GAAQ,CACZ,eAAA+V,EACA,OAAQ,CACN,GAAA5W,GACA,KAAM2Q,EAAc,KACpB,KAAMkI,CAClB,CACS,EACDqC,GAAAA,wBAAwB,IAAM,CACGD,IAAYpa,EAAK,EAChD4X,EAAUjB,GAAO,YAAY,EAC7Ba,EAAS,CACP,KAAMhU,EAAO,UACb,mBAAA8J,EACA,OAAQnO,EACpB,CAAW,EACDsY,EAAqB,CACnB,KAAM,cACN,MAAAzX,EACZ,CAAW,EACDoY,GAAgB2B,GAAgB,OAAO,EACvC1B,GAAkBtC,CAAc,CAC1C,CAAS,CACF,EAED,OAAO1J,EAAa,CAClBmL,EAAS,CACP,KAAMhU,EAAO,SACb,YAAA6I,CACV,CAAS,CACF,EAED,MAAOiO,GAAc9W,EAAO,OAAO,EACnC,SAAU8W,GAAc9W,EAAO,UAAU,CAC/C,CAAK,EACDuW,GAAgB,QAAUE,EAE1B,SAASK,GAAcpY,EAAM,CAC3B,OAAO,gBAAyB,CAC9B,KAAM,CACJ,OAAAI,EACA,WAAAkC,EACA,KAAAhC,GACA,wBAAAmX,EACD,EAAGV,GAAc,QAClB,IAAIjZ,GAAQ,KAEZ,GAAIsC,GAAUqX,GAAyB,CACrC,KAAM,CACJ,WAAAY,EACD,EAAGjC,GAAY,QAChBtY,GAAQ,CACN,eAAA+V,EACA,OAAQzT,EACR,WAAAkC,EACA,MAAOmV,GACP,KAAAnX,EACD,EAEGN,IAASsB,EAAO,SAAW,OAAO+W,IAAe,YAC9B,MAAM,QAAQ,QAAQA,GAAWva,EAAK,CAAC,IAG1DkC,EAAOsB,EAAO,WAG5B,CAEQ0U,EAAU,QAAU,KACpBmC,GAAAA,wBAAwB,IAAM,CAC5B7C,EAAS,CACP,KAAAtV,CACZ,CAAW,EACD0V,EAAUjB,GAAO,aAAa,EAC9BkD,GAAQ,IAAI,EACZzB,GAAgB,IAAI,EACpBC,GAAkB,IAAI,EACtB0B,GAAgB,QAAU,KAC1B,MAAM5P,GAAYjI,IAASsB,EAAO,QAAU,YAAc,eAE1D,GAAIxD,GAAO,CACT,MAAM3C,GAAUib,GAAY,QAAQnO,EAAS,EAClB9M,KAAQ2C,EAAK,EACxCyX,EAAqB,CACnB,KAAMtN,GACN,MAAAnK,EACd,CAAa,CACb,CACA,CAAS,CACF,CACP,CACG,EACD,CAAC6P,CAAc,CAAC,EACV2K,GAAoChd,EAAAA,YAAY,CAACH,EAASsG,IACvD,CAAC3D,EAAOsC,KAAW,CACxB,MAAMmY,EAAcza,EAAM,YACpB0a,EAAsB7K,EAAe,IAAIvN,EAAM,EAErD,GACA4V,EAAU,UAAY,MACtB,CAACwC,GACDD,EAAY,QAAUA,EAAY,iBAChC,OAGF,MAAME,EAAoB,CACxB,OAAQD,CACT,EACsBrd,EAAQ2C,EAAO2D,EAAO,QAASgX,CAAiB,IAEhD,KACrBF,EAAY,OAAS,CACnB,WAAY9W,EAAO,MACpB,EACDuU,EAAU,QAAU5V,GACpB0X,GAAkBha,EAAO2D,CAAM,EAElC,EACA,CAACkM,EAAgBmK,EAAiB,CAAC,EAChCY,GAAa5K,GAAqBlM,EAAS0W,EAAiC,EAClF3G,GAAe/P,CAAO,EACtB7G,EAA0B,IAAM,CAC1B8b,GAAkBpB,IAAWhB,GAAO,cACtCiB,EAAUjB,GAAO,WAAW,CAElC,EAAK,CAACoC,EAAgBpB,CAAM,CAAC,EAC3Bxa,EAAAA,UAAU,IAAM,CACd,KAAM,CACJ,WAAA0d,CACD,EAAGvC,GAAY,QACV,CACJ,OAAAhW,EACA,eAAAyT,EACA,WAAAvR,GACA,KAAAhC,CACD,EAAGyW,GAAc,QAElB,GAAI,CAAC3W,GAAU,CAACyT,EACd,OAGF,MAAM/V,EAAQ,CACZ,OAAAsC,EACA,eAAAyT,EACA,WAAAvR,GACA,MAAO,CACL,EAAGmV,GAAwB,EAC3B,EAAGA,GAAwB,CAC5B,EACD,KAAAnX,CACD,EACD6X,GAAAA,wBAAwB,IAAM,CACEQ,IAAW7a,CAAK,EAC9CyX,EAAqB,CACnB,KAAM,aACN,MAAAzX,CACR,CAAO,CACP,CAAK,CACF,EACD,CAAC2Z,GAAwB,EAAGA,GAAwB,CAAC,CAAC,EACtDxc,EAAAA,UAAU,IAAM,CACd,KAAM,CACJ,OAAAmF,EACA,eAAAyT,EACA,WAAAvR,EACA,oBAAAiB,GACA,wBAAAkU,CACD,EAAGV,GAAc,QAElB,GAAI,CAAC3W,GAAU4V,EAAU,SAAW,MAAQ,CAACnC,GAAkB,CAAC4D,EAC9D,OAGF,KAAM,CACJ,WAAAmB,CACD,EAAGxC,GAAY,QACVyC,EAAgBtV,GAAoB,IAAImU,EAAM,EAC9CpX,GAAOuY,GAAiBA,EAAc,KAAK,QAAU,CACzD,GAAIA,EAAc,GAClB,KAAMA,EAAc,KAAK,QACzB,KAAMA,EAAc,KACpB,SAAUA,EAAc,QAC9B,EAAQ,KACE/a,EAAQ,CACZ,OAAAsC,EACA,eAAAyT,EACA,WAAAvR,EACA,MAAO,CACL,EAAGmV,EAAwB,EAC3B,EAAGA,EAAwB,CAC5B,EACD,KAAAnX,EACD,EACD6X,GAAAA,wBAAwB,IAAM,CAC5BR,GAAQrX,EAAI,EACkBsY,IAAW9a,CAAK,EAC9CyX,EAAqB,CACnB,KAAM,aACN,MAAAzX,CACR,CAAO,CACP,CAAK,CACF,EACD,CAAC4Z,EAAM,CAAC,EACR3c,EAA0B,IAAM,CAC9Bgc,GAAc,QAAU,CACtB,eAAAlD,EACA,OAAAzT,EACA,WAAA+I,EACA,cAAA9F,GACA,WAAAf,GACA,eAAAgB,GACA,eAAAqK,EACA,aAAAuJ,GACA,iBAAAC,GACA,oBAAA5T,EACA,KAAAjD,EACA,oBAAA4G,EACA,wBAAAuQ,EACD,EACD3B,EAAY,QAAU,CACpB,QAASqB,GACT,WAAY9T,EACb,CACF,EAAE,CAACjD,EAAQ+I,EAAY7G,GAAYe,GAAesK,EAAgBuJ,GAAcC,GAAkB7T,GAAgBC,EAAqBjD,EAAM4G,EAAqBuQ,EAAuB,CAAC,EAC3LvL,GAAgB,CAAE,GAAGwK,GACnB,MAAOb,EACP,aAAcxS,GACd,mBAAAmJ,GACA,oBAAAtF,EACA,wBAAAuF,EACJ,CAAG,EACD,MAAMqM,GAAgBxc,EAAAA,QAAQ,KACZ,CACd,OAAA8D,EACA,WAAA+I,EACA,eAAA0N,EACA,eAAAhD,EACA,WAAAvR,GACA,kBAAAwU,GACA,YAAAG,GACA,eAAAtJ,EACA,oBAAApK,EACA,eAAAD,GACA,KAAAhD,EACA,2BAAA0O,GACA,oBAAA9H,EACA,wBAAAuF,GACA,uBAAA8J,GACA,mBAAAC,GACA,WAAArE,EACD,GAEA,CAAC/R,EAAQ+I,EAAY0N,EAAgBhD,EAAgBvR,GAAYwU,GAAmBG,GAAatJ,EAAgBpK,EAAqBD,GAAgBhD,EAAM0O,GAA4B9H,EAAqBuF,GAAyB8J,GAAwBC,GAAoBrE,EAAU,CAAC,EAC1R4G,GAAkBzc,EAAAA,QAAQ,KACd,CACd,eAAAuX,EACA,WAAA6E,GACA,OAAAtY,EACA,eAAAyW,EACA,kBAAmB,CACjB,UAAWR,EACZ,EACD,SAAAf,EACA,eAAA3H,EACA,KAAArN,EACA,2BAAA0O,EACD,GAEA,CAAC6E,EAAgB6E,GAAYtY,EAAQyW,EAAgBvB,EAAUe,GAAwB1I,EAAgBrN,EAAM0O,EAA0B,CAAC,EAC3I,OAAO/P,EAAM,cAAcQ,GAAkB,SAAU,CACrD,MAAO+V,CACR,EAAEvW,EAAM,cAAcqU,GAAgB,SAAU,CAC/C,MAAOyF,EACR,EAAE9Z,EAAM,cAAcsU,GAAc,SAAU,CAC7C,MAAOuF,EACR,EAAE7Z,EAAM,cAAcuV,GAAuB,SAAU,CACtD,MAAOjW,EACR,EAAE0W,CAAQ,CAAC,EAAGhW,EAAM,cAAc2U,GAAc,CAC/C,SAA4CoB,GAAc,eAAkB,EAC7E,CAAA,CAAC,EAAG/V,EAAM,cAAcwB,GAAe,CAAE,GAAGuU,EAC3C,wBAAyBqB,EAC7B,CAAG,CAAC,EAEF,SAASM,IAAyB,CAChC,MAAMqC,EAAkE/C,IAAa,oBAAuB,GACtGgD,EAA6B,OAAOhM,GAAe,SAAWA,EAAW,UAAY,GAAQA,IAAe,GAC5GZ,EAAUsJ,GAAiB,CAACqD,GAAkC,CAACC,EAErE,OAAI,OAAOhM,GAAe,SACjB,CAAE,GAAGA,EACV,QAAAZ,CACD,EAGI,CACL,QAAAA,CACD,CACL,CACA,CAAC,EAEK6M,GAA2BxZ,EAAa,cAAC,IAAI,EAC7CyZ,GAAc,SACdC,GAAY,YAClB,SAASC,GAAa3a,EAAM,CAC1B,GAAI,CACF,GAAAzB,EACA,KAAAqc,EACA,SAAA/L,EAAW,GACX,WAAAgM,CACJ,EAAM7a,EACJ,MAAMjB,EAAMV,GAAYqc,EAAS,EAC3B,CACJ,WAAAV,EACA,eAAA7E,EACA,OAAAzT,EACA,eAAAyW,EACA,kBAAA2C,EACA,eAAA7L,EACA,KAAArN,CACJ,EAAMT,EAAAA,WAAWyT,EAAe,EACxB,CACJ,KAAAmG,EAAON,GACP,gBAAAO,EAAkB,YAClB,SAAAC,EAAW,CACf,EAAMJ,GAAkC,CAAE,EAClCK,EAAwCxZ,GAAO,KAAQnD,EACvDsB,EAAYsB,EAAU,WAAC+Z,EAAapF,GAAyB0E,EAAW,EACxE,CAAC7e,EAAMsC,CAAU,EAAIH,GAAY,EACjC,CAACwX,EAAe6F,CAAmB,EAAIrd,GAAY,EACnDuD,EAAY+R,GAAsB4G,EAAYzb,CAAE,EAChD6c,EAAU9d,GAAesd,CAAI,EACnCve,EAA0B,KACxB4S,EAAe,IAAI1Q,EAAI,CACrB,GAAAA,EACA,IAAAQ,EACA,KAAApD,EACA,cAAA2Z,EACA,KAAM8F,CACZ,CAAK,EACM,IAAM,CACX,MAAMzf,EAAOsT,EAAe,IAAI1Q,CAAE,EAE9B5C,GAAQA,EAAK,MAAQoD,GACvBkQ,EAAe,OAAO1Q,CAAE,CAE3B,GAEH,CAAC0Q,EAAgB1Q,CAAE,CAAC,EACpB,MAAM8c,EAAqBzd,EAAAA,QAAQ,KAAO,CACxC,KAAAmd,EACA,SAAAE,EACA,gBAAiBpM,EACjB,eAAgBqM,GAAcH,IAASN,GAAc,GAAO,OAC5D,uBAAwBO,EACxB,mBAAoBF,EAAkB,SAC1C,GAAM,CAACjM,EAAUkM,EAAME,EAAUC,EAAYF,EAAiBF,EAAkB,SAAS,CAAC,EACxF,MAAO,CACL,OAAApZ,EACA,eAAAyT,EACA,eAAAgD,EACA,WAAYkD,EACZ,WAAAH,EACA,UAAWrM,EAAW,OAAYxN,EAClC,KAAA1F,EACA,KAAAiG,EACA,WAAA3D,EACA,oBAAAkd,EACA,UAAAtb,CACD,CACH,CAEA,SAASyb,IAAgB,CACvB,OAAOna,EAAAA,WAAW0T,EAAa,CACjC,CAEA,MAAM0G,GAAc,YACdC,GAA8B,CAClC,QAAS,EACX,EACA,SAASC,GAAazb,EAAM,CAC1B,GAAI,CACF,KAAA4a,EACA,SAAA/L,EAAW,GACX,GAAAtQ,EACA,qBAAAmd,CACJ,EAAM1b,EACJ,MAAMjB,EAAMV,GAAYkd,EAAW,EAC7B,CACJ,OAAA7Z,EACA,SAAAkV,EACA,KAAAhV,EACA,2BAAA0O,CACJ,EAAMnP,EAAAA,WAAWyT,EAAe,EACxB+G,EAAWhf,EAAAA,OAAO,CACtB,SAAAkS,CACJ,CAAG,EACK+M,EAA0Bjf,EAAM,OAAC,EAAK,EACtC0G,EAAO1G,EAAM,OAAC,IAAI,EAClBkf,EAAalf,EAAM,OAAC,IAAI,EACxB,CACJ,SAAUmf,EACV,sBAAAC,EACA,QAASC,CACV,EAAG,CAAE,GAAGR,GACP,GAAGE,CACJ,EACKtd,EAAMd,GAAeye,GAAwDxd,CAAE,EAC/E2S,EAAetU,EAAAA,YAAY,IAAM,CACrC,GAAI,CAACgf,EAAwB,QAAS,CAGpCA,EAAwB,QAAU,GAClC,MACN,CAEQC,EAAW,SAAW,MACxB,aAAaA,EAAW,OAAO,EAGjCA,EAAW,QAAU,WAAW,IAAM,CACpCvL,EAA2B,MAAM,QAAQlS,EAAI,OAAO,EAAIA,EAAI,QAAU,CAACA,EAAI,OAAO,CAAC,EACnFyd,EAAW,QAAU,IACtB,EAAEG,CAAqB,CACzB,EACD,CAACA,CAAqB,CAAC,EACjB7K,EAAiBF,GAAkB,CACvC,SAAUC,EACV,SAAU4K,GAA0B,CAACpa,CACzC,CAAG,EACKsS,EAAmBpX,EAAAA,YAAY,CAACqf,EAAYC,IAAoB,CAC/D/K,IAID+K,IACF/K,EAAe,UAAU+K,CAAe,EACxCN,EAAwB,QAAU,IAGhCK,GACF9K,EAAe,QAAQ8K,CAAU,EAEvC,EAAK,CAAC9K,CAAc,CAAC,EACb,CAAC8C,EAAShW,CAAU,EAAIH,GAAWkW,CAAgB,EACnDoH,EAAU9d,GAAesd,CAAI,EACnCre,OAAAA,EAAAA,UAAU,IAAM,CACV,CAAC4U,GAAkB,CAAC8C,EAAQ,UAIhC9C,EAAe,WAAY,EAC3ByK,EAAwB,QAAU,GAClCzK,EAAe,QAAQ8C,EAAQ,OAAO,EAC1C,EAAK,CAACA,EAAS9C,CAAc,CAAC,EAC5B5U,EAAAA,UAAU,KACRqa,EAAS,CACP,KAAMhU,EAAO,kBACb,QAAS,CACP,GAAArE,EACA,IAAAQ,EACA,SAAA8P,EACA,KAAMoF,EACN,KAAA5Q,EACA,KAAM+X,CACd,CACA,CAAK,EACM,IAAMxE,EAAS,CACpB,KAAMhU,EAAO,oBACb,IAAA7D,EACA,GAAAR,CACN,CAAK,GAEH,CAACA,CAAE,CAAC,EACJhC,EAAAA,UAAU,IAAM,CACVsS,IAAa8M,EAAS,QAAQ,WAChC/E,EAAS,CACP,KAAMhU,EAAO,qBACb,GAAArE,EACA,IAAAQ,EACA,SAAA8P,CACR,CAAO,EACD8M,EAAS,QAAQ,SAAW9M,EAE/B,EAAE,CAACtQ,EAAIQ,EAAK8P,EAAU+H,CAAQ,CAAC,EACzB,CACL,OAAAlV,EACA,KAAA2B,EACA,OAAiCzB,GAAK,KAAQrD,EAC9C,KAAM0V,EACN,KAAArS,EACA,WAAA3D,CACD,CACH,CAEA,SAASke,GAAiBnc,EAAM,CAC9B,GAAI,CACF,UAAAoc,EACA,SAAA7F,CACJ,EAAMvW,EACJ,KAAM,CAACqc,EAAgBC,CAAiB,EAAIxb,EAAAA,SAAS,IAAI,EACnD,CAACtF,EAAS+gB,CAAU,EAAIzb,EAAAA,SAAS,IAAI,EACrC0b,EAAmBte,GAAYqY,CAAQ,EAE7C,MAAI,CAACA,GAAY,CAAC8F,GAAkBG,GAClCF,EAAkBE,CAAgB,EAGpCngB,EAA0B,IAAM,CAC9B,GAAI,CAACb,EACH,OAGF,MAAMuD,EAAwCsd,GAAe,IACvD9d,EAAuC8d,GAAe,MAAM,GAElE,GAAItd,GAAO,MAAQR,GAAM,KAAM,CAC7B+d,EAAkB,IAAI,EACtB,MACN,CAEI,QAAQ,QAAQF,EAAU7d,EAAI/C,CAAO,CAAC,EAAE,KAAK,IAAM,CACjD8gB,EAAkB,IAAI,CAC5B,CAAK,CACF,EAAE,CAACF,EAAWC,EAAgB7gB,CAAO,CAAC,EAChC+E,EAAM,cAAcA,EAAM,SAAU,KAAMgW,EAAU8F,EAAiBI,EAAY,aAACJ,EAAgB,CACvG,IAAKE,CACN,CAAA,EAAI,IAAI,CACX,CAEA,MAAMG,GAAmB,CACvB,EAAG,EACH,EAAG,EACH,OAAQ,EACR,OAAQ,CACV,EACA,SAASC,GAAyB3c,EAAM,CACtC,GAAI,CACF,SAAAuW,CACJ,EAAMvW,EACJ,OAAOO,EAAM,cAAcqU,GAAgB,SAAU,CACnD,MAAOD,EACR,EAAEpU,EAAM,cAAcuV,GAAuB,SAAU,CACtD,MAAO4G,EACR,EAAEnG,CAAQ,CAAC,CACd,CAEA,MAAMqG,GAAa,CACjB,SAAU,QACV,YAAa,MACf,EAEMC,GAAoB1H,GACI9V,GAAgB8V,CAAc,EAC7B,uBAAyB,OAGlD2H,GAAiCC,EAAU,WAAC,CAAC/c,EAAM7B,IAAQ,CAC/D,GAAI,CACF,GAAA6e,EACA,eAAA7H,EACA,YAAApQ,EACA,SAAAwR,EACA,UAAA0G,EACA,KAAA5Z,EACA,MAAA6Z,EACA,UAAArd,EACA,WAAAsd,EAAaN,EACjB,EAAM7c,EAEJ,GAAI,CAACqD,EACH,OAAO,KAGT,MAAM+Z,EAAyBrY,EAAclF,EAAY,CAAE,GAAGA,EAC5D,OAAQ,EACR,OAAQ,CACT,EACKwd,EAAS,CAAE,GAAGT,GAClB,MAAOvZ,EAAK,MACZ,OAAQA,EAAK,OACb,IAAKA,EAAK,IACV,KAAMA,EAAK,KACX,UAAWzD,GAAI,UAAU,SAASwd,CAAsB,EACxD,gBAAiBrY,GAAeoQ,EAAiB/R,GAA2B+R,EAAgB9R,CAAI,EAAI,OACpG,WAAY,OAAO8Z,GAAe,WAAaA,EAAWhI,CAAc,EAAIgI,EAC5E,GAAGD,CACJ,EACD,OAAO3c,EAAM,cAAcyc,EAAI,CAC7B,UAAAC,EACA,MAAOI,EACP,IAAAlf,CACD,EAAEoY,CAAQ,CACb,CAAC,EAEK+G,GAAkCta,GAAWhD,GAAQ,CACzD,GAAI,CACF,OAAA0B,EACA,YAAA6W,CACJ,EAAMvY,EACJ,MAAMud,EAAiB,CAAE,EACnB,CACJ,OAAAF,EACA,UAAAJ,CACJ,EAAMja,EAEJ,GAAIqa,GAAU,MAAQA,EAAO,OAC3B,SAAW,CAACte,EAAKxB,CAAK,IAAK,OAAO,QAAQ8f,EAAO,MAAM,EACjD9f,IAAU,SAIdggB,EAAexe,CAAG,EAAI2C,EAAO,KAAK,MAAM,iBAAiB3C,CAAG,EAC5D2C,EAAO,KAAK,MAAM,YAAY3C,EAAKxB,CAAK,GAI5C,GAAI8f,GAAU,MAAQA,EAAO,YAC3B,SAAW,CAACte,EAAKxB,CAAK,IAAK,OAAO,QAAQ8f,EAAO,WAAW,EACtD9f,IAAU,QAIdgb,EAAY,KAAK,MAAM,YAAYxZ,EAAKxB,CAAK,EAIjD,OAAI0f,GAAa,MAAQA,EAAU,QACjCvb,EAAO,KAAK,UAAU,IAAIub,EAAU,MAAM,EAGxCA,GAAa,MAAQA,EAAU,aACjC1E,EAAY,KAAK,UAAU,IAAI0E,EAAU,WAAW,EAG/C,UAAmB,CACxB,SAAW,CAACle,EAAKxB,CAAK,IAAK,OAAO,QAAQggB,CAAc,EACtD7b,EAAO,KAAK,MAAM,YAAY3C,EAAKxB,CAAK,EAGtC0f,GAAa,MAAQA,EAAU,QACjCvb,EAAO,KAAK,UAAU,OAAOub,EAAU,MAAM,CAEhD,CACH,EAEMO,GAA0B7b,GAAS,CACvC,GAAI,CACF,UAAW,CACT,QAAA8b,EACA,MAAAC,CACN,CACA,EAAM/b,EACJ,MAAO,CAAC,CACN,UAAW/B,GAAI,UAAU,SAAS6d,CAAO,CAC7C,EAAK,CACD,UAAW7d,GAAI,UAAU,SAAS8d,CAAK,CAC3C,CAAG,CACH,EAEMC,GAAoC,CACxC,SAAU,IACV,OAAQ,OACR,UAAWH,GACX,YAA0BF,GAAgC,CACxD,OAAQ,CACN,OAAQ,CACN,QAAS,GACjB,CACA,CACG,CAAA,CACH,EACA,SAASM,GAAiB/b,EAAO,CAC/B,GAAI,CACF,OAAAiO,EACA,eAAAb,EACA,oBAAApK,EACA,uBAAAgT,CACJ,EAAMhW,EACJ,OAAOrF,GAAS,CAAC+B,EAAI5C,IAAS,CAC5B,GAAImU,IAAW,KACb,OAGF,MAAM+N,EAAkB5O,EAAe,IAAI1Q,CAAE,EAE7C,GAAI,CAACsf,EACH,OAGF,MAAMpT,EAAaoT,EAAgB,KAAK,QAExC,GAAI,CAACpT,EACH,OAGF,MAAMqT,EAAiBjK,GAAkBlY,CAAI,EAE7C,GAAI,CAACmiB,EACH,OAGF,KAAM,CACJ,UAAAje,CACD,EAAGjE,EAAUD,CAAI,EAAE,iBAAiBA,CAAI,EACnC8J,EAAkBH,GAAezF,CAAS,EAEhD,GAAI,CAAC4F,EACH,OAGF,MAAM2W,EAAY,OAAOtM,GAAW,WAAaA,EAASiO,GAA2BjO,CAAM,EAC3F,OAAAnH,GAAuB8B,EAAYoN,EAAuB,UAAU,OAAO,EACpEuE,EAAU,CACf,OAAQ,CACN,GAAA7d,EACA,KAAMsf,EAAgB,KACtB,KAAMpT,EACN,KAAMoN,EAAuB,UAAU,QAAQpN,CAAU,CAC1D,EACD,eAAAwE,EACA,YAAa,CACX,KAAAtT,EACA,KAAMkc,EAAuB,YAAY,QAAQiG,CAAc,CAChE,EACD,oBAAAjZ,EACA,uBAAAgT,EACA,UAAWpS,CACjB,CAAK,CACL,CAAG,CACH,CAEA,SAASsY,GAA2B/a,EAAS,CAC3C,KAAM,CACJ,SAAA5F,EACA,OAAA8C,EACA,YAAA8d,EACA,UAAAC,CACD,EAAG,CAAE,GAAGN,GACP,GAAG3a,CACJ,EACD,OAAOlB,GAAS,CACd,GAAI,CACF,OAAAJ,EACA,YAAA6W,EACA,UAAA1Y,EACA,GAAGqe,CACT,EAAQpc,EAEJ,GAAI,CAAC1E,EAEH,OAGF,MAAMwM,EAAQ,CACZ,EAAG2O,EAAY,KAAK,KAAO7W,EAAO,KAAK,KACvC,EAAG6W,EAAY,KAAK,IAAM7W,EAAO,KAAK,GACvC,EACKyc,EAAQ,CACZ,OAAQte,EAAU,SAAW,EAAI6B,EAAO,KAAK,MAAQ7B,EAAU,OAAS0Y,EAAY,KAAK,MAAQ,EACjG,OAAQ1Y,EAAU,SAAW,EAAI6B,EAAO,KAAK,OAAS7B,EAAU,OAAS0Y,EAAY,KAAK,OAAS,CACpG,EACK6F,EAAiB,CACrB,EAAGve,EAAU,EAAI+J,EAAM,EACvB,EAAG/J,EAAU,EAAI+J,EAAM,EACvB,GAAGuU,CACJ,EACKE,EAAqBJ,EAAU,CAAE,GAAGC,EACxC,OAAAxc,EACA,YAAA6W,EACA,UAAW,CACT,QAAS1Y,EACT,MAAOue,CACf,CACA,CAAK,EACK,CAACE,CAAa,EAAID,EAClBE,EAAeF,EAAmBA,EAAmB,OAAS,CAAC,EAErE,GAAI,KAAK,UAAUC,CAAa,IAAM,KAAK,UAAUC,CAAY,EAE/D,OAGF,MAAM3L,EAAyCoL,IAAY,CACzD,OAAAtc,EACA,YAAA6W,EACA,GAAG2F,CACT,CAAK,EACK9B,EAAY7D,EAAY,KAAK,QAAQ8F,EAAoB,CAC7D,SAAAjhB,EACA,OAAA8C,EACA,KAAM,UACZ,CAAK,EACD,OAAO,IAAI,QAAQse,GAAW,CAC5BpC,EAAU,SAAW,IAAM,CACExJ,IAAS,EACpC4L,EAAS,CACV,CACP,CAAK,CACF,CACH,CAEA,IAAIzf,GAAM,EACV,SAAS0f,GAAOlgB,EAAI,CAClB,OAAOX,EAAO,QAAC,IAAM,CACnB,GAAIW,GAAM,KAIV,OAAAQ,KACOA,EACX,EAAK,CAACR,CAAE,CAAC,CACT,CAEK,MAACmgB,GAA2Bne,EAAM,KAAKP,GAAQ,CAClD,GAAI,CACF,YAAA+E,EAAc,GACd,SAAAwR,EACA,cAAeoI,EACf,MAAAzB,EACA,WAAAC,EACA,UAAA1H,EACA,eAAAmJ,EAAiB,MACjB,UAAA3B,EACA,OAAA4B,EAAS,GACb,EAAM7e,EACJ,KAAM,CACJ,eAAAmV,EACA,OAAAzT,EACA,eAAAyW,EACA,kBAAAC,EACA,eAAAnJ,EACA,oBAAApK,EACA,YAAA0T,EACA,KAAA3W,EACA,uBAAAiW,EACA,oBAAArP,EACA,wBAAAuF,EACA,WAAA0F,CACD,EAAG6H,GAAe,EACbzb,EAAYsB,EAAU,WAAC2U,EAAsB,EAC7C/W,EAAM0f,GAAiC/c,GAAO,EAAE,EAChDod,EAAoBtJ,GAAeC,EAAW,CAClD,eAAAN,EACA,OAAAzT,EACA,eAAAyW,EACA,kBAAAC,EACA,iBAAkBG,EAAY,KAC9B,KAAA3W,EACA,gBAAiB2W,EAAY,KAC7B,oBAAA/P,EACA,wBAAAuF,EACA,UAAAlO,EACA,WAAA4T,CACJ,CAAG,EACK1B,EAAcrB,GAAgByH,CAAc,EAC5C4G,EAAgBnB,GAAiB,CACrC,OAAQe,EACR,eAAA1P,EACA,oBAAApK,EACA,uBAAAgT,CACJ,CAAG,EAGK1Z,EAAM4T,EAAcwG,EAAY,OAAS,OAC/C,OAAOhY,EAAM,cAAcoc,GAA0B,KAAMpc,EAAM,cAAc4b,GAAkB,CAC/F,UAAW4C,CACZ,EAAErd,GAAU3C,EAAMwB,EAAM,cAAcuc,GAAmB,CACxD,IAAK/d,EACL,GAAI2C,EAAO,GACX,IAAKvD,EACL,GAAIygB,EACJ,eAAgBzJ,EAChB,YAAapQ,EACb,UAAWkY,EACX,WAAYE,EACZ,KAAMpL,EACN,MAAO,CACL,OAAA8M,EACA,GAAG3B,CACJ,EACD,UAAW4B,CACf,EAAKvI,CAAQ,EAAI,IAAI,CAAC,CACtB,CAAC", "x_google_ignoreList": [0, 1, 2]}