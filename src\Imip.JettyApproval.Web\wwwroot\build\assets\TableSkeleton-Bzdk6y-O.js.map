{"version": 3, "file": "TableSkeleton-Bzdk6y-O.js", "sources": ["../../../../../frontend/src/components/ui/skeleton.tsx", "../../../../../frontend/src/components/ui/alert-dialog.tsx", "../../../../../frontend/src/components/ui/TableSkeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"skeleton\"\r\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n", "\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { buttonVariants } from \"@/components/ui/button\"\r\n\r\nfunction AlertDialog({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Root>) {\r\n  return <AlertDialogPrimitive.Root data-slot=\"alert-dialog\" {...props} />\r\n}\r\n\r\nfunction AlertDialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Trigger>) {\r\n  return (\r\n    <AlertDialogPrimitive.Trigger data-slot=\"alert-dialog-trigger\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Portal>) {\r\n  return (\r\n    <AlertDialogPrimitive.Portal data-slot=\"alert-dialog-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Overlay>) {\r\n  return (\r\n    <AlertDialogPrimitive.Overlay\r\n      data-slot=\"alert-dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Content>) {\r\n  return (\r\n    <AlertDialogPortal>\r\n      <AlertDialogOverlay />\r\n      <AlertDialogPrimitive.Content\r\n        data-slot=\"alert-dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </AlertDialogPortal>\r\n  )\r\n}\r\n\r\nfunction AlertDialogHeader({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogFooter({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Title>) {\r\n  return (\r\n    <AlertDialogPrimitive.Title\r\n      data-slot=\"alert-dialog-title\"\r\n      className={cn(\"text-lg font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Description>) {\r\n  return (\r\n    <AlertDialogPrimitive.Description\r\n      data-slot=\"alert-dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogAction({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Action>) {\r\n  return (\r\n    <AlertDialogPrimitive.Action\r\n      className={cn(buttonVariants(), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogCancel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Cancel>) {\r\n  return (\r\n    <AlertDialogPrimitive.Cancel\r\n      className={cn(buttonVariants({ variant: \"outline\" }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  AlertDialog,\r\n  AlertDialogPortal,\r\n  AlertDialogOverlay,\r\n  AlertDialogTrigger,\r\n  AlertDialogContent,\r\n  AlertDialogHeader,\r\n  AlertDialogFooter,\r\n  AlertDialogTitle,\r\n  AlertDialogDescription,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n}\r\n", "import { Skeleton } from '@/components/ui/skeleton'\r\nimport { Card } from '@/components/ui/card'\r\n\r\ninterface TableSkeletonProps {\r\n  rowCount?: number\r\n  columnCount?: number\r\n  hasTitle?: boolean\r\n  hasSearch?: boolean\r\n  hasFilters?: boolean\r\n  hasPagination?: boolean\r\n  hasActions?: boolean\r\n}\r\n\r\nexport function TableSkeleton({\r\n  rowCount = 10,\r\n  columnCount = 4,\r\n  hasTitle = true,\r\n  hasSearch = true,\r\n  hasFilters = true,\r\n  hasPagination = true,\r\n  hasActions = true,\r\n}: TableSkeletonProps) {\r\n  // Create arrays for rows and columns\r\n  const rows = Array.from({ length: rowCount }, (_, i) => i)\r\n  const columns = Array.from({ length: columnCount }, (_, i) => i)\r\n  \r\n  return (\r\n    <Card className=\"space-y-4 py-4\">\r\n      {/* Table header skeleton */}\r\n      {hasTitle && (\r\n        <div className=\"flex items-center justify-between mb-6 px-4\">\r\n          <Skeleton className=\"h-8 w-48\" /> {/* Title */}\r\n          {hasActions && (\r\n            <div className=\"flex space-x-2\">\r\n              <Skeleton className=\"h-9 w-24\" /> {/* Button */}\r\n              <Skeleton className=\"h-9 w-24\" /> {/* Button */}\r\n            </div>\r\n          )}\r\n        </div>\r\n      )}\r\n      \r\n      {/* Search and filter bar skeleton */}\r\n      {(hasSearch || hasFilters) && (\r\n        <div className=\"flex items-center justify-between mb-4 px-4\">\r\n          {hasSearch && <Skeleton className=\"h-10 w-64\" />} {/* Search bar */}\r\n          {hasFilters && <Skeleton className=\"h-10 w-32\" />} {/* Filter button */}\r\n        </div>\r\n      )}\r\n      \r\n      {/* Table header row */}\r\n      <div className=\"flex w-full border-b pb-2 px-4\">\r\n        <Skeleton className=\"h-6 w-8 mr-4\" /> {/* Checkbox */}\r\n        {columns.map((col) => (\r\n          <Skeleton \r\n            key={`header-${col}`} \r\n            className={`h-6 ${col === columns.length - 1 ? 'w-1/6' : 'w-1/4 mr-4'}`} \r\n          />\r\n        ))}\r\n      </div>\r\n      \r\n      {/* Table rows */}\r\n      {rows.map((row) => (\r\n        <div key={`row-${row}`} className=\"flex w-full py-3 border-b px-4\">\r\n          <Skeleton className=\"h-5 w-5 mr-4\" /> {/* Checkbox */}\r\n          {columns.map((col) => (\r\n            <Skeleton \r\n              key={`cell-${row}-${col}`} \r\n              className={`h-5 ${col === columns.length - 1 ? 'w-1/6' : 'w-1/4 mr-4'}`} \r\n            />\r\n          ))}\r\n        </div>\r\n      ))}\r\n      \r\n      {/* Pagination skeleton */}\r\n      {hasPagination && (\r\n        <div className=\"flex items-center justify-between pt-4 px-4\">\r\n          <Skeleton className=\"h-5 w-32\" /> {/* Page info */}\r\n          <div className=\"flex space-x-1\">\r\n            <Skeleton className=\"h-8 w-8\" /> {/* Pagination button */}\r\n            <Skeleton className=\"h-8 w-8\" /> {/* Pagination button */}\r\n            <Skeleton className=\"h-8 w-8\" /> {/* Pagination button */}\r\n            <Skeleton className=\"h-8 w-8\" /> {/* Pagination button */}\r\n          </div>\r\n        </div>\r\n      )}\r\n    </Card>\r\n  )\r\n}\r\n"], "names": ["Skeleton", "className", "props", "jsx", "cn", "AlertDialog", "AlertDialogPrimitive.Root", "AlertDialogPortal", "AlertDialogPrimitive.Portal", "AlertDialogOverlay", "AlertDialogPrimitive.Overlay", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AlertDialogPrimitive.Content", "Alert<PERSON><PERSON><PERSON>H<PERSON>er", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AlertDialogTitle", "AlertDialogPrimitive.Title", "AlertDialogDescription", "AlertDialogPrimitive.Description", "AlertDialogAction", "AlertDialogPrimitive.Action", "buttonVariants", "AlertDialogCancel", "AlertDialogPrimitive.Cancel", "TableSkeleton", "rowCount", "columnCount", "hasTitle", "hasSearch", "hasFilters", "hasPagination", "hasActions", "rows", "_", "i", "columns", "jsxs", "Card", "col", "row"], "mappings": "6NAEA,SAASA,EAAS,CAAE,UAAAC,EAAW,GAAGC,GAAsC,CAEpE,OAAAC,EAAA,IAAC,MAAA,CACC,YAAU,WACV,UAAWC,EAAG,qCAAsCH,CAAS,EAC5D,GAAGC,CAAA,CACN,CAEJ,CCFA,SAASG,EAAY,CACnB,GAAGH,CACL,EAA2D,CACzD,aAAQI,EAAA,CAA0B,YAAU,eAAgB,GAAGJ,EAAO,CACxE,CAUA,SAASK,EAAkB,CACzB,GAAGL,CACL,EAA6D,CAC3D,aACGM,EAAA,CAA4B,YAAU,sBAAuB,GAAGN,EAAO,CAE5E,CAEA,SAASO,EAAmB,CAC1B,UAAAR,EACA,GAAGC,CACL,EAA8D,CAE1D,OAAAC,EAAA,IAACO,EAAA,CACC,YAAU,uBACV,UAAWN,EACT,yJACAH,CACF,EACC,GAAGC,CAAA,CACN,CAEJ,CAEA,SAASS,EAAmB,CAC1B,UAAAV,EACA,GAAGC,CACL,EAA8D,CAC5D,cACGK,EACC,CAAA,SAAA,CAAAJ,EAAA,IAACM,EAAmB,EAAA,EACpBN,EAAA,IAACS,EAAA,CACC,YAAU,uBACV,UAAWR,EACT,8WACAH,CACF,EACC,GAAGC,CAAA,CAAA,CACN,EACF,CAEJ,CAEA,SAASW,EAAkB,CACzB,UAAAZ,EACA,GAAGC,CACL,EAAgC,CAE5B,OAAAC,EAAA,IAAC,MAAA,CACC,YAAU,sBACV,UAAWC,EAAG,+CAAgDH,CAAS,EACtE,GAAGC,CAAA,CACN,CAEJ,CAEA,SAASY,EAAkB,CACzB,UAAAb,EACA,GAAGC,CACL,EAAgC,CAE5B,OAAAC,EAAA,IAAC,MAAA,CACC,YAAU,sBACV,UAAWC,EACT,yDACAH,CACF,EACC,GAAGC,CAAA,CACN,CAEJ,CAEA,SAASa,EAAiB,CACxB,UAAAd,EACA,GAAGC,CACL,EAA4D,CAExD,OAAAC,EAAA,IAACa,EAAA,CACC,YAAU,qBACV,UAAWZ,EAAG,wBAAyBH,CAAS,EAC/C,GAAGC,CAAA,CACN,CAEJ,CAEA,SAASe,EAAuB,CAC9B,UAAAhB,EACA,GAAGC,CACL,EAAkE,CAE9D,OAAAC,EAAA,IAACe,EAAA,CACC,YAAU,2BACV,UAAWd,EAAG,gCAAiCH,CAAS,EACvD,GAAGC,CAAA,CACN,CAEJ,CAEA,SAASiB,EAAkB,CACzB,UAAAlB,EACA,GAAGC,CACL,EAA6D,CAEzD,OAAAC,EAAA,IAACiB,EAAA,CACC,UAAWhB,EAAGiB,EAAe,EAAGpB,CAAS,EACxC,GAAGC,CAAA,CACN,CAEJ,CAEA,SAASoB,EAAkB,CACzB,UAAArB,EACA,GAAGC,CACL,EAA6D,CAEzD,OAAAC,EAAA,IAACoB,EAAA,CACC,UAAWnB,EAAGiB,EAAe,CAAE,QAAS,SAAU,CAAC,EAAGpB,CAAS,EAC9D,GAAGC,CAAA,CACN,CAEJ,CCjIO,SAASsB,EAAc,CAC5B,SAAAC,EAAW,GACX,YAAAC,EAAc,EACd,SAAAC,EAAW,GACX,UAAAC,EAAY,GACZ,WAAAC,EAAa,GACb,cAAAC,EAAgB,GAChB,WAAAC,EAAa,EACf,EAAuB,CAEf,MAAAC,EAAO,MAAM,KAAK,CAAE,OAAQP,GAAY,CAACQ,EAAGC,IAAMA,CAAC,EACnDC,EAAU,MAAM,KAAK,CAAE,OAAQT,GAAe,CAACO,EAAGC,IAAMA,CAAC,EAG7D,OAAAE,EAAA,KAACC,EAAK,CAAA,UAAU,iBAEb,SAAA,CACCV,GAAAS,EAAA,KAAC,MAAI,CAAA,UAAU,8CACb,SAAA,CAACjC,EAAAA,IAAAH,EAAA,CAAS,UAAU,UAAW,CAAA,EAAE,IAChC+B,GACCK,EAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACjC,EAAAA,IAAAH,EAAA,CAAS,UAAU,UAAW,CAAA,EAAE,IACjCG,EAAAA,IAACH,EAAS,CAAA,UAAU,UAAW,CAAA,EAAE,GAAA,CACnC,CAAA,CAAA,EAEJ,GAIA4B,GAAaC,IACZO,EAAA,KAAA,MAAA,CAAI,UAAU,8CACZ,SAAA,CAAaR,GAAAzB,EAAAA,IAACH,EAAS,CAAA,UAAU,WAAY,CAAA,EAAG,IAChD6B,GAAc1B,EAAAA,IAACH,EAAS,CAAA,UAAU,WAAY,CAAA,EAAG,GAAA,EACpD,EAIFoC,EAAAA,KAAC,MAAI,CAAA,UAAU,iCACb,SAAA,CAACjC,EAAAA,IAAAH,EAAA,CAAS,UAAU,cAAe,CAAA,EAAE,IACpCmC,EAAQ,IAAKG,GACZnC,EAAA,IAACH,EAAA,CAEC,UAAW,OAAOsC,IAAQH,EAAQ,OAAS,EAAI,QAAU,YAAY,EAAA,EADhE,UAAUG,CAAG,EAGrB,CAAA,CAAA,EACH,EAGCN,EAAK,IAAKO,GACRH,EAAAA,KAAA,MAAA,CAAuB,UAAU,iCAChC,SAAA,CAACjC,EAAAA,IAAAH,EAAA,CAAS,UAAU,cAAe,CAAA,EAAE,IACpCmC,EAAQ,IAAKG,GACZnC,EAAA,IAACH,EAAA,CAEC,UAAW,OAAOsC,IAAQH,EAAQ,OAAS,EAAI,QAAU,YAAY,EAAA,EADhE,QAAQI,CAAG,IAAID,CAAG,EAG1B,CAAA,CAPO,CAAA,EAAA,OAAOC,CAAG,EAQpB,CACD,EAGAT,GACCM,EAAA,KAAC,MAAI,CAAA,UAAU,8CACb,SAAA,CAACjC,EAAAA,IAAAH,EAAA,CAAS,UAAU,UAAW,CAAA,EAAE,IACjCoC,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACjC,EAAAA,IAAAH,EAAA,CAAS,UAAU,SAAU,CAAA,EAAE,IAChCG,EAAAA,IAACH,EAAS,CAAA,UAAU,SAAU,CAAA,EAAE,IAChCG,EAAAA,IAACH,EAAS,CAAA,UAAU,SAAU,CAAA,EAAE,IAChCG,EAAAA,IAACH,EAAS,CAAA,UAAU,SAAU,CAAA,EAAE,GAAA,CAClC,CAAA,CAAA,CACF,CAAA,CAAA,EAEJ,CAEJ"}