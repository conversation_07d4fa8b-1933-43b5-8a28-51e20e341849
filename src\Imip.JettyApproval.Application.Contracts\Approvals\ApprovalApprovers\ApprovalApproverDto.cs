using System;
using Volo.Abp.Application.Dtos;

namespace Imip.JettyApproval.Approvals.ApprovalApprovers;

/// <summary>
/// DTO for ApprovalApprover entity
/// </summary>
public class ApprovalApproverDto : FullAuditedEntityDto<Guid>
{
    /// <summary>
    /// Foreign key to the approval template
    /// </summary>
    public Guid ApprovalId { get; set; }

    /// <summary>
    /// ID of the approver
    /// </summary>
    public Guid ApproverId { get; set; }

    /// <summary>
    /// Sequence number for approval order
    /// </summary>
    public int Sequence { get; set; }

    /// <summary>
    /// Status of the approver
    /// </summary>
    public string? Status { get; set; }
}