{"version": 3, "file": "table-CAbNlII1.js", "sources": ["../../../../../frontend/src/components/ui/table.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"table-container\"\r\n      className=\"relative w-full overflow-x-auto\"\r\n    >\r\n      <table\r\n        data-slot=\"table\"\r\n        className={cn(\"w-full caption-bottom text-sm\", className)}\r\n        {...props}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\r\n  return (\r\n    <thead\r\n      data-slot=\"table-header\"\r\n      className={cn(\"[&_tr]:border-b\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\r\n  return (\r\n    <tbody\r\n      data-slot=\"table-body\"\r\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\r\n  return (\r\n    <tfoot\r\n      data-slot=\"table-footer\"\r\n      className={cn(\r\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\r\n  return (\r\n    <tr\r\n      data-slot=\"table-row\"\r\n      className={cn(\r\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\r\n  return (\r\n    <th\r\n      data-slot=\"table-head\"\r\n      className={cn(\r\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\r\n  return (\r\n    <td\r\n      data-slot=\"table-cell\"\r\n      className={cn(\r\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCaption({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"caption\">) {\r\n  return (\r\n    <caption\r\n      data-slot=\"table-caption\"\r\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n}\r\n"], "names": ["Table", "className", "props", "jsx", "cn", "TableHeader", "TableBody", "TableRow", "TableHead", "TableCell"], "mappings": "sFAIA,SAASA,EAAM,CAAE,UAAAC,EAAW,GAAGC,GAAwC,CAEnE,OAAAC,EAAA,IAAC,MAAA,CACC,YAAU,kBACV,UAAU,kCAEV,SAAAA,EAAA,IAAC,QAAA,CACC,YAAU,QACV,UAAWC,EAAG,gCAAiCH,CAAS,EACvD,GAAGC,CAAA,CAAA,CACN,CACF,CAEJ,CAEA,SAASG,EAAY,CAAE,UAAAJ,EAAW,GAAGC,GAAwC,CAEzE,OAAAC,EAAA,IAAC,QAAA,CACC,YAAU,eACV,UAAWC,EAAG,kBAAmBH,CAAS,EACzC,GAAGC,CAAA,CACN,CAEJ,CAEA,SAASI,EAAU,CAAE,UAAAL,EAAW,GAAGC,GAAwC,CAEvE,OAAAC,EAAA,IAAC,QAAA,CACC,YAAU,aACV,UAAWC,EAAG,6BAA8BH,CAAS,EACpD,GAAGC,CAAA,CACN,CAEJ,CAeA,SAASK,EAAS,CAAE,UAAAN,EAAW,GAAGC,GAAqC,CAEnE,OAAAC,EAAA,IAAC,KAAA,CACC,YAAU,YACV,UAAWC,EACT,8EACAH,CACF,EACC,GAAGC,CAAA,CACN,CAEJ,CAEA,SAASM,EAAU,CAAE,UAAAP,EAAW,GAAGC,GAAqC,CAEpE,OAAAC,EAAA,IAAC,KAAA,CACC,YAAU,aACV,UAAWC,EACT,qJACAH,CACF,EACC,GAAGC,CAAA,CACN,CAEJ,CAEA,SAASO,EAAU,CAAE,UAAAR,EAAW,GAAGC,GAAqC,CAEpE,OAAAC,EAAA,IAAC,KAAA,CACC,YAAU,aACV,UAAWC,EACT,yGACAH,CACF,EACC,GAAGC,CAAA,CACN,CAEJ"}