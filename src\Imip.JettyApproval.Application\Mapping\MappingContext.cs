using System;
using System.Collections.Generic;
using Imip.JettyApproval.Contracts;
using Volo.Abp.DependencyInjection;
using Volo.Abp.MultiTenancy;
using Volo.Abp.Users;

namespace Imip.JettyApproval.Mapping;

public class MappingContext : IMappingContext, ITransientDependency
{
    public ICurrentUser? CurrentUser { get; }
    public DateTime Now => DateTime.UtcNow;
    public Guid? TenantId { get; }
    public Dictionary<string, object> Items { get; } = new();

    public MappingContext(ICurrentUser currentUser, ICurrentTenant currentTenant)
    {
        CurrentUser = currentUser;
        TenantId = currentTenant.Id;
    }
}
