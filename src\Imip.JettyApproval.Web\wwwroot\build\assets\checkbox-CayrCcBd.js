import{j as e}from"./vendor-CrSBzUoz.js";import{N as i,Q as t}from"./radix-DaY-mnHi.js";import{M as s,N as o}from"./app-layout-CNB1Wtrx.js";function l({className:r,...a}){return e.jsx(i,{"data-slot":"checkbox",className:s("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",r),...a,children:e.jsx(t,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:e.jsx(o,{className:"size-3.5"})})})}export{l as C};
//# sourceMappingURL=checkbox-CayrCcBd.js.map
