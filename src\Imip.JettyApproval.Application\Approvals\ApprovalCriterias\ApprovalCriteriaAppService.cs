using Imip.JettyApproval.Approvals.ApprovalCriterias;
using Imip.JettyApproval.Mapping.Mappers;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.JettyApproval.Approvals.ApprovalCriterias;

/// <summary>
/// Application service for ApprovalCriteria entity
/// </summary>
public class ApprovalCriteriaAppService :
    CrudAppService<ApprovalCriteria, ApprovalCriteriaDto, Guid, PagedAndSortedResultRequestDto, CreateUpdateApprovalCriteriaDto, CreateUpdateApprovalCriteriaDto>,
    IApprovalCriteriaAppService
{
    private readonly IApprovalCriteriaRepository _approvalCriteriaRepository;
    private readonly ApprovalCriteriaMapper _mapper;
    private readonly ILogger<ApprovalCriteriaAppService> _logger;

    public ApprovalCriteriaAppService(
        IApprovalCriteriaRepository approvalCriteriaRepository,
        ApprovalCriteriaMapper mapper,
        ILogger<ApprovalCriteriaAppService> logger)
        : base(approvalCriteriaRepository)
    {
        _approvalCriteriaRepository = approvalCriteriaRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public override async Task<ApprovalCriteriaDto> CreateAsync(CreateUpdateApprovalCriteriaDto input)
    {
        await CheckCreatePolicyAsync();

        var entity = _mapper.CreateEntityWithId(input, Guid.NewGuid());

        await _approvalCriteriaRepository.InsertAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task<ApprovalCriteriaDto> UpdateAsync(Guid id, CreateUpdateApprovalCriteriaDto input)
    {
        await CheckUpdatePolicyAsync();

        var entity = await _approvalCriteriaRepository.GetAsync(id);

        _mapper.MapToEntity(input, entity);

        await _approvalCriteriaRepository.UpdateAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task DeleteAsync(Guid id)
    {
        await CheckDeletePolicyAsync();

        var entity = await _approvalCriteriaRepository.GetAsync(id);

        await _approvalCriteriaRepository.DeleteAsync(entity, autoSave: true);
    }

    public override async Task<ApprovalCriteriaDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var entity = await _approvalCriteriaRepository.GetAsync(id);
        return _mapper.MapToDto(entity);
    }

    public override async Task<PagedResultDto<ApprovalCriteriaDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        await CheckGetListPolicyAsync();

        var queryable = await _approvalCriteriaRepository.GetQueryableAsync();

        // Apply sorting
        if (!string.IsNullOrWhiteSpace(input.Sorting))
        {
            queryable = queryable.OrderBy(input.Sorting);
        }
        else
        {
            queryable = queryable.OrderByDescending(x => x.CreationTime);
        }

        var totalCount = await AsyncExecuter.CountAsync(queryable);

        var entities = await AsyncExecuter.ToListAsync(
            queryable.PageBy(input.SkipCount, input.MaxResultCount)
        );

        var dtos = entities.Select(_mapper.MapToDto).ToList();

        return new PagedResultDto<ApprovalCriteriaDto>(totalCount, dtos);
    }
}