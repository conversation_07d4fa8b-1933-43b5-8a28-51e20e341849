{"version": 3, "file": "report-BdV3qOLp.js", "sources": ["../../../../../frontend/src/pages/report.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\r\nimport AppLayout from '@/layouts/app-layout';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\r\nimport DatePickerReact from '@/components/ui/date-picker';\r\nimport { Label } from '@/components/ui/label';\r\nimport type { DateValue } from 'react-aria-components';\r\n\r\nimport { HotTable } from '@handsontable/react-wrapper';\r\nimport type { HotTableRef } from '@handsontable/react-wrapper';\r\nimport { registerAllModules } from 'handsontable/registry';\r\nimport 'handsontable/styles/handsontable.min.css';\r\nimport 'handsontable/styles/ht-theme-main.min.css';\r\nimport Handsontable from 'handsontable';\r\n\r\nregisterAllModules();\r\n\r\ntype ParameterType = 'text' | 'date' | 'select';\r\n\r\ninterface ReportParameter {\r\n  name: string;\r\n  label: string;\r\n  type: ParameterType;\r\n  options?: { value: string; label: string }[];\r\n}\r\n\r\ninterface ReportDefinition {\r\n  id: string;\r\n  name: string;\r\n  parameters: ReportParameter[];\r\n  mockDataGenerator: (params: Record<string, string | DateValue | null>) => Handsontable.CellValue[][];\r\n  columns: Handsontable.ColumnSettings[];\r\n}\r\n\r\nconst mockReports: ReportDefinition[] = [\r\n  {\r\n    id: 'jetty_schedule_report',\r\n    name: 'Jetty Schedule Report',\r\n    parameters: [\r\n      { name: 'startDate', label: 'Start Date', type: 'date' },\r\n      { name: 'endDate', label: 'End Date', type: 'date' },\r\n      {\r\n        name: 'jetty', label: 'Jetty', type: 'select', options: [\r\n          { value: 'all', label: 'All Jetties' },\r\n          { value: 'jetty_a', label: 'Jetty A' },\r\n          { value: 'jetty_b', label: 'Jetty B' },\r\n          { value: 'jetty_c', label: 'Jetty C' }\r\n        ]\r\n      },\r\n      {\r\n        name: 'status', label: 'Status', type: 'select', options: [\r\n          { value: 'all', label: 'All Status' },\r\n          { value: 'scheduled', label: 'Scheduled' },\r\n          { value: 'in_progress', label: 'In Progress' },\r\n          { value: 'completed', label: 'Completed' }\r\n        ]\r\n      }\r\n    ],\r\n    mockDataGenerator: (params) => {\r\n      console.log('Generating data for Jetty Schedule Report with params:', params);\r\n      const data = [];\r\n      for (let i = 0; i < 10; i++) {\r\n        data.push([\r\n          `Vessel_${i + 1}`,\r\n          `2024-03-${10 + i}`,\r\n          `2024-03-${12 + i}`,\r\n          params.jetty === 'all' ? `Jetty ${String.fromCharCode(65 + (i % 3))}` : params.jetty,\r\n          ['scheduled', 'in_progress', 'completed'][i % 3],\r\n          `Cargo Type ${i % 3 + 1}`,\r\n          `${Math.floor(Math.random() * 1000)} tons`\r\n        ]);\r\n      }\r\n      return data;\r\n    },\r\n    columns: [\r\n      { data: 0, title: 'Vessel Name' },\r\n      { data: 1, title: 'Arrival Date' },\r\n      { data: 2, title: 'Departure Date' },\r\n      { data: 3, title: 'Jetty' },\r\n      { data: 4, title: 'Status' },\r\n      { data: 5, title: 'Cargo Type' },\r\n      { data: 6, title: 'Cargo Volume' }\r\n    ],\r\n  },\r\n  {\r\n    id: 'bounded_zone_report',\r\n    name: 'Bounded Zone Report',\r\n    parameters: [\r\n      { name: 'date', label: 'Date', type: 'date' },\r\n      {\r\n        name: 'zone', label: 'Zone', type: 'select', options: [\r\n          { value: 'all', label: 'All Zones' },\r\n          { value: 'zone_a', label: 'Zone A' },\r\n          { value: 'zone_b', label: 'Zone B' },\r\n          { value: 'zone_c', label: 'Zone C' }\r\n        ]\r\n      },\r\n      {\r\n        name: 'activity', label: 'Activity Type', type: 'select', options: [\r\n          { value: 'all', label: 'All Activities' },\r\n          { value: 'loading', label: 'Loading' },\r\n          { value: 'unloading', label: 'Unloading' },\r\n          { value: 'storage', label: 'Storage' }\r\n        ]\r\n      }\r\n    ],\r\n    mockDataGenerator: (params) => {\r\n      console.log('Generating data for Bounded Zone Report with params:', params);\r\n      const data = [];\r\n      for (let i = 0; i < 10; i++) {\r\n        data.push([\r\n          params.zone === 'all' ? `Zone ${String.fromCharCode(65 + (i % 3))}` : params.zone,\r\n          ['loading', 'unloading', 'storage'][i % 3],\r\n          `Cargo_${i + 1}`,\r\n          `${Math.floor(Math.random() * 500)} tons`,\r\n          `Vessel_${i + 1}`,\r\n          `Operator_${i + 1}`,\r\n          params.date ? params.date.toString() : '2024-03-15'\r\n        ]);\r\n      }\r\n      return data;\r\n    },\r\n    columns: [\r\n      { data: 0, title: 'Zone' },\r\n      { data: 1, title: 'Activity Type' },\r\n      { data: 2, title: 'Cargo ID' },\r\n      { data: 3, title: 'Volume' },\r\n      { data: 4, title: 'Vessel' },\r\n      { data: 5, title: 'Operator' },\r\n      { data: 6, title: 'Date' }\r\n    ],\r\n  }\r\n];\r\n\r\nconst ReportPage: React.FC = () => {\r\n  const [selectedReportId, setSelectedReportId] = useState<string>('');\r\n  const [selectedReport, setSelectedReport] = useState<ReportDefinition | null>(null);\r\n  const [parameterValues, setParameterValues] = useState<Record<string, string | DateValue | null>>({});\r\n  const [reportData, setReportData] = useState<Handsontable.CellValue[][]>([]);\r\n  const hotRef = useRef<HotTableRef>(null);\r\n\r\n  useEffect(() => {\r\n    if (selectedReportId) {\r\n      const report = mockReports.find(r => r.id === selectedReportId);\r\n      setSelectedReport(report || null);\r\n      const initialParams: Record<string, string | DateValue | null> = {};\r\n      report?.parameters.forEach(param => {\r\n        if (param.type === 'date') {\r\n          initialParams[param.name] = null;\r\n        } else {\r\n          initialParams[param.name] = '';\r\n        }\r\n      });\r\n      setParameterValues(initialParams);\r\n      setReportData([]); // Clear previous report data\r\n    } else {\r\n      setSelectedReport(null);\r\n      setParameterValues({});\r\n      setReportData([]);\r\n    }\r\n  }, [selectedReportId]);\r\n\r\n  const handleParameterChange = (name: string, value: string | DateValue | null) => {\r\n    setParameterValues(prev => ({ ...prev, [name]: value }));\r\n  };\r\n\r\n  const handleRunReport = () => {\r\n    if (selectedReport) {\r\n      const data = selectedReport.mockDataGenerator(parameterValues);\r\n      setReportData(data);\r\n      if (hotRef.current?.hotInstance) {\r\n        hotRef.current.hotInstance.loadData(data);\r\n      }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <AppLayout>\r\n      <div className=\"flex flex-col space-y-2 p-2\">\r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle>Report Selection and Parameters</CardTitle>\r\n          </CardHeader>\r\n          <CardContent className=\"p-3\">\r\n            <div className=\"mb-4\">\r\n              <Label htmlFor=\"report-select\" className=\"text-sm mb-1\">Select Report</Label>\r\n              <Select onValueChange={setSelectedReportId} value={selectedReportId}>\r\n                <SelectTrigger className=\"w-[240px]\">\r\n                  <SelectValue placeholder=\"Select a report\" />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  {mockReports.map(report => (\r\n                    <SelectItem key={report.id} value={report.id}>\r\n                      {report.name}\r\n                    </SelectItem>\r\n                  ))}\r\n                </SelectContent>\r\n              </Select>\r\n            </div>\r\n            {selectedReport && (\r\n              <>\r\n                <h4 className=\"text-lg font-semibold mb-3\">{selectedReport.name} Parameters</h4>\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-x-2 gap-y-2\">\r\n                  {selectedReport.parameters.map(param => (\r\n                    <div key={param.name}>\r\n                      <Label htmlFor={param.name} className=\"text-sm mb-1\">{param.label}</Label>\r\n                      {param.type === 'text' && (\r\n                        <Input\r\n                          id={param.name}\r\n                          value={parameterValues[param.name] as string || ''}\r\n                          onChange={(e) => handleParameterChange(param.name, e.target.value)}\r\n                          className=\"w-[240px]\"\r\n                        />\r\n                      )}\r\n                      {param.type === 'date' && (\r\n                        <DatePickerReact\r\n                          value={parameterValues[param.name] as DateValue || null}\r\n                          onChange={(date) => handleParameterChange(param.name, date)}\r\n\r\n                        />\r\n                      )}\r\n                      {param.type === 'select' && (\r\n                        <Select\r\n                          value={parameterValues[param.name] as string || ''}\r\n                          onValueChange={(value) => handleParameterChange(param.name, value)}\r\n                        >\r\n                          <SelectTrigger className=\"w-[240px]\">\r\n                            <SelectValue placeholder={`Select ${param.label}`} />\r\n                          </SelectTrigger>\r\n                          <SelectContent>\r\n                            {param.options?.map(option => (\r\n                              <SelectItem key={option.value} value={option.value}>\r\n                                {option.label}\r\n                              </SelectItem>\r\n                            ))}\r\n                          </SelectContent>\r\n                        </Select>\r\n                      )}\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n                <Button onClick={handleRunReport} className=\"mt-4\">Run Report</Button>\r\n              </>\r\n            )}\r\n          </CardContent>\r\n        </Card>\r\n\r\n        {reportData.length > 0 && selectedReport && (\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle>{selectedReport.name} Results</CardTitle>\r\n            </CardHeader>\r\n            <CardContent className=\"p-3\">\r\n              <HotTable\r\n                ref={hotRef}\r\n                themeName=\"ht-theme-main\"\r\n                data={reportData}\r\n                columns={selectedReport.columns}\r\n                colHeaders={selectedReport.columns.map(col => col.title as string)}\r\n                rowHeaders={true}\r\n                height=\"auto\"\r\n                width=\"auto\"\r\n                stretchH=\"all\"\r\n                licenseKey=\"non-commercial-and-evaluation\"\r\n              />\r\n            </CardContent>\r\n          </Card>\r\n        )}\r\n      </div>\r\n    </AppLayout>\r\n  );\r\n};\r\n\r\nexport default ReportPage;\r\n"], "names": ["registerAllModules", "mockReports", "params", "data", "i", "ReportPage", "selectedReportId", "setSelectedReportId", "useState", "selectedReport", "setSelectedReport", "parameterValues", "setParameterValues", "reportData", "setReportData", "hotRef", "useRef", "useEffect", "report", "r", "initialParams", "param", "handleParameterChange", "name", "value", "prev", "handleRunReport", "jsx", "AppLayout", "jsxs", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "<PERSON><PERSON><PERSON><PERSON>", "Label", "Select", "SelectTrigger", "SelectValue", "SelectContent", "SelectItem", "Fragment", "Input", "e", "DatePickerReact", "date", "option", "<PERSON><PERSON>", "HotTable", "col"], "mappings": "kWAiBAA,EAAmB,EAmBnB,MAAMC,EAAkC,CACtC,CACE,GAAI,wBACJ,KAAM,wBACN,WAAY,CACV,CAAE,KAAM,YAAa,MAAO,aAAc,KAAM,MAAO,EACvD,CAAE,KAAM,UAAW,MAAO,WAAY,KAAM,MAAO,EACnD,CACE,KAAM,QAAS,MAAO,QAAS,KAAM,SAAU,QAAS,CACtD,CAAE,MAAO,MAAO,MAAO,aAAc,EACrC,CAAE,MAAO,UAAW,MAAO,SAAU,EACrC,CAAE,MAAO,UAAW,MAAO,SAAU,EACrC,CAAE,MAAO,UAAW,MAAO,SAAU,CAAA,CAEzC,EACA,CACE,KAAM,SAAU,MAAO,SAAU,KAAM,SAAU,QAAS,CACxD,CAAE,MAAO,MAAO,MAAO,YAAa,EACpC,CAAE,MAAO,YAAa,MAAO,WAAY,EACzC,CAAE,MAAO,cAAe,MAAO,aAAc,EAC7C,CAAE,MAAO,YAAa,MAAO,WAAY,CAAA,CAC3C,CAEJ,EACA,kBAAoBC,GAAW,CAE7B,MAAMC,EAAO,CAAC,EACd,QAASC,EAAI,EAAGA,EAAI,GAAIA,IACtBD,EAAK,KAAK,CACR,UAAUC,EAAI,CAAC,GACf,WAAW,GAAKA,CAAC,GACjB,WAAW,GAAKA,CAAC,GACjBF,EAAO,QAAU,MAAQ,SAAS,OAAO,aAAa,GAAME,EAAI,CAAE,CAAC,GAAKF,EAAO,MAC/E,CAAC,YAAa,cAAe,WAAW,EAAEE,EAAI,CAAC,EAC/C,cAAcA,EAAI,EAAI,CAAC,GACvB,GAAG,KAAK,MAAM,KAAK,OAAO,EAAI,GAAI,CAAC,OAAA,CACpC,EAEI,OAAAD,CACT,EACA,QAAS,CACP,CAAE,KAAM,EAAG,MAAO,aAAc,EAChC,CAAE,KAAM,EAAG,MAAO,cAAe,EACjC,CAAE,KAAM,EAAG,MAAO,gBAAiB,EACnC,CAAE,KAAM,EAAG,MAAO,OAAQ,EAC1B,CAAE,KAAM,EAAG,MAAO,QAAS,EAC3B,CAAE,KAAM,EAAG,MAAO,YAAa,EAC/B,CAAE,KAAM,EAAG,MAAO,cAAe,CAAA,CAErC,EACA,CACE,GAAI,sBACJ,KAAM,sBACN,WAAY,CACV,CAAE,KAAM,OAAQ,MAAO,OAAQ,KAAM,MAAO,EAC5C,CACE,KAAM,OAAQ,MAAO,OAAQ,KAAM,SAAU,QAAS,CACpD,CAAE,MAAO,MAAO,MAAO,WAAY,EACnC,CAAE,MAAO,SAAU,MAAO,QAAS,EACnC,CAAE,MAAO,SAAU,MAAO,QAAS,EACnC,CAAE,MAAO,SAAU,MAAO,QAAS,CAAA,CAEvC,EACA,CACE,KAAM,WAAY,MAAO,gBAAiB,KAAM,SAAU,QAAS,CACjE,CAAE,MAAO,MAAO,MAAO,gBAAiB,EACxC,CAAE,MAAO,UAAW,MAAO,SAAU,EACrC,CAAE,MAAO,YAAa,MAAO,WAAY,EACzC,CAAE,MAAO,UAAW,MAAO,SAAU,CAAA,CACvC,CAEJ,EACA,kBAAoBD,GAAW,CAE7B,MAAMC,EAAO,CAAC,EACd,QAASC,EAAI,EAAGA,EAAI,GAAIA,IACtBD,EAAK,KAAK,CACRD,EAAO,OAAS,MAAQ,QAAQ,OAAO,aAAa,GAAME,EAAI,CAAE,CAAC,GAAKF,EAAO,KAC7E,CAAC,UAAW,YAAa,SAAS,EAAEE,EAAI,CAAC,EACzC,SAASA,EAAI,CAAC,GACd,GAAG,KAAK,MAAM,KAAK,SAAW,GAAG,CAAC,QAClC,UAAUA,EAAI,CAAC,GACf,YAAYA,EAAI,CAAC,GACjBF,EAAO,KAAOA,EAAO,KAAK,WAAa,YAAA,CACxC,EAEI,OAAAC,CACT,EACA,QAAS,CACP,CAAE,KAAM,EAAG,MAAO,MAAO,EACzB,CAAE,KAAM,EAAG,MAAO,eAAgB,EAClC,CAAE,KAAM,EAAG,MAAO,UAAW,EAC7B,CAAE,KAAM,EAAG,MAAO,QAAS,EAC3B,CAAE,KAAM,EAAG,MAAO,QAAS,EAC3B,CAAE,KAAM,EAAG,MAAO,UAAW,EAC7B,CAAE,KAAM,EAAG,MAAO,MAAO,CAAA,CAC3B,CAEJ,EAEME,EAAuB,IAAM,CACjC,KAAM,CAACC,EAAkBC,CAAmB,EAAIC,EAAAA,SAAiB,EAAE,EAC7D,CAACC,EAAgBC,CAAiB,EAAIF,EAAAA,SAAkC,IAAI,EAC5E,CAACG,EAAiBC,CAAkB,EAAIJ,EAAAA,SAAoD,CAAA,CAAE,EAC9F,CAACK,EAAYC,CAAa,EAAIN,EAAAA,SAAqC,CAAA,CAAE,EACrEO,EAASC,SAAoB,IAAI,EAEvCC,EAAAA,UAAU,IAAM,CACd,GAAIX,EAAkB,CACpB,MAAMY,EAASjB,EAAY,KAAUkB,GAAAA,EAAE,KAAOb,CAAgB,EAC9DI,EAAkBQ,GAAU,IAAI,EAChC,MAAME,EAA2D,CAAC,EAC1DF,GAAA,WAAW,QAAiBG,GAAA,CAC9BA,EAAM,OAAS,OACHD,EAAAC,EAAM,IAAI,EAAI,KAEdD,EAAAC,EAAM,IAAI,EAAI,EAC9B,CACD,EACDT,EAAmBQ,CAAa,EAChCN,EAAc,CAAA,CAAE,CAAA,MAEhBJ,EAAkB,IAAI,EACtBE,EAAmB,CAAA,CAAE,EACrBE,EAAc,CAAA,CAAE,CAClB,EACC,CAACR,CAAgB,CAAC,EAEf,MAAAgB,EAAwB,CAACC,EAAcC,IAAqC,CAC7DZ,EAAAa,IAAS,CAAE,GAAGA,EAAM,CAACF,CAAI,EAAGC,GAAQ,CACzD,EAEME,EAAkB,IAAM,CAC5B,GAAIjB,EAAgB,CACZ,MAAAN,EAAOM,EAAe,kBAAkBE,CAAe,EAC7DG,EAAcX,CAAI,EACdY,EAAO,SAAS,aACXA,EAAA,QAAQ,YAAY,SAASZ,CAAI,CAC1C,CAEJ,EAEA,OACGwB,EAAA,IAAAC,EAAA,CACC,SAACC,EAAAA,KAAA,MAAA,CAAI,UAAU,8BACb,SAAA,CAAAA,OAACC,EACC,CAAA,SAAA,CAAAH,MAACI,EACC,CAAA,SAAAJ,EAAA,IAACK,EAAU,CAAA,SAAA,iCAA+B,CAAA,EAC5C,EACAH,EAAAA,KAACI,EAAY,CAAA,UAAU,MACrB,SAAA,CAACJ,EAAAA,KAAA,MAAA,CAAI,UAAU,OACb,SAAA,CAAAF,MAACO,EAAM,CAAA,QAAQ,gBAAgB,UAAU,eAAe,SAAa,gBAAA,EACpEL,EAAA,KAAAM,EAAA,CAAO,cAAe5B,EAAqB,MAAOD,EACjD,SAAA,CAAAqB,EAAAA,IAACS,GAAc,UAAU,YACvB,eAACC,EAAY,CAAA,YAAY,kBAAkB,CAC7C,CAAA,EACCV,MAAAW,EAAA,CACE,SAAYrC,EAAA,OACV0B,EAAAA,IAAAY,EAAA,CAA2B,MAAOrB,EAAO,GACvC,SAAOA,EAAA,MADOA,EAAO,EAExB,CACD,CACH,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EACCT,GAEGoB,EAAA,KAAAW,WAAA,CAAA,SAAA,CAACX,EAAAA,KAAA,KAAA,CAAG,UAAU,6BAA8B,SAAA,CAAepB,EAAA,KAAK,aAAA,EAAW,EAC3EkB,EAAAA,IAAC,OAAI,UAAU,kDACZ,WAAe,WAAW,IACzBN,GAAAQ,EAAA,KAAC,MACC,CAAA,SAAA,CAAAF,EAAAA,IAACO,GAAM,QAASb,EAAM,KAAM,UAAU,eAAgB,WAAM,KAAM,CAAA,EACjEA,EAAM,OAAS,QACdM,EAAA,IAACc,EAAA,CACC,GAAIpB,EAAM,KACV,MAAOV,EAAgBU,EAAM,IAAI,GAAe,GAChD,SAAWqB,GAAMpB,EAAsBD,EAAM,KAAMqB,EAAE,OAAO,KAAK,EACjE,UAAU,WAAA,CACZ,EAEDrB,EAAM,OAAS,QACdM,EAAA,IAACgB,EAAA,CACC,MAAOhC,EAAgBU,EAAM,IAAI,GAAkB,KACnD,SAAWuB,GAAStB,EAAsBD,EAAM,KAAMuB,CAAI,CAAA,CAE5D,EAEDvB,EAAM,OAAS,UACdQ,EAAA,KAACM,EAAA,CACC,MAAOxB,EAAgBU,EAAM,IAAI,GAAe,GAChD,cAAgBG,GAAUF,EAAsBD,EAAM,KAAMG,CAAK,EAEjE,SAAA,CAACG,EAAA,IAAAS,EAAA,CAAc,UAAU,YACvB,SAACT,EAAAA,IAAAU,EAAA,CAAY,YAAa,UAAUhB,EAAM,KAAK,EAAI,CAAA,EACrD,QACCiB,EACE,CAAA,SAAAjB,EAAM,SAAS,OACbM,MAAAY,EAAA,CAA8B,MAAOM,EAAO,MAC1C,SAAOA,EAAA,OADOA,EAAO,KAExB,CACD,CACH,CAAA,CAAA,CAAA,CAAA,CA/BI,CAAA,EAAAxB,EAAM,IAkChB,CACD,EACH,QACCyB,EAAO,CAAA,QAASpB,EAAiB,UAAU,OAAO,SAAU,YAAA,CAAA,CAAA,CAC/D,CAAA,CAAA,CAEJ,CAAA,CAAA,EACF,EAECb,EAAW,OAAS,GAAKJ,UACvBqB,EACC,CAAA,SAAA,CAACH,EAAA,IAAAI,EAAA,CACC,gBAACC,EAAW,CAAA,SAAA,CAAevB,EAAA,KAAK,UAAA,CAAA,CAAQ,CAC1C,CAAA,EACAkB,EAAAA,IAACM,EAAY,CAAA,UAAU,MACrB,SAAAN,EAAA,IAACoB,EAAA,CACC,IAAKhC,EACL,UAAU,gBACV,KAAMF,EACN,QAASJ,EAAe,QACxB,WAAYA,EAAe,QAAQ,IAAIuC,GAAOA,EAAI,KAAe,EACjE,WAAY,GACZ,OAAO,OACP,MAAM,OACN,SAAS,MACT,WAAW,+BAAA,CAAA,CAEf,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CAEJ,CACF,CAAA,CAEJ"}