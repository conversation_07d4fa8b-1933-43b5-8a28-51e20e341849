using Imip.JettyApproval.Web.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using Volo.Abp.AspNetCore.Mvc;

namespace Imip.JettyApproval.Web.Controllers;

[Authorize]
public class AppToAppController : AbpController
{
    private readonly AppToAppService _appToAppService;

    public AppToAppController(AppToAppService appToAppService)
    {
        _appToAppService = appToAppService;
    }

    [HttpGet("/api/app-to-app/data")]
    public async Task<IActionResult> GetDataFromOtherApp()
    {
        try
        {
            var data = await _appToAppService.GetDataFromOtherAppAsync();
            return Ok(data);
        }
        catch (System.UnauthorizedAccessException)
        {
            return Unauthorized("Failed to authenticate with other app");
        }
        catch (System.Exception ex)
        {
            return BadRequest($"Error calling other app: {ex.Message}");
        }
    }

    [HttpPost("/api/app-to-app/data")]
    public async Task<IActionResult> SendDataToOtherApp([FromBody] object data)
    {
        try
        {
            var result = await _appToAppService.SendDataToOtherAppAsync(data);
            return Ok(result);
        }
        catch (System.UnauthorizedAccessException)
        {
            return Unauthorized("Failed to authenticate with other app");
        }
        catch (System.Exception ex)
        {
            return BadRequest($"Error calling other app: {ex.Message}");
        }
    }

    [HttpGet("/api/app-to-app/custom")]
    public async Task<IActionResult> CallCustomEndpoint([FromQuery] string appUrl, [FromQuery] string endpoint)
    {
        try
        {
            var data = await _appToAppService.CallOtherAppAsync<object>(appUrl, endpoint);
            return Ok(data);
        }
        catch (System.UnauthorizedAccessException)
        {
            return Unauthorized("Failed to authenticate with other app");
        }
        catch (System.Exception ex)
        {
            return BadRequest($"Error calling other app: {ex.Message}");
        }
    }
}