import{r as x,j as e,h as E,u as G,f as J}from"./vendor-CrSBzUoz.js";import{n as A,o as he,q as me,Q as N,r as xe,s as ye,T as Y,S as I,a as P,b as O,c as F,t as M,I as g,B as j,v as ge,u as W,w as je,D as fe,e as Ce,f as be,j as Q,x as ve,A as Se}from"./app-layout-CNB1Wtrx.js";import{u as K,h as Z,D,t as Te}from"./DataTableColumnHeader-DJ80pmDz.js";import{A as Ne,a as Ae,b as we,c as qe,d as ke,e as Ue,f as De,g as Ie,T as Pe}from"./TableSkeleton-Bzdk6y-O.js";import{D as X,b as ee,c as ie,d as te,e as se,a as Oe}from"./dialog-DAr_Mtxm.js";import{u as ne}from"./index.esm-CT1elm-0.js";import{d as H,a as Fe,l as Me,p as Re,_ as Le}from"./popover-7NwOVASC.js";import{M as $,c as L}from"./multi-select-BfYI64yx.js";import{F as ae,a as o}from"./FormField-BFBouSal.js";import{C as Ve}from"./cog-DTsC64pW.js";import{L as _e}from"./lock-BgQcysNv.js";import{D as $e}from"./DataTable-BL4SJdnU.js";import{B as ze}from"./badge-BtBZs1VC.js";import{g as He,e as Ee,N as Ge}from"./NotionFilter-COtzX9y0.js";import{$ as Ke}from"./App-CGHLK9xH.js";import"./radix-DaY-mnHi.js";import"./card-BAJCNJxm.js";import"./scroll-area-5IehhMpa.js";import"./table-CAbNlII1.js";import"./checkbox-CayrCcBd.js";import"./tiny-invariant-CopsF_GD.js";import"./search-YAT3sv3T.js";import"./index-CZZWNLgr.js";/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qe=[["rect",{width:"14",height:"8",x:"5",y:"2",rx:"2",key:"wc9tft"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",key:"w68u3i"}],["path",{d:"M6 18h2",key:"rwmk9e"}],["path",{d:"M12 18h6",key:"aqd8w3"}]],Be=A("computer",Qe);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Je=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]],Ye=A("globe",Je);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const We=[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 9.9-1",key:"1mm8w8"}]],Ze=A("lock-open",We);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xe=[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]],ei=A("shield-check",Xe);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ii=[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"M9.1 9a3 3 0 0 1 5.82 1c0 2-3 3-3 3",key:"mhlwft"}],["path",{d:"M12 17h.01",key:"p32p05"}]],ti=A("shield-question",ii);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const si=[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]],ni=A("smartphone",si);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ai=[["path",{d:"m17 2-5 5-5-5",key:"16satq"}],["rect",{width:"20",height:"15",x:"2",y:"7",rx:"2",key:"1e6viu"}]],ri=A("tv",ai);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const li=[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],oi=A("user-check",li),ci=({dataId:n,dataEdit:i,onDismiss:l})=>{const{toast:a}=he(),[r,m]=x.useState(!1),c=async()=>{try{await me({path:{id:n}}),a({title:"Success",description:`Client "${i.displayName}" has been deleted successfully.`}),l()}catch(p){p instanceof Error&&a({title:"Failed",description:`There was a problem when deleting the client "${i.displayName}". Kindly try again.`,variant:"destructive"})}};return x.useEffect(()=>{m(!0)},[]),e.jsx(Ne,{open:r,children:e.jsxs(Ae,{children:[e.jsxs(we,{children:[e.jsx(qe,{children:"Are you absolutely sure?"}),e.jsxs(ke,{children:['This action cannot be undone. This will permanently delete your this client "',i.displayName,'"']})]}),e.jsxs(Ue,{children:[e.jsx(De,{onClick:l,children:"Cancel"}),e.jsx(Ie,{onClick:c,children:"Yes"})]})]})})},re=()=>E({queryKey:[N.GetOpeniddictApplicationsPermissions],queryFn:async()=>(await xe()).data?.data}),le=()=>E({queryKey:[N.GetOpeniddictRequirements],queryFn:async()=>(await ye()).data?.data}),oe=[{value:"web",option:"Web Application",description:"Server-side web applications running on a web server (e.g., ASP.NET, Node.js, PHP)",icon:Ye},{value:"spa",option:"Single Page Application",description:"JavaScript applications running in the browser (e.g., React, Angular, Vue.js)",icon:Be},{value:"native",option:"Native Application",description:"Desktop or mobile applications running natively on devices",icon:ni},{value:"device",option:"Device Application",description:"IoT devices, smart TVs, or other limited-input devices",icon:ri},{value:"machine",option:"Machine-to-Machine",description:"Service accounts, daemons, or backend services that run without user interaction",icon:Ve}],ce=[{value:"public",option:"Public",description:"Clients that cannot maintain the confidentiality of their credentials",icon:Ze},{value:"confidential",option:"Confidential",description:"Clients capable of maintaining the confidentiality of their credentials",icon:_e}],de=[{value:"implicit",option:"Implicit",description:"Consent is assumed without requiring explicit user approval",icon:ei},{value:"explicit",option:"Explicit",description:"Requires explicit user approval before granting access",icon:oi},{value:"hybrid",option:"Hybrid",description:"Requires explicit user approval before granting access",icon:ti}];function pe(n){if(!n)return"";const i=n.split(":");if(i.length!==2)return n;const[l,a]=i;if(!a||!l)return n;switch(l){case"ept":return`${a.charAt(0).toUpperCase()}${a.slice(1)} Endpoint`;case"gt":return`${a.split("_").map(r=>r.charAt(0).toUpperCase()+r.slice(1)).join(" ")} Grant`;case"rst":return`${a.charAt(0).toUpperCase()}${a.slice(1)} Response Type`;case"scp":return`${a.charAt(0).toUpperCase()}${a.slice(1)} Scope`;default:return n}}function ue(){const n=["swift","bright","clever","dynamic","efficient","flexible","global","innovative","logical","modern","nimble","optimal","precise","quick","reliable","secure","stable","tech","unified","virtual","wise","active","agile","bold","central","digital"],i=["app","api","system","platform","service","portal","hub","core","base","cloud","data","engine","framework","grid","interface","logic","matrix","network","object","project","resource","solution","tool","utility","vision","workspace"],l=n[Math.floor(Math.random()*n.length)],a=i[Math.floor(Math.random()*i.length)],r=Math.floor(Math.random()*1e3);return`${l}${a}${r}`}function di(){const n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";let i="";for(let l=0;l<32;l++)i+=n.charAt(Math.floor(Math.random()*n.length));return i}function z(n){return Array.isArray(n)?n.filter(i=>i&&i.length>0):n?String(n).split(",").map(i=>i.trim()).filter(i=>i.length>0):[]}const B=({dataEdit:n,dataId:i,onDismiss:l})=>{const[a,r]=x.useState(!0),{toast:m}=K(),c=G(),{handleSubmit:p,register:d,setValue:y,reset:w}=ne(),[q,V]=x.useState(n.permissions??[]),[R,k]=x.useState(n.requirements??[]),{data:b,isLoading:v}=re(),{data:S,isLoading:U}=le(),u=()=>{w({applicationType:"",clientType:"",clientId:"",clientSecret:"",displayName:"",consentType:"",redirectUris:[],postLogoutRedirectUris:[],permissions:[],requirements:[]})},C=b?Array.isArray(b)?b.map(s=>({value:s,label:pe(s)})):[]:[],h=S?Array.isArray(S)?S.map(s=>({value:s.value,label:s.label})):[]:[],f=J({mutationFn:async s=>ge({path:{id:i},body:s}),onSuccess:()=>{m({title:"Success",description:"Client Created Successfully",variant:"success"}),c.invalidateQueries({queryKey:[N.GetOpeniddictApplications]}),u(),r(!1),l()},onError:s=>{const _=Z(s);m({title:_.title,description:_.description,variant:"error"})}}),T=s=>{const _={...s,applicationType:s.applicationType??n.applicationType??"",clientType:s.clientType??n.clientType??"",permissions:q,redirectUris:z(s.redirectUris),postLogoutRedirectUris:z(s.postLogoutRedirectUris)};f.mutate(_)},t=s=>{s&&(u(),l()),r(s)};return e.jsxs("section",{children:[e.jsx(Y,{}),e.jsx(X,{open:a,onOpenChange:t,children:e.jsxs(ee,{style:{maxWidth:"800px"},children:[e.jsx(ie,{children:e.jsx(te,{children:"Edit Client"})}),e.jsxs("form",{onSubmit:p(T),className:"mt-2",children:[e.jsx("section",{className:"flex w-full flex-col space-y-2",children:e.jsxs(ae,{children:[e.jsx(o,{label:"Application Type",description:"The type of the application",children:e.jsxs(I,{defaultValue:n.applicationType??"",onValueChange:s=>y("applicationType",s),children:[e.jsx(P,{className:"w-full",clearable:!0,onClear:()=>y("applicationType",""),children:e.jsx(O,{placeholder:"Select application type"})}),e.jsx(F,{children:oe.map(s=>e.jsx(M,{value:s.value,option:s.option,description:s.description,icon:s.icon,clearable:!0},s.value))})]})}),e.jsx(o,{label:"Client Type",description:"The type of the client",children:e.jsxs(I,{defaultValue:n.clientType??"",onValueChange:s=>y("clientType",s),children:[e.jsx(P,{className:"w-full",children:e.jsx(O,{placeholder:"Select client type"})}),e.jsx(F,{children:ce.map(s=>e.jsx(M,{value:s.value,option:s.option,description:s.description,icon:s.icon},s.value))})]})}),e.jsx(o,{label:"Client Id",description:"The client id of the client",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(g,{required:!0,...d("clientId"),defaultValue:n.clientId??"",placeholder:"Client Id"}),e.jsx(j,{type:"button",variant:"secondary",onClick:ue,title:"Generate Client ID",children:e.jsx(H,{className:"size-4"})})]})}),e.jsx(o,{label:"Display name",description:"The display name of the application",children:e.jsx(g,{placeholder:"Display name",...d("displayName"),defaultValue:n.displayName??""})}),e.jsx(o,{label:"Consent type",description:"The consent type of the client",children:e.jsxs(I,{...d("consentType"),defaultValue:n.consentType??"",children:[e.jsx(P,{className:"w-full",children:e.jsx(O,{placeholder:"Select consent type"})}),e.jsx(F,{children:de.map(s=>e.jsx(M,{value:s.value,option:s.option,description:s.description,icon:s.icon},s.value))})]})}),e.jsx(o,{label:"Client Uri",description:"The client uri of the client",children:e.jsx(g,{placeholder:"Client Uri",...d("clientUri"),defaultValue:n.clientUri??""})}),e.jsx(o,{label:"Redirect Uris",description:"The redirect uris of the client",children:e.jsx(g,{placeholder:"Redirect Uris",...d("redirectUris"),defaultValue:n.redirectUris??""})}),e.jsx(o,{label:"Post logout redirect Uris",description:"The post logout redirect uris of the client",children:e.jsx(g,{placeholder:"Post logout redirect Uris",...d("postLogoutRedirectUris"),defaultValue:n.postLogoutRedirectUris??""})}),e.jsx(o,{label:"Permissions",description:"The permissions of the client",children:e.jsx($,{options:C,value:q,onChange:s=>{V(s),y("permissions",s)},placeholder:v?"Loading permissions...":"Select permissions",disabled:v,maxHeight:300})}),e.jsx(o,{label:"Requirements",description:"The requirements of the client",children:e.jsx($,{mode:"single",options:h,value:R,onChange:s=>{k(s),y("requirements",s)},placeholder:U?"Loading requirements...":"Select requirements",disabled:U,maxHeight:300})})]})}),e.jsxs(se,{className:"mt-5",children:[e.jsx(j,{variant:"ghost",onClick:s=>{s.preventDefault(),r(!1)},disabled:f.isPending,children:"Cancel"}),e.jsx(j,{type:"submit",disabled:f.isPending,children:f.isPending?"Saving...":"Save"})]})]})]})})]})},pi=({children:n})=>{const{can:i}=W(),[l,a]=x.useState(!1),{toast:r}=K(),m=G(),{handleSubmit:c,register:p,setValue:d,reset:y}=ne(),[w,q]=x.useState([]),[V,R]=x.useState([]),{data:k,isLoading:b}=re(),{data:v,isLoading:S}=le(),U=()=>{y({applicationType:"",clientType:"",clientId:"",clientSecret:"",displayName:"",consentType:"",redirectUris:[],postLogoutRedirectUris:[],permissions:[],requirements:[]})},u=k?Array.isArray(k)?k.map(t=>({value:t,label:pe(t)})):[]:[],C=v?Array.isArray(v)?v.map(t=>({value:t.value,label:t.label})):[]:[],h=J({mutationFn:async t=>je({body:t}),onSuccess:()=>{r({title:"Success",description:"Client Created Successfully",variant:"success"}),m.invalidateQueries({queryKey:[N.GetOpeniddictApplications]}),a(!1),U()},onError:t=>{const s=Z(t);r({title:s.title,description:s.description,variant:"error"})}}),f=t=>{const s={...t,redirectUris:z(t.redirectUris),postLogoutRedirectUris:z(t.postLogoutRedirectUris),permissions:w};h.mutate(s)},T=t=>{t&&U(),a(t)};return e.jsxs("section",{children:[e.jsx(Y,{}),e.jsxs(X,{open:l,onOpenChange:T,children:[e.jsx(Oe,{asChild:!0,children:n}),e.jsx("section",{className:"flex items-center justify-between pb-5",children:i("AbpIdentity.Users.Create")&&e.jsxs(j,{size:"sm",className:"w-full sm:py-1 sm:mt-0 sm:w-fit",onClick:()=>T(!0),children:[e.jsx(Fe,{className:"-ml-1 size-4 shrink-0","aria-hidden":"true"}),e.jsx("span",{className:"hidden truncate sm:inline",children:"Create New Client"})]})}),e.jsxs(ee,{style:{maxWidth:"800px"},children:[e.jsx(ie,{children:e.jsx(te,{children:"Create a New Client"})}),e.jsxs("form",{onSubmit:c(f),className:"mt-2",children:[e.jsx("section",{className:"flex w-full flex-col space-y-2",children:e.jsxs(ae,{children:[e.jsx(o,{label:"Application Type",description:"The type of the application",children:e.jsxs(I,{...p("applicationType"),onValueChange:t=>d("applicationType",t),children:[e.jsx(P,{className:"w-full",clearable:!0,onClear:()=>d("applicationType",""),children:e.jsx(O,{placeholder:"Select application type"})}),e.jsx(F,{children:oe.map(t=>e.jsx(M,{value:t.value,option:t.option,description:t.description,icon:t.icon,clearable:!0},t.value))})]})}),e.jsx(o,{label:"Client Type",description:"The type of the client",children:e.jsxs(I,{...p("clientType"),onValueChange:t=>d("clientType",t),children:[e.jsx(P,{className:"w-full",children:e.jsx(O,{placeholder:"Select client type"})}),e.jsx(F,{children:ce.map(t=>e.jsx(M,{value:t.value,option:t.option,description:t.description,icon:t.icon},t.value))})]})}),e.jsx(o,{label:"Client Id",description:"The client id of the client",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(g,{required:!0,...p("clientId"),placeholder:"Client Id"}),e.jsx(j,{type:"button",size:"sm",variant:"secondary",onClick:()=>d("clientId",ue()),title:"Generate Client ID",children:e.jsx(H,{className:"size-4"})})]})}),e.jsx(o,{label:"Client Secret",description:"The client secret of the client",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(g,{required:!0,...p("clientSecret"),placeholder:"Client Secret"}),e.jsx(j,{type:"button",variant:"secondary",size:"sm",onClick:()=>d("clientSecret",di()),title:"Generate Client Secret",children:e.jsx(H,{className:"size-4"})})]})}),e.jsx(o,{label:"Display name",description:"The display name of the application",children:e.jsx(g,{placeholder:"Display name",...p("displayName")})}),e.jsx(o,{label:"Consent type",description:"The consent type of the client",children:e.jsxs(I,{...p("consentType"),onValueChange:t=>d("consentType",t),children:[e.jsx(P,{className:"w-full",children:e.jsx(O,{placeholder:"Select consent type"})}),e.jsx(F,{children:de.map(t=>e.jsx(M,{value:t.value,option:t.option,description:t.description,icon:t.icon},t.value))})]})}),e.jsx(o,{label:"Client Uri",description:"The client uri of the client",children:e.jsx(g,{placeholder:"Client Uri",...p("clientUri")})}),e.jsx(o,{label:"Redirect Uris",description:"The redirect uris of the client",children:e.jsx(g,{placeholder:"Redirect Uris",...p("redirectUris")})}),e.jsx(o,{label:"Post logout redirect Uris",description:"The post logout redirect uris of the client",children:e.jsx(g,{placeholder:"Post logout redirect Uris",...p("postLogoutRedirectUris")})}),e.jsx(o,{label:"Permissions",description:"The permissions of the client",children:e.jsx($,{options:u,value:w,onChange:t=>{q(t),d("permissions",t)},placeholder:b?"Loading permissions...":"Select permissions",disabled:b,maxHeight:300})}),e.jsx(o,{label:"Requirements",description:"The requirements of the client",children:e.jsx($,{mode:"single",options:C,value:V,onChange:t=>{R(t),d("requirements",t)},placeholder:S?"Loading requirements...":"Select requirements",disabled:S,maxHeight:300})})]})}),e.jsxs(se,{className:"mt-5",children:[e.jsx(j,{variant:"ghost",onClick:t=>{t.preventDefault(),a(!1)},disabled:h.isPending,children:"Cancel"}),e.jsx(j,{type:"submit",disabled:h.isPending,children:h.isPending?"Saving...":"Save"})]})]})]})]})]})},ui=({dataId:n,dataEdit:i,onAction:l,variant:a="dropdown"})=>{const{can:r}=W();return a==="dropdown"?e.jsx("div",{className:"flex justify-end",children:e.jsxs(fe,{children:[e.jsx(Ce,{asChild:!0,children:e.jsxs(j,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:[e.jsx(Me,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"Open menu"})]})}),e.jsxs(be,{align:"end",className:"w-[160px]",children:[r("IdentityServer.OpenIddictApplications.Edit")&&e.jsx(Q,{className:"cursor-pointer text-sm",onClick:()=>l(n,i,"edit"),children:"Edit"}),r("IdentityServer.OpenIddictApplications.Delete")&&e.jsx(Q,{className:"cursor-pointer text-sm text-red-500",onClick:()=>l(n,i,"delete"),children:"Delete"})]})]})}):e.jsxs("div",{className:"flex items-center justify-end gap-1",children:[r("AbpIdentity.Users.ManagePermissions")&&e.jsxs(j,{variant:"primary",size:"sm",className:"flex items-center gap-1 px-2 py-1",onClick:()=>l(n,i,"permission"),children:[e.jsx(Re,{className:"h-4 w-4"}),e.jsx("span",{children:"Permission"})]}),r("AbpIdentity.Users.Update")&&e.jsxs(j,{variant:"primary",size:"sm",className:"flex items-center gap-1 px-2 py-1",onClick:()=>l(n,i,"edit"),children:[e.jsx(Le,{className:"h-4 w-4"}),e.jsx("span",{children:"Edit"})]})]})},hi=n=>[{accessorKey:"clientId",header:({column:i})=>e.jsx(D,{column:i,title:"Client ID"}),enableSorting:!0,filterFn:L,enableHiding:!0,cell:i=>i.getValue(),meta:{className:"text-left",displayName:"Client ID"}},{accessorKey:"displayName",header:({column:i})=>e.jsx(D,{column:i,title:"Display Name"}),enableSorting:!0,filterFn:L,enableHiding:!0,cell:i=>i.getValue(),meta:{className:"text-left",displayName:"Display Name"}},{accessorKey:"applicationType",header:({column:i})=>e.jsx(D,{column:i,title:"Application Type"}),enableSorting:!0,filterFn:L,enableHiding:!0,cell:i=>i.getValue(),meta:{className:"text-left",displayName:"Application Type"}},{accessorKey:"consentType",header:({column:i})=>e.jsx(D,{column:i,title:"Consent Type"}),enableSorting:!0,filterFn:L,enableHiding:!0,cell:i=>i.getValue(),meta:{className:"text-left",displayName:"Consent Type"}},{accessorKey:"redirectUris",header:({column:i})=>e.jsx(D,{column:i,title:"Redirect URIs"}),enableSorting:!0,filterFn:L,enableHiding:!0,cell:i=>{const l=i.getValue();return e.jsx("div",{className:"flex flex-wrap gap-1",children:l?.map(a=>e.jsx(ze,{variant:"secondary",children:a},a))})},meta:{className:"text-left",displayName:"Redirect URIs"}},{id:"actions",header:({column:i})=>e.jsx(D,{column:i,title:"Actions"}),enableSorting:!1,enableHiding:!0,cell:i=>e.jsx(ui,{dataId:i.row.original.id,dataEdit:i.row.original,onAction:n,variant:"dropdown"}),meta:{className:"text-right",displayName:"Actions"}}],mi=(n,i,l=[],a)=>E({queryKey:[N.GetOpeniddictApplications,n,i,JSON.stringify(l),a],queryFn:async()=>{try{const r=He({pageIndex:n,pageSize:i,sorting:a,filterConditions:l});return(await ve({body:r})).data?.data}catch(r){const{title:m,description:c}=Ee(r,"Error loading clients");return Te({title:m,description:c,variant:"destructive"}),{items:[],totalCount:0}}},retry:!1}),xi=()=>{const{toast:n}=K(),i=G(),[l,a]=x.useState(""),[r,m]=x.useState([]),[c,p]=x.useState(),[d,y]=x.useState({pageIndex:0,pageSize:10}),{isLoading:w,data:q}=mi(d.pageIndex,d.pageSize,r),R=hi((u,C,h)=>{p({dataId:u,dataEdit:C,dialogType:h})}),k=u=>{a(u);const h=[...r.filter(t=>t.fieldName!=="displayName")];u&&h.push({fieldName:"displayName",operator:"Contains",value:u});const f=JSON.stringify(r),T=JSON.stringify(h);f!==T&&(m(h),y(t=>({...t,pageIndex:0})))},b=u=>{y(u)},v=()=>{i.invalidateQueries({queryKey:[N.GetOpeniddictApplications]}),n({title:"Data refreshed",description:"The client list has been refreshed.",variant:"success"})};if(w)return e.jsx(Pe,{rowCount:d.pageSize,columnCount:4,hasTitle:!0,hasSearch:!0,hasFilters:!0,hasPagination:!0,hasActions:!0});const S=q?.items??[],U=q?.totalCount??0;return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"space-y-4",children:e.jsx($e,{title:"Clients Management",columns:R,data:S,totalCount:U,isLoading:w,manualPagination:!0,pageSize:d.pageSize,onPaginationChange:b,onSearch:k,searchValue:l,customFilterbar:u=>e.jsx(Ge,{...u,activeFilters:r,onServerFilter:C=>{const h=JSON.stringify(r),f=JSON.stringify(C);h!==f&&(m(C),y(T=>({...T,pageIndex:0})))}}),hideDefaultFilterbar:!0,onRefresh:v,enableRowSelection:!1,actionButton:{onClick:()=>{},content:e.jsx(pi,{})}})}),c&&c.dialogType==="edit"&&e.jsx(B,{dataEdit:c.dataEdit,dataId:c.dataId,onDismiss:()=>{i.invalidateQueries({queryKey:[N.GetOpeniddictApplications]}),p(null)}}),c&&c.dialogType==="permission"&&e.jsx(B,{dataEdit:c.dataEdit,dataId:c.dataId,onDismiss:()=>p(null)}),c&&c.dialogType==="delete"&&e.jsx(ci,{dataEdit:c.dataEdit,dataId:c.dataId,onDismiss:()=>{i.invalidateQueries({queryKey:[N.GetOpeniddictApplications]}),p(null)}})]})};function Vi(){return e.jsxs(Se,{children:[e.jsx(Ke,{title:"Dashboard"}),e.jsx(xi,{})]})}export{Vi as default};
//# sourceMappingURL=client-Dzf-sMcJ.js.map
