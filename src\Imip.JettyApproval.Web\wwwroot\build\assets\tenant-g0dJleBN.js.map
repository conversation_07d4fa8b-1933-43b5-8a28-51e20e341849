{"version": 3, "file": "tenant-g0dJleBN.js", "sources": ["../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-first.js", "../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-last.js", "../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/pencil.js", "../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/settings-2.js", "../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/trash.js", "../../../../../frontend/src/components/ui/table-pagination.tsx", "../../../../../frontend/src/components/ui/CustomTable.tsx", "../../../../../frontend/src/lib/hooks/useTenants.ts", "../../../../../frontend/src/components/app/permission/PermissionActions.tsx", "../../../../../frontend/src/components/app/tenant/DeleteTenant.tsx", "../../../../../frontend/src/lib/hooks/useFeatures.ts", "../../../../../frontend/src/components/app/tenant/FeatureList.tsx", "../../../../../frontend/src/components/app/tenant/TenantEdit.tsx", "../../../../../frontend/src/components/app/tenant/TenantList.tsx", "../../../../../frontend/src/pages/tenant.tsx"], "sourcesContent": ["/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"m17 18-6-6 6-6\", key: \"1yerx2\" }],\n  [\"path\", { d: \"M7 6v12\", key: \"1p53r6\" }]\n];\nconst ChevronFirst = createLucideIcon(\"chevron-first\", __iconNode);\n\nexport { __iconNode, ChevronFirst as default };\n//# sourceMappingURL=chevron-first.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"m7 18 6-6-6-6\", key: \"lwmzdw\" }],\n  [\"path\", { d: \"M17 6v12\", key: \"1o0aio\" }]\n];\nconst ChevronLast = createLucideIcon(\"chevron-last\", __iconNode);\n\nexport { __iconNode, ChevronLast as default };\n//# sourceMappingURL=chevron-last.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\n    \"path\",\n    {\n      d: \"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z\",\n      key: \"1a8usu\"\n    }\n  ],\n  [\"path\", { d: \"m15 5 4 4\", key: \"1mk7zo\" }]\n];\nconst Pencil = createLucideIcon(\"pencil\", __iconNode);\n\nexport { __iconNode, Pencil as default };\n//# sourceMappingURL=pencil.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"M20 7h-9\", key: \"3s1dr2\" }],\n  [\"path\", { d: \"M14 17H5\", key: \"gfn3mx\" }],\n  [\"circle\", { cx: \"17\", cy: \"17\", r: \"3\", key: \"18b49y\" }],\n  [\"circle\", { cx: \"7\", cy: \"7\", r: \"3\", key: \"dfmy0x\" }]\n];\nconst Settings2 = createLucideIcon(\"settings-2\", __iconNode);\n\nexport { __iconNode, Settings2 as default };\n//# sourceMappingURL=settings-2.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"M3 6h18\", key: \"d0wm0j\" }],\n  [\"path\", { d: \"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6\", key: \"4alrt4\" }],\n  [\"path\", { d: \"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2\", key: \"v07s0e\" }]\n];\nconst Trash = createLucideIcon(\"trash\", __iconNode);\n\nexport { __iconNode, Trash as default };\n//# sourceMappingURL=trash.js.map\n", "import { getPages } from '@/lib/utils'\r\nimport { type Table } from '@tanstack/react-table'\r\nimport { ChevronFirstIcon, ChevronLastIcon, ChevronLeftIcon, ChevronRightIcon } from 'lucide-react'\r\nimport { v4 } from 'uuid'\r\nimport { Button } from './button'\r\n\r\ntype PaginationProps<T> = {\r\n  pageCount: number\r\n  table: Table<T>\r\n}\r\nexport const Pagination = <T,>({ pageCount, table }: PaginationProps<T>) => {\r\n  const counts = getPages(pageCount, table.getState().pagination.pageIndex)\r\n\r\n  // table.getCanNextPage() doesn't seem to be working. So, it is just a work around.\r\n  const canNextPage =\r\n    table.getState().pagination.pageIndex >= 0 &&\r\n    table.getState().pagination.pageIndex < pageCount - 1\r\n\r\n  const renderButtons = (count: number | string) => {\r\n    if (count === 'SPACER') {\r\n      return (\r\n        <span key={v4()} className=\"text-primary\">\r\n          ...\r\n        </span>\r\n      )\r\n    }\r\n\r\n    return (\r\n      <Button\r\n        size=\"sm\"\r\n        key={v4()}\r\n        disabled={table.getState().pagination.pageIndex === Number(count) - 1}\r\n        onClick={() => {\r\n          table.setPageIndex(Number(count) - 1)\r\n        }}\r\n      >\r\n        {count}\r\n      </Button>\r\n    )\r\n  }\r\n  return (\r\n    <section className=\"pagination flex items-center space-x-1\">\r\n      <Button\r\n        size=\"sm\"\r\n        disabled={!table.getCanPreviousPage()}\r\n        onClick={() => {\r\n          if (!table.getCanPreviousPage()) return\r\n          table.setPageIndex(0)\r\n        }}\r\n      >\r\n        <ChevronFirstIcon width={24} height={24} />\r\n      </Button>\r\n      <Button\r\n        size=\"sm\"\r\n        disabled={!table.getCanPreviousPage()}\r\n        onClick={() => {\r\n          if (!table.getCanPreviousPage()) return\r\n          table.previousPage()\r\n        }}\r\n      >\r\n        <ChevronLeftIcon width={24} height={24} />\r\n      </Button>\r\n      <div className=\"block pl-2 pr-2 lg:hidden\">\r\n        {table.getState().pagination.pageIndex} / {pageCount}\r\n      </div>\r\n      <div className=\"hidden sm:ml-1 sm:mr-1 sm:space-x-2 lg:inline-block\">\r\n        {counts.map(renderButtons)}\r\n      </div>\r\n      <Button\r\n        size=\"sm\"\r\n        disabled={!canNextPage}\r\n        onClick={() => {\r\n          if (!canNextPage) {\r\n            return\r\n          }\r\n          table.nextPage()\r\n        }}\r\n      >\r\n        <ChevronRightIcon width={24} height={24} />\r\n      </Button>\r\n      <Button\r\n        size=\"sm\"\r\n        disabled={!canNextPage}\r\n        onClick={() => {\r\n          if (!canNextPage) {\r\n            return\r\n          }\r\n          table.setPageIndex(pageCount - 1)\r\n        }}\r\n      >\r\n        <ChevronLastIcon width={24} height={24} />\r\n      </Button>\r\n    </section>\r\n  )\r\n}", "import { flexRender, type Table } from '@tanstack/react-table'\r\nimport { useCallback } from 'react'\r\nimport { Pagination } from './table-pagination'\r\n\r\nexport type TableViewProps<T> = {\r\n  table: Table<T>\r\n  totalCount: number\r\n  pageSize: number\r\n}\r\n\r\nconst TableView = <T,>({ table, totalCount, pageSize }: TableViewProps<T>) => {\r\n  const renderHeader = useCallback(() => {\r\n    const headerGroups = table.getHeaderGroups()\r\n    return headerGroups.map((headerGroup) => {\r\n      const headers = headerGroup.headers\r\n      return (\r\n        <tr key={headerGroup.id} className=\"first:hidden\">\r\n          {headers.map((header) => {\r\n            if (header.isPlaceholder) return false\r\n            return (\r\n              <th key={header.id} className=\"last:1/2 truncate px-3 lg:last:w-1/4\">\r\n                {flexRender(header.column.columnDef.header, header.getContext())}\r\n              </th>\r\n            )\r\n          })}\r\n        </tr>\r\n      )\r\n    })\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [])\r\n\r\n  const renderBody = useCallback(() => {\r\n    const rows = table.getRowModel().rows\r\n    return rows.map((row) => {\r\n      const cells = row.getVisibleCells()\r\n      return (\r\n        <tr\r\n          key={row.id}\r\n          className=\"hover:text-primary-content border-b border-b-primary transition delay-75 ease-in hover:bg-primary/90\"\r\n        >\r\n          {cells.map((cell) => {\r\n            return (\r\n              <td key={cell.id} className=\"truncate py-3 pl-3 text-left text-xs\">\r\n                {flexRender(cell.column.columnDef.cell, cell.getContext())}\r\n              </td>\r\n            )\r\n          })}\r\n        </tr>\r\n      )\r\n    })\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [])\r\n\r\n  if (totalCount === 0) {\r\n    return (\r\n      <section className=\"flex justify-center p-3\">\r\n        <h3 className=\"text-base-content leading-3\">No Records Found</h3>\r\n      </section>\r\n    )\r\n  }\r\n  const pageCount = Math.ceil(totalCount / pageSize)\r\n  return (\r\n    <section>\r\n      <section className=\"overflow-auto\">\r\n        <table className=\"divide-base-200 text-base-content w-full table-auto divide-y text-left sm:overflow-x-auto lg:table-fixed\">\r\n          <thead>{renderHeader()}</thead>\r\n          <tbody>{renderBody()}</tbody>\r\n        </table>\r\n      </section>\r\n      <div className=\"flex flex-col border-t p-5 lg:flex-row lg:items-center\">\r\n        <div className=\"text-base-content grow pb-2\">{totalCount} total</div>\r\n        {totalCount > 10 && <Pagination<T> pageCount={pageCount} table={table} />}\r\n      </div>\r\n    </section>\r\n  )\r\n}\r\n\r\nexport const CustomTable = TableView\r\n", "import { getApiMultiTenancyTenants } from '@/client'\r\nimport { useQuery } from '@tanstack/react-query'\r\nimport { QueryNames } from './QueryConstants'\r\n\r\n/**\r\n * Custom hook to fetch a list of tenants.\r\n *\r\n * This hook uses the `useQuery` hook from `react-query` to fetch the tenant data\r\n * asynchronously. The query key used is `QueryNames.GetTenants` along with pagination,\r\n * filter, and sorting parameters.\r\n *\r\n * @param {number} pageIndex - The current page index.\r\n * @param {number} pageSize - The number of items per page.\r\n * @param {string} [filter] - Optional filter string.\r\n * @param {string} [sorting] - Optional sorting string.\r\n * @returns {UseQueryResult} The result of the query, which includes the tenant data and query status.\r\n */\r\nexport const useTenants = (\r\n  pageIndex: number,\r\n  pageSize: number,\r\n  filter?: string  ,\r\n  sorting?: string  \r\n) => {\r\n  return useQuery({\r\n    queryKey: [QueryNames.GetTenants, pageIndex, pageSize, filter, sorting],\r\n    queryFn: async () => {\r\n      let skip = 0\r\n      if (pageIndex > 0) {\r\n        skip = pageIndex * pageSize\r\n      }\r\n      const { data } = await getApiMultiTenancyTenants({\r\n        query: {\r\n          MaxResultCount: pageSize,\r\n          SkipCount: skip,\r\n          Filter: filter,\r\n          Sorting: sorting,\r\n        }\r\n      })\r\n      return data\r\n    },\r\n  })\r\n}\r\n", "import { type Policy, useGrantedPolicies } from '@/lib/hooks/useGrantedPolicies'\r\nimport { Cog, CogIcon, PencilIcon, Settings2, Trash } from 'lucide-react'\r\nimport { v4 } from 'uuid'\r\nimport { Button } from '@/components/ui/button'\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu'\r\ntype PermissionActionsProps = {\r\n  actions: Array<{\r\n    icon: 'permission' | 'trash' | 'pencil' | 'features'\r\n    callback: () => void\r\n    policy: Policy\r\n    visible?: boolean\r\n  }>\r\n}\r\nexport const PermissionActions = ({ actions }: PermissionActionsProps) => {\r\n  const { can } = useGrantedPolicies()\r\n  const renderElement = (action: (typeof actions)[0]) => {\r\n    if (!can(action.policy) || action.visible) return false\r\n\r\n    return (\r\n      <DropdownMenuItem key={v4()}>\r\n        <Button onClick={action.callback}>\r\n          {action.icon === 'permission' && (\r\n            <div className=\"flex items-center space-x-1\">\r\n              <Settings2 width={18} height={18} className=\"text-primary-content flex-1\" />\r\n              <span className=\"text-primary-content hidden sm:inline\">Permission</span>\r\n            </div>\r\n          )}\r\n          {action.icon === 'trash' && (\r\n            <div className=\"flex items-center space-x-1\">\r\n              <Trash width={18} height={18} className=\"text-primary-content flex-1\" />\r\n              <span className=\"text-primary-content hidden sm:inline\">Delete</span>\r\n            </div>\r\n          )}\r\n          {action.icon === 'pencil' && (\r\n            <div className=\"flex items-center space-x-1\">\r\n              <PencilIcon width={18} height={18} className=\"text-primary-content flex-1\" />\r\n              <span className=\"text-primary-content hidden sm:inline\">Edit</span>\r\n            </div>\r\n          )}\r\n          {action.icon === 'features' && (\r\n            <div className=\"flex items-center space-x-1\">\r\n              <CogIcon width={18} height={18} className=\"text-primary-content flex-1\" />\r\n              <span className=\"text-primary-content hidden sm:inline\">Settings</span>\r\n            </div>\r\n          )}\r\n        </Button>\r\n      </DropdownMenuItem>\r\n    )\r\n  }\r\n  return (\r\n    <section className=\"flex items-center space-x-2\">\r\n      <DropdownMenu>\r\n        <DropdownMenuTrigger key={v4()} asChild>\r\n          <Button size=\"sm\" className=\"flex items-center space-x-1\">\r\n            <Cog width={16} height={16} />\r\n            <span className=\"hidden sm:inline\">Actions</span>\r\n          </Button>\r\n        </DropdownMenuTrigger>\r\n        <DropdownMenuContent>{actions.map(renderElement)}</DropdownMenuContent>\r\n      </DropdownMenu>\r\n    </section>\r\n  )\r\n}\r\n", "import { deleteApiMultiTenancyTenantsById } from '@/client'\r\nimport { useEffect, useState } from 'react'\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@/components/ui/alert-dialog'\r\nimport { useToast } from \"@/hooks/use-toast\"\r\n\r\ntype DeleteTenantProps = {\r\n  tenant: { tenantId: string; tenantName: string }\r\n  onDismiss: () => void\r\n}\r\nexport const DeleteTenant = ({\r\n  tenant: { tenantId, tenantName },\r\n  onDismiss,\r\n}: DeleteTenantProps) => {\r\n  const { toast } = useToast()\r\n  const [open, setOpen] = useState<boolean>(false)\r\n\r\n  const onYesEvent = async () => {\r\n    try {\r\n      await deleteApiMultiTenancyTenantsById({\r\n        path: { id: tenantId },\r\n      })\r\n      toast({\r\n        title: 'Success',\r\n        description: `Tenant \"${tenantName}\" has been deleted successfully.`,\r\n      })\r\n      onDismiss()\r\n    } catch (err: unknown) {\r\n      if (err instanceof Error) {\r\n        toast({\r\n          title: 'Failed',\r\n          description: `There was a problem when deleting the role ${tenantName}. Kindly try again.`,\r\n          variant: 'destructive',\r\n        })\r\n      }\r\n    }\r\n  }\r\n\r\n  useEffect(() => {\r\n    setOpen(true)\r\n  }, [])\r\n\r\n  return (\r\n    <AlertDialog open={open}>\r\n      <AlertDialogContent>\r\n        <AlertDialogHeader>\r\n          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>\r\n          <AlertDialogDescription>\r\n            This action cannot be undone. This will permanently delete your tenant name &quot;\r\n            {tenantName}&quot;\r\n          </AlertDialogDescription>\r\n        </AlertDialogHeader>\r\n        <AlertDialogFooter>\r\n          <AlertDialogCancel onClick={onDismiss}>Cancel</AlertDialogCancel>\r\n          <AlertDialogAction onClick={onYesEvent}>Yes</AlertDialogAction>\r\n        </AlertDialogFooter>\r\n      </AlertDialogContent>\r\n    </AlertDialog>\r\n  )\r\n}\r\n", "import { type GetFeatureListResultDto, getApiFeatureManagementFeatures } from '@/client'\r\nimport { type UseQueryResult, useQuery } from '@tanstack/react-query'\r\nimport { QueryNames } from './QueryConstants'\r\n\r\n/**\r\n * Custom hook to fetch feature list based on provider name and provider key.\r\n *\r\n * @param providerName - The name of the provider.\r\n * @param providerKey - The key of the provider.\r\n * @returns A query result containing the feature list.\r\n */\r\nexport const useFeatures = (\r\n  providerName: string | undefined,\r\n  providerKey: string | undefined\r\n): UseQueryResult<GetFeatureListResultDto, unknown> => {\r\n  return useQuery({\r\n    queryKey: [QueryNames.GetFeatures, providerName, providerKey],\r\n    queryFn: async () => {\r\n      const { data } = await getApiFeatureManagementFeatures({\r\n        query: { providerName, providerKey }\r\n      })\r\n      return data!\r\n    },\r\n  })\r\n}\r\n", "import { type FeatureGroupDto, type UpdateFeaturesDto, deleteApiFeatureManagementFeatures, putApiFeatureManagementFeatures } from '@/client'\r\nimport { Button } from '@/components/ui/button'\r\nimport {\r\n  <PERSON><PERSON>,\r\n  DialogContent,\r\n  DialogFooter,\r\n  <PERSON>alogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog'\r\nimport { QueryNames } from '@/lib/hooks/QueryConstants'\r\nimport { useFeatures } from '@/lib/hooks/useFeatures'\r\nimport { PermissionProvider } from '@/lib/utils'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { useEffect, useState } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { v4 } from 'uuid'\r\nimport { Checkbox } from '@/components/ui/checkbox'\r\nimport { useToast } from \"@/hooks/use-toast\"\r\n\r\nexport type FeatureListProps = {\r\n  onDismiss: () => void\r\n  tenantId: string\r\n}\r\n\r\nexport const FeatureList = ({ onDismiss, tenantId }: FeatureListProps) => {\r\n  const { data } = useFeatures(PermissionProvider.T, tenantId)\r\n  const queryClient = useQueryClient()\r\n  const [enableSetting, setEnableSetting] = useState<boolean>(false)\r\n  const [enableEmailSetting, setEnableEmailSetting] = useState<boolean>(false)\r\n  const [open, setOpen] = useState(false)\r\n  const { handleSubmit } = useForm()\r\n  const { toast } = useToast()\r\n\r\n  const onCloseEvent = () => {\r\n    setOpen(false)\r\n    onDismiss()\r\n  }\r\n\r\n  useEffect(() => {\r\n    setOpen(true)\r\n    data?.groups?.forEach((g) => {\r\n      g.features?.forEach((f) => {\r\n        if (f.name === 'SettingManagement.Enable' && f.value === 'true') {\r\n          setEnableSetting(true)\r\n        } else if (\r\n          f.name === 'SettingManagement.AllowChangingEmailSettings' &&\r\n          f.value === 'true'\r\n        ) {\r\n          setEnableEmailSetting(true)\r\n        }\r\n      })\r\n    })\r\n    return () => {\r\n      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetFeatures] }).then()\r\n      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetTenants] }).then()\r\n      void queryClient.invalidateQueries({ queryKey: [PermissionProvider.T] }).then()\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [onDismiss, data])\r\n\r\n  const onCheckedEvent = (value: boolean, name: string) => {\r\n    if (name === 'SettingManagement.Enable') {\r\n      setEnableSetting(value)\r\n    } else if (name === 'SettingManagement.AllowChangingEmailSettings') {\r\n      setEnableEmailSetting(value)\r\n    }\r\n  }\r\n\r\n  const onSubmit = async () => {\r\n    try {\r\n      const featureUpdateDto = {} as UpdateFeaturesDto\r\n      featureUpdateDto.features = [\r\n        {\r\n          name: 'SettingManagement.Enable',\r\n          value: enableSetting.toString(),\r\n        },\r\n        {\r\n          name: 'SettingManagement.AllowChangingEmailSettings',\r\n          value: enableEmailSetting.toString(),\r\n        },\r\n      ]\r\n\r\n      await putApiFeatureManagementFeatures({\r\n        body: featureUpdateDto,\r\n        query: { providerKey: PermissionProvider.T, providerName: tenantId },\r\n      })\r\n      toast({\r\n        title: 'Success',\r\n        description: 'Features Update Successfully',\r\n        variant: 'default',\r\n      })\r\n      onCloseEvent()\r\n    } catch (err: unknown) {\r\n      if (err instanceof Error) {\r\n        toast({\r\n          title: 'Failed',\r\n          description: \"Feature update failed.\",\r\n          variant: 'destructive',\r\n        })\r\n      }\r\n    }\r\n  }\r\n\r\n  const onResetToDefaultEvent = async () => {\r\n    try {\r\n      await deleteApiFeatureManagementFeatures({\r\n        query: { providerKey: PermissionProvider.T, providerName: tenantId },\r\n      })\r\n      toast({\r\n        title: 'Success',\r\n        description: 'Features has been set to default.',\r\n        variant: 'default',\r\n      })\r\n      onCloseEvent()\r\n    } catch (err: unknown) {\r\n      if (err instanceof Error) {\r\n        toast({\r\n          title: 'Failed',\r\n          description: \"Features wasn't able to reset tp default.\",\r\n          variant: 'destructive',\r\n        })\r\n      }\r\n    }\r\n  }\r\n\r\n  return (\r\n    <section className=\"p-3\">\r\n      <Dialog open={open} onOpenChange={onCloseEvent}>\r\n        <DialogContent>\r\n          <DialogHeader>\r\n            <DialogTitle>Features</DialogTitle>\r\n          </DialogHeader>\r\n          <form onSubmit={handleSubmit(onSubmit)}>\r\n            <div className=\"grid grid-cols-1 items-baseline gap-2 sm:grid-cols-[12rem_minmax(10rem,1fr)_auto]\">\r\n              <div className=\"p-3\">\r\n                {data?.groups?.map((el: FeatureGroupDto) => (\r\n                  <span key={v4()}>{el.displayName}</span>\r\n                ))}\r\n              </div>\r\n              <div className=\"mt-5 p-3\">\r\n                {data?.groups?.map((el: FeatureGroupDto) => (\r\n                  <div key={v4()}>\r\n                    <h3 className=\"text-xl font-medium\">{el.displayName}</h3>\r\n                    <hr className=\"mt-2 w-full pb-2\" />\r\n                    {el.features?.map((feature) => (\r\n                      <div key={v4()} className=\"mt-2 text-base\">\r\n                        <Checkbox\r\n                          id={`${feature.name}_enable`}\r\n                          name={feature.name!}\r\n                          checked={\r\n                            feature.name === 'SettingManagement.Enable'\r\n                              ? enableSetting\r\n                              : enableEmailSetting\r\n                          }\r\n                          onCheckedChange={(checked) =>\r\n                            onCheckedEvent(!!checked.valueOf(), feature.name!)\r\n                          }\r\n                        />\r\n                        <label\r\n                          htmlFor={`${feature.name}_enable`}\r\n                          className=\"text-sm font-medium leading-none\"\r\n                        >\r\n                          <span className=\"pl-2\">{feature.displayName}</span>\r\n                        </label>\r\n                        <p className=\"pl-6 pt-1 text-xs\">{feature.description}</p>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n\r\n            <DialogFooter className=\"mt-5\">\r\n              <Button\r\n                onClick={async (e: { preventDefault: () => void }) => {\r\n                  e.preventDefault()\r\n                  await onResetToDefaultEvent()\r\n                }}\r\n              >\r\n                Reset to default\r\n              </Button>\r\n              <Button\r\n                onClick={(e: { preventDefault: () => void }) => {\r\n                  e.preventDefault()\r\n                  onCloseEvent()\r\n                }}\r\n              >\r\n                Cancel\r\n              </Button>\r\n              <Button type=\"submit\">Save</Button>\r\n            </DialogFooter>\r\n          </form>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </section>\r\n  )\r\n}\r\n", "import { type TenantUpdateDto, putApiMultiTenancyTenantsById } from '@/client'\r\nimport { Button } from '@/components/ui/button'\r\nimport { Checkbox } from '@/components/ui/checkbox'\r\nimport {\r\n  <PERSON><PERSON>,\r\n  DialogContent,\r\n  DialogFooter,\r\n  <PERSON><PERSON>Header,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog'\r\nimport { Input } from '@/components/ui/input'\r\nimport { useToast } from \"@/hooks/use-toast\"\r\nimport { QueryNames } from '@/lib/hooks/QueryConstants'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport classNames from 'clsx'\r\nimport { useEffect, useState } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\n\r\n// Define a type with the Host property for extraProperties\r\ninterface TenantExtraProperties {\r\n  Host?: string;\r\n}\r\n\r\nexport type TenantEditProps = {\r\n  tenantDto: TenantUpdateDto\r\n  tenantId: string\r\n  onDismiss: () => void\r\n}\r\n\r\nexport const TenantEdit = ({ tenantDto, tenantId, onDismiss }: TenantEditProps) => {\r\n  const queryClient = useQueryClient()\r\n  const [open, setOpen] = useState(false)\r\n\r\n  const [enableHost, setEnableHost] = useState(false)\r\n  const { toast } = useToast()\r\n  const { handleSubmit, register } = useForm()\r\n\r\n  useEffect(() => {\r\n    // Cast extraProperties to our known type\r\n    if ((tenantDto?.extraProperties as TenantExtraProperties)?.Host) {\r\n      setEnableHost(true)\r\n    }\r\n  }, [tenantDto, (tenantDto?.extraProperties as TenantExtraProperties)?.Host])\r\n\r\n  const onSubmit = async (dto: unknown) => {\r\n    const tenant = dto as TenantUpdateDto & { host?: string }\r\n\r\n    try {\r\n      await putApiMultiTenancyTenantsById({\r\n        body: tenant,\r\n        path: { id: tenantId },\r\n      })\r\n      // if (enableHost && tenant?.host) {\r\n      //   await postApiMultiTenancyTenants({\r\n      //     body: {\r\n      //       id: tenantId,\r\n      //       host: tenant.host,\r\n      //     },\r\n      //   })\r\n      // }\r\n      toast({\r\n        title: 'Success',\r\n        description: 'Tenant information updated successfully',\r\n        variant: 'default',\r\n      })\r\n      setOpen(false)\r\n    } catch (err: unknown) {\r\n      if (err instanceof Error) {\r\n        toast({\r\n          title: 'Failed',\r\n          description: \"Tenant update wasn't successfull.\",\r\n          variant: 'destructive',\r\n        })\r\n      }\r\n    }\r\n  }\r\n\r\n  const onCloseEvent = () => {\r\n    setOpen(false)\r\n    onDismiss()\r\n  }\r\n\r\n  useEffect(() => {\r\n    setOpen(true)\r\n    return () => {\r\n      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetTenants] })\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [])\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={onCloseEvent}>\r\n      <DialogContent>\r\n        <DialogHeader>\r\n          <DialogTitle>Update a Tenant Name</DialogTitle>\r\n        </DialogHeader>\r\n        <form onSubmit={handleSubmit(onSubmit)}>\r\n          <section className=\"flex flex-col space-y-5\">\r\n            <Input\r\n              required\r\n              placeholder=\"Tenant Name\"\r\n              defaultValue={tenantDto.name ?? ''}\r\n              {...register('name')}\r\n            />\r\n          </section>\r\n          <div className={classNames('flex items-center space-x-2 pb-2 pt-5')}>\r\n            <Checkbox\r\n              id=\"enableHost\"\r\n              name=\"enableHost\"\r\n              defaultChecked\r\n              checked={enableHost}\r\n              onCheckedChange={(checked) => setEnableHost(!!checked.valueOf())}\r\n            />\r\n            <label htmlFor=\"activeHost\" className=\"text-sm font-medium leading-none\">\r\n              Enable host\r\n            </label>\r\n          </div>\r\n          {enableHost && (\r\n            <div className=\"flex w-auto flex-col pb-5 pt-5\">\r\n              <section className=\"flex w-full flex-col space-y-5\">\r\n                <Input required {...register('host')} placeholder=\"Hose Name\" />\r\n              </section>\r\n            </div>\r\n          )}\r\n          <DialogFooter className=\"mt-5\">\r\n            <Button\r\n              onClick={(e: { preventDefault: () => void }) => {\r\n                e.preventDefault()\r\n                onCloseEvent()\r\n              }}\r\n            >\r\n              Cancel\r\n            </Button>\r\n            <Button type=\"submit\">Save</Button>\r\n          </DialogFooter>\r\n        </form>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n", "'use client'\r\nimport { type TenantDto, type TenantUpdateDto } from '@/client'\r\nimport { CustomTable } from '@/components/ui/CustomTable'\r\nimport Error from '@/components/ui/Error'\r\nimport Loader from '@/components/ui/Loader'\r\nimport { Search } from '@/components/ui/search'\r\nimport { useToast } from \"@/hooks/use-toast\"\r\nimport { QueryNames } from '@/lib/hooks/QueryConstants'\r\nimport { useTenants } from '@/lib/hooks/useTenants'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { type ColumnDef, type PaginationState, getCoreRowModel, useReactTable } from '@tanstack/react-table'\r\nimport { useMemo, useState } from 'react'\r\nimport { PermissionActions } from '@/components/app/permission/PermissionActions'\r\nimport { DeleteTenant } from './DeleteTenant'\r\nimport { FeatureList } from './FeatureList'\r\nimport { TenantEdit } from './TenantEdit'\r\n\r\nexport const TenantList = () => {\r\n  const { toast } = useToast()\r\n  const queryClient = useQueryClient()\r\n  const [searchStr, setSearchStr] = useState<string | undefined>()\r\n  const [tenantActionDialog, setTenantActionDialog] = useState<{\r\n    tenantId: string\r\n    tenantDto: TenantUpdateDto\r\n    dialgoType?: 'edit' | 'manage_features' | 'delete'\r\n  } | null>()\r\n\r\n  const [{ pageIndex, pageSize }, setPagination] = useState<PaginationState>({\r\n    pageIndex: 0,\r\n    pageSize: 10,\r\n  })\r\n  const { isLoading, data, isError } = useTenants(pageIndex, pageSize, searchStr)\r\n  const pagination = useMemo(\r\n    () => ({\r\n      pageIndex,\r\n      pageSize,\r\n    }),\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n    [pageIndex, pageSize, toast]\r\n  )\r\n\r\n  const defaultColumns: ColumnDef<TenantDto>[] = useMemo(\r\n    () => [\r\n      {\r\n        header: 'Tenant Management',\r\n        columns: [\r\n          {\r\n            accessorKey: 'actions',\r\n            header: 'Actions',\r\n            cell: (info) => {\r\n              return (\r\n                <PermissionActions\r\n                  actions={[\r\n                    {\r\n                      icon: 'features',\r\n                      policy: 'AbpTenantManagement.Tenants.ManageFeatures',\r\n                      callback: () => {\r\n                        setTenantActionDialog({\r\n                          dialgoType: 'manage_features',\r\n                          tenantId: info.row.original.id!,\r\n                          tenantDto: info.row.original as TenantUpdateDto,\r\n                        })\r\n                      },\r\n                    },\r\n                    {\r\n                      icon: 'pencil',\r\n                      policy: 'AbpTenantManagement.Tenants.Update',\r\n                      callback: () => {\r\n                        setTenantActionDialog({\r\n                          dialgoType: 'edit',\r\n                          tenantId: info.row.original.id!,\r\n                          tenantDto: info.row.original as TenantUpdateDto,\r\n                        })\r\n                      },\r\n                    },\r\n                    {\r\n                      icon: 'trash',\r\n                      policy: 'AbpTenantManagement.Tenants.Delete',\r\n                      callback: () => {\r\n                        setTenantActionDialog({\r\n                          tenantId: info.row.original.id!,\r\n                          tenantDto: info.row.original as TenantUpdateDto,\r\n                          dialgoType: 'delete',\r\n                        })\r\n                      },\r\n                    },\r\n                  ]}\r\n                />\r\n              )\r\n            },\r\n          },\r\n          {\r\n            accessorKey: 'name',\r\n            header: 'Tenant Name',\r\n            cell: (info) => info.getValue() as string,\r\n          },\r\n        ],\r\n      },\r\n    ],\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n    [tenantActionDialog]\r\n  )\r\n\r\n  const onSearchUpdateEvent = (value: string) => {\r\n    setSearchStr(value)\r\n  }\r\n\r\n  const table = useReactTable({\r\n    data: data?.items ?? [],\r\n    pageCount: data?.totalCount ?? -1,\r\n    state: {\r\n      pagination,\r\n    },\r\n    columns: defaultColumns,\r\n    getCoreRowModel: getCoreRowModel(),\r\n    onPaginationChange: setPagination,\r\n    manualPagination: true,\r\n  })\r\n\r\n  if (isLoading) return <Loader />\r\n  if (isError) return <Error />\r\n\r\n  return (\r\n    <>\r\n      {tenantActionDialog?.dialgoType === 'edit' && (\r\n        <TenantEdit\r\n          tenantDto={tenantActionDialog.tenantDto}\r\n          tenantId={tenantActionDialog.tenantId}\r\n          onDismiss={() => {\r\n            void queryClient.invalidateQueries({ queryKey: [QueryNames.GetTenants] })\r\n            setTenantActionDialog(null)\r\n          }}\r\n        />\r\n      )}\r\n      {tenantActionDialog?.dialgoType === 'delete' && (\r\n        <DeleteTenant\r\n          tenant={{\r\n            tenantId: tenantActionDialog.tenantId,\r\n            tenantName: tenantActionDialog.tenantDto.name,\r\n          }}\r\n          onDismiss={() => {\r\n            void queryClient.invalidateQueries({ queryKey: [QueryNames.GetTenants] })\r\n            setTenantActionDialog(null)\r\n          }}\r\n        />\r\n      )}\r\n      {tenantActionDialog?.dialgoType === 'manage_features' && (\r\n        <FeatureList\r\n          onDismiss={() => setTenantActionDialog(null)}\r\n          tenantId={tenantActionDialog.tenantId}\r\n        />\r\n      )}\r\n      <Search onUpdate={onSearchUpdateEvent} value={searchStr ?? ''} />\r\n      <CustomTable<TenantDto>\r\n        table={table}\r\n        totalCount={data?.totalCount ?? 0}\r\n        pageSize={pageSize}\r\n      />\r\n    </>\r\n  )\r\n}\r\n", "'use client';\r\n\r\nimport { TenantList } from '@/components/app/tenant/TenantList';\r\nimport AppLayout from '@/layouts/app-layout';\r\nimport { Head } from '@inertiajs/react';\r\nexport default function OverViewLayout() {\r\n  return (\r\n    <AppLayout>\r\n      <Head title=\"Dashboard\" />\r\n\r\n      <TenantList />\r\n    </AppLayout>\r\n  );\r\n}\r\n"], "names": ["__iconNode", "ChevronFirst", "createLucideIcon", "ChevronLast", "Pencil", "Settings2", "Trash", "Pagination", "pageCount", "table", "counts", "getPages", "canNextPage", "renderButtons", "count", "v4", "jsx", "<PERSON><PERSON>", "jsxs", "ChevronFirstIcon", "ChevronLeftIcon", "ChevronRightIcon", "ChevronLastIcon", "TableView", "totalCount", "pageSize", "renderHeader", "useCallback", "headerGroup", "headers", "header", "renderBody", "row", "cells", "cell", "CustomTable", "useTenants", "pageIndex", "filter", "sorting", "useQuery", "QueryNames", "skip", "data", "getApiMultiTenancyTenants", "PermissionActions", "actions", "can", "useGrantedPolicies", "renderElement", "action", "DropdownMenuItem", "PencilIcon", "CogIcon", "DropdownMenu", "DropdownMenuTrigger", "Cog", "DropdownMenuContent", "DeleteTenant", "tenantId", "tenantName", "on<PERSON><PERSON><PERSON>", "toast", "useToast", "open", "<PERSON><PERSON><PERSON>", "useState", "onYesEvent", "deleteApiMultiTenancyTenantsById", "err", "useEffect", "AlertDialog", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Alert<PERSON><PERSON><PERSON>H<PERSON>er", "AlertDialogTitle", "AlertDialogDescription", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AlertDialogCancel", "AlertDialogAction", "useFeatures", "providerName", "providerKey", "getApiFeatureManagementFeatures", "FeatureList", "PermissionProvider", "queryClient", "useQueryClient", "enableSetting", "setEnableSetting", "enableEmailSetting", "setEnableEmailSetting", "handleSubmit", "useForm", "onCloseEvent", "g", "f", "onCheckedEvent", "value", "name", "onSubmit", "featureUpdateDto", "putApiFeatureManagementFeatures", "onResetToDefaultEvent", "deleteApiFeatureManagementFeatures", "Dialog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "DialogTitle", "el", "feature", "Checkbox", "checked", "<PERSON><PERSON><PERSON><PERSON>er", "e", "TenantEdit", "tenantDto", "enableHost", "setEnableHost", "register", "dto", "tenant", "putApiMultiTenancyTenantsById", "Input", "classNames", "TenantList", "searchStr", "setSearchStr", "tenantActionDialog", "setTenantActionDialog", "setPagination", "isLoading", "isError", "pagination", "useMemo", "defaultColumns", "info", "onSearchUpdateEvent", "useReactTable", "getCoreRowModel", "Loader", "Error", "Fragment", "Search", "OverViewLayout", "AppLayout", "Head"], "mappings": "m1BAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,iBAAkB,IAAK,QAAQ,CAAE,EAC/C,CAAC,OAAQ,CAAE,EAAG,UAAW,IAAK,QAAU,CAAA,CAC1C,EACMC,GAAeC,EAAiB,gBAAiBF,EAAU,ECbjE;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,gBAAiB,IAAK,QAAQ,CAAE,EAC9C,CAAC,OAAQ,CAAE,EAAG,WAAY,IAAK,QAAU,CAAA,CAC3C,EACMG,GAAcD,EAAiB,eAAgBF,EAAU,ECb/D;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CACE,OACA,CACE,EAAG,mIACH,IAAK,QACX,CACG,EACD,CAAC,OAAQ,CAAE,EAAG,YAAa,IAAK,QAAU,CAAA,CAC5C,EACMI,GAASF,EAAiB,SAAUF,EAAU,ECnBpD;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,WAAY,IAAK,QAAQ,CAAE,EACzC,CAAC,OAAQ,CAAE,EAAG,WAAY,IAAK,QAAQ,CAAE,EACzC,CAAC,SAAU,CAAE,GAAI,KAAM,GAAI,KAAM,EAAG,IAAK,IAAK,SAAU,EACxD,CAAC,SAAU,CAAE,GAAI,IAAK,GAAI,IAAK,EAAG,IAAK,IAAK,QAAU,CAAA,CACxD,EACMK,GAAYH,EAAiB,aAAcF,EAAU,ECf3D;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,UAAW,IAAK,QAAQ,CAAE,EACxC,CAAC,OAAQ,CAAE,EAAG,wCAAyC,IAAK,QAAQ,CAAE,EACtE,CAAC,OAAQ,CAAE,EAAG,qCAAsC,IAAK,QAAU,CAAA,CACrE,EACMM,GAAQJ,EAAiB,QAASF,EAAU,ECJrCO,GAAa,CAAK,CAAE,UAAAC,EAAW,MAAAC,KAAgC,CAC1E,MAAMC,EAASC,EAASH,EAAWC,EAAM,SAAS,EAAE,WAAW,SAAS,EAGlEG,EACJH,EAAM,SAAS,EAAE,WAAW,WAAa,GACzCA,EAAM,SAAS,EAAE,WAAW,UAAYD,EAAY,EAEhDK,EAAiBC,GACjBA,IAAU,eAET,OAAgB,CAAA,UAAU,eAAe,SAAA,OAA/BC,GAEX,EAKFC,EAAA,IAACC,EAAA,CACC,KAAK,KAEL,SAAUR,EAAM,WAAW,WAAW,YAAc,OAAOK,CAAK,EAAI,EACpE,QAAS,IAAM,CACbL,EAAM,aAAa,OAAOK,CAAK,EAAI,CAAC,CACtC,EAEC,SAAAA,CAAA,EANIC,EAAG,CAOV,EAIF,OAAAG,EAAA,KAAC,UAAQ,CAAA,UAAU,yCACjB,SAAA,CAAAF,EAAA,IAACC,EAAA,CACC,KAAK,KACL,SAAU,CAACR,EAAM,mBAAmB,EACpC,QAAS,IAAM,CACRA,EAAM,sBACXA,EAAM,aAAa,CAAC,CACtB,EAEA,SAACO,EAAA,IAAAG,GAAA,CAAiB,MAAO,GAAI,OAAQ,EAAI,CAAA,CAAA,CAC3C,EACAH,EAAA,IAACC,EAAA,CACC,KAAK,KACL,SAAU,CAACR,EAAM,mBAAmB,EACpC,QAAS,IAAM,CACRA,EAAM,sBACXA,EAAM,aAAa,CACrB,EAEA,SAACO,EAAA,IAAAI,GAAA,CAAgB,MAAO,GAAI,OAAQ,EAAI,CAAA,CAAA,CAC1C,EACAF,EAAAA,KAAC,MAAI,CAAA,UAAU,4BACZ,SAAA,CAAMT,EAAA,WAAW,WAAW,UAAU,MAAID,CAAA,EAC7C,QACC,MAAI,CAAA,UAAU,sDACZ,SAAOE,EAAA,IAAIG,CAAa,EAC3B,EACAG,EAAA,IAACC,EAAA,CACC,KAAK,KACL,SAAU,CAACL,EACX,QAAS,IAAM,CACRA,GAGLH,EAAM,SAAS,CACjB,EAEA,SAACO,EAAA,IAAAK,EAAA,CAAiB,MAAO,GAAI,OAAQ,EAAI,CAAA,CAAA,CAC3C,EACAL,EAAA,IAACC,EAAA,CACC,KAAK,KACL,SAAU,CAACL,EACX,QAAS,IAAM,CACRA,GAGCH,EAAA,aAAaD,EAAY,CAAC,CAClC,EAEA,SAACQ,EAAA,IAAAM,GAAA,CAAgB,MAAO,GAAI,OAAQ,EAAI,CAAA,CAAA,CAAA,CAC1C,EACF,CAEJ,ECpFMC,GAAY,CAAK,CAAE,MAAAd,EAAO,WAAAe,EAAY,SAAAC,KAAkC,CACtE,MAAAC,EAAeC,EAAAA,YAAY,IACVlB,EAAM,gBAAgB,EACvB,IAAKmB,GAAgB,CACvC,MAAMC,EAAUD,EAAY,QAC5B,aACG,KAAwB,CAAA,UAAU,eAChC,SAAQC,EAAA,IAAKC,GACRA,EAAO,cAAsB,GAE9Bd,EAAAA,IAAA,KAAA,CAAmB,UAAU,uCAC3B,WAAWc,EAAO,OAAO,UAAU,OAAQA,EAAO,WAAY,CAAA,CAAA,EADxDA,EAAO,EAEhB,CAEH,CARM,EAAAF,EAAY,EASrB,CAAA,CAEH,EAEA,EAAE,EAECG,EAAaJ,EAAAA,YAAY,IAChBlB,EAAM,YAAA,EAAc,KACrB,IAAKuB,GAAQ,CACjB,MAAAC,EAAQD,EAAI,gBAAgB,EAEhC,OAAAhB,EAAA,IAAC,KAAA,CAEC,UAAU,uGAET,SAAAiB,EAAM,IAAKC,GAEPlB,EAAAA,IAAA,KAAA,CAAiB,UAAU,uCACzB,WAAWkB,EAAK,OAAO,UAAU,KAAMA,EAAK,WAAY,CAAA,CAAA,EADlDA,EAAK,EAEd,CAEH,CAAA,EATIF,EAAI,EAUX,CAAA,CAEH,EAEA,EAAE,EAEL,GAAIR,IAAe,EAEf,OAAAR,EAAA,IAAC,WAAQ,UAAU,0BACjB,eAAC,KAAG,CAAA,UAAU,8BAA8B,SAAA,kBAAA,CAAgB,CAC9D,CAAA,EAGJ,MAAMR,EAAY,KAAK,KAAKgB,EAAaC,CAAQ,EACjD,cACG,UACC,CAAA,SAAA,CAAAT,EAAAA,IAAC,WAAQ,UAAU,gBACjB,SAACE,EAAA,KAAA,QAAA,CAAM,UAAU,2GACf,SAAA,CAACF,EAAAA,IAAA,QAAA,CAAO,WAAe,CAAA,CAAA,EACvBA,EAAAA,IAAC,QAAO,CAAA,SAAAe,EAAA,CAAa,CAAA,CAAA,CAAA,CACvB,CACF,CAAA,EACAb,EAAAA,KAAC,MAAI,CAAA,UAAU,yDACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,8BAA+B,SAAA,CAAAM,EAAW,QAAA,EAAM,EAC9DA,EAAa,IAAOR,EAAA,IAAAT,GAAA,CAAc,UAAAC,EAAsB,MAAAC,CAAc,CAAA,CAAA,CACzE,CAAA,CAAA,EACF,CAEJ,EAEa0B,GAAcZ,GC5Dda,GAAa,CACxBC,EACAZ,EACAa,EACAC,IAEOC,EAAS,CACd,SAAU,CAACC,EAAW,WAAYJ,EAAWZ,EAAUa,EAAQC,CAAO,EACtE,QAAS,SAAY,CACnB,IAAIG,EAAO,EACPL,EAAY,IACdK,EAAOL,EAAYZ,GAErB,KAAM,CAAE,KAAAkB,GAAS,MAAMC,EAA0B,CAC/C,MAAO,CACL,eAAgBnB,EAChB,UAAWiB,EACX,OAAQJ,EACR,QAASC,CAAA,CACX,CACD,EACM,OAAAI,CAAA,CACT,CACD,ECtBUE,GAAoB,CAAC,CAAE,QAAAC,KAAsC,CAClE,KAAA,CAAE,IAAAC,CAAI,EAAIC,EAAmB,EAC7BC,EAAiBC,GACjB,CAACH,EAAIG,EAAO,MAAM,GAAKA,EAAO,QAAgB,SAG/CC,EACC,CAAA,SAAAjC,EAAA,KAACD,EAAO,CAAA,QAASiC,EAAO,SACrB,SAAA,CAAAA,EAAO,OAAS,cACdhC,EAAA,KAAA,MAAA,CAAI,UAAU,8BACb,SAAA,CAAAF,MAACX,IAAU,MAAO,GAAI,OAAQ,GAAI,UAAU,8BAA8B,EACzEW,EAAA,IAAA,OAAA,CAAK,UAAU,wCAAwC,SAAU,YAAA,CAAA,CAAA,EACpE,EAEDkC,EAAO,OAAS,SACdhC,EAAA,KAAA,MAAA,CAAI,UAAU,8BACb,SAAA,CAAAF,MAACV,IAAM,MAAO,GAAI,OAAQ,GAAI,UAAU,8BAA8B,EACrEU,EAAA,IAAA,OAAA,CAAK,UAAU,wCAAwC,SAAM,QAAA,CAAA,CAAA,EAChE,EAEDkC,EAAO,OAAS,UACdhC,EAAA,KAAA,MAAA,CAAI,UAAU,8BACb,SAAA,CAAAF,MAACoC,IAAW,MAAO,GAAI,OAAQ,GAAI,UAAU,8BAA8B,EAC1EpC,EAAA,IAAA,OAAA,CAAK,UAAU,wCAAwC,SAAI,MAAA,CAAA,CAAA,EAC9D,EAEDkC,EAAO,OAAS,YACdhC,EAAA,KAAA,MAAA,CAAI,UAAU,8BACb,SAAA,CAAAF,MAACqC,GAAQ,MAAO,GAAI,OAAQ,GAAI,UAAU,8BAA8B,EACvErC,EAAA,IAAA,OAAA,CAAK,UAAU,wCAAwC,SAAQ,UAAA,CAAA,CAAA,CAClE,CAAA,CAAA,EAEJ,CAAA,EA1BqBD,GA2BvB,EAGJ,OACGC,EAAA,IAAA,UAAA,CAAQ,UAAU,8BACjB,gBAACsC,EACC,CAAA,SAAA,CAACtC,EAAAA,IAAAuC,EAAA,CAA+B,QAAO,GACrC,SAAArC,EAAAA,KAACD,GAAO,KAAK,KAAK,UAAU,8BAC1B,SAAA,CAAAD,EAAA,IAACwC,EAAI,CAAA,MAAO,GAAI,OAAQ,GAAI,EAC3BxC,EAAA,IAAA,OAAA,CAAK,UAAU,mBAAmB,SAAO,SAAA,CAAA,CAAA,EAC5C,CAAA,EAJwBD,GAK1B,EACCC,EAAA,IAAAyC,EAAA,CAAqB,SAAQX,EAAA,IAAIG,CAAa,CAAE,CAAA,CAAA,CAAA,CACnD,CACF,CAAA,CAEJ,ECjDaS,GAAe,CAAC,CAC3B,OAAQ,CAAE,SAAAC,EAAU,WAAAC,CAAW,EAC/B,UAAAC,CACF,IAAyB,CACjB,KAAA,CAAE,MAAAC,CAAM,EAAIC,EAAS,EACrB,CAACC,EAAMC,CAAO,EAAIC,EAAAA,SAAkB,EAAK,EAEzCC,EAAa,SAAY,CACzB,GAAA,CACF,MAAMC,EAAiC,CACrC,KAAM,CAAE,GAAIT,CAAS,CAAA,CACtB,EACKG,EAAA,CACJ,MAAO,UACP,YAAa,WAAWF,CAAU,kCAAA,CACnC,EACSC,EAAA,QACHQ,EAAc,CACjBA,aAAe,OACXP,EAAA,CACJ,MAAO,SACP,YAAa,8CAA8CF,CAAU,sBACrE,QAAS,aAAA,CACV,CACH,CAEJ,EAEAU,OAAAA,EAAAA,UAAU,IAAM,CACdL,EAAQ,EAAI,CACd,EAAG,EAAE,EAGFjD,EAAA,IAAAuD,GAAA,CAAY,KAAAP,EACX,SAAA9C,OAACsD,GACC,CAAA,SAAA,CAAAtD,OAACuD,GACC,CAAA,SAAA,CAAAzD,EAAAA,IAAC0D,IAAiB,SAAwB,0BAAA,CAAA,SACzCC,GAAuB,CAAA,SAAA,CAAA,gFAErBf,EAAW,GAAA,CACd,CAAA,CAAA,EACF,SACCgB,GACC,CAAA,SAAA,CAAC5D,EAAA,IAAA6D,GAAA,CAAkB,QAAShB,EAAW,SAAM,SAAA,EAC5C7C,EAAA,IAAA8D,GAAA,CAAkB,QAASX,EAAY,SAAG,KAAA,CAAA,CAAA,CAC7C,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,ECxDaY,GAAc,CACzBC,EACAC,IAEOzC,EAAS,CACd,SAAU,CAACC,EAAW,YAAauC,EAAcC,CAAW,EAC5D,QAAS,SAAY,CACnB,KAAM,CAAE,KAAAtC,GAAS,MAAMuC,EAAgC,CACrD,MAAO,CAAE,aAAAF,EAAc,YAAAC,CAAY,CAAA,CACpC,EACM,OAAAtC,CAAA,CACT,CACD,ECCUwC,GAAc,CAAC,CAAE,UAAAtB,EAAW,SAAAF,KAAiC,CACxE,KAAM,CAAE,KAAAhB,CAAK,EAAIoC,GAAYK,EAAmB,EAAGzB,CAAQ,EACrD0B,EAAcC,EAAe,EAC7B,CAACC,EAAeC,CAAgB,EAAItB,EAAAA,SAAkB,EAAK,EAC3D,CAACuB,EAAoBC,CAAqB,EAAIxB,EAAAA,SAAkB,EAAK,EACrE,CAACF,EAAMC,CAAO,EAAIC,EAAAA,SAAS,EAAK,EAChC,CAAE,aAAAyB,CAAa,EAAIC,EAAQ,EAC3B,CAAE,MAAA9B,CAAM,EAAIC,EAAS,EAErB8B,EAAe,IAAM,CACzB5B,EAAQ,EAAK,EACHJ,EAAA,CACZ,EAEAS,EAAAA,UAAU,KACRL,EAAQ,EAAI,EACNtB,GAAA,QAAQ,QAASmD,GAAM,CACzBA,EAAA,UAAU,QAASC,GAAM,CACrBA,EAAE,OAAS,4BAA8BA,EAAE,QAAU,OACvDP,EAAiB,EAAI,EAErBO,EAAE,OAAS,gDACXA,EAAE,QAAU,QAEZL,EAAsB,EAAI,CAC5B,CACD,CAAA,CACF,EACM,IAAM,CACNL,EAAY,kBAAkB,CAAE,SAAU,CAAC5C,EAAW,WAAW,EAAG,EAAE,KAAK,EAC3E4C,EAAY,kBAAkB,CAAE,SAAU,CAAC5C,EAAW,UAAU,EAAG,EAAE,KAAK,EAC1E4C,EAAY,kBAAkB,CAAE,SAAU,CAACD,EAAmB,CAAC,EAAG,EAAE,KAAK,CAChF,GAEC,CAACvB,EAAWlB,CAAI,CAAC,EAEd,MAAAqD,EAAiB,CAACC,EAAgBC,IAAiB,CACnDA,IAAS,2BACXV,EAAiBS,CAAK,EACbC,IAAS,gDAClBR,EAAsBO,CAAK,CAE/B,EAEME,EAAW,SAAY,CACvB,GAAA,CACF,MAAMC,EAAmB,CAAC,EAC1BA,EAAiB,SAAW,CAC1B,CACE,KAAM,2BACN,MAAOb,EAAc,SAAS,CAChC,EACA,CACE,KAAM,+CACN,MAAOE,EAAmB,SAAS,CAAA,CAEvC,EAEA,MAAMY,EAAgC,CACpC,KAAMD,EACN,MAAO,CAAE,YAAahB,EAAmB,EAAG,aAAczB,CAAS,CAAA,CACpE,EACKG,EAAA,CACJ,MAAO,UACP,YAAa,+BACb,QAAS,SAAA,CACV,EACY+B,EAAA,QACNxB,EAAc,CACjBA,aAAe,OACXP,EAAA,CACJ,MAAO,SACP,YAAa,yBACb,QAAS,aAAA,CACV,CACH,CAEJ,EAEMwC,EAAwB,SAAY,CACpC,GAAA,CACF,MAAMC,EAAmC,CACvC,MAAO,CAAE,YAAanB,EAAmB,EAAG,aAAczB,CAAS,CAAA,CACpE,EACKG,EAAA,CACJ,MAAO,UACP,YAAa,oCACb,QAAS,SAAA,CACV,EACY+B,EAAA,QACNxB,EAAc,CACjBA,aAAe,OACXP,EAAA,CACJ,MAAO,SACP,YAAa,4CACb,QAAS,aAAA,CACV,CACH,CAEJ,EAGE,OAAA9C,EAAAA,IAAC,UAAQ,CAAA,UAAU,MACjB,SAAAA,MAACwF,GAAO,KAAAxC,EAAY,aAAc6B,EAChC,SAAA3E,EAAAA,KAACuF,EACC,CAAA,SAAA,CAAAzF,MAAC0F,EACC,CAAA,SAAA1F,EAAA,IAAC2F,EAAY,CAAA,SAAA,UAAQ,CAAA,EACvB,EACCzF,EAAA,KAAA,OAAA,CAAK,SAAUyE,EAAaQ,CAAQ,EACnC,SAAA,CAACjF,EAAAA,KAAA,MAAA,CAAI,UAAU,oFACb,SAAA,CAAAF,MAAC,MAAI,CAAA,UAAU,MACZ,SAAA2B,GAAM,QAAQ,IAAKiE,GAClB5F,MAAC,QAAiB,SAAG4F,EAAA,aAAV7F,GAAsB,CAClC,EACH,EACAC,EAAAA,IAAC,MAAI,CAAA,UAAU,WACZ,SAAA2B,GAAM,QAAQ,IAAKiE,GAClB1F,EAAA,KAAC,MACC,CAAA,SAAA,CAAAF,EAAA,IAAC,KAAG,CAAA,UAAU,sBAAuB,SAAA4F,EAAG,YAAY,EACpD5F,EAAAA,IAAC,KAAG,CAAA,UAAU,kBAAmB,CAAA,EAChC4F,EAAG,UAAU,IAAKC,GAChB3F,OAAA,MAAA,CAAe,UAAU,iBACxB,SAAA,CAAAF,EAAA,IAAC8F,EAAA,CACC,GAAI,GAAGD,EAAQ,IAAI,UACnB,KAAMA,EAAQ,KACd,QACEA,EAAQ,OAAS,2BACbtB,EACAE,EAEN,gBAAkBsB,GAChBf,EAAe,CAAC,CAACe,EAAQ,QAAA,EAAWF,EAAQ,IAAK,CAAA,CAErD,EACA7F,EAAA,IAAC,QAAA,CACC,QAAS,GAAG6F,EAAQ,IAAI,UACxB,UAAU,mCAEV,SAAC7F,EAAAA,IAAA,OAAA,CAAK,UAAU,OAAQ,WAAQ,WAAY,CAAA,CAAA,CAC9C,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,oBAAqB,WAAQ,WAAY,CAAA,CAAA,CAnB9C,EAAAD,EAAA,CAoBV,CACD,CAAA,GAzBOA,EA0BV,CAAA,CACD,CACH,CAAA,CAAA,EACF,EAEAG,EAAAA,KAAC8F,EAAa,CAAA,UAAU,OACtB,SAAA,CAAAhG,EAAA,IAACC,EAAA,CACC,QAAS,MAAOgG,GAAsC,CACpDA,EAAE,eAAe,EACjB,MAAMX,EAAsB,CAC9B,EACD,SAAA,kBAAA,CAED,EACAtF,EAAA,IAACC,EAAA,CACC,QAAUgG,GAAsC,CAC9CA,EAAE,eAAe,EACJpB,EAAA,CACf,EACD,SAAA,QAAA,CAED,EACC7E,EAAA,IAAAC,EAAA,CAAO,KAAK,SAAS,SAAI,MAAA,CAAA,CAAA,CAC5B,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,CAEJ,ECvKaiG,GAAa,CAAC,CAAE,UAAAC,EAAW,SAAAxD,EAAU,UAAAE,KAAiC,CACjF,MAAMwB,EAAcC,EAAe,EAC7B,CAACtB,EAAMC,CAAO,EAAIC,EAAAA,SAAS,EAAK,EAEhC,CAACkD,EAAYC,CAAa,EAAInD,EAAAA,SAAS,EAAK,EAC5C,CAAE,MAAAJ,CAAM,EAAIC,EAAS,EACrB,CAAE,aAAA4B,EAAc,SAAA2B,CAAS,EAAI1B,EAAQ,EAE3CtB,EAAAA,UAAU,IAAM,CAET6C,GAAW,iBAA2C,MACzDE,EAAc,EAAI,GAEnB,CAACF,EAAYA,GAAW,iBAA2C,IAAI,CAAC,EAErE,MAAAhB,EAAW,MAAOoB,GAAiB,CACvC,MAAMC,EAASD,EAEX,GAAA,CACF,MAAME,EAA8B,CAClC,KAAMD,EACN,KAAM,CAAE,GAAI7D,CAAS,CAAA,CACtB,EASKG,EAAA,CACJ,MAAO,UACP,YAAa,0CACb,QAAS,SAAA,CACV,EACDG,EAAQ,EAAK,QACNI,EAAc,CACjBA,aAAe,OACXP,EAAA,CACJ,MAAO,SACP,YAAa,oCACb,QAAS,aAAA,CACV,CACH,CAEJ,EAEM+B,EAAe,IAAM,CACzB5B,EAAQ,EAAK,EACHJ,EAAA,CACZ,EAEAS,OAAAA,EAAAA,UAAU,KACRL,EAAQ,EAAI,EACL,IAAM,CACNoB,EAAY,kBAAkB,CAAE,SAAU,CAAC5C,EAAW,UAAU,EAAG,CAC1E,GAEC,EAAE,QAGF+D,EAAO,CAAA,KAAAxC,EAAY,aAAc6B,EAChC,gBAACY,EACC,CAAA,SAAA,CAAAzF,MAAC0F,EACC,CAAA,SAAA1F,EAAA,IAAC2F,EAAY,CAAA,SAAA,sBAAoB,CAAA,EACnC,EACCzF,EAAA,KAAA,OAAA,CAAK,SAAUyE,EAAaQ,CAAQ,EACnC,SAAA,CAACnF,EAAAA,IAAA,UAAA,CAAQ,UAAU,0BACjB,SAAAA,EAAA,IAAC0G,EAAA,CACC,SAAQ,GACR,YAAY,cACZ,aAAcP,EAAU,MAAQ,GAC/B,GAAGG,EAAS,MAAM,CAAA,CAAA,EAEvB,EACCpG,EAAA,KAAA,MAAA,CAAI,UAAWyG,EAAW,uCAAuC,EAChE,SAAA,CAAA3G,EAAA,IAAC8F,EAAA,CACC,GAAG,aACH,KAAK,aACL,eAAc,GACd,QAASM,EACT,gBAAkBL,GAAYM,EAAc,CAAC,CAACN,EAAQ,QAAS,CAAA,CAAA,CACjE,QACC,QAAM,CAAA,QAAQ,aAAa,UAAU,mCAAmC,SAEzE,aAAA,CAAA,CAAA,EACF,EACCK,SACE,MAAI,CAAA,UAAU,iCACb,SAACpG,MAAA,UAAA,CAAQ,UAAU,iCACjB,SAAAA,EAAA,IAAC0G,GAAM,SAAQ,GAAE,GAAGJ,EAAS,MAAM,EAAG,YAAY,YAAY,EAChE,CACF,CAAA,EAEFpG,EAAAA,KAAC8F,EAAa,CAAA,UAAU,OACtB,SAAA,CAAAhG,EAAA,IAACC,EAAA,CACC,QAAUgG,GAAsC,CAC9CA,EAAE,eAAe,EACJpB,EAAA,CACf,EACD,SAAA,QAAA,CAED,EACC7E,EAAA,IAAAC,EAAA,CAAO,KAAK,SAAS,SAAI,MAAA,CAAA,CAAA,CAC5B,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,EC1Ha2G,GAAa,IAAM,CACxB,KAAA,CAAE,MAAA9D,CAAM,EAAIC,EAAS,EACrBsB,EAAcC,EAAe,EAC7B,CAACuC,EAAWC,CAAY,EAAI5D,WAA6B,EACzD,CAAC6D,EAAoBC,CAAqB,EAAI9D,WAI1C,EAEJ,CAAC,CAAE,UAAA7B,EAAW,SAAAZ,CAAY,EAAAwG,CAAa,EAAI/D,EAAAA,SAA0B,CACzE,UAAW,EACX,SAAU,EAAA,CACX,EACK,CAAE,UAAAgE,EAAW,KAAAvF,EAAM,QAAAwF,CAAA,EAAY/F,GAAWC,EAAWZ,EAAUoG,CAAS,EACxEO,EAAaC,EAAA,QACjB,KAAO,CACL,UAAAhG,EACA,SAAAZ,CAAA,GAGF,CAACY,EAAWZ,EAAUqC,CAAK,CAC7B,EAEMwE,EAAyCD,EAAA,QAC7C,IAAM,CACJ,CACE,OAAQ,oBACR,QAAS,CACP,CACE,YAAa,UACb,OAAQ,UACR,KAAOE,GAEHvH,EAAA,IAAC6B,GAAA,CACC,QAAS,CACP,CACE,KAAM,WACN,OAAQ,6CACR,SAAU,IAAM,CACQmF,EAAA,CACpB,WAAY,kBACZ,SAAUO,EAAK,IAAI,SAAS,GAC5B,UAAWA,EAAK,IAAI,QAAA,CACrB,CAAA,CAEL,EACA,CACE,KAAM,SACN,OAAQ,qCACR,SAAU,IAAM,CACQP,EAAA,CACpB,WAAY,OACZ,SAAUO,EAAK,IAAI,SAAS,GAC5B,UAAWA,EAAK,IAAI,QAAA,CACrB,CAAA,CAEL,EACA,CACE,KAAM,QACN,OAAQ,qCACR,SAAU,IAAM,CACQP,EAAA,CACpB,SAAUO,EAAK,IAAI,SAAS,GAC5B,UAAWA,EAAK,IAAI,SACpB,WAAY,QAAA,CACb,CAAA,CACH,CACF,CACF,CACF,CAGN,EACA,CACE,YAAa,OACb,OAAQ,cACR,KAAOA,GAASA,EAAK,SAAS,CAAA,CAChC,CACF,CAEJ,EAEA,CAACR,CAAkB,CACrB,EAEMS,EAAuBvC,GAAkB,CAC7C6B,EAAa7B,CAAK,CACpB,EAEMxF,EAAQgI,GAAc,CAC1B,KAAM9F,GAAM,OAAS,CAAC,EACtB,UAAWA,GAAM,YAAc,GAC/B,MAAO,CACL,WAAAyF,CACF,EACA,QAASE,EACT,gBAAiBI,GAAgB,EACjC,mBAAoBT,EACpB,iBAAkB,EAAA,CACnB,EAEG,OAAAC,EAAkBlH,MAAC2H,GAAO,CAAA,CAAA,EAC1BR,EAAgBnH,MAAC4H,GAAM,CAAA,CAAA,EAItB1H,EAAA,KAAA2H,WAAA,CAAA,SAAA,CAAAd,GAAoB,aAAe,QAClC/G,EAAA,IAACkG,GAAA,CACC,UAAWa,EAAmB,UAC9B,SAAUA,EAAmB,SAC7B,UAAW,IAAM,CACV1C,EAAY,kBAAkB,CAAE,SAAU,CAAC5C,EAAW,UAAU,EAAG,EACxEuF,EAAsB,IAAI,CAAA,CAC5B,CACF,EAEDD,GAAoB,aAAe,UAClC/G,EAAA,IAAC0C,GAAA,CACC,OAAQ,CACN,SAAUqE,EAAmB,SAC7B,WAAYA,EAAmB,UAAU,IAC3C,EACA,UAAW,IAAM,CACV1C,EAAY,kBAAkB,CAAE,SAAU,CAAC5C,EAAW,UAAU,EAAG,EACxEuF,EAAsB,IAAI,CAAA,CAC5B,CACF,EAEDD,GAAoB,aAAe,mBAClC/G,EAAA,IAACmE,GAAA,CACC,UAAW,IAAM6C,EAAsB,IAAI,EAC3C,SAAUD,EAAmB,QAAA,CAC/B,QAEDe,GAAO,CAAA,SAAUN,EAAqB,MAAOX,GAAa,GAAI,EAC/D7G,EAAA,IAACmB,GAAA,CACC,MAAA1B,EACA,WAAYkC,GAAM,YAAc,EAChC,SAAAlB,CAAA,CAAA,CACF,EACF,CAEJ,EC3JA,SAAwBsH,IAAiB,CACvC,cACGC,EACC,CAAA,SAAA,CAAChI,EAAAA,IAAAiI,GAAA,CAAK,MAAM,WAAY,CAAA,QAEvBrB,GAAW,CAAA,CAAA,CAAA,EACd,CAEJ", "x_google_ignoreList": [0, 1, 2, 3, 4]}