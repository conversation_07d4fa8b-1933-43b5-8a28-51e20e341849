using Imip.JettyApproval.JettyRequests;
using Imip.JettyApproval.Mapping.Mappers;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.JettyApproval.JettyRequests;

/// <summary>
/// Application service for JettyRequestItem entity
/// </summary>
public class JettyRequestItemAppService :
    CrudAppService<JettyRequestItem, JettyRequestItemDto, Guid, PagedAndSortedResultRequestDto, CreateUpdateJettyRequestItemDto, CreateUpdateJettyRequestItemDto>,
    IJettyRequestItemAppService
{
    private readonly IJettyRequestItemRepository _jettyRequestItemRepository;
    private readonly JettyRequestItemMapper _mapper;
    private readonly ILogger<JettyRequestItemAppService> _logger;

    public JettyRequestItemAppService(
        IJettyRequestItemRepository jettyRequestItemRepository,
        JettyRequestItemMapper mapper,
        ILogger<JettyRequestItemAppService> logger)
        : base(jettyRequestItemRepository)
    {
        _jettyRequestItemRepository = jettyRequestItemRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public override async Task<JettyRequestItemDto> CreateAsync(CreateUpdateJettyRequestItemDto input)
    {
        await CheckCreatePolicyAsync();

        var entity = _mapper.CreateEntityWithId(input, Guid.NewGuid());

        await _jettyRequestItemRepository.InsertAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task<JettyRequestItemDto> UpdateAsync(Guid id, CreateUpdateJettyRequestItemDto input)
    {
        await CheckUpdatePolicyAsync();

        var entity = await _jettyRequestItemRepository.GetAsync(id);

        _mapper.MapToEntity(input, entity);

        await _jettyRequestItemRepository.UpdateAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task DeleteAsync(Guid id)
    {
        await CheckDeletePolicyAsync();

        var entity = await _jettyRequestItemRepository.GetAsync(id);

        await _jettyRequestItemRepository.DeleteAsync(entity, autoSave: true);
    }

    public override async Task<JettyRequestItemDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var entity = await _jettyRequestItemRepository.GetAsync(id);
        return _mapper.MapToDto(entity);
    }

    public override async Task<PagedResultDto<JettyRequestItemDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        await CheckGetListPolicyAsync();

        var queryable = await _jettyRequestItemRepository.GetQueryableAsync();

        // Apply sorting
        if (!string.IsNullOrWhiteSpace(input.Sorting))
        {
            queryable = queryable.OrderBy(input.Sorting);
        }
        else
        {
            queryable = queryable.OrderByDescending(x => x.CreationTime);
        }

        var totalCount = await AsyncExecuter.CountAsync(queryable);

        var entities = await AsyncExecuter.ToListAsync(
            queryable.PageBy(input.SkipCount, input.MaxResultCount)
        );

        var dtos = entities.Select(_mapper.MapToDto).ToList();

        return new PagedResultDto<JettyRequestItemDto>(totalCount, dtos);
    }

    public virtual async Task<List<JettyRequestItemDto>> GetByJettyRequestIdAsync(Guid jettyRequestId)
    {
        var entities = await _jettyRequestItemRepository.GetByJettyRequestIdAsync(jettyRequestId);
        return _mapper.MapToDtoList(entities);
    }

    public virtual async Task<List<JettyRequestItemDto>> GetByTenantNameAsync(string tenantName)
    {
        var entities = await _jettyRequestItemRepository.GetByTenantNameAsync(tenantName);
        return _mapper.MapToDtoList(entities);
    }

    public virtual async Task<List<JettyRequestItemDto>> GetByStatusAsync(string status)
    {
        var entities = await _jettyRequestItemRepository.GetByStatusAsync(status);
        return _mapper.MapToDtoList(entities);
    }

    public virtual async Task<List<JettyRequestItemDto>> GetByItemNameAsync(string itemName)
    {
        var entities = await _jettyRequestItemRepository.GetByItemNameAsync(itemName);
        return _mapper.MapToDtoList(entities);
    }
}