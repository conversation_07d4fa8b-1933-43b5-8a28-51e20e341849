{"version": 3, "file": "checkbox-CayrCcBd.js", "sources": ["../../../../../frontend/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\r\nimport { CheckIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Checkbox({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\r\n  return (\r\n    <CheckboxPrimitive.Root\r\n      data-slot=\"checkbox\"\r\n      className={cn(\r\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <CheckboxPrimitive.Indicator\r\n        data-slot=\"checkbox-indicator\"\r\n        className=\"flex items-center justify-center text-current transition-none\"\r\n      >\r\n        <CheckIcon className=\"size-3.5\" />\r\n      </CheckboxPrimitive.Indicator>\r\n    </CheckboxPrimitive.Root>\r\n  )\r\n}\r\n\r\nexport { Checkbox }\r\n"], "names": ["Checkbox", "className", "props", "jsx", "CheckboxPrimitive.Root", "cn", "CheckboxPrimitive.Indicator", "CheckIcon"], "mappings": "4IAQA,SAASA,EAAS,CAChB,UAAAC,EACA,GAAGC,CACL,EAAwD,CAEpD,OAAAC,EAAA,IAACC,EAAA,CACC,YAAU,WACV,UAAWC,EACT,8eACAJ,CACF,EACC,GAAGC,EAEJ,SAAAC,EAAA,IAACG,EAAA,CACC,YAAU,qBACV,UAAU,gEAEV,SAAAH,EAAAA,IAACI,EAAU,CAAA,UAAU,UAAW,CAAA,CAAA,CAAA,CAClC,CACF,CAEJ"}