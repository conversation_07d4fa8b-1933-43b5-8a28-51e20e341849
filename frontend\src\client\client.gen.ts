// This file is auto-generated by @hey-api/openapi-ts

import type { ClientOptions } from './types.gen';
import { type Config, type ClientOptions as DefaultClientOptions, createClient, createConfig } from '@hey-api/client-fetch';

/**
 * The `createClientConfig()` function will be called on client initialization
 * and the returned object will become the client's initial configuration.
 *
 * You may want to initialize your client this way instead of calling
 * `setConfig()`. This is useful for example if you're using Next.js
 * to ensure your client always has the correct values.
 */
export type CreateClientConfig<T extends DefaultClientOptions = ClientOptions> = (override?: Config<DefaultClientOptions & T>) => Config<Required<DefaultClientOptions> & T>;

// Custom fetch function with desired options
const customFetch = (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
  const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
  return fetch(input, {
    credentials: 'include',
    duplex: 'half',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRF-TOKEN': csrfToken || '',
      'X-Requested-With': 'XMLHttpRequest'
    },
    ...init, // Spread any additional options passed by the client
  } as RequestInit);
};


export const client = createClient(createConfig<ClientOptions>({
  throwOnError: true,
  baseUrl: "/",
  fetch: customFetch
})); 