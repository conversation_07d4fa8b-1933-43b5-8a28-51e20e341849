{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"build": "tsc -b && vite build", "dev": "vite", "gen-api": "curl -k http://localhost:5000/swagger/v1/swagger.json -o swagger.json && npx @hey-api/openapi-ts -i swagger.json -o src/client -c @hey-api/client-fetch && pnpm openapi-ts", "lint": "eslint .", "preview": "vite preview", "openapi-ts": "openapi-ts"}, "dependencies": {"@abp/signalr": "^9.2.0", "@atlaskit/pragmatic-drag-and-drop": "^1.7.2", "@atlaskit/pragmatic-drag-and-drop-flourish": "^2.0.3", "@atlaskit/pragmatic-drag-and-drop-hitbox": "^1.1.0", "@atlaskit/pragmatic-drag-and-drop-live-region": "^1.3.0", "@atlaskit/pragmatic-drag-and-drop-react-drop-indicator": "^3.2.1", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@handsontable/react-wrapper": "^15.3.0", "@hey-api/client-fetch": "^0.12.0", "@hey-api/openapi-ts": "^0.71.0", "@hookform/resolvers": "^5.0.1", "@internationalized/date": "^3.8.2", "@microsoft/signalr": "^8.0.7", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@remixicon/react": "^4.6.0", "@tabler/icons-react": "^3.34.0", "@tailwindcss/vite": "^4.1.8", "@tanstack/react-query": "^5.80.6", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "handsontable": "^15.3.0", "input-otp": "^1.4.2", "js-cookie": "^3.0.5", "kbar": "0.1.0-beta.45", "lucide-react": "^0.513.0", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "openid-client": "^6.5.0", "react": "^19.1.0", "react-aria-components": "^1.10.0", "react-day-picker": "8.10.1", "react-dom": "^19.1.0", "react-hook-form": "^7.57.0", "react-resizable-panels": "^3.0.2", "recharts": "^2.15.3", "sonner": "^2.0.5", "tailwind-merge": "^3.3.0", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.1.8", "tiny-invariant": "^1.3.3", "use-debounce": "^10.0.5", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.25.55"}, "devDependencies": {"@eslint/js": "^9.25.0", "@inertiajs/react": "^2.0.11", "@types/js-cookie": "^3.0.6", "@types/node": "^22.15.30", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.5.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "laravel-vite-plugin": "^1.3.0", "rollup-plugin-visualizer": "^6.0.3", "tw-animate-css": "^1.3.4", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vite-plugin-compression2": "^2.0.1"}}