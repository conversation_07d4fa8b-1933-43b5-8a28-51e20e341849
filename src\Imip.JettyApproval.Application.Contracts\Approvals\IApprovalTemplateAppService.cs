using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Imip.JettyApproval.Approvals.ApprovalTemplates;

namespace Imip.JettyApproval.Approvals;

/// <summary>
/// Application service interface for ApprovalTemplate entity
/// </summary>
public interface IApprovalTemplateAppService :
    ICrudAppService<
        ApprovalTemplateDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateApprovalTemplateDto,
        CreateUpdateApprovalTemplateDto>
{
}