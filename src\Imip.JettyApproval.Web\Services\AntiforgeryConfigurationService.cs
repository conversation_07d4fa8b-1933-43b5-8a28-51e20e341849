using System;
using Microsoft.AspNetCore.Antiforgery;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Volo.Abp.AspNetCore.Mvc.AntiForgery;

namespace Imip.JettyApproval.Web.Services;

public static class AntiforgeryConfigurationService
{
    /// <summary>
    /// Configures antiforgery services with environment-specific security settings
    /// </summary>
    public static void ConfigureAntiforgery(IServiceCollection services, IConfiguration configuration, IWebHostEnvironment hostingEnvironment)
    {
        ConfigureAbpAntiforgeryOptions(services, hostingEnvironment);
        ConfigureAspNetCoreAntiforgeryOptions(services, configuration, hostingEnvironment);
    }

    private static void ConfigureAbpAntiforgeryOptions(IServiceCollection services, IWebHostEnvironment hostingEnvironment)
    {
        services.Configure<AbpAntiForgeryOptions>(options =>
        {
            // Exclude API endpoints from antiforgery validation since they use Bearer token authentication
            options.AutoValidateFilter = type =>
                !type.FullName!.StartsWith("SilkierQuartz.Controllers") &&
                !type.FullName!.StartsWith("Imip.JettyApproval.Controllers") &&
                !type.FullName!.StartsWith("Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations");

            options.TokenCookie.Expiration = TimeSpan.FromDays(365);

            // In development, allow non-HTTPS for easier debugging
            if (hostingEnvironment.IsDevelopment())
            {
                options.TokenCookie.SecurePolicy = CookieSecurePolicy.SameAsRequest;
            }
        });
    }

    private static void ConfigureAspNetCoreAntiforgeryOptions(IServiceCollection services, IConfiguration configuration, IWebHostEnvironment hostingEnvironment)
    {
        services.Configure<AntiforgeryOptions>(options =>
        {
            options.Cookie.Name = ".Imip.JettyApproval.Antiforgery";
            options.Cookie.HttpOnly = true;

            var requireHttps = !hostingEnvironment.IsDevelopment() &&
                              configuration.GetValue<bool>("AuthServer:RequireHttpsMetadata", true);

            options.Cookie.SecurePolicy = requireHttps ? CookieSecurePolicy.Always : CookieSecurePolicy.SameAsRequest;
            options.Cookie.SameSite = SameSiteMode.Lax;
            options.Cookie.MaxAge = TimeSpan.FromHours(2); // Longer lifetime for development
            options.FormFieldName = "__RequestVerificationToken";
            options.HeaderName = "X-CSRF-TOKEN";
        });
    }
}
