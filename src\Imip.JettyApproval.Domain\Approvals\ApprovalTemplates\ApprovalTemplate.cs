using System;
using System.Collections.Generic;
using Volo.Abp.Domain.Entities.Auditing;
using Imip.JettyApproval.Approvals.ApprovalApprovers;
using Imip.JettyApproval.Approvals.ApprovalCriterias;
using Imip.JettyApproval.Approvals.ApprovalStages;

namespace Imip.JettyApproval.Approvals.ApprovalTemplates;

/// <summary>
/// Entity for storing approval template information
/// </summary>
public class ApprovalTemplate : FullAuditedAggregateRoot<Guid>
{
    /// <summary>
    /// Name of the approval template
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Description of the approval template
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Code for the approval template
    /// </summary>
    public string? Code { get; set; }

    /// <summary>
    /// Navigation property for approval approvers
    /// </summary>
    public virtual ICollection<ApprovalApprover> Approvers { get; set; }

    /// <summary>
    /// Navigation property for approval criterias
    /// </summary>
    public virtual ICollection<ApprovalCriteria> Criterias { get; set; }

    /// <summary>
    /// Navigation property for approval stages
    /// </summary>
    public virtual ICollection<ApprovalStage> Stages { get; set; }

    /// <summary>
    /// Default constructor for EF Core
    /// </summary>
    protected ApprovalTemplate()
    {
        Approvers = new List<ApprovalApprover>();
        Criterias = new List<ApprovalCriteria>();
        Stages = new List<ApprovalStage>();
    }

    /// <summary>
    /// Creates a new ApprovalTemplate
    /// </summary>
    public ApprovalTemplate(
        Guid id,
        string? name = null,
        string? description = null,
        string? code = null)
        : base(id)
    {
        Name = name;
        Description = description;
        Code = code;
        Approvers = new List<ApprovalApprover>();
        Criterias = new List<ApprovalCriteria>();
        Stages = new List<ApprovalStage>();
    }

    /// <summary>
    /// Creates a new ApprovalTemplate without ID (for mapping from DTOs)
    /// </summary>
    public ApprovalTemplate(
        string? name = null,
        string? description = null,
        string? code = null)
    {
        Name = name;
        Description = description;
        Code = code;
        Approvers = new List<ApprovalApprover>();
        Criterias = new List<ApprovalCriteria>();
        Stages = new List<ApprovalStage>();
    }
}
