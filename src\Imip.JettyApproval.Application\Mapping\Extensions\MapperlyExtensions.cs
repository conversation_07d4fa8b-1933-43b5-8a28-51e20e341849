using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.Application.Dtos;

namespace Imip.JettyApproval.Mapping.Extensions;

public static class MapperlyExtensions
{
    public static IServiceCollection AddMapperlyProfiles(this IServiceCollection services, params Type[] profileTypes)
    {
        foreach (var profileType in profileTypes)
        {
            services.AddTransient(profileType);
        }
        return services;
    }

    // Pagination support
    public static PagedResultDto<TDestination> MapPagedResult<TSource, TDestination>(
        this ICollection<TDestination> mappedItems,
        long totalCount)
    {
        return new PagedResultDto<TDestination>(totalCount, mappedItems.ToList());
    }

    // Queryable projection (similar to AutoMapper's ProjectTo)
    public static IQueryable<TDestination> ProjectTo<TSource, TDestination>(
        this IQueryable<TSource> queryable,
        Func<TSource, TDestination> mapper)
    {
        return queryable.Select(x => mapper(x));
    }

    // Filtering support
    public static IQueryable<T> WhereIf<T>(this IQueryable<T> query, bool condition, string predicate, params object[] args)
    {
        return condition ? query.Where(predicate, args) : query;
    }

    // Sorting support
    public static IQueryable<T> OrderByIf<T>(this IQueryable<T> query, bool condition, string sorting)
    {
        return condition && !string.IsNullOrWhiteSpace(sorting)
            ? query.OrderBy(sorting)
            : query;
    }
}
