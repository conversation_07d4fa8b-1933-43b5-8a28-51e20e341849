using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace Imip.JettyApproval.Approvals.ApprovalCriterias;

/// <summary>
/// Repository interface for ApprovalCriteria entity
/// </summary>
public interface IApprovalCriteriaRepository : IRepository<ApprovalCriteria, Guid>
{
    /// <summary>
    /// Gets criteria by approval template ID
    /// </summary>
    Task<List<ApprovalCriteria>> GetByApprovalIdAsync(Guid approvalId);

    /// <summary>
    /// Gets criteria by document type
    /// </summary>
    Task<List<ApprovalCriteria>> GetByDocumentTypeAsync(string documentType);
}