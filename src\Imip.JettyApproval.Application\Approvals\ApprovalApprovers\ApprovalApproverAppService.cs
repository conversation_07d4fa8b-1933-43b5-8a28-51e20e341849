using Imip.JettyApproval.Approvals.ApprovalApprovers;
using Imip.JettyApproval.Mapping.Mappers;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.JettyApproval.Approvals.ApprovalApprovers;

/// <summary>
/// Application service for ApprovalApprover entity
/// </summary>
public class ApprovalApproverAppService :
    CrudAppService<ApprovalApprover, ApprovalApproverDto, Guid, PagedAndSortedResultRequestDto, CreateUpdateApprovalApproverDto, CreateUpdateApprovalApproverDto>,
    IApprovalApproverAppService
{
    private readonly IApprovalApproverRepository _approvalApproverRepository;
    private readonly ApprovalApproverMapper _mapper;
    private readonly ILogger<ApprovalApproverAppService> _logger;

    public ApprovalApproverAppService(
        IApprovalApproverRepository approvalApproverRepository,
        ApprovalApproverMapper mapper,
        ILogger<ApprovalApproverAppService> logger)
        : base(approvalApproverRepository)
    {
        _approvalApproverRepository = approvalApproverRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public override async Task<ApprovalApproverDto> CreateAsync(CreateUpdateApprovalApproverDto input)
    {
        await CheckCreatePolicyAsync();

        var entity = _mapper.CreateEntityWithId(input, Guid.NewGuid());

        await _approvalApproverRepository.InsertAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task<ApprovalApproverDto> UpdateAsync(Guid id, CreateUpdateApprovalApproverDto input)
    {
        await CheckUpdatePolicyAsync();

        var entity = await _approvalApproverRepository.GetAsync(id);

        _mapper.MapToEntity(input, entity);

        await _approvalApproverRepository.UpdateAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task DeleteAsync(Guid id)
    {
        await CheckDeletePolicyAsync();

        var entity = await _approvalApproverRepository.GetAsync(id);

        await _approvalApproverRepository.DeleteAsync(entity, autoSave: true);
    }

    public override async Task<ApprovalApproverDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var entity = await _approvalApproverRepository.GetAsync(id);
        return _mapper.MapToDto(entity);
    }

    public override async Task<PagedResultDto<ApprovalApproverDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        await CheckGetListPolicyAsync();

        var queryable = await _approvalApproverRepository.GetQueryableAsync();

        // Apply sorting
        if (!string.IsNullOrWhiteSpace(input.Sorting))
        {
            queryable = queryable.OrderBy(input.Sorting);
        }
        else
        {
            queryable = queryable.OrderByDescending(x => x.CreationTime);
        }

        var totalCount = await AsyncExecuter.CountAsync(queryable);

        var entities = await AsyncExecuter.ToListAsync(
            queryable.PageBy(input.SkipCount, input.MaxResultCount)
        );

        var dtos = entities.Select(_mapper.MapToDto).ToList();

        return new PagedResultDto<ApprovalApproverDto>(totalCount, dtos);
    }
}