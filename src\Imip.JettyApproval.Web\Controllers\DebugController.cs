using System;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.Identity;
using Volo.Abp.Security.Claims;
using IdentityUser = Volo.Abp.Identity.IdentityUser;

namespace Imip.JettyApproval.Web.Controllers;

[Authorize]
public class DebugController : AbpController
{
    private readonly ICurrentPrincipalAccessor _currentPrincipalAccessor;
    private readonly IdentityUserManager _userManager;
    private readonly IMemoryCache _memoryCache;
    private readonly ILogger<DebugController> _logger;

    public DebugController(
        ICurrentPrincipalAccessor currentPrincipalAccessor,
        IdentityUserManager userManager,
        IMemoryCache memoryCache,
        ILogger<DebugController> logger)
    {
        _currentPrincipalAccessor = currentPrincipalAccessor;
        _userManager = userManager;
        _memoryCache = memoryCache;
        _logger = logger;
    }

    [HttpGet("/api/debug/current-user")]
    public async Task<IActionResult> GetCurrentUserInfo()
    {
        try
        {
            var principal = _currentPrincipalAccessor.Principal;
            if (principal?.Identity?.IsAuthenticated != true)
            {
                return Unauthorized();
            }

            var userId = principal.FindFirst(AbpClaimTypes.UserId)?.Value;
            if (string.IsNullOrEmpty(userId) || !Guid.TryParse(userId, out var userGuid))
            {
                return BadRequest("User ID not found in claims");
            }

            // Get user from database
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                return NotFound("User not found in database");
            }

            // Get all claims
            var claims = await _userManager.GetClaimsAsync(user);

            var result = new
            {
                UserId = user.Id,
                UserName = user.UserName,
                Name = user.Name,
                Surname = user.Surname,
                Email = user.Email,
                IsActive = user.IsActive,
                CreationTime = user.CreationTime,
                LastModificationTime = user.LastModificationTime,
                Claims = claims.Select(c => new { Type = c.Type, Value = c.Value }).ToList(),
                PrincipalClaims = principal.Claims.Select(c => new { Type = c.Type, Value = c.Value }).ToList()
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting current user info");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("/api/debug/clear-user-cache")]
    public IActionResult ClearUserCache()
    {
        try
        {
            var principal = _currentPrincipalAccessor.Principal;
            if (principal?.Identity?.IsAuthenticated != true)
            {
                return Unauthorized();
            }

            var userId = principal.FindFirst(AbpClaimTypes.UserId)?.Value;
            if (string.IsNullOrEmpty(userId) || !Guid.TryParse(userId, out var userGuid))
            {
                return BadRequest("User ID not found in claims");
            }

            // Clear user-related caches
            var cacheKeys = new[]
            {
                $"UserSync:User:{userGuid}",
                $"UserSync:Hash:{userGuid}",
                $"permission_{userGuid}_*",
                $"jwt_token_info_*"
            };

            var clearedCount = 0;
            foreach (var key in cacheKeys)
            {
                // Note: This is a simplified cache clearing. In a real implementation,
                // you might need to iterate through all cache keys that match the pattern
                if (_memoryCache.TryGetValue(key, out _))
                {
                    _memoryCache.Remove(key);
                    clearedCount++;
                }
            }

            _logger.LogInformation("Cleared {Count} cache entries for user {UserId}", clearedCount, userGuid);

            return Ok(new { Message = $"Cleared {clearedCount} cache entries", UserId = userGuid });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing user cache");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("/api/debug/update-user-name")]
    public async Task<IActionResult> UpdateUserName([FromBody] UpdateUserNameRequest request)
    {
        try
        {
            var principal = _currentPrincipalAccessor.Principal;
            if (principal?.Identity?.IsAuthenticated != true)
            {
                return Unauthorized();
            }

            var userId = principal.FindFirst(AbpClaimTypes.UserId)?.Value;
            if (string.IsNullOrEmpty(userId) || !Guid.TryParse(userId, out var userGuid))
            {
                return BadRequest("User ID not found in claims");
            }

            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                return NotFound("User not found in database");
            }

            var oldName = user.Name;
            user.Name = request.Name;

            var result = await _userManager.UpdateAsync(user);
            if (result.Succeeded)
            {
                _logger.LogInformation("Updated user name from '{OldName}' to '{NewName}' for user {UserId}",
                    oldName, request.Name, userGuid);

                // Clear user cache
                var cacheKey = $"UserSync:User:{userGuid}";
                _memoryCache.Remove(cacheKey);

                return Ok(new
                {
                    Message = "User name updated successfully",
                    OldName = oldName,
                    NewName = request.Name,
                    UserId = userGuid
                });
            }
            else
            {
                var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                return BadRequest($"Failed to update user name: {errors}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user name");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("/api/debug/force-user-sync")]
    public async Task<IActionResult> ForceUserSync()
    {
        try
        {
            var principal = _currentPrincipalAccessor.Principal;
            if (principal?.Identity?.IsAuthenticated != true)
            {
                return Unauthorized();
            }

            var userId = principal.FindFirst(AbpClaimTypes.UserId)?.Value;
            if (string.IsNullOrEmpty(userId) || !Guid.TryParse(userId, out var userGuid))
            {
                return BadRequest("User ID not found in claims");
            }

            // Clear all user-related caches
            var cacheKey = $"UserSync:User:{userGuid}";
            _memoryCache.Remove(cacheKey);

            // Get user from database to verify current state
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                return NotFound("User not found in database");
            }

            // Log current user state
            _logger.LogInformation("Current user state - Name: {Name}, UserName: {UserName}, Email: {Email}",
                user.Name, user.UserName, user.Email);

            // Extract claims from current principal to see what should be used
            var givenName = principal.FindFirst("given_name")?.Value;
            var name = principal.FindFirst("name")?.Value;
            var preferredUsername = principal.FindFirst("preferred_username")?.Value;

            _logger.LogInformation("Claims from principal - given_name: {GivenName}, name: {Name}, preferred_username: {PreferredUsername}",
                givenName, name, preferredUsername);

            return Ok(new
            {
                Message = "User cache cleared and state logged",
                UserId = userGuid,
                CurrentName = user.Name,
                CurrentUserName = user.UserName,
                GivenNameClaim = givenName,
                NameClaim = name,
                PreferredUsernameClaim = preferredUsername
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error forcing user sync");
            return StatusCode(500, "Internal server error");
        }
    }
}

public class UpdateUserNameRequest
{
    public string Name { get; set; } = string.Empty;
}