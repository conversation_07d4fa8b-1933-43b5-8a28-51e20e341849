{"version": 3, "file": "dialog-DAr_Mtxm.js", "sources": ["../../../../../frontend/src/components/ui/dialog.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst dialogContentVariants = cva(\r\n  \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200\",\r\n  {\r\n    variants: {\r\n      size: {\r\n        sm: \"sm:max-w-sm\",\r\n        md: \"sm:max-w-md\",\r\n        lg: \"sm:max-w-lg\",\r\n        xl: \"sm:max-w-xl\",\r\n        \"2xl\": \"sm:max-w-2xl\",\r\n        \"3xl\": \"sm:max-w-3xl\",\r\n        \"4xl\": \"sm:max-w-4xl\",\r\n        \"5xl\": \"sm:max-w-5xl\",\r\n        \"6xl\": \"sm:max-w-6xl\",\r\n        \"7xl\": \"sm:max-w-7xl\",\r\n        full: \"sm:max-w-full\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      size: \"lg\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Dialog({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\r\n}\r\n\r\nfunction DialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\r\n}\r\n\r\nfunction DialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\r\n}\r\n\r\nfunction DialogClose({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot=\"dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  showCloseButton = true,\r\n  size,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Content> & \r\n  VariantProps<typeof dialogContentVariants> & {\r\n  showCloseButton?: boolean\r\n}) {\r\n  return (\r\n    <DialogPortal data-slot=\"dialog-portal\">\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot=\"dialog-content\"\r\n        className={cn(dialogContentVariants({ size }), className)}\r\n        {...props}\r\n      >\r\n        {children}\r\n        {showCloseButton && (\r\n          <DialogPrimitive.Close\r\n            data-slot=\"dialog-close\"\r\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\r\n          >\r\n            <XIcon />\r\n            <span className=\"sr-only\">Close</span>\r\n          </DialogPrimitive.Close>\r\n        )}\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  )\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot=\"dialog-title\"\r\n      className={cn(\"text-lg leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot=\"dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n}\r\n"], "names": ["dialogContentVariants", "cva", "Dialog", "props", "DialogPrimitive.Root", "DialogTrigger", "DialogPrimitive.Trigger", "DialogPortal", "DialogPrimitive.Portal", "DialogOverlay", "className", "jsx", "DialogPrimitive.Overlay", "cn", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "showCloseButton", "size", "jsxs", "DialogPrimitive.Content", "DialogPrimitive.Close", "XIcon", "DialogHeader", "<PERSON><PERSON><PERSON><PERSON>er", "DialogTitle", "DialogPrimitive.Title", "DialogDescription", "DialogPrimitive.Description"], "mappings": "8LAOA,MAAMA,EAAwBC,EAC5B,kWACA,CACE,SAAU,CACR,KAAM,CACJ,GAAI,cACJ,GAAI,cACJ,GAAI,cACJ,GAAI,cACJ,MAAO,eACP,MAAO,eACP,MAAO,eACP,MAAO,eACP,MAAO,eACP,MAAO,eACP,KAAM,eAAA,CAEV,EACA,gBAAiB,CACf,KAAM,IAAA,CACR,CAEJ,EAEA,SAASC,EAAO,CACd,GAAGC,CACL,EAAsD,CACpD,aAAQC,EAAA,CAAqB,YAAU,SAAU,GAAGD,EAAO,CAC7D,CAEA,SAASE,EAAc,CACrB,GAAGF,CACL,EAAyD,CACvD,aAAQG,EAAA,CAAwB,YAAU,iBAAkB,GAAGH,EAAO,CACxE,CAEA,SAASI,EAAa,CACpB,GAAGJ,CACL,EAAwD,CACtD,aAAQK,EAAA,CAAuB,YAAU,gBAAiB,GAAGL,EAAO,CACtE,CAQA,SAASM,EAAc,CACrB,UAAAC,EACA,GAAGP,CACL,EAAyD,CAErD,OAAAQ,EAAA,IAACC,EAAA,CACC,YAAU,iBACV,UAAWC,EACT,yJACAH,CACF,EACC,GAAGP,CAAA,CACN,CAEJ,CAEA,SAASW,EAAc,CACrB,UAAAJ,EACA,SAAAK,EACA,gBAAAC,EAAkB,GAClB,KAAAC,EACA,GAAGd,CACL,EAGG,CAEC,OAAAe,EAAA,KAACX,EAAa,CAAA,YAAU,gBACtB,SAAA,CAAAI,EAAA,IAACF,EAAc,EAAA,EACfS,EAAA,KAACC,EAAA,CACC,YAAU,iBACV,UAAWN,EAAGb,EAAsB,CAAE,KAAAiB,CAAM,CAAA,EAAGP,CAAS,EACvD,GAAGP,EAEH,SAAA,CAAAY,EACAC,GACCE,EAAA,KAACE,EAAA,CACC,YAAU,eACV,UAAU,oWAEV,SAAA,CAAAT,EAAA,IAACU,EAAM,EAAA,EACNV,EAAA,IAAA,OAAA,CAAK,UAAU,UAAU,SAAK,OAAA,CAAA,CAAA,CAAA,CAAA,CACjC,CAAA,CAAA,CAEJ,EACF,CAEJ,CAEA,SAASW,EAAa,CAAE,UAAAZ,EAAW,GAAGP,GAAsC,CAExE,OAAAQ,EAAA,IAAC,MAAA,CACC,YAAU,gBACV,UAAWE,EAAG,+CAAgDH,CAAS,EACtE,GAAGP,CAAA,CACN,CAEJ,CAEA,SAASoB,EAAa,CAAE,UAAAb,EAAW,GAAGP,GAAsC,CAExE,OAAAQ,EAAA,IAAC,MAAA,CACC,YAAU,gBACV,UAAWE,EACT,yDACAH,CACF,EACC,GAAGP,CAAA,CACN,CAEJ,CAEA,SAASqB,EAAY,CACnB,UAAAd,EACA,GAAGP,CACL,EAAuD,CAEnD,OAAAQ,EAAA,IAACc,EAAA,CACC,YAAU,eACV,UAAWZ,EAAG,qCAAsCH,CAAS,EAC5D,GAAGP,CAAA,CACN,CAEJ,CAEA,SAASuB,EAAkB,CACzB,UAAAhB,EACA,GAAGP,CACL,EAA6D,CAEzD,OAAAQ,EAAA,IAACgB,EAAA,CACC,YAAU,qBACV,UAAWd,EAAG,gCAAiCH,CAAS,EACvD,GAAGP,CAAA,CACN,CAEJ"}