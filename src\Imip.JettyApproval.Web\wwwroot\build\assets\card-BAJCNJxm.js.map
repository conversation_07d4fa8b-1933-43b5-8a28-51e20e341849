{"version": 3, "file": "card-BAJCNJxm.js", "sources": ["../../../../../frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": ["Card", "className", "props", "jsx", "cn", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "CardAction", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>er"], "mappings": "sFAIA,SAASA,EAAK,CAAE,UAAAC,EAAW,GAAGC,GAAsC,CAEhE,OAAAC,EAAA,IAAC,MAAA,CACC,YAAU,OACV,UAAWC,EACT,oFACAH,CACF,EACC,GAAGC,CAAA,CACN,CAEJ,CAEA,SAASG,EAAW,CAAE,UAAAJ,EAAW,GAAGC,GAAsC,CAEtE,OAAAC,EAAA,IAAC,MAAA,CACC,YAAU,cACV,UAAWC,EACT,6JACAH,CACF,EACC,GAAGC,CAAA,CACN,CAEJ,CAEA,SAASI,EAAU,CAAE,UAAAL,EAAW,GAAGC,GAAsC,CAErE,OAAAC,EAAA,IAAC,MAAA,CACC,YAAU,aACV,UAAWC,EAAG,6BAA8BH,CAAS,EACpD,GAAGC,CAAA,CACN,CAEJ,CAEA,SAASK,EAAgB,CAAE,UAAAN,EAAW,GAAGC,GAAsC,CAE3E,OAAAC,EAAA,IAAC,MAAA,CACC,YAAU,mBACV,UAAWC,EAAG,gCAAiCH,CAAS,EACvD,GAAGC,CAAA,CACN,CAEJ,CAEA,SAASM,EAAW,CAAE,UAAAP,EAAW,GAAGC,GAAsC,CAEtE,OAAAC,EAAA,IAAC,MAAA,CACC,YAAU,cACV,UAAWC,EACT,iEACAH,CACF,EACC,GAAGC,CAAA,CACN,CAEJ,CAEA,SAASO,EAAY,CAAE,UAAAR,EAAW,GAAGC,GAAsC,CAEvE,OAAAC,EAAA,IAAC,MAAA,CACC,YAAU,eACV,UAAWC,EAAG,OAAQH,CAAS,EAC9B,GAAGC,CAAA,CACN,CAEJ,CAEA,SAASQ,EAAW,CAAE,UAAAT,EAAW,GAAGC,GAAsC,CAEtE,OAAAC,EAAA,IAAC,MAAA,CACC,YAAU,cACV,UAAWC,EAAG,0CAA2CH,CAAS,EACjE,GAAGC,CAAA,CACN,CAEJ"}