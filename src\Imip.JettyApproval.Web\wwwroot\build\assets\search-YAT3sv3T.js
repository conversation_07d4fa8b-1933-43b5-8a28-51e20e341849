import{r as e,j as a}from"./vendor-CrSBzUoz.js";import{I as l}from"./app-layout-CNB1Wtrx.js";const p=(r,t=800)=>{const[s,c]=e.useState(r);return e.useEffect(()=>{const n=setTimeout(()=>{c(r)},t);return()=>{clearTimeout(n)}},[r,t]),s},S=({onUpdate:r,value:t})=>{const[s,c]=e.useState(t),n=e.useRef(null),u=p(s),o=e.useRef(!1),f=e.useCallback(h=>{const m=h.target,{value:i}=m;o.current=!0,c(i)},[]);return e.useEffect(()=>{o.current&&(r(u||""),o.current=!1)},[u,r]),e.useEffect(()=>{t!==s&&!o.current&&c(t),t&&n.current?.focus()},[t,s]),a.jsx("section",{className:"search",children:a.jsx(l,{ref:n,type:"text",value:s,placeholder:"Search...",onChange:f})})},b=e.memo(S);export{b as S};
//# sourceMappingURL=search-YAT3sv3T.js.map
