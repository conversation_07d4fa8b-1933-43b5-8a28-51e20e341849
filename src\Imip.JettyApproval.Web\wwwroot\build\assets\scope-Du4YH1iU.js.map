{"version": 3, "file": "scope-Du4YH1iU.js", "sources": ["../../../../../frontend/src/components/app/clients/scopes/Delete.tsx", "../../../../../frontend/src/components/app/clients/scopes/Actions.tsx", "../../../../../frontend/src/components/app/clients/scopes/Columns.tsx", "../../../../../frontend/src/lib/hooks/useOpeniddictScopesWithFilters.ts", "../../../../../frontend/src/lib/hooks/useOpeniddictResourceSelect.ts", "../../../../../frontend/src/components/app/clients/scopes/Add.tsx", "../../../../../frontend/src/components/app/clients/scopes/Edit.tsx", "../../../../../frontend/src/components/app/clients/scopes/List.tsx", "../../../../../frontend/src/pages/client/scope.tsx"], "sourcesContent": ["import { deleteApiOpeniddictScopesById, type OpenIddictScopeDto } from '@/client'\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@/components/ui/alert-dialog'\r\nimport { useToast } from \"@/hooks/use-toast\"\r\nimport { useEffect, useState } from 'react'\r\n\r\ntype DeleteUserProps = {\r\n  data: { dataId: string; dataEdit: OpenIddictScopeDto }\r\n  onDismiss: () => void\r\n}\r\nexport const Delete = ({ data: { dataId, dataEdit }, onDismiss }: DeleteUserProps) => {\r\n  const { toast } = useToast()\r\n  const [open, setOpen] = useState<boolean>(false)\r\n  const onYesEvent = async () => {\r\n    try {\r\n      await deleteApiOpeniddictScopesById({\r\n        path: { id: dataId },\r\n      })\r\n      toast({\r\n        title: 'Success',\r\n        description: `User \"${dataEdit.name}\" has been deleted successfully.`,\r\n      })\r\n      onDismiss()\r\n    } catch (err: unknown) {\r\n      if (err instanceof Error) {\r\n        toast({\r\n          title: 'Failed',\r\n          description: `There was a problem when deleting the user \"${dataEdit.name}\". Kindly try again.`,\r\n          variant: 'destructive',\r\n        })\r\n      }\r\n    }\r\n  }\r\n\r\n  useEffect(() => {\r\n    setOpen(true)\r\n  }, [])\r\n\r\n  return (\r\n    <AlertDialog open={open}>\r\n      <AlertDialogContent>\r\n        <AlertDialogHeader>\r\n          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>\r\n          <AlertDialogDescription>\r\n            This action cannot be undone. This will permanently delete your this user &quot;\r\n            {dataEdit.name}&quot;\r\n          </AlertDialogDescription>\r\n        </AlertDialogHeader>\r\n        <AlertDialogFooter>\r\n          <AlertDialogCancel onClick={onDismiss}>Cancel</AlertDialogCancel>\r\n          <AlertDialogAction onClick={onYesEvent}>Yes</AlertDialogAction>\r\n        </AlertDialogFooter>\r\n      </AlertDialogContent>\r\n    </AlertDialog>\r\n  )\r\n}\r\n", "'use client'\r\n\r\nimport { type OpenIddictScopeDto } from '@/client'\r\nimport { useGrantedPolicies } from '@/lib/hooks/useGrantedPolicies'\r\nimport { RiMoreLine, RiPencilLine } from '@remixicon/react'\r\nimport { Button } from '@/components/ui/button'\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu'\r\n\r\ntype DataEditActionProps = {\r\n  dataId: string\r\n  dataEdit: OpenIddictScopeDto\r\n  onAction: (dataId: string, dataEdit: OpenIddictScopeDto, dialogType: 'edit' | 'permission' | 'delete') => void\r\n  variant?: 'dropdown' | 'buttons'\r\n}\r\n\r\nexport const Actions = ({ dataId, dataEdit, onAction, variant = 'dropdown' }: DataEditActionProps) => {\r\n  const { can } = useGrantedPolicies()\r\n\r\n  // For dropdown menu style (first image)\r\n  if (variant === 'dropdown') {\r\n    return (\r\n      <div className=\"flex justify-end\">\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"h-8 w-8 p-0\"\r\n            >\r\n              <RiMoreLine className=\"h-4 w-4\" />\r\n              <span className=\"sr-only\">Open menu</span>\r\n            </Button>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent align=\"end\" className=\"w-[160px]\">\r\n            {can('IdentityServer.OpenIddictScopes.Edit') && (\r\n              <DropdownMenuItem\r\n                className=\"cursor-pointer text-sm\"\r\n                onClick={() => onAction(dataId, dataEdit, 'edit')}\r\n              >\r\n                Edit\r\n              </DropdownMenuItem>\r\n            )}\r\n            {can('IdentityServer.OpenIddictScopes.Delete') && (\r\n              <DropdownMenuItem\r\n                className=\"cursor-pointer text-sm text-red-500\"\r\n                onClick={() => onAction(dataId, dataEdit, 'delete')}\r\n              >\r\n                Delete\r\n              </DropdownMenuItem>\r\n            )}\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // For button group style (second image)\r\n  return (\r\n    <div className=\"flex items-center justify-end gap-1\">\r\n      {can('IdentityServer.OpenIddictScopes.Edit') && (\r\n        <Button\r\n          variant=\"primary\"\r\n          size=\"sm\"\r\n          className=\"flex items-center gap-1 px-2 py-1\"\r\n          onClick={() => onAction(dataId, dataEdit, 'edit')}\r\n        >\r\n          <RiPencilLine className=\"h-4 w-4\" />\r\n          <span>Edit</span>\r\n        </Button>\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n", "'use client'\r\n\r\nimport { type OpenIddictScopeDto } from '@/client'\r\nimport { type ColumnDef } from '@tanstack/react-table'\r\nimport { Actions } from './Actions'\r\nimport { DataTableColumnHeader } from '@/components/data-table/DataTableColumnHeader'\r\nimport { Badge } from '@/components/ui/badge'\r\nimport { customFilterFunction } from '../../../data-table/filterFunctions'\r\n\r\n// Type for the callback function to handle user actions\r\ntype UserActionCallback = (dataId: string, dataEdit: OpenIddictScopeDto, dialogType: 'edit' | 'permission' | 'delete') => void\r\n\r\n// Function to create user columns with the action callback\r\nexport const getColumns = (\r\n  handleUserAction: UserActionCallback\r\n): ColumnDef<OpenIddictScopeDto>[] => [\r\n    {\r\n      accessorKey: 'name',\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title=\"Name\" />\r\n      ),\r\n      enableSorting: true,\r\n      enableHiding: true,\r\n      cell: (info) => info.getValue(),\r\n      meta: {\r\n        className: \"text-left\",\r\n        displayName: \"Name\",\r\n      },\r\n      filterFn: customFilterFunction,\r\n    },\r\n    {\r\n      accessorKey: 'displayName',\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title=\"Display Name\" />\r\n      ),\r\n      enableSorting: true,\r\n      enableHiding: true,\r\n      cell: (info) => info.getValue(),\r\n      meta: {\r\n        className: \"text-left\",\r\n        displayName: \"Display Name\",\r\n      },\r\n      filterFn: customFilterFunction,\r\n    },\r\n    {\r\n      accessorKey: 'description',\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title=\"Description\" />\r\n      ),\r\n      enableSorting: true,\r\n      enableHiding: true,\r\n      cell: (info) => info.getValue(),\r\n      meta: {\r\n        className: \"text-left\",\r\n        displayName: \"Description\",\r\n      },\r\n      filterFn: customFilterFunction,\r\n    },\r\n    {\r\n      accessorKey: 'resources',\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title=\"Resources\" />\r\n      ),\r\n      enableSorting: true,\r\n      enableHiding: true,\r\n      filterFn: customFilterFunction,\r\n      cell: (info) => {\r\n        const resources = info.getValue() as string[];\r\n        return (\r\n          <div className=\"flex flex-wrap gap-1\">\r\n            {resources?.map((resource) => (\r\n              <Badge key={resource} variant=\"secondary\">\r\n                {resource}\r\n              </Badge>\r\n            ))}\r\n          </div>\r\n        );\r\n      },\r\n      meta: {\r\n        className: \"text-left\",\r\n        displayName: \"Resources\",\r\n      },\r\n    },\r\n    {\r\n      id: 'actions',\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title=\"Actions\" />\r\n      ),\r\n      enableHiding: true,\r\n      cell: (info) => (\r\n        <Actions\r\n          dataId={info.row.original.id!}\r\n          dataEdit={info.row.original}\r\n          onAction={handleUserAction}\r\n          variant=\"dropdown\" // Use \"dropdown\" for the first image style or \"buttons\" for the second image style\r\n        />\r\n      ),\r\n      meta: {\r\n        className: \"text-right\",\r\n        displayName: \"Actions\",\r\n      },\r\n    },\r\n  ]\r\n\r\n", "import {\r\n  type PagedResultDtoOfOpenIddictApplicationDto,\r\n  postApiOpeniddictScopesList,\r\n} from '@/client'\r\nimport { extractApiError } from '@/lib/query-utils'\r\nimport { generateExtendedQueryParameters } from '@/lib/query-utils-extended'\r\nimport { toast } from '@/lib/useToast'\r\nimport { useQuery } from '@tanstack/react-query'\r\nimport { QueryNames } from './QueryConstants'\r\nimport type { FilterCondition } from '@/lib/interfaces/IFilterCondition'\r\n\r\nexport const useOpeniddictScopesWithFilters = (\r\n  pageIndex: number,\r\n  pageSize: number,\r\n  filterConditions: FilterCondition[] = [],\r\n  sorting?: string\r\n) => {\r\n  return useQuery({\r\n    queryKey: [QueryNames.GetOpeniddictScopes, pageIndex, pageSize, JSON.stringify(filterConditions), sorting],\r\n    queryFn: async () => {\r\n      try {\r\n        // Generate query parameters using the extended utility function\r\n        const body = generateExtendedQueryParameters({\r\n          pageIndex,\r\n          pageSize,\r\n          sorting,\r\n          filterConditions,\r\n        })\r\n\r\n        const response = await postApiOpeniddictScopesList({\r\n          body,\r\n        })\r\n\r\n        // Ensure we return a valid data structure even if the API response is unexpected\r\n        return response.data?.data as PagedResultDtoOfOpenIddictApplicationDto\r\n      } catch (error) {\r\n        // Use the error extraction utility\r\n        const { title, description } = extractApiError(error, 'Error loading scopes')\r\n\r\n        // Show toast notification\r\n        toast({\r\n          title,\r\n          description,\r\n          variant: 'destructive',\r\n        })\r\n\r\n        // Return empty data to prevent UI crashes\r\n        return { items: [], totalCount: 0 }\r\n      }\r\n    },\r\n    retry: false, // Don't retry on error\r\n  })\r\n}\r\n", "import { getApiOpeniddictResourcesAvailableResources } from '@/client'\r\nimport { useQuery } from '@tanstack/react-query'\r\nimport { QueryNames } from './QueryConstants'\r\n\r\nexport const useOpeniddictResourceSelect = () => {\r\n  return useQuery({\r\n    queryKey: [QueryNames.GetOpeniddictResourcesAvailableResources],\r\n    queryFn: async () => {\r\n      const response = await getApiOpeniddictResourcesAvailableResources()\r\n      return response.data?.data\r\n    },\r\n  })\r\n}\r\n", "'use client'\r\nimport { type CreateUpdateOpenIddictScopeDto, postApiOpeniddictScopes } from '@/client'\r\nimport { Button } from '@/components/ui/button'\r\nimport {\r\n  <PERSON><PERSON>,\r\n  DialogContent,\r\n  DialogFooter,\r\n  <PERSON><PERSON>Header,\r\n  <PERSON>alogTitle,\r\n  DialogTrigger,\r\n} from '@/components/ui/dialog'\r\nimport { Input } from '@/components/ui/input'\r\nimport { useToast } from '@/lib/useToast'\r\nimport { QueryNames } from '@/lib/hooks/QueryConstants'\r\nimport { useGrantedPolicies } from '@/lib/hooks/useGrantedPolicies'\r\nimport { useMutation, useQueryClient } from '@tanstack/react-query'\r\nimport { useState } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { Toaster } from '@/components/ui/toaster'\r\nimport { RiAddLine } from '@remixicon/react'\r\nimport { MultiSelect } from '@/components/ui/multi-select'\r\nimport { FormField, FormSection } from '@/components/ui/FormField'\r\nimport { Textarea } from '@/components/ui/textarea'\r\nimport { useOpeniddictResourceSelect } from '@/lib/hooks/useOpeniddictResourceSelect'\r\nimport { handleApiError } from '@/lib/handleApiError'\r\n\r\nexport type AddClientProps = {\r\n  children?: React.ReactNode\r\n}\r\n\r\nexport const Add = ({ children }: AddClientProps) => {\r\n  const { can } = useGrantedPolicies()\r\n  const [open, setOpen] = useState(false)\r\n  const { toast } = useToast()\r\n  const queryClient = useQueryClient()\r\n  const { handleSubmit, register, setValue } = useForm<CreateUpdateOpenIddictScopeDto>()\r\n\r\n  const [selectedResources, setSelectedResources] = useState<string[]>([])\r\n  // Fetch available permissions from the API\r\n  const { data: resourcesData, isLoading: resourcesLoading } = useOpeniddictResourceSelect()\r\n\r\n  const createMutation = useMutation({\r\n    mutationFn: async (dataMutation: CreateUpdateOpenIddictScopeDto) =>\r\n      postApiOpeniddictScopes({\r\n        body: dataMutation\r\n      }),\r\n    onSuccess: () => {\r\n      toast({\r\n        title: 'Success',\r\n        description: 'Client Created Successfully',\r\n        variant: 'success',\r\n      })\r\n      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetOpeniddictScopes] })\r\n      setOpen(false)\r\n    },\r\n    onError: (err: unknown) => {\r\n      // Use the global helper to handle API errors\r\n      const error = handleApiError(err);\r\n      toast({\r\n        title: error.title,\r\n        description: error.description,\r\n        variant: 'error',\r\n      })\r\n    }\r\n  })\r\n\r\n  const onSubmit = (formData: CreateUpdateOpenIddictScopeDto) => {\r\n    // Merge form data with consent type and permissions\r\n    const userData: CreateUpdateOpenIddictScopeDto = {\r\n      ...formData,\r\n      resources: selectedResources\r\n    }\r\n\r\n    createMutation.mutate(userData)\r\n  }\r\n\r\n  return (\r\n    <section>\r\n      <Toaster />\r\n      <Dialog open={open} onOpenChange={setOpen}>\r\n        <DialogTrigger asChild>{children}</DialogTrigger>\r\n        <section className=\"flex items-center justify-between pb-5\">\r\n          {can('AbpIdentity.Users.Create') && (\r\n            <Button size='sm' className=\"w-full sm:py-1 sm:mt-0 sm:w-fit\" onClick={() => setOpen(true)}>\r\n              <RiAddLine className=\"-ml-1 size-4 shrink-0\" aria-hidden=\"true\" />\r\n              <span className=\"hidden truncate sm:inline\">Create Scope</span>\r\n            </Button>\r\n          )}\r\n        </section>\r\n        <DialogContent size='xl'>\r\n          <DialogHeader>\r\n            <DialogTitle>Create a New Scope</DialogTitle>\r\n          </DialogHeader>\r\n          <form onSubmit={handleSubmit(onSubmit)} className='mt-2'>\r\n            <section className=\"flex w-full flex-col space-y-2\">\r\n              <FormSection>\r\n                <FormField\r\n                  label=\"Name\"\r\n                  description=\"The unique identifier for this resource. Used in requests\"\r\n                >\r\n                  <Input required {...register('name')} placeholder=\"Name\" />\r\n                </FormField>\r\n                <FormField\r\n                  label=\"Display Name\"\r\n                  description=\"The display name for this resource\"\r\n                >\r\n                  <Input required {...register('displayName')} placeholder=\"Display Name\" />\r\n                </FormField>\r\n                <FormField\r\n                  label=\"Description\"\r\n                  description=\"The description for this resource\"\r\n                >\r\n                  <Textarea required {...register('description')} placeholder=\"Description\" />\r\n                </FormField>\r\n\r\n                <FormField\r\n                  label=\"Resources\"\r\n                  description=\"The resources of the scope\"\r\n                >\r\n                  <MultiSelect\r\n                    options={resourcesData?.map(resource => ({\r\n                      value: resource.value ?? '',\r\n                      label: resource.label ?? ''\r\n                    })) ?? []}\r\n                    value={selectedResources}\r\n                    onChange={(values) => {\r\n                      setSelectedResources(values);\r\n                      setValue('resources', values);\r\n                    }}\r\n                    placeholder={resourcesLoading ? \"Loading resources...\" : \"Select resources\"}\r\n                    disabled={resourcesLoading}\r\n                    maxHeight={300}\r\n                  />\r\n                </FormField>\r\n              </FormSection>\r\n            </section>\r\n            <DialogFooter className=\"mt-5\">\r\n              <Button\r\n                variant=\"ghost\"\r\n                onClick={(e) => {\r\n                  e.preventDefault()\r\n                  setOpen(false)\r\n                }}\r\n                disabled={createMutation.isPending}\r\n              >\r\n                Cancel\r\n              </Button>\r\n              <Button type=\"submit\" disabled={createMutation.isPending}>\r\n                {createMutation.isPending ? 'Saving...' : 'Save'}\r\n              </Button>\r\n            </DialogFooter>\r\n          </form>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </section>\r\n  )\r\n}\r\n", "'use client'\r\nimport { type CreateUpdateOpenIddictScopeDto, type OpenIddictScopeDto, putApiOpeniddictScopesById } from '@/client'\r\nimport { Button } from '@/components/ui/button'\r\nimport {\r\n  <PERSON><PERSON>,\r\n  DialogContent,\r\n  <PERSON><PERSON>Footer,\r\n  <PERSON><PERSON>Header,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog'\r\nimport { Input } from '@/components/ui/input'\r\nimport { useToast } from '@/lib/useToast'\r\nimport { QueryNames } from '@/lib/hooks/QueryConstants'\r\nimport { useMutation, useQueryClient } from '@tanstack/react-query'\r\nimport { useState } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { Toaster } from '@/components/ui/toaster'\r\nimport { MultiSelect } from '@/components/ui/multi-select'\r\nimport { FormField, FormSection } from '@/components/ui/FormField'\r\nimport { Textarea } from '@/components/ui/textarea'\r\nimport { useOpeniddictResourceSelect } from '@/lib/hooks/useOpeniddictResourceSelect'\r\nimport { handleApiError } from '@/lib/handleApiError'\r\n\r\nexport type EditDataProps = {\r\n  dataEdit: OpenIddictScopeDto\r\n  dataId: string\r\n  onDismiss: () => void\r\n}\r\n\r\nexport const Edit = ({ dataEdit, dataId, onDismiss }: EditDataProps) => {\r\n  const [open, setOpen] = useState(true)\r\n  const { toast } = useToast()\r\n  const queryClient = useQueryClient()\r\n  const { handleSubmit, register, setValue, reset } = useForm<CreateUpdateOpenIddictScopeDto>()\r\n\r\n  const [selectedResources, setSelectedResources] = useState<string[]>(dataEdit.resources ?? [])\r\n  // Fetch available permissions from the API\r\n  const { data: resourcesData, isLoading: resourcesLoading } = useOpeniddictResourceSelect()\r\n\r\n  const resetForm = () => {\r\n    reset({\r\n      name: '',\r\n      displayName: '',\r\n      description: '',\r\n      resources: []\r\n    })\r\n  }\r\n\r\n  const createMutation = useMutation({\r\n    mutationFn: async (dataMutation: CreateUpdateOpenIddictScopeDto) =>\r\n      putApiOpeniddictScopesById({\r\n        path: { id: dataId },\r\n        body: dataMutation\r\n      }),\r\n    onSuccess: () => {\r\n      toast({\r\n        title: 'Success',\r\n        description: 'Scope Updated Successfully',\r\n        variant: 'success',\r\n      })\r\n      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetOpeniddictScopes] })\r\n      resetForm()\r\n      setOpen(false)\r\n      onDismiss()\r\n    },\r\n    onError: (err: unknown) => {\r\n      console.log('Error creating client:', err);\r\n      // Use the global helper to handle API errors\r\n      const error = handleApiError(err);\r\n      toast({\r\n        title: error.title,\r\n        description: error.description,\r\n        variant: 'error',\r\n      })\r\n    }\r\n  })\r\n\r\n  const onSubmit = (formData: CreateUpdateOpenIddictScopeDto) => {\r\n    // Merge form data with consent type and permissions\r\n    const scopeData: CreateUpdateOpenIddictScopeDto = {\r\n      ...formData,\r\n      resources: selectedResources\r\n    }\r\n\r\n    createMutation.mutate(scopeData)\r\n  }\r\n\r\n  const handleOpenChange = (newOpen: boolean) => {\r\n    if (!newOpen) {\r\n      onDismiss()\r\n    }\r\n    setOpen(newOpen)\r\n  }\r\n\r\n  return (\r\n    <section>\r\n      <Toaster />\r\n      <Dialog open={open} onOpenChange={handleOpenChange}>\r\n        <DialogContent size=\"xl\">\r\n          <DialogHeader>\r\n            <DialogTitle>Edit Scope</DialogTitle>\r\n          </DialogHeader>\r\n          <form onSubmit={handleSubmit(onSubmit)} className='mt-2'>\r\n            <section className=\"flex w-full flex-col space-y-2\">\r\n              <FormSection>\r\n                <FormField\r\n                  label=\"Name\"\r\n                  description=\"The unique identifier for this resource. Used in requests\"\r\n                >\r\n                  <Input required {...register('name')} placeholder=\"Name\" defaultValue={dataEdit.name ?? ''} />\r\n                </FormField>\r\n                <FormField\r\n                  label=\"Display Name\"\r\n                  description=\"The display name for this resource\"\r\n                >\r\n                  <Input required {...register('displayName')} placeholder=\"Display Name\" defaultValue={dataEdit.displayName ?? ''} />\r\n                </FormField>\r\n                <FormField\r\n                  label=\"Description\"\r\n                  description=\"The description for this resource\"\r\n                >\r\n                  <Textarea required {...register('description')} placeholder=\"Description\" defaultValue={dataEdit.description ?? ''} />\r\n                </FormField>\r\n\r\n                <FormField\r\n                  label=\"Resources\"\r\n                  description=\"The resources of the scope\"\r\n                >\r\n                  <MultiSelect\r\n                    options={resourcesData?.map(resource => ({\r\n                      value: resource.value ?? '',\r\n                      label: resource.label ?? ''\r\n                    })) ?? []}\r\n                    value={selectedResources}\r\n                    onChange={(values) => {\r\n                      setSelectedResources(values);\r\n                      setValue('resources', values);\r\n                    }}\r\n                    placeholder={resourcesLoading ? \"Loading resources...\" : \"Select resources\"}\r\n                    disabled={resourcesLoading}\r\n                    maxHeight={300}\r\n                  />\r\n                </FormField>\r\n              </FormSection>\r\n            </section>\r\n            <DialogFooter className=\"mt-5\">\r\n              <Button\r\n                variant=\"ghost\"\r\n                onClick={(e) => {\r\n                  e.preventDefault()\r\n                  setOpen(false)\r\n                }}\r\n                disabled={createMutation.isPending}\r\n              >\r\n                Cancel\r\n              </Button>\r\n              <Button type=\"submit\" disabled={createMutation.isPending}>\r\n                {createMutation.isPending ? 'Updating...' : 'Update'}\r\n              </Button>\r\n            </DialogFooter>\r\n          </form>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </section>\r\n  )\r\n}\r\n", "'use client'\r\nimport { QueryNames } from '@/lib/hooks/QueryConstants'\r\nimport { useState } from 'react'\r\n\r\nimport { type OpenIddictScopeDto } from '@/client'\r\nimport { TableSkeleton } from '@/components/ui/TableSkeleton'\r\nimport { type PaginationState } from '@tanstack/react-table'\r\n\r\nimport { useToast } from '@/lib/useToast'\r\nimport { Delete } from './Delete'\r\n\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { DataTable } from '@/components/data-table/DataTable'\r\nimport { getColumns } from './Columns'\r\nimport { NotionFilter } from '../../../data-table/NotionFilter'\r\nimport { useOpeniddictScopesWithFilters } from '@/lib/hooks/useOpeniddictScopesWithFilters'\r\nimport { Add } from './Add'\r\nimport { Edit } from './Edit'\r\nimport { type FilterCondition } from '@/lib/interfaces/IFilterCondition'\r\n\r\nexport const ClientScopeList = () => {\r\n  const { toast } = useToast()\r\n  const queryClient = useQueryClient()\r\n\r\n  const [searchStr, setSearchStr] = useState<string>('')\r\n  const [filterConditions, setFilterConditions] = useState<FilterCondition[]>([])\r\n  const [actionDialog, setActionDialog] = useState<{\r\n    dataId: string\r\n    dataEdit: OpenIddictScopeDto\r\n    dialogType?: 'edit' | 'permission' | 'delete'\r\n  } | null>()\r\n\r\n  const [pagination, setPagination] = useState<PaginationState>({\r\n    pageIndex: 0,\r\n    pageSize: 10,\r\n  })\r\n\r\n  const { isLoading, data } = useOpeniddictScopesWithFilters(\r\n    pagination.pageIndex,\r\n    pagination.pageSize,\r\n    filterConditions\r\n  )\r\n\r\n  // Handler for user actions (edit, permission, delete)\r\n  const handleUserAction = (dataId: string, dataEdit: OpenIddictScopeDto, dialogType: 'edit' | 'permission' | 'delete') => {\r\n    setActionDialog({\r\n      dataId,\r\n      dataEdit,\r\n      dialogType,\r\n    })\r\n  }\r\n\r\n  // Get columns with the action handler\r\n  const columns = getColumns(handleUserAction)\r\n\r\n  const handleSearch = (value: string) => {\r\n    // Always update the search string for UI consistency\r\n    setSearchStr(value)\r\n\r\n    // Create a search filter condition if there's a search value\r\n    // First, remove any existing name filter\r\n    const existingFilters = filterConditions.filter(fc => fc.fieldName !== 'name')\r\n    const newFilterConditions = [...existingFilters]\r\n\r\n    // Only add the search filter if there's a value\r\n    if (value) {\r\n      newFilterConditions.push({\r\n        fieldName: 'name',\r\n        operator: 'Contains',\r\n        value: value\r\n      })\r\n    }\r\n\r\n    // Only update state if filters have changed\r\n    const currentFiltersStr = JSON.stringify(filterConditions)\r\n    const newFiltersStr = JSON.stringify(newFilterConditions)\r\n\r\n    if (currentFiltersStr !== newFiltersStr) {\r\n      setFilterConditions(newFilterConditions)\r\n      setPagination(prev => ({ ...prev, pageIndex: 0 })) // Reset to first page on search\r\n    }\r\n  }\r\n\r\n  const handlePaginationChange = (newPagination: PaginationState) => {\r\n    setPagination(newPagination)\r\n  }\r\n\r\n  // Handler for refreshing the data\r\n  const handleRefresh = () => {\r\n    // Invalidate the query to fetch fresh data\r\n    void queryClient.invalidateQueries({ queryKey: [QueryNames.GetOpeniddictScopes] })\r\n\r\n    // Show toast notification after a short delay to match the animation\r\n    setTimeout(() => {\r\n      toast({\r\n        title: \"Data refreshed\",\r\n        description: \"The client list has been refreshed.\",\r\n        variant: \"success\",\r\n      })\r\n    }, 800)\r\n  }\r\n\r\n  if (isLoading) return (\r\n    <TableSkeleton\r\n      rowCount={pagination.pageSize}\r\n      columnCount={4}\r\n      hasTitle={true}\r\n      hasSearch={true}\r\n      hasFilters={true}\r\n      hasPagination={true}\r\n      hasActions={true}\r\n    />\r\n  )\r\n\r\n  // Ensure we have valid data to render\r\n  const items = data?.items ?? [];\r\n  const totalCount = data?.totalCount ?? 0;\r\n\r\n  return (\r\n    <>\r\n      <div className=\"space-y-4\">\r\n        <DataTable\r\n          title=\"Scopes Management\"\r\n          columns={columns}\r\n          data={items}\r\n          totalCount={totalCount}\r\n          isLoading={isLoading}\r\n          manualPagination={true}\r\n          pageSize={pagination.pageSize}\r\n          onPaginationChange={handlePaginationChange}\r\n          onSearch={handleSearch}\r\n          searchValue={searchStr}\r\n          customFilterbar={(props) => (\r\n            <NotionFilter\r\n              {...props}\r\n              activeFilters={filterConditions}\r\n              onServerFilter={(conditions) => {\r\n                // Only update if the conditions have actually changed\r\n                const currentStr = JSON.stringify(filterConditions);\r\n                const newStr = JSON.stringify(conditions);\r\n\r\n                if (currentStr !== newStr) {\r\n                  setFilterConditions(conditions)\r\n                  setPagination(prev => ({ ...prev, pageIndex: 0 })) // Reset to first page on filter change\r\n                }\r\n              }}\r\n            />\r\n          )}\r\n          hideDefaultFilterbar={true}\r\n          onRefresh={handleRefresh}\r\n          enableRowSelection={false}\r\n          actionButton={{\r\n            // label: \"Create New User\",\r\n            onClick: () => { /* Required but not used */ },\r\n            content: <Add />\r\n          }}\r\n        />\r\n      </div>\r\n\r\n      {actionDialog && actionDialog.dialogType === 'edit' && (\r\n        <Edit\r\n          dataId={actionDialog.dataId}\r\n          dataEdit={actionDialog.dataEdit}\r\n          onDismiss={() => {\r\n            void queryClient.invalidateQueries({ queryKey: [QueryNames.GetOpeniddictScopes] })\r\n            setActionDialog(null)\r\n          }}\r\n        />\r\n      )}\r\n      {actionDialog && actionDialog.dialogType === 'delete' && (\r\n        <Delete\r\n          data={{\r\n            dataId: actionDialog.dataId,\r\n            dataEdit: actionDialog.dataEdit,\r\n          }}\r\n          onDismiss={() => {\r\n            void queryClient.invalidateQueries({ queryKey: [QueryNames.GetOpeniddictScopes] })\r\n            setActionDialog(null)\r\n          }}\r\n        />\r\n      )}\r\n    </>\r\n  )\r\n}\r\n", "'use client';\r\n\r\nimport { ClientScopeList } from '@/components/app/clients/scopes/List';\r\nimport AppLayout from '@/layouts/app-layout';\r\nimport { Head } from '@inertiajs/react';\r\nexport default function OverViewLayout() {\r\n  return (\r\n    <AppLayout>\r\n      <Head title=\"Dashboard\" />\r\n\r\n      <ClientScopeList />\r\n    </AppLayout>\r\n  );\r\n}\r\n"], "names": ["Delete", "dataId", "dataEdit", "on<PERSON><PERSON><PERSON>", "toast", "useToast", "open", "<PERSON><PERSON><PERSON>", "useState", "onYesEvent", "deleteApiOpeniddictScopesById", "err", "useEffect", "jsx", "AlertDialog", "jsxs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Alert<PERSON><PERSON><PERSON>H<PERSON>er", "AlertDialogTitle", "AlertDialogDescription", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AlertDialogCancel", "AlertDialogAction", "Actions", "onAction", "variant", "can", "useGrantedPolicies", "DropdownMenu", "DropdownMenuTrigger", "<PERSON><PERSON>", "RiMoreLine", "DropdownMenuContent", "DropdownMenuItem", "RiPencilLine", "getColumns", "handleUserAction", "column", "DataTableColumnHeader", "info", "customFilterFunction", "resources", "resource", "Badge", "useOpeniddictScopesWithFilters", "pageIndex", "pageSize", "filterConditions", "sorting", "useQuery", "QueryNames", "body", "generateExtendedQueryParameters", "postApiOpeniddictScopesList", "error", "title", "description", "extractApiError", "useOpeniddictResourceSelect", "getApiOpeniddictResourcesAvailableResources", "Add", "children", "queryClient", "useQueryClient", "handleSubmit", "register", "setValue", "useForm", "selectedResources", "setSelectedResources", "resourcesData", "resourcesLoading", "createMutation", "useMutation", "dataMutation", "postApiOpeniddictScopes", "handleApiError", "onSubmit", "formData", "userData", "Toaster", "Dialog", "DialogTrigger", "RiAddLine", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "DialogTitle", "FormSection", "FormField", "Input", "Textarea", "MultiSelect", "values", "<PERSON><PERSON><PERSON><PERSON>er", "e", "Edit", "reset", "resetForm", "putApiOpeniddictScopesById", "scopeData", "handleOpenChange", "newOpen", "ClientScopeList", "searchStr", "setSearchStr", "setFilterConditions", "actionDialog", "setActionDialog", "pagination", "setPagination", "isLoading", "data", "columns", "dialogType", "handleSearch", "value", "newFilterConditions", "fc", "currentFiltersStr", "newFiltersStr", "prev", "handlePaginationChange", "newPagination", "handleRefresh", "TableSkeleton", "items", "totalCount", "Fragment", "DataTable", "props", "NotionFilter", "conditions", "currentStr", "newStr", "OverViewLayout", "AppLayout", "Head"], "mappings": "wpCAkBa,MAAAA,GAAS,CAAC,CAAE,KAAM,CAAE,OAAAC,EAAQ,SAAAC,CAAA,EAAY,UAAAC,KAAiC,CAC9E,KAAA,CAAE,MAAAC,CAAM,EAAIC,EAAS,EACrB,CAACC,EAAMC,CAAO,EAAIC,EAAAA,SAAkB,EAAK,EACzCC,EAAa,SAAY,CACzB,GAAA,CACF,MAAMC,EAA8B,CAClC,KAAM,CAAE,GAAIT,CAAO,CAAA,CACpB,EACKG,EAAA,CACJ,MAAO,UACP,YAAa,SAASF,EAAS,IAAI,kCAAA,CACpC,EACSC,EAAA,QACHQ,EAAc,CACjBA,aAAe,OACXP,EAAA,CACJ,MAAO,SACP,YAAa,+CAA+CF,EAAS,IAAI,uBACzE,QAAS,aAAA,CACV,CACH,CAEJ,EAEAU,OAAAA,EAAAA,UAAU,IAAM,CACdL,EAAQ,EAAI,CACd,EAAG,EAAE,EAGFM,EAAA,IAAAC,GAAA,CAAY,KAAAR,EACX,SAAAS,OAACC,GACC,CAAA,SAAA,CAAAD,OAACE,GACC,CAAA,SAAA,CAAAJ,EAAAA,IAACK,IAAiB,SAAwB,0BAAA,CAAA,SACzCC,GAAuB,CAAA,SAAA,CAAA,8EAErBjB,EAAS,KAAK,GAAA,CACjB,CAAA,CAAA,EACF,SACCkB,GACC,CAAA,SAAA,CAACP,EAAA,IAAAQ,GAAA,CAAkB,QAASlB,EAAW,SAAM,SAAA,EAC5CU,EAAA,IAAAS,GAAA,CAAkB,QAASb,EAAY,SAAG,KAAA,CAAA,CAAA,CAC7C,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,EC3Cac,GAAU,CAAC,CAAE,OAAAtB,EAAQ,SAAAC,EAAU,SAAAsB,EAAU,QAAAC,EAAU,cAAsC,CAC9F,KAAA,CAAE,IAAAC,CAAI,EAAIC,EAAmB,EAGnC,OAAIF,IAAY,WAEXZ,EAAA,IAAA,MAAA,CAAI,UAAU,mBACb,gBAACe,EACC,CAAA,SAAA,CAACf,EAAAA,IAAAgB,GAAA,CAAoB,QAAO,GAC1B,SAAAd,EAAA,KAACe,EAAA,CACC,QAAQ,QACR,KAAK,KACL,UAAU,cAEV,SAAA,CAACjB,EAAAA,IAAAkB,GAAA,CAAW,UAAU,SAAU,CAAA,EAC/BlB,EAAA,IAAA,OAAA,CAAK,UAAU,UAAU,SAAS,WAAA,CAAA,CAAA,CAAA,CAAA,EAEvC,EACCE,EAAA,KAAAiB,GAAA,CAAoB,MAAM,MAAM,UAAU,YACxC,SAAA,CAAAN,EAAI,sCAAsC,GACzCb,EAAA,IAACoB,EAAA,CACC,UAAU,yBACV,QAAS,IAAMT,EAASvB,EAAQC,EAAU,MAAM,EACjD,SAAA,MAAA,CAED,EAEDwB,EAAI,wCAAwC,GAC3Cb,EAAA,IAACoB,EAAA,CACC,UAAU,sCACV,QAAS,IAAMT,EAASvB,EAAQC,EAAU,QAAQ,EACnD,SAAA,QAAA,CAAA,CAED,CAEJ,CAAA,CAAA,CAAA,CACF,CACF,CAAA,QAMD,MAAI,CAAA,UAAU,sCACZ,SAAAwB,EAAI,sCAAsC,GACzCX,EAAA,KAACe,EAAA,CACC,QAAQ,UACR,KAAK,KACL,UAAU,oCACV,QAAS,IAAMN,EAASvB,EAAQC,EAAU,MAAM,EAEhD,SAAA,CAACW,EAAAA,IAAAqB,GAAA,CAAa,UAAU,SAAU,CAAA,EAClCrB,EAAAA,IAAC,QAAK,SAAI,MAAA,CAAA,CAAA,CAAA,CAAA,EAGhB,CAEJ,EChEasB,GACXC,GACoC,CAClC,CACE,YAAa,OACb,OAAQ,CAAC,CAAE,OAAAC,CAAA,IACRxB,EAAA,IAAAyB,EAAA,CAAsB,OAAAD,EAAgB,MAAM,OAAO,EAEtD,cAAe,GACf,aAAc,GACd,KAAOE,GAASA,EAAK,SAAS,EAC9B,KAAM,CACJ,UAAW,YACX,YAAa,MACf,EACA,SAAUC,CACZ,EACA,CACE,YAAa,cACb,OAAQ,CAAC,CAAE,OAAAH,CAAA,IACRxB,EAAA,IAAAyB,EAAA,CAAsB,OAAAD,EAAgB,MAAM,eAAe,EAE9D,cAAe,GACf,aAAc,GACd,KAAOE,GAASA,EAAK,SAAS,EAC9B,KAAM,CACJ,UAAW,YACX,YAAa,cACf,EACA,SAAUC,CACZ,EACA,CACE,YAAa,cACb,OAAQ,CAAC,CAAE,OAAAH,CAAA,IACRxB,EAAA,IAAAyB,EAAA,CAAsB,OAAAD,EAAgB,MAAM,cAAc,EAE7D,cAAe,GACf,aAAc,GACd,KAAOE,GAASA,EAAK,SAAS,EAC9B,KAAM,CACJ,UAAW,YACX,YAAa,aACf,EACA,SAAUC,CACZ,EACA,CACE,YAAa,YACb,OAAQ,CAAC,CAAE,OAAAH,CAAA,IACRxB,EAAA,IAAAyB,EAAA,CAAsB,OAAAD,EAAgB,MAAM,YAAY,EAE3D,cAAe,GACf,aAAc,GACd,SAAUG,EACV,KAAOD,GAAS,CACR,MAAAE,EAAYF,EAAK,SAAS,EAChC,OACG1B,EAAA,IAAA,MAAA,CAAI,UAAU,uBACZ,YAAW,IAAK6B,GACf7B,EAAAA,IAAC8B,IAAqB,QAAQ,YAC3B,SADSD,CAAA,EAAAA,CAEZ,CACD,EACH,CAEJ,EACA,KAAM,CACJ,UAAW,YACX,YAAa,WAAA,CAEjB,EACA,CACE,GAAI,UACJ,OAAQ,CAAC,CAAE,OAAAL,CAAA,IACRxB,EAAA,IAAAyB,EAAA,CAAsB,OAAAD,EAAgB,MAAM,UAAU,EAEzD,aAAc,GACd,KAAOE,GACL1B,EAAA,IAACU,GAAA,CACC,OAAQgB,EAAK,IAAI,SAAS,GAC1B,SAAUA,EAAK,IAAI,SACnB,SAAUH,EACV,QAAQ,UAAA,CACV,EAEF,KAAM,CACJ,UAAW,aACX,YAAa,SAAA,CACf,CAEJ,EC3FWQ,GAAiC,CAC5CC,EACAC,EACAC,EAAsC,CAAA,EACtCC,IAEOC,EAAS,CACd,SAAU,CAACC,EAAW,oBAAqBL,EAAWC,EAAU,KAAK,UAAUC,CAAgB,EAAGC,CAAO,EACzG,QAAS,SAAY,CACf,GAAA,CAEF,MAAMG,EAAOC,GAAgC,CAC3C,UAAAP,EACA,SAAAC,EACA,QAAAE,EACA,iBAAAD,CAAA,CACD,EAOD,OALiB,MAAMM,GAA4B,CACjD,KAAAF,CAAA,CACD,GAGe,MAAM,WACfG,EAAO,CAEd,KAAM,CAAE,MAAAC,EAAO,YAAAC,CAAA,EAAgBC,GAAgBH,EAAO,sBAAsB,EAGtE,OAAAlD,GAAA,CACJ,MAAAmD,EACA,YAAAC,EACA,QAAS,aAAA,CACV,EAGM,CAAE,MAAO,GAAI,WAAY,CAAE,CAAA,CAEtC,EACA,MAAO,EAAA,CACR,EC/CUE,EAA8B,IAClCT,EAAS,CACd,SAAU,CAACC,EAAW,wCAAwC,EAC9D,QAAS,UACU,MAAMS,GAA4C,GACnD,MAAM,IACxB,CACD,ECmBUC,GAAM,CAAC,CAAE,SAAAC,KAA+B,CAC7C,KAAA,CAAE,IAAAnC,CAAI,EAAIC,EAAmB,EAC7B,CAACrB,EAAMC,CAAO,EAAIC,EAAAA,SAAS,EAAK,EAChC,CAAE,MAAAJ,CAAM,EAAIC,EAAS,EACrByD,EAAcC,EAAe,EAC7B,CAAE,aAAAC,EAAc,SAAAC,EAAU,SAAAC,CAAA,EAAaC,EAAwC,EAE/E,CAACC,EAAmBC,CAAoB,EAAI7D,EAAAA,SAAmB,CAAA,CAAE,EAEjE,CAAE,KAAM8D,EAAe,UAAWC,CAAA,EAAqBb,EAA4B,EAEnFc,EAAiBC,EAAY,CACjC,WAAY,MAAOC,GACjBC,GAAwB,CACtB,KAAMD,CAAA,CACP,EACH,UAAW,IAAM,CACTtE,EAAA,CACJ,MAAO,UACP,YAAa,8BACb,QAAS,SAAA,CACV,EACI0D,EAAY,kBAAkB,CAAE,SAAU,CAACZ,EAAW,mBAAmB,EAAG,EACjF3C,EAAQ,EAAK,CACf,EACA,QAAUI,GAAiB,CAEnB,MAAA2C,EAAQsB,EAAejE,CAAG,EAC1BP,EAAA,CACJ,MAAOkD,EAAM,MACb,YAAaA,EAAM,YACnB,QAAS,OAAA,CACV,CAAA,CACH,CACD,EAEKuB,EAAYC,GAA6C,CAE7D,MAAMC,EAA2C,CAC/C,GAAGD,EACH,UAAWV,CACb,EAEAI,EAAe,OAAOO,CAAQ,CAChC,EAEA,cACG,UACC,CAAA,SAAA,CAAAlE,EAAA,IAACmE,EAAQ,EAAA,EACRjE,EAAA,KAAAkE,EAAA,CAAO,KAAA3E,EAAY,aAAcC,EAChC,SAAA,CAACM,EAAAA,IAAAqE,GAAA,CAAc,QAAO,GAAE,SAAArB,CAAS,CAAA,QAChC,UAAQ,CAAA,UAAU,yCAChB,SAAAnC,EAAI,0BAA0B,GAC7BX,OAACe,EAAO,CAAA,KAAK,KAAK,UAAU,kCAAkC,QAAS,IAAMvB,EAAQ,EAAI,EACvF,SAAA,CAAAM,EAAA,IAACsE,GAAU,CAAA,UAAU,wBAAwB,cAAY,OAAO,EAC/DtE,EAAA,IAAA,OAAA,CAAK,UAAU,4BAA4B,SAAY,cAAA,CAAA,CAAA,CAAA,CAC1D,CAEJ,CAAA,EACAE,EAAAA,KAACqE,EAAc,CAAA,KAAK,KAClB,SAAA,CAAAvE,MAACwE,EACC,CAAA,SAAAxE,EAAA,IAACyE,EAAY,CAAA,SAAA,oBAAkB,CAAA,EACjC,SACC,OAAK,CAAA,SAAUtB,EAAaa,CAAQ,EAAG,UAAU,OAChD,SAAA,CAAAhE,MAAC,UAAQ,CAAA,UAAU,iCACjB,SAAAE,EAAA,KAACwE,EACC,CAAA,SAAA,CAAA1E,EAAA,IAAC2E,EAAA,CACC,MAAM,OACN,YAAY,4DAEZ,SAAA3E,EAAA,IAAC4E,GAAM,SAAQ,GAAE,GAAGxB,EAAS,MAAM,EAAG,YAAY,MAAO,CAAA,CAAA,CAC3D,EACApD,EAAA,IAAC2E,EAAA,CACC,MAAM,eACN,YAAY,qCAEZ,SAAA3E,EAAA,IAAC4E,GAAM,SAAQ,GAAE,GAAGxB,EAAS,aAAa,EAAG,YAAY,cAAe,CAAA,CAAA,CAC1E,EACApD,EAAA,IAAC2E,EAAA,CACC,MAAM,cACN,YAAY,oCAEZ,SAAA3E,EAAA,IAAC6E,GAAS,SAAQ,GAAE,GAAGzB,EAAS,aAAa,EAAG,YAAY,aAAc,CAAA,CAAA,CAC5E,EAEApD,EAAA,IAAC2E,EAAA,CACC,MAAM,YACN,YAAY,6BAEZ,SAAA3E,EAAA,IAAC8E,EAAA,CACC,QAASrB,GAAe,IAAiB5B,IAAA,CACvC,MAAOA,EAAS,OAAS,GACzB,MAAOA,EAAS,OAAS,EAC3B,EAAE,GAAK,CAAC,EACR,MAAO0B,EACP,SAAWwB,GAAW,CACpBvB,EAAqBuB,CAAM,EAC3B1B,EAAS,YAAa0B,CAAM,CAC9B,EACA,YAAarB,EAAmB,uBAAyB,mBACzD,SAAUA,EACV,UAAW,GAAA,CAAA,CACb,CAAA,CACF,CAAA,CACF,CACF,CAAA,EACAxD,EAAAA,KAAC8E,EAAa,CAAA,UAAU,OACtB,SAAA,CAAAhF,EAAA,IAACiB,EAAA,CACC,QAAQ,QACR,QAAUgE,GAAM,CACdA,EAAE,eAAe,EACjBvF,EAAQ,EAAK,CACf,EACA,SAAUiE,EAAe,UAC1B,SAAA,QAAA,CAED,EACA3D,EAAAA,IAACiB,EAAO,CAAA,KAAK,SAAS,SAAU0C,EAAe,UAC5C,SAAAA,EAAe,UAAY,YAAc,MAC5C,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,CAEJ,EC/HauB,GAAO,CAAC,CAAE,SAAA7F,EAAU,OAAAD,EAAQ,UAAAE,KAA+B,CACtE,KAAM,CAACG,EAAMC,CAAO,EAAIC,EAAAA,SAAS,EAAI,EAC/B,CAAE,MAAAJ,CAAM,EAAIC,EAAS,EACrByD,EAAcC,EAAe,EAC7B,CAAE,aAAAC,EAAc,SAAAC,EAAU,SAAAC,EAAU,MAAA8B,CAAA,EAAU7B,EAAwC,EAEtF,CAACC,EAAmBC,CAAoB,EAAI7D,WAAmBN,EAAS,WAAa,EAAE,EAEvF,CAAE,KAAMoE,EAAe,UAAWC,CAAA,EAAqBb,EAA4B,EAEnFuC,EAAY,IAAM,CAChBD,EAAA,CACJ,KAAM,GACN,YAAa,GACb,YAAa,GACb,UAAW,CAAA,CAAC,CACb,CACH,EAEMxB,EAAiBC,EAAY,CACjC,WAAY,MAAOC,GACjBwB,GAA2B,CACzB,KAAM,CAAE,GAAIjG,CAAO,EACnB,KAAMyE,CAAA,CACP,EACH,UAAW,IAAM,CACTtE,EAAA,CACJ,MAAO,UACP,YAAa,6BACb,QAAS,SAAA,CACV,EACI0D,EAAY,kBAAkB,CAAE,SAAU,CAACZ,EAAW,mBAAmB,EAAG,EACvE+C,EAAA,EACV1F,EAAQ,EAAK,EACHJ,EAAA,CACZ,EACA,QAAUQ,GAAiB,CAGnB,MAAA2C,EAAQsB,EAAejE,CAAG,EAC1BP,EAAA,CACJ,MAAOkD,EAAM,MACb,YAAaA,EAAM,YACnB,QAAS,OAAA,CACV,CAAA,CACH,CACD,EAEKuB,EAAYC,GAA6C,CAE7D,MAAMqB,EAA4C,CAChD,GAAGrB,EACH,UAAWV,CACb,EAEAI,EAAe,OAAO2B,CAAS,CACjC,EAEMC,EAAoBC,GAAqB,CACxCA,GACOlG,EAAA,EAEZI,EAAQ8F,CAAO,CACjB,EAEA,cACG,UACC,CAAA,SAAA,CAAAxF,EAAA,IAACmE,EAAQ,EAAA,EACTnE,EAAAA,IAACoE,GAAO,KAAA3E,EAAY,aAAc8F,EAChC,SAACrF,EAAAA,KAAAqE,EAAA,CAAc,KAAK,KAClB,SAAA,CAAAvE,MAACwE,EACC,CAAA,SAAAxE,EAAA,IAACyE,EAAY,CAAA,SAAA,YAAU,CAAA,EACzB,SACC,OAAK,CAAA,SAAUtB,EAAaa,CAAQ,EAAG,UAAU,OAChD,SAAA,CAAAhE,MAAC,UAAQ,CAAA,UAAU,iCACjB,SAAAE,EAAA,KAACwE,EACC,CAAA,SAAA,CAAA1E,EAAA,IAAC2E,EAAA,CACC,MAAM,OACN,YAAY,4DAEZ,SAAC3E,EAAA,IAAA4E,EAAA,CAAM,SAAQ,GAAE,GAAGxB,EAAS,MAAM,EAAG,YAAY,OAAO,aAAc/D,EAAS,MAAQ,EAAI,CAAA,CAAA,CAC9F,EACAW,EAAA,IAAC2E,EAAA,CACC,MAAM,eACN,YAAY,qCAEZ,SAAC3E,EAAA,IAAA4E,EAAA,CAAM,SAAQ,GAAE,GAAGxB,EAAS,aAAa,EAAG,YAAY,eAAe,aAAc/D,EAAS,aAAe,EAAI,CAAA,CAAA,CACpH,EACAW,EAAA,IAAC2E,EAAA,CACC,MAAM,cACN,YAAY,oCAEZ,SAAC3E,EAAA,IAAA6E,EAAA,CAAS,SAAQ,GAAE,GAAGzB,EAAS,aAAa,EAAG,YAAY,cAAc,aAAc/D,EAAS,aAAe,EAAI,CAAA,CAAA,CACtH,EAEAW,EAAA,IAAC2E,EAAA,CACC,MAAM,YACN,YAAY,6BAEZ,SAAA3E,EAAA,IAAC8E,EAAA,CACC,QAASrB,GAAe,IAAiB5B,IAAA,CACvC,MAAOA,EAAS,OAAS,GACzB,MAAOA,EAAS,OAAS,EAC3B,EAAE,GAAK,CAAC,EACR,MAAO0B,EACP,SAAWwB,GAAW,CACpBvB,EAAqBuB,CAAM,EAC3B1B,EAAS,YAAa0B,CAAM,CAC9B,EACA,YAAarB,EAAmB,uBAAyB,mBACzD,SAAUA,EACV,UAAW,GAAA,CAAA,CACb,CAAA,CACF,CAAA,CACF,CACF,CAAA,EACAxD,EAAAA,KAAC8E,EAAa,CAAA,UAAU,OACtB,SAAA,CAAAhF,EAAA,IAACiB,EAAA,CACC,QAAQ,QACR,QAAUgE,GAAM,CACdA,EAAE,eAAe,EACjBvF,EAAQ,EAAK,CACf,EACA,SAAUiE,EAAe,UAC1B,SAAA,QAAA,CAED,EACA3D,EAAAA,IAACiB,EAAO,CAAA,KAAK,SAAS,SAAU0C,EAAe,UAC5C,SAAAA,EAAe,UAAY,cAAgB,QAC9C,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,EACF,CAEJ,ECjJa8B,GAAkB,IAAM,CAC7B,KAAA,CAAE,MAAAlG,CAAM,EAAIC,EAAS,EACrByD,EAAcC,EAAe,EAE7B,CAACwC,EAAWC,CAAY,EAAIhG,EAAAA,SAAiB,EAAE,EAC/C,CAACuC,EAAkB0D,CAAmB,EAAIjG,EAAAA,SAA4B,CAAA,CAAE,EACxE,CAACkG,EAAcC,CAAe,EAAInG,WAI9B,EAEJ,CAACoG,EAAYC,CAAa,EAAIrG,WAA0B,CAC5D,UAAW,EACX,SAAU,EAAA,CACX,EAEK,CAAE,UAAAsG,EAAW,KAAAC,CAAA,EAASnE,GAC1BgE,EAAW,UACXA,EAAW,SACX7D,CACF,EAYMiE,EAAU7E,GATS,CAAClC,EAAgBC,EAA8B+G,IAAiD,CACvGN,EAAA,CACd,OAAA1G,EACA,SAAAC,EACA,WAAA+G,CAAA,CACD,CACH,CAG2C,EAErCC,EAAgBC,GAAkB,CAEtCX,EAAaW,CAAK,EAKZ,MAAAC,EAAsB,CAAC,GADLrE,EAAiB,OAAasE,GAAAA,EAAG,YAAc,MAAM,CAC9B,EAG3CF,GACFC,EAAoB,KAAK,CACvB,UAAW,OACX,SAAU,WACV,MAAAD,CAAA,CACD,EAIG,MAAAG,EAAoB,KAAK,UAAUvE,CAAgB,EACnDwE,EAAgB,KAAK,UAAUH,CAAmB,EAEpDE,IAAsBC,IACxBd,EAAoBW,CAAmB,EACvCP,MAAuB,CAAE,GAAGW,EAAM,UAAW,GAAI,EAErD,EAEMC,EAA0BC,GAAmC,CACjEb,EAAca,CAAa,CAC7B,EAGMC,EAAgB,IAAM,CAErB7D,EAAY,kBAAkB,CAAE,SAAU,CAACZ,EAAW,mBAAmB,EAAG,EAGjF,WAAW,IAAM,CACT9C,EAAA,CACJ,MAAO,iBACP,YAAa,sCACb,QAAS,SAAA,CACV,GACA,GAAG,CACR,EAEA,GAAI0G,EACF,OAAAjG,EAAA,IAAC+G,GAAA,CACC,SAAUhB,EAAW,SACrB,YAAa,EACb,SAAU,GACV,UAAW,GACX,WAAY,GACZ,cAAe,GACf,WAAY,EAAA,CACd,EAII,MAAAiB,EAAQd,GAAM,OAAS,CAAC,EACxBe,EAAaf,GAAM,YAAc,EAEvC,OAEIhG,EAAA,KAAAgH,WAAA,CAAA,SAAA,CAAClH,EAAAA,IAAA,MAAA,CAAI,UAAU,YACb,SAAAA,EAAA,IAACmH,GAAA,CACC,MAAM,oBACN,QAAAhB,EACA,KAAMa,EACN,WAAAC,EACA,UAAAhB,EACA,iBAAkB,GAClB,SAAUF,EAAW,SACrB,mBAAoBa,EACpB,SAAUP,EACV,YAAaX,EACb,gBAAkB0B,GAChBpH,EAAA,IAACqH,GAAA,CACE,GAAGD,EACJ,cAAelF,EACf,eAAiBoF,GAAe,CAExB,MAAAC,EAAa,KAAK,UAAUrF,CAAgB,EAC5CsF,EAAS,KAAK,UAAUF,CAAU,EAEpCC,IAAeC,IACjB5B,EAAoB0B,CAAU,EAC9BtB,MAAuB,CAAE,GAAGW,EAAM,UAAW,GAAI,EACnD,CACF,CACF,EAEF,qBAAsB,GACtB,UAAWG,EACX,mBAAoB,GACpB,aAAc,CAEZ,QAAS,IAAM,CAA8B,EAC7C,cAAU/D,GAAI,CAAA,CAAA,CAAA,CAChB,CAAA,EAEJ,EAEC8C,GAAgBA,EAAa,aAAe,QAC3C7F,EAAA,IAACkF,GAAA,CACC,OAAQW,EAAa,OACrB,SAAUA,EAAa,SACvB,UAAW,IAAM,CACV5C,EAAY,kBAAkB,CAAE,SAAU,CAACZ,EAAW,mBAAmB,EAAG,EACjFyD,EAAgB,IAAI,CAAA,CACtB,CACF,EAEDD,GAAgBA,EAAa,aAAe,UAC3C7F,EAAA,IAACb,GAAA,CACC,KAAM,CACJ,OAAQ0G,EAAa,OACrB,SAAUA,EAAa,QACzB,EACA,UAAW,IAAM,CACV5C,EAAY,kBAAkB,CAAE,SAAU,CAACZ,EAAW,mBAAmB,EAAG,EACjFyD,EAAgB,IAAI,CAAA,CACtB,CAAA,CACF,EAEJ,CAEJ,EClLA,SAAwB2B,IAAiB,CACvC,cACGC,GACC,CAAA,SAAA,CAAC1H,EAAAA,IAAA2H,GAAA,CAAK,MAAM,WAAY,CAAA,QAEvBlC,GAAgB,CAAA,CAAA,CAAA,EACnB,CAEJ"}