import{j as r}from"./vendor-CrSBzUoz.js";import{S as s}from"./scroll-area-5IehhMpa.js";import{A as i}from"./app-layout-CNB1Wtrx.js";import{O as o}from"./overview-DuQ0rNeC.js";import{$ as m}from"./App-CGHLK9xH.js";import"./radix-DaY-mnHi.js";import"./card-BAJCNJxm.js";import"./badge-BtBZs1VC.js";import"./index-CZZWNLgr.js";import"./checkbox-CayrCcBd.js";import"./table-CAbNlII1.js";import"./tiny-invariant-CopsF_GD.js";function p({children:e,scrollable:t=!0}){return r.jsx(r.Fragment,{children:t?r.jsx(s,{className:"h-[calc(100dvh-52px)]",children:r.jsx("div",{className:"flex flex-1 p-4 md:px-6",children:e})}):r.jsx("div",{className:"flex flex-1 p-4 md:px-6",children:e})})}function A(){return r.jsxs(i,{policy:"AbpIdentity.Users",children:[r.jsx(m,{title:"Users"}),r.jsx(p,{children:r.jsx(o,{})})]})}export{A as default};
//# sourceMappingURL=users-CItjVOyq.js.map
