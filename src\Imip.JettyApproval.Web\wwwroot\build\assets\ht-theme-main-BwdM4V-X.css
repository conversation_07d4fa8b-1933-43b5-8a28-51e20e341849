/*!
 * Copyright (c) HANDSONCODE sp. z o. o.
 *
 * HANDSONTABLE is a software distributed by HANDSONCODE sp. z o. o., a Polish corporation based in
 * Gdynia, Poland, at Aleja Zwyciestwa 96-98, registered by the District Court in Gdansk under number
 * 538651, EU tax ID number: PL5862294002, share capital: PLN 62,800.00.
 *
 * This software is protected by applicable copyright laws, including international treaties, and dual-
 * licensed - depending on whether your use for commercial purposes, meaning intended for or
 * resulting in commercial advantage or monetary compensation, or not.
 *
 * If your use is strictly personal or solely for evaluation purposes, meaning for the purposes of testing
 * the suitability, performance, and usefulness of this software outside the production environment,
 * you agree to be bound by the terms included in the "handsontable-non-commercial-license.pdf" file.
 *
 * Your use of this software for commercial purposes is subject to the terms included in an applicable
 * license agreement.
 *
 * In any case, you must not make any such use of this software as to develop software which may be
 * considered competitive with this software.
 *
 * UNLESS EXPRESSLY AGREED OTHERWISE, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> PROVIDES THIS SOFTWARE ON AN "AS IS"
 * BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, AND IN NO EVENT AND UNDER NO
 * LEGAL THEORY, SHALL HANDSONCODE BE LIABLE TO YOU FOR DAMAGES, INCLUDING ANY DIRECT,
 * INDIRECT, SPECIAL, INCIDENTAL, OR CONSEQUENTIAL DAMAGES OF ANY CHARACTER ARISING FROM
 * USE OR INABILITY TO USE THIS SOFTWARE.
 *
 * Version: 15.3.0
 * Release date: 29/04/2025 (built at 24/04/2025 10:59:11)
 */.ht-wrapper:not([class*=ht-theme]){--ht-gap-size:4px;--ht-checkbox-size:16px;--ht-cell-horizontal-padding:8px;--ht-cell-vertical-padding:4px;--ht-font-size:14px;--ht-line-height:20px;--ht-border-color:#222;--ht-foreground-color:#222;--ht-background-color:#fff;--ht-header-foreground-color:#222;--ht-header-background-color:#f7f7f9;--ht-header-row-foreground-color:#222;--ht-header-row-background-color:#f7f7f9;--ht-cell-horizontal-border-color:#222;--ht-cell-vertical-border-color:#222;--ht-header-active-border-color:#1a42e8;--ht-header-active-foreground-color:#fff;--ht-header-active-background-color:#1a42e8;--ht-header-row-active-foreground-color:#fff;--ht-header-row-active-background-color:#1a42e8;--ht-cell-selection-border-color:#1a42e8}.handsontable{color:var(--ht-foreground-color);font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen,Ubuntu,Helvetica Neue,Arial,sans-serif;font-size:var(--ht-font-size);font-weight:var(--ht-font-weight);letter-spacing:0;line-height:var(--ht-line-height);position:relative;scrollbar-color:var(--ht-scrollbar-thumb-color) var(--ht-scrollbar-track-color);scrollbar-width:thin;touch-action:manipulation}.handsontable::-webkit-scrollbar-track{border-radius:var(--ht-scrollbar-border-radius)}.handsontable.ht-wrapper{border-radius:var(--ht-wrapper-border-radius,0)}.handsontable.ht-wrapper:before{border:var(--ht-wrapper-border-width) solid var(--ht-wrapper-border-color);border-radius:var(--ht-wrapper-border-radius,0);content:"";display:block;inset:0;overflow:hidden;pointer-events:none;position:absolute;z-index:999}.handsontable.htGhostTable table thead th{border-bottom-width:0}.handsontable.htGhostTable table tbody tr td,.handsontable.htGhostTable table tbody tr th{border-top-width:0;height:calc(var(--ht-cell-vertical-padding)*2 + var(--ht-line-height) + 1px)}.handsontable.htGhostTable table.htGhostTableFirstRow tbody tr td,.handsontable.htGhostTable table.htGhostTableFirstRow tbody tr th{border-top-width:1px;height:calc(var(--ht-cell-vertical-padding)*2 + var(--ht-line-height) + 2px)}.handsontable.htHasScrollX .ht_master .wtHolder,.handsontable.htHasScrollY .ht_master .wtHolder{background-color:var(--ht-background-color);border-radius:var(--ht-wrapper-border-radius,0)}.handsontable tr.ht__row_even th{background-color:var(--ht-row-header-even-background-color)}.handsontable tr.ht__row_even td{background-color:var(--ht-row-cell-even-background-color)}.handsontable tr.ht__row_odd th{background-color:var(--ht-row-header-odd-background-color)}.handsontable tr.ht__row_odd td{background-color:var(--ht-row-cell-odd-background-color)}.handsontable td,.handsontable th{border-bottom:1px solid var(--ht-cell-vertical-border-color);border-inline-end-color:var(--ht-cell-horizontal-border-color);border-inline-end-width:1px;border-inline-start-color:var(--ht-cell-horizontal-border-color);border-inline-start-width:0;border-left-style:solid;border-right-style:solid;border-top:0 solid var(--ht-cell-vertical-border-color);box-sizing:border-box;color:var(--ht-foreground-color);empty-cells:show;font-size:var(--ht-font-size);height:calc(var(--ht-cell-vertical-padding)*2 + var(--ht-line-height) + 1px);line-height:var(--ht-line-height);outline:none;outline-width:0;overflow:hidden;padding:var(--ht-cell-vertical-padding) var(--ht-cell-horizontal-padding);vertical-align:top;white-space:pre-wrap}.handsontable td.invisibleSelection,.handsontable th.invisibleSelection{outline:none}.handsontable td.invisibleSelection::selection,.handsontable th.invisibleSelection::selection{background:transparent}.handsontable td:first-of-type{border-inline-start-width:1px}.handsontable th{background-color:var(--ht-header-background-color);color:var(--ht-header-foreground-color);font-weight:var(--ht-header-font-weight);overflow:visible;position:relative;text-align:center;white-space:nowrap}.handsontable th:last-child{border-bottom-width:1px;border-inline-end-color:var(--ht-border-color);border-inline-end-width:1px;border-inline-start-width:0}.handsontable th:last-child.ht__active_highlight{border-inline-end-color:var(--ht-header-active-border-color)}.handsontable th:first-child{border-inline-start-color:var(--ht-border-color)}.handsontable th:first-child.ht__active_highlight{border-inline-start-color:var(--ht-header-active-border-color)}.handsontable th:first-child,.handsontable th:nth-child(2){border-inline-start-width:1px}.handsontable th.current{box-shadow:inset 0 0 0 1px var(--ht-cell-selection-border-color)}.handsontable th.active{background-color:var(--ht-header-active-background-color);color:var(--ht-header-active-foreground-color)}.handsontable th.ht__highlight{background-color:var(--ht-header-highlighted-background-color);color:var(--ht-header-highlighted-foreground-color)}.handsontable th.ht__active_highlight{background-color:var(--ht-header-active-background-color);border-color:var(--ht-header-active-border-color);color:var(--ht-header-active-foreground-color)}.handsontable tbody tr.ht__row_even th.ht__highlight,.handsontable tbody tr.ht__row_odd th.ht__highlight{background-color:var(--ht-header-row-highlighted-background-color);color:var(--ht-header-row-highlighted-foreground-color)}.handsontable tbody tr.ht__row_even th.ht__active_highlight,.handsontable tbody tr.ht__row_odd th.ht__active_highlight{background-color:var(--ht-header-row-active-background-color);color:var(--ht-header-row-active-foreground-color)}.handsontable tbody tr:first-of-type td,.handsontable tbody tr:first-of-type th{height:calc(var(--ht-cell-vertical-padding)*2 + var(--ht-line-height) + 2px)}.handsontable tbody tr th{background-color:var(--ht-header-row-background-color);color:var(--ht-header-row-foreground-color);padding:0}.handsontable tbody tr th.ht__active_highlight{box-shadow:0 -1px 0 0 var(--ht-header-active-border-color)}.handsontable tbody tr th .relative{height:100%;padding:var(--ht-cell-vertical-padding) var(--ht-cell-horizontal-padding)}.handsontable tbody tr td:first-child{border-inline-start-color:var(--ht-border-color)}.handsontable tbody tr td:last-child{border-inline-end-color:var(--ht-border-color)}.handsontable thead tr th{padding:0}.handsontable thead tr th.ht__active_highlight{box-shadow:-1px 0 0 0 var(--ht-header-active-border-color)}.handsontable thead tr th .relative{padding:var(--ht-cell-vertical-padding) var(--ht-cell-horizontal-padding)}.handsontable thead tr th .relative .colHeader{max-width:calc(100% + 1px);overflow:hidden;text-overflow:ellipsis;vertical-align:top}.handsontable thead tr th .relative:has(.collapsibleIndicator,.changeType) .colHeader{max-width:calc(100% - var(--ht-icon-size) - var(--ht-gap-size))}.handsontable tr:first-child td,.handsontable tr:first-child th{border-top-color:var(--ht-border-color);border-top-width:1px}.handsontable tr:first-child td.ht__active_highlight,.handsontable tr:first-child th.ht__active_highlight{border-top-color:var(--ht-header-active-border-color)}.handsontable tr:last-child td,.handsontable tr:last-child th{border-bottom-color:var(--ht-border-color)}.handsontable tr:last-child td.ht__active_highlight,.handsontable tr:last-child th.ht__active_highlight{border-bottom-color:var(--ht-header-active-border-color)}.handsontable div[class^=ht_clone] thead .ht__highlight .relative:after{background-color:var(--ht-accent-color);bottom:-1px;content:"";display:block;height:var(--ht-header-highlighted-shadow-size);left:-1px;position:absolute;right:-1px}.handsontable div[class^=ht_clone] thead tr:only-of-type .ht__highlight .relative:after{bottom:0}.handsontable div[class^=ht_clone] tbody .ht__highlight .relative:after{background-color:var(--ht-accent-color);bottom:-1px;content:"";display:block;position:absolute;right:-1px;top:-1px;width:var(--ht-header-highlighted-shadow-size)}.handsontable thead tr:not(:last-child) th{overflow:hidden}.handsontable .hide{display:none}.handsontable .relative{box-sizing:border-box;position:relative}.handsontable .wtHider{width:0}.handsontable .wtSpreader{height:auto;position:relative;width:0}.handsontable .htAutoSize{left:-99000px;position:absolute;top:-99000px;visibility:hidden}.handsontable .htFocusCatcher{border:0;height:0;margin:0;opacity:0;padding:0;position:absolute;width:0;z-index:-1}.handsontable div,.handsontable input,.handsontable table,.handsontable tbody,.handsontable textarea,.handsontable thead{box-sizing:content-box}.handsontable input,.handsontable textarea{min-height:auto}.handsontable .htCore{background-color:var(--ht-background-color);border-collapse:separate;border-spacing:0;border-width:0;cursor:default;margin:0;outline-width:0;table-layout:fixed;width:0}.handsontable col,.handsontable col.rowHeader{width:64px}.handsontable col.hidden{width:0!important}.handsontable span.colHeader,.handsontable span.rowHeader{display:inline-block;font-size:var(--ht-font-size);line-height:var(--ht-line-height)}.handsontable tr.hidden,.handsontable tr.hidden td,.handsontable tr.hidden th{display:none}.handsontable a{color:var(--ht-link-color)}.handsontable a:hover{color:var(--ht-link-hover-color)}.handsontable .ht_clone_top th:nth-child(2){border-inline-end-width:1px;border-inline-start-width:0}.handsontable.htRowHeaders thead tr th:nth-child(2){border-inline-start-width:1px}.handsontable .ht_master:not(.innerBorderInlineStart):not(.emptyColumns) tbody tr th,.handsontable .ht_master:not(.innerBorderInlineStart):not(.emptyColumns) thead tr th:first-child,.handsontable .ht_master:not(.innerBorderInlineStart):not(.emptyColumns)~.handsontable:not(.htGhostTable) tbody tr th,.handsontable .ht_master:not(.innerBorderInlineStart):not(.emptyColumns)~.handsontable:not(.ht_clone_top):not(.htGhostTable) thead tr th:first-child{border-inline-end-width:0;border-inline-start-width:1px}.handsontable .ht_master:not(.innerBorderTop):not(.innerBorderBottom) thead tr.lastChild th,.handsontable .ht_master:not(.innerBorderTop):not(.innerBorderBottom) thead tr:last-child th,.handsontable .ht_master:not(.innerBorderTop):not(.innerBorderBottom)~.handsontable thead tr.lastChild th,.handsontable .ht_master:not(.innerBorderTop):not(.innerBorderBottom)~.handsontable thead tr:last-child th{border-bottom-width:0}.handsontable .ht_clone_inline_start table.htCore>thead,.handsontable .ht_master table.htCore>tbody>tr>th,.handsontable .ht_master table.htCore>thead{visibility:hidden}.ht_clone_bottom,.ht_clone_bottom .wtHolder,.ht_clone_inline_start,.ht_clone_inline_start .wtHolder,.ht_clone_top,.ht_clone_top .wtHolder,.ht_master,.ht_master .wtHolder{overflow:hidden}.ht_master .wtHolder{overflow:auto}[dir=rtl].handsontable td[dir=ltr]{border-inline-end-width:0;border-inline-start-width:1px}[dir=rtl].handsontable tbody tr td[dir=ltr]:last-child{border-inline-start-color:var(--ht-border-color)}[dir=rtl].handsontable div[class^=ht_clone] tbody .ht__highlight .relative:after{left:-1px;right:auto}.handsontable.ht-wrapper:not(.htColumnHeaders) .ht_master .htCore tbody tr:first-child td:first-child{border-start-start-radius:var(--ht-wrapper-border-radius)}.handsontable.ht-wrapper:not(.htColumnHeaders) .ht_master .htCore tbody tr:first-child td:last-child{border-start-end-radius:var(--ht-wrapper-border-radius)}.handsontable.ht-wrapper:not(.htColumnHeaders) .ht_clone_top .htCore tbody tr:first-child td:first-child,.handsontable.ht-wrapper:not(.htColumnHeaders) .ht_clone_top_inline_start_corner .htCore tbody tr:first-child td:first-child{border-start-start-radius:var(--ht-wrapper-border-radius)}.handsontable.ht-wrapper:not(.htColumnHeaders) .ht_clone_top .htCore tbody tr:first-child td:last-child{border-start-end-radius:var(--ht-wrapper-border-radius)}.handsontable.ht-wrapper:not(.htColumnHeaders) .ht_clone_inline_start .htCore tbody tr:first-child td:first-child,.handsontable.ht-wrapper:not(.htColumnHeaders) .ht_clone_inline_start .htCore tbody tr:first-child th:first-child{border-start-start-radius:var(--ht-wrapper-border-radius)}.handsontable.ht-wrapper .ht_master .htCore{border-radius:var(--ht-wrapper-border-radius)}.handsontable.ht-wrapper .ht_master .htCore thead tr:first-child th:first-child{border-start-start-radius:var(--ht-wrapper-border-radius)}.handsontable.ht-wrapper .ht_master .htCore thead tr:first-child th:last-child{border-start-end-radius:var(--ht-wrapper-border-radius)}.handsontable.ht-wrapper .ht_master .htCore thead tr:last-child th:first-child{border-end-start-radius:var(--ht-wrapper-border-radius)}.handsontable.ht-wrapper .ht_master .htCore thead tr:last-child th:last-child{border-end-end-radius:var(--ht-wrapper-border-radius)}.handsontable.ht-wrapper .ht_master .htCore tbody tr:last-child td:first-child{border-end-start-radius:var(--ht-wrapper-border-radius)}.handsontable.ht-wrapper .ht_master .htCore tbody tr:last-child td:last-child{border-end-end-radius:var(--ht-wrapper-border-radius)}.handsontable.ht-wrapper .ht_clone_top .htCore,.handsontable.ht-wrapper .ht_clone_top_inline_start_corner .htCore,.handsontable.ht-wrapper .ht_clone_top_inline_start_corner .htCore thead tr:first-child th:first-child{border-start-start-radius:var(--ht-wrapper-border-radius)}.handsontable.ht-wrapper .ht_clone_top .htCore{border-start-end-radius:var(--ht-wrapper-border-radius)}.handsontable.ht-wrapper .ht_clone_top .htCore thead tr:first-child th:first-child{border-start-start-radius:var(--ht-wrapper-border-radius)}.handsontable.ht-wrapper .ht_clone_top .htCore thead tr:first-child th:last-child{border-start-end-radius:var(--ht-wrapper-border-radius)}.handsontable.ht-wrapper .ht_clone_inline_start .htCore{border-end-start-radius:var(--ht-wrapper-border-radius)}.handsontable.ht-wrapper .ht_clone_inline_start .htCore,.handsontable.ht-wrapper .ht_clone_inline_start .htCore thead tr:first-child th:first-child{border-start-start-radius:var(--ht-wrapper-border-radius)}.handsontable.ht-wrapper .ht_clone_bottom .htCore,.handsontable.ht-wrapper .ht_clone_bottom_inline_start_corner .htCore,.handsontable.ht-wrapper .ht_clone_bottom_inline_start_corner .htCore tr:last-child td:first-child,.handsontable.ht-wrapper .ht_clone_bottom_inline_start_corner .htCore tr:last-child th:first-child,.handsontable.ht-wrapper .ht_clone_inline_start .htCore tbody tr:last-child td:first-child,.handsontable.ht-wrapper .ht_clone_inline_start .htCore tbody tr:last-child th:first-child{border-end-start-radius:var(--ht-wrapper-border-radius)}.handsontable.ht-wrapper .ht_clone_bottom .htCore{border-end-end-radius:var(--ht-wrapper-border-radius)}.handsontable.ht-wrapper .ht_clone_bottom .htCore tr:last-child td:first-child,.handsontable.ht-wrapper .ht_clone_bottom .htCore tr:last-child th:first-child{border-end-start-radius:var(--ht-wrapper-border-radius)}.handsontable.ht-wrapper .ht_clone_bottom .htCore tr:last-child td:last-child,.handsontable.ht-wrapper .ht_clone_bottom .htCore tr:last-child th:last-child{border-end-end-radius:var(--ht-wrapper-border-radius)}.handsontable.ht-wrapper.htHasScrollX .htCore{border-end-end-radius:0;border-end-start-radius:0}.handsontable.ht-wrapper.htHasScrollX .htCore tbody tr:last-child td:first-child,.handsontable.ht-wrapper.htHasScrollX .htCore tbody tr:last-child th:first-child,.handsontable.ht-wrapper.htHasScrollX .htCore thead tr:last-child th:first-child{border-end-start-radius:0!important}.handsontable.ht-wrapper.htHasScrollX .htCore tbody tr:last-child td:last-child,.handsontable.ht-wrapper.htHasScrollX .htCore tbody tr:last-child th:last-child,.handsontable.ht-wrapper.htHasScrollX .htCore thead tr:last-child th:last-child{border-end-end-radius:0!important}.handsontable.ht-wrapper.htHasScrollY .htCore{border-end-end-radius:0;border-start-end-radius:0}.handsontable.ht-wrapper.htHasScrollY .htCore tbody tr:first-child td:last-child,.handsontable.ht-wrapper.htHasScrollY .htCore tbody tr:first-child th:last-child,.handsontable.ht-wrapper.htHasScrollY .htCore thead tr:first-child th:last-child{border-start-end-radius:0!important}.handsontable.ht-wrapper.htHasScrollY .htCore tbody tr:last-child td:last-child,.handsontable.ht-wrapper.htHasScrollY .htCore tbody tr:last-child th:last-child,.handsontable.ht-wrapper.htHasScrollY .htCore thead tr:last-child th:last-child{border-end-end-radius:0!important}.handsontable.mobile,.handsontable.mobile .wtHolder{user-select:none;-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-overflow-scrolling:touch}.handsontable.mobile .handsontableInput:focus{-webkit-appearance:none;box-shadow:inset 0 0 0 2px var(--ht-accent-color)}.handsontable .bottomSelectionHandle,.handsontable .bottomSelectionHandle-HitArea,.handsontable .topSelectionHandle,.handsontable .topSelectionHandle-HitArea{inset-inline-end:unset;inset-inline-start:-10000px;top:-10000px;z-index:9999}.handsontable.hide-tween{-webkit-animation:opacity-hide .2s;animation:opacity-hide .2s;animation-fill-mode:forwards;-webkit-animation-fill-mode:forwards}.handsontable.show-tween{-webkit-animation:opacity-show .3s;animation:opacity-show .3s;animation-fill-mode:forwards;-webkit-animation-fill-mode:forwards}.ht_clone_master{z-index:100}.ht_clone_inline_start{z-index:120}.ht_clone_bottom{z-index:130}.ht_clone_bottom_inline_start_corner{z-index:150}.ht_clone_top{z-index:160}.ht_clone_top_inline_start_corner{z-index:180}.ht_editor_hidden{z-index:-1}.ht_editor_visible{z-index:200}.handsontable td.area{position:relative}.handsontable td.area:before{background:var(--ht-cell-selection-background-color,#1a42e8);content:"";inset:0;position:absolute}.handsontable td.area-1{position:relative}.handsontable td.area-1:before{background:var(--ht-cell-selection-background-color,#1a42e8);content:"";inset:0;position:absolute}.handsontable td.area-2{position:relative}.handsontable td.area-2:before{background:var(--ht-cell-selection-background-color,#1a42e8);content:"";inset:0;position:absolute}.handsontable td.area-3{position:relative}.handsontable td.area-3:before{background:var(--ht-cell-selection-background-color,#1a42e8);content:"";inset:0;position:absolute}.handsontable td.area-4{position:relative}.handsontable td.area-4:before{background:var(--ht-cell-selection-background-color,#1a42e8);content:"";inset:0;position:absolute}.handsontable td.area-5{position:relative}.handsontable td.area-5:before{background:var(--ht-cell-selection-background-color,#1a42e8);content:"";inset:0;position:absolute}.handsontable td.area-6{position:relative}.handsontable td.area-6:before{background:var(--ht-cell-selection-background-color,#1a42e8);content:"";inset:0;position:absolute}.handsontable td.area-7{position:relative}.handsontable td.area-7:before{background:var(--ht-cell-selection-background-color,#1a42e8);content:"";inset:0;position:absolute}.handsontable td.area:before{opacity:.14}.handsontable td.area-1:before{opacity:.21}.handsontable td.area-2:before{opacity:.28}.handsontable td.area-3:before{opacity:.35}.handsontable td.area-4:before{opacity:.42}.handsontable td.area-5:before{opacity:.49}.handsontable td.area-6:before{opacity:.56}.handsontable td.area-7:before{opacity:.63}.handsontable td.area{border-color:color-mix(in srgb,var(--ht-border-color),var(--ht-foreground-color) 10%)}.handsontable .wtBorder{font-size:0;position:absolute}.handsontable .wtBorder.hidden{display:none!important}.handsontable .wtBorder.current{z-index:10}.handsontable .wtBorder.area,.handsontable .wtBorder.current{background-color:var(--ht-cell-selection-border-color)!important}.handsontable .wtBorder.area{z-index:8}.handsontable .wtBorder.fill{background-color:var(--ht-cell-autofill-fill-border-color,#68696c)!important;z-index:6}.handsontable .wtBorder.corner{background-color:var(--ht-cell-autofill-background-color,#1a42e8)!important;border-color:var(--ht-cell-autofill-border-color)!important;border-radius:var(--ht-cell-autofill-border-radius)!important;cursor:crosshair;font-size:0;z-index:10}.hot-display-license-info{background-color:var(--ht-license-background-color,#f7f7f9);border:1px solid var(--ht-border-color,#f7f7f9);border-radius:var(--ht-wrapper-border-radius);color:var(--ht-license-foreground-color);font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen,Ubuntu,Helvetica Neue,Arial,sans-serif;font-size:calc(var(--ht-font-size) - 2px);line-height:calc(var(--ht-line-height) - 2px);margin-top:10px;padding:var(--ht-license-vertical-padding,16px) var(--ht-license-horizontal-padding,16px);text-align:left}.hot-display-license-info a{color:var(--ht-link-color);font-size:var(--ht-license-font-size)}.hot-display-license-info a:hover{color:var(--ht-link-hover-color)}.handsontable .htAutocompleteArrow{cursor:default;float:right;font-size:0;height:var(--ht-icon-size);margin-inline-start:calc(var(--ht-gap-size)*2);position:relative;top:calc((var(--ht-line-height) - var(--ht-icon-size))/2);width:var(--ht-icon-size)}.handsontable .htAutocompleteArrow:after{content:"";display:block;left:0;opacity:.6;position:absolute;top:0}[dir=rtl].handsontable .htAutocompleteArrow{float:left}.handsontable .htCheckboxRendererInput{cursor:pointer;display:inline-block;height:var(--ht-checkbox-size);margin:-2px 0 0;position:relative;vertical-align:middle;width:var(--ht-checkbox-size)}.handsontable .htCheckboxRendererInput:first-child{margin-inline-end:var(--ht-gap-size)}.handsontable .htCheckboxRendererInput:last-child{margin-inline-start:var(--ht-gap-size)}.handsontable .htCheckboxRendererInput:before{background-color:var(--ht-checkbox-background-color);border:1px solid var(--ht-checkbox-border-color);border-radius:var(--ht-checkbox-border-radius);box-sizing:border-box;content:"";display:inline-block;height:var(--ht-checkbox-size);pointer-events:none;transition:all var(--ht-table-transition) ease-in-out;width:var(--ht-checkbox-size)}.handsontable .htCheckboxRendererInput:after{color:var(--ht-checkbox-icon-color);content:"";display:block;left:0;pointer-events:none;position:absolute;top:0}.handsontable .htCheckboxRendererInput:checked:before{background-color:var(--ht-checkbox-checked-background-color);border-color:var(--ht-checkbox-checked-border-color)}.handsontable .htCheckboxRendererInput:checked:after{color:var(--ht-checkbox-checked-icon-color)}.handsontable .htCheckboxRendererInput:checked:disabled:before{background-color:var(--ht-checkbox-checked-disabled-background-color);border-color:var(--ht-checkbox-checked-disabled-border-color)}.handsontable .htCheckboxRendererInput:checked:disabled:after{color:var(--ht-checkbox-checked-disabled-icon-color)}.handsontable .htCheckboxRendererInput:checked:focus:before{background-color:var(--ht-checkbox-checked-focus-background-color);border-color:var(--ht-checkbox-checked-focus-border-color);outline:1px solid var(--ht-checkbox-checked-focus-ring-color)}.handsontable .htCheckboxRendererInput:checked:focus:after{color:var(--ht-checkbox-checked-focus-icon-color)}.handsontable .htCheckboxRendererInput:focus:before{background-color:var(--ht-checkbox-focus-background-color);border-color:var(--ht-checkbox-focus-border-color);outline:1px solid var(--ht-checkbox-focus-ring-color)}.handsontable .htCheckboxRendererInput:focus:after{color:var(--ht-checkbox-focus-icon-color)}.handsontable .htCheckboxRendererInput:disabled{cursor:auto}.handsontable .htCheckboxRendererInput:disabled:before{background-color:var(--ht-checkbox-disabled-background-color);border-color:var(--ht-checkbox-disabled-border-color)}.handsontable .htCheckboxRendererInput:disabled:after{color:var(--ht-checkbox-disabled-icon-color)}.handsontable .htCheckboxRendererInput:indeterminate:before{background-color:red}.handsontable .htCheckboxRendererInput.noValue{opacity:.5}.handsontable .htCheckboxRendererLabel{color:inherit;cursor:pointer;display:inline-block;font-size:inherit;line-height:inherit;margin:0;position:relative}.handsontable .htCheckboxRendererLabel.fullWidth{width:100%}.handsontable .htPlaceholder{color:var(--ht-placeholder-color)}.handsontable .htDimmed{color:var(--ht-read-only-color)}.handsontable .htLeft{text-align:left}.handsontable .htCenter{text-align:center}.handsontable .htRight{text-align:right}.handsontable .htJustify{text-align:justify}.handsontable .htTop{vertical-align:top}.handsontable .htMiddle{vertical-align:middle}.handsontable .htBottom{vertical-align:bottom}.handsontable .htInvalid{background-color:var(--ht-cell-error-background-color)!important}.handsontable .htNoWrap{white-space:nowrap}.handsontable .htSearchResult{background:var(--ht-cell-success-background-color)!important}.handsontableInputHolder{left:0;position:absolute;top:0}.handsontableInputHolder .handsontableInput{-webkit-appearance:none!important;background-color:var(--ht-cell-editor-background-color,#fff);border:none;border-radius:0;box-shadow:inset 0 0 0 var(--ht-cell-editor-border-width,2px) var(--ht-cell-editor-border-color,#1a42e8),0 0 var(--ht-cell-editor-shadow-blur-radius,0) 0 var(--ht-cell-editor-shadow-color,transparent);box-sizing:border-box;color:var(--ht-cell-editor-foreground-color);display:block;font-family:inherit!important;font-size:inherit!important;line-height:inherit!important;margin:0;outline-width:0;padding:calc(var(--ht-cell-vertical-padding, 4px) + 1px) calc(var(--ht-cell-horizontal-padding, 8px) + 1px)}.handsontableInputHolder .handsontableInput:focus{outline:none}.htSelectEditor{position:absolute}.htSelectEditor select{-webkit-appearance:none!important;background-color:var(--ht-cell-editor-background-color,#fff);border:none;border-radius:0;box-shadow:inset 0 0 0 var(--ht-cell-editor-border-width,2px) var(--ht-cell-editor-border-color,#1a42e8),0 0 var(--ht-cell-editor-shadow-blur-radius,0) 0 var(--ht-cell-editor-shadow-color,transparent);box-sizing:border-box;color:var(--ht-cell-editor-foreground-color);font-family:inherit!important;font-size:inherit!important;height:100%;inset:0;line-height:inherit!important;margin:0;overflow:hidden;padding:calc(var(--ht-cell-vertical-padding, 4px) + 1px) calc(var(--ht-cell-horizontal-padding, 8px) + 1px);padding-inline-end:26px;position:absolute;text-overflow:ellipsis;white-space:nowrap;width:100%}.htSelectEditor select:focus{outline:none}.htSelectEditor .htAutocompleteArrow{margin-inline-end:4px;pointer-events:none;top:50%;transform:translateY(-50%);z-index:1}.handsontable.listbox{border:var(--ht-menu-border-width,1px) solid var(--ht-menu-border-color,#e5e5e9);box-shadow:var(--ht-menu-shadow-x,0) var(--ht-menu-shadow-y,0) var(--ht-menu-shadow-blur,8px) var(--ht-menu-shadow-color,rgba(0,0,0,.08));margin:0}.handsontable.listbox .ht_master{background-color:var(--ht-background-color);overflow:hidden}.handsontable.listbox .wtHolder{overflow:auto}.handsontable.listbox .htCore,.handsontable.listbox .wtHider{border-radius:0!important}.handsontable.listbox table{overflow:hidden}.handsontable.listbox table td,.handsontable.listbox table th,.handsontable.listbox table tr:first-child td,.handsontable.listbox table tr:first-child th,.handsontable.listbox table tr:last-child th{border-color:transparent!important}.handsontable.listbox table td,.handsontable.listbox table th{border-radius:0!important;text-overflow:ellipsis;white-space:nowrap}.handsontable.listbox table td.htDimmed{color:inherit;cursor:default;font-style:inherit}.handsontable.listbox table .wtBorder{visibility:hidden}.handsontable.listbox table strong{color:inherit;font-weight:700}.handsontable.listbox table tr td.current,.handsontable.listbox table tr td:hover,.handsontable.listbox table tr:hover td{background:var(--ht-menu-item-hover-color,#e5e5e9);cursor:pointer}.handsontable.listbox table thead th{border-width:0 1px 1px;height:auto!important;text-align:left}.handsontable.listbox table thead th>div{padding-bottom:8px;padding-top:8px}.handsontable.listbox table thead th:first-child{padding-left:8px}.handsontable.listbox .ht_clone_top .wtHider{overflow:hidden}.handsontable.listbox .ht_clone_top table{border-radius:0}.handsontable.listbox .htBorders div{background:none!important}.handsontable.autocompleteEditor tr.ht__row_even td,.handsontable.autocompleteEditor tr.ht__row_even th,.handsontable.autocompleteEditor tr.ht__row_odd td,.handsontable.autocompleteEditor tr.ht__row_odd th{background-color:var(--ht-background-color,#fff)}.pika-single{background:var(--ht-background-color,#fff);border:var(--ht-menu-border-width,1px) solid var(--ht-menu-border-color,#e5e5e9);border-radius:var(--ht-menu-border-radius);box-shadow:var(--ht-menu-shadow-x,0) var(--ht-menu-shadow-y,0) var(--ht-menu-shadow-blur,8px) var(--ht-menu-shadow-color,rgba(0,0,0,.08));color:var(--ht-foreground-color);display:block;font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen,Ubuntu,Helvetica Neue,Arial,sans-serif;font-size:var(--ht-font-size);font-weight:var(--ht-font-weight);line-height:var(--ht-line-height);padding:10px;position:relative;z-index:9999}.pika-single.is-hidden{display:none}.pika-single .pika-row{background-color:transparent}.pika-single .pika-title{align-items:center;display:flex;justify-content:center}.pika-single .pika-label{color:var(--ht-accent-color);order:2;padding:5px;position:relative}.pika-single .pika-label .pika-select{cursor:pointer;inset:0;opacity:0;position:absolute}.pika-single .pika-next,.pika-single .pika-prev{background:none;border:none;color:var(--ht-accent-color);cursor:pointer;height:20px;text-indent:-9999px;white-space:nowrap;width:20px}.pika-single .pika-prev{margin-right:auto;order:1}.pika-single .pika-next{margin-left:auto;order:3}.pika-single .pika-table thead{height:50px}.pika-single .pika-table th{padding:1px;vertical-align:middle}.pika-single .pika-table th abbr{border:none;cursor:auto;font-weight:400;text-decoration:none}.pika-single .pika-table td{padding:1px;vertical-align:middle}.pika-single .pika-table td.is-disabled .pika-button{opacity:.5;pointer-events:none}.pika-single .pika-table td.is-today .pika-button{background:var(--ht-menu-item-hover-color)}.pika-single .pika-table td.is-selected .pika-button{background:var(--ht-accent-color,#1a42e8);color:var(--ht-background-color,#fff)}.pika-single .pika-table td.pika-week{text-align:center;width:12.5%}.pika-single .pika-table .pika-button{aspect-ratio:1/1;background:transparent;border:none;border-radius:var(--ht-button-border-radius);color:var(--ht-foreground-color);cursor:pointer;font-size:inherit;margin:0;padding:10px;width:100%}.pika-single .pika-table .pika-button:hover{background:var(--ht-menu-item-hover-color)}[dir=rtl].htDatepickerHolder .pika-single .pika-next{margin-left:0;margin-right:auto;transform:rotate(180deg)}[dir=rtl].htDatepickerHolder .pika-single .pika-prev{margin-left:auto;margin-right:0;transform:rotate(180deg)}.handsontable .htUISelectCaption{background-color:var(--ht-input-background-color);border:var(--ht-input-border-width) solid var(--ht-input-border-color);border-radius:var(--ht-input-border-radius);box-sizing:border-box;color:var(--ht-input-foreground-color);cursor:pointer;font-family:inherit;font-size:var(--ht-font-size);font-weight:var(--ht-font-weight);line-height:var(--ht-line-height);margin:0;outline:none!important;overflow:hidden;padding:var(--ht-input-vertical-padding) var(--ht-input-horizontal-padding);padding-inline-end:calc(var(--ht-input-horizontal-padding) + var(--ht-icon-size));text-overflow:ellipsis;white-space:nowrap;width:100%}.handsontable .htUISelectCaption:after{content:"";display:block;position:absolute;right:calc(var(--ht-input-horizontal-padding) - var(--ht-icon-size)/4);top:50%;transform:translateY(-50%)}.handsontable .htUISelectCaption:hover{background-color:var(--ht-input-hover-background-color);border-color:var(--ht-input-hover-border-color);color:var(--ht-input-hover-foreground-color)}.handsontable .htUISelectCaption:disabled{background-color:var(--ht-input-disabled-background-color);border-color:var(--ht-input-disabled-border-color);color:var(--ht-input-disabled-foreground-color)}.handsontable .htUISelectCaption:focus{background-color:var(--ht-input-focus-background-color);border-color:var(--ht-input-focus-border-color);color:var(--ht-input-focus-foreground-color)}.handsontable .htUISelect{cursor:pointer;outline:none!important;position:relative}.handsontable .htUISelect:focus .htUISelectCaption{background-color:var(--ht-input-focus-background-color);border-color:var(--ht-input-focus-border-color);color:var(--ht-input-focus-foreground-color)}[dir=rtl].handsontable .htUISelectCaption:after{left:calc(var(--ht-input-horizontal-padding) - var(--ht-icon-size)/4);right:auto}.handsontable .htFiltersMenuCondition .htUIInput input,.handsontable .htFiltersMenuValue .htUIMultipleSelectSearch input{background-color:var(--ht-input-background-color);border:var(--ht-input-border-width) solid var(--ht-input-border-color);border-radius:var(--ht-input-border-radius);box-sizing:border-box;color:var(--ht-input-foreground-color);cursor:pointer;font-family:inherit;font-size:var(--ht-font-size);font-weight:var(--ht-font-weight);line-height:var(--ht-line-height);outline:none!important;padding:var(--ht-input-vertical-padding) var(--ht-input-horizontal-padding);transition:var(--ht-table-transition) all ease-in-out;width:100%}.handsontable .htFiltersMenuCondition .htUIInput input:hover,.handsontable .htFiltersMenuValue .htUIMultipleSelectSearch input:hover{background-color:var(--ht-input-hover-background-color);border-color:var(--ht-input-hover-border-color);color:var(--ht-input-hover-foreground-color)}.handsontable .htFiltersMenuCondition .htUIInput input:disabled,.handsontable .htFiltersMenuValue .htUIMultipleSelectSearch input:disabled{background-color:var(--ht-input-disabled-background-color);border-color:var(--ht-input-disabled-border-color);color:var(--ht-input-disabled-foreground-color)}.handsontable .htFiltersMenuCondition .htUIInput input:focus,.handsontable .htFiltersMenuValue .htUIMultipleSelectSearch input:focus{background-color:var(--ht-input-focus-background-color);border-color:var(--ht-input-focus-border-color);color:var(--ht-input-focus-foreground-color)}.handsontable .htUIInput{outline:none!important;padding:0;position:relative;text-align:center}.handsontable .htUIInputIcon{position:absolute}.handsontable .htUIInput.htUIButton{cursor:pointer;display:inline-block}.handsontable .htUIInput.htUIButton input{background-color:var(--ht-secondary-button-background-color);border:1px solid var(--ht-secondary-button-border-color);border-radius:var(--ht-button-border-radius);box-sizing:border-box;color:var(--ht-secondary-button-foreground-color);cursor:pointer;font-family:inherit;font-size:var(--ht-font-size);font-weight:var(--ht-font-weight);line-height:var(--ht-line-height);min-width:64px;outline:none!important;padding:var(--ht-button-vertical-padding) var(--ht-button-horizontal-padding);transition:var(--ht-table-transition) all ease-in-out}.handsontable .htUIInput.htUIButton input:hover{background-color:var(--ht-secondary-button-hover-background-color);border-color:var(--ht-secondary-button-hover-border-color);color:var(--ht-secondary-button-hover-foreground-color)}.handsontable .htUIInput.htUIButton input:disabled{background-color:var(--ht-secondary-button-disabled-background-color);border-color:var(--ht-secondary-button-disabled-border-color);color:var(--ht-secondary-button-disabled-foreground-color)}.handsontable .htUIInput.htUIButton input:focus{background-color:var(--ht-secondary-button-focus-background-color);border-color:var(--ht-secondary-button-focus-border-color);box-shadow:0 0 0 1px var(--ht-accent-color);color:var(--ht-secondary-button-focus-foreground-color)}.handsontable .htUIInput.htUIButtonOK{margin-inline-end:calc(var(--ht-gap-size, 2px)*2);margin-inline-start:0}.handsontable .htUIInput.htUIButtonOK input{background-color:var(--ht-primary-button-background-color);border-color:var(--ht-primary-button-border-color);color:var(--ht-primary-button-foreground-color)}.handsontable .htUIInput.htUIButtonOK input:hover{background-color:var(--ht-primary-button-hover-background-color);border-color:var(--ht-primary-button-hover-border-color);color:var(--ht-primary-button-hover-foreground-color)}.handsontable .htUIInput.htUIButtonOK input:disabled{background-color:var(--ht-primary-button-disabled-background-color);border-color:var(--ht-primary-button-disabled-border-color);color:var(--ht-primary-button-disabled-foreground-color)}.handsontable .htUIInput.htUIButtonOK input:focus{background-color:var(--ht-primary-button-focus-background-color);border-color:var(--ht-primary-button-focus-border-color);box-shadow:0 0 0 1px var(--ht-accent-color);color:var(--ht-primary-button-focus-foreground-color)}.handsontable .htUIRadio{align-items:center;cursor:pointer;display:inline-flex;gap:var(--ht-gap-size);margin:0;padding:var(--ht-gap-size) calc(var(--ht-gap-size)*2);padding-bottom:0;position:relative}.handsontable .htUIRadio:first-child{padding-inline-start:0}.handsontable .htUIRadio>input[type=radio]{appearance:none;cursor:pointer;height:var(--ht-checkbox-size);margin:0;position:relative;width:var(--ht-checkbox-size)}.handsontable .htUIRadio>input[type=radio]:before{background-color:var(--ht-checkbox-background-color);border:1px solid var(--ht-checkbox-border-color);border-radius:calc(var(--ht-checkbox-size)*.5);content:"";display:block;inset:0;position:absolute}.handsontable .htUIRadio>input[type=radio]:after{color:var(--ht-checkbox-background-color);content:"";display:block;position:absolute}.handsontable .htUIRadio>input[type=radio]:checked:before{border-color:var(--ht-checkbox-checked-border-color)}.handsontable .htUIRadio>input[type=radio]:checked:after{color:var(--ht-checkbox-checked-background-color)}.handsontable .htUIRadio>input[type=radio]:checked:disabled{cursor:auto}.handsontable .htUIRadio>input[type=radio]:checked:disabled:before{border-color:var(--ht-checkbox-checked-disabled-border-color)}.handsontable .htUIRadio>input[type=radio]:checked:disabled:after{color:var(--ht-checkbox-checked-disabled-background-color)}.handsontable .htUIRadio>input[type=radio]:checked:focus:before{border-color:var(--ht-checkbox-checked-focus-background-color)}.handsontable .htUIRadio>input[type=radio]:checked:focus:after{color:var(--ht-checkbox-checked-focus-background-color)}.handsontable .htUIRadio>input[type=radio]:disabled{cursor:auto}.handsontable .htUIRadio>input[type=radio]:disabled:before{border-color:var(--ht-checkbox-disabled-border-color)}.handsontable .htUIRadio>input[type=radio]:disabled:after{color:var(--ht-checkbox-disabled-background-color)}.handsontable .htUIRadio>input[type=radio]:focus:before{border-color:var(--ht-checkbox-focus-background-color)}.handsontable .htUIRadio>input[type=radio]:focus:after{color:var(--ht-checkbox-focus-background-color)}.handsontable .htUIRadio label{color:inherit;cursor:pointer;vertical-align:middle}.handsontable .htUIRadio label:before{content:"";display:block;inset:0;position:absolute}.handsontable .collapsibleIndicator{margin-inline-start:var(--ht-gap-size);top:calc((var(--ht-line-height) - var(--ht-icon-size))/2)}.handsontable .collapsibleIndicator,.handsontable .ht_nestingButton{border-radius:var(--ht-collapse-button-border-radius);box-sizing:border-box;cursor:pointer;float:right;font-size:0;height:var(--ht-icon-size);overflow:hidden;position:relative;text-indent:-100px;width:var(--ht-icon-size)}.handsontable .collapsibleIndicator:before,.handsontable .ht_nestingButton:before{content:"";display:block;left:0;position:absolute;top:0;transition:var(--ht-table-transition) all ease-in-out}.handsontable .collapsibleIndicator.expanded,.handsontable .collapsibleIndicator.ht_nestingCollapse,.handsontable .ht_nestingButton.expanded,.handsontable .ht_nestingButton.ht_nestingCollapse{background-color:var(--ht-collapse-button-open-background-color);box-shadow:0 0 0 1px var(--ht-collapse-button-open-border-color)}.handsontable .collapsibleIndicator.expanded:before,.handsontable .collapsibleIndicator.ht_nestingCollapse:before,.handsontable .ht_nestingButton.expanded:before,.handsontable .ht_nestingButton.ht_nestingCollapse:before{color:var(--ht-collapse-button-open-icon-color)}.handsontable .collapsibleIndicator.expanded:hover,.handsontable .collapsibleIndicator.ht_nestingCollapse:hover,.handsontable .ht_nestingButton.expanded:hover,.handsontable .ht_nestingButton.ht_nestingCollapse:hover{background-color:var(--ht-collapse-button-open-hover-background-color);box-shadow:0 0 0 1px var(--ht-collapse-button-open-hover-border-color)}.handsontable .collapsibleIndicator.expanded:hover:before,.handsontable .collapsibleIndicator.ht_nestingCollapse:hover:before,.handsontable .ht_nestingButton.expanded:hover:before,.handsontable .ht_nestingButton.ht_nestingCollapse:hover:before{color:var(--ht-collapse-button-open-hover-icon-color)}.handsontable .collapsibleIndicator.collapsed,.handsontable .collapsibleIndicator.ht_nestingExpand,.handsontable .ht_nestingButton.collapsed,.handsontable .ht_nestingButton.ht_nestingExpand{background-color:var(--ht-collapse-button-close-background-color);box-shadow:0 0 0 1px var(--ht-collapse-button-close-border-color)}.handsontable .collapsibleIndicator.collapsed:before,.handsontable .collapsibleIndicator.ht_nestingExpand:before,.handsontable .ht_nestingButton.collapsed:before,.handsontable .ht_nestingButton.ht_nestingExpand:before{color:var(--ht-collapse-button-close-icon-color)}.handsontable .collapsibleIndicator.collapsed:hover,.handsontable .collapsibleIndicator.ht_nestingExpand:hover,.handsontable .ht_nestingButton.collapsed:hover,.handsontable .ht_nestingButton.ht_nestingExpand:hover{background-color:var(--ht-collapse-button-close-hover-background-color);box-shadow:0 0 0 1px var(--ht-collapse-button-close-hover-border-color)}.handsontable .collapsibleIndicator.collapsed:hover:before,.handsontable .collapsibleIndicator.ht_nestingExpand:hover:before,.handsontable .ht_nestingButton.collapsed:hover:before,.handsontable .ht_nestingButton.ht_nestingExpand:hover:before{color:var(--ht-collapse-button-close-hover-icon-color)}.handsontable th.ht__active_highlight .collapsibleIndicator.collapsed:before,.handsontable th.ht__active_highlight .collapsibleIndicator.ht_nestingExpand:before,.handsontable th.ht__active_highlight .ht_nestingButton.collapsed:before,.handsontable th.ht__active_highlight .ht_nestingButton.ht_nestingExpand:before{color:var(--ht-collapse-button-close-icon-active-color)}.handsontable th.ht__active_highlight .collapsibleIndicator.collapsed:hover:before,.handsontable th.ht__active_highlight .collapsibleIndicator.ht_nestingExpand:hover:before,.handsontable th.ht__active_highlight .ht_nestingButton.collapsed:hover:before,.handsontable th.ht__active_highlight .ht_nestingButton.ht_nestingExpand:hover:before{color:var(--ht-collapse-button-close-hover-icon-active-color)}.handsontable th.ht__active_highlight .collapsibleIndicator.expanded:before,.handsontable th.ht__active_highlight .collapsibleIndicator.ht_nestingCollapse:before,.handsontable th.ht__active_highlight .ht_nestingButton.expanded:before,.handsontable th.ht__active_highlight .ht_nestingButton.ht_nestingCollapse:before{color:var(--ht-collapse-button-open-icon-active-color)}.handsontable th.ht__active_highlight .collapsibleIndicator.expanded:hover:before,.handsontable th.ht__active_highlight .collapsibleIndicator.ht_nestingCollapse:hover:before,.handsontable th.ht__active_highlight .ht_nestingButton.expanded:hover:before,.handsontable th.ht__active_highlight .ht_nestingButton.ht_nestingCollapse:hover:before{color:var(--ht-collapse-button-open-hover-icon-active-color)}[dir=rtl].handsontable .collapsibleIndicator,[dir=rtl].handsontable .ht_nestingButton{float:left}.handsontable thead th.hiddenHeader:not(:first-of-type){display:none}.handsontable thead th.hiddenHeaderText .colHeader{opacity:0}.handsontable th.ht_nestingLevels>.relative{align-items:center;display:flex;gap:5px}.handsontable th.ht_nestingLevels span:last-child{padding-left:calc(var(--ht-icon-size) + 5px)}.handsontable th.ht_nestingLevels span.ht_nestingLevel{display:inline-block}.handsontable th.ht_nestingLevels span.ht_nestingLevel_empty{display:inline-block;height:1px;order:-2;position:relative;width:5px}.handsontable th.ht_nestingLevels .ht_nestingButton{cursor:pointer;order:-1;position:relative}.handsontable th.afterHiddenColumn,.handsontable th.beforeHiddenColumn{position:relative}.handsontable th.afterHiddenColumn:after,.handsontable th.afterHiddenColumn:before,.handsontable th.beforeHiddenColumn:after,.handsontable th.beforeHiddenColumn:before{color:var(--ht-hidden-indicator-color);height:10px!important;position:absolute;top:50%;transform:translateY(-50%);width:10px!important}.handsontable th.beforeHiddenColumn:after{content:"";display:block;right:-2px}.handsontable th.afterHiddenColumn:before{content:"";display:block;left:-2px}.handsontable th.ht__active_highlight.afterHiddenColumn:after,.handsontable th.ht__active_highlight.afterHiddenColumn:before,.handsontable th.ht__active_highlight.beforeHiddenColumn:after,.handsontable th.ht__active_highlight.beforeHiddenColumn:before{color:var(--ht-icon-active-button-icon-color)}[dir=rtl].handsontable th.beforeHiddenColumn:after{left:-2px;right:auto;transform:translateY(-50%) rotate(180deg)}[dir=rtl].handsontable th.afterHiddenColumn:before{left:auto;right:-2px;transform:translateY(-50%) rotate(180deg)}.handsontable th.afterHiddenRow,.handsontable th.beforeHiddenRow{position:relative}.handsontable th.afterHiddenRow:after,.handsontable th.afterHiddenRow:before,.handsontable th.beforeHiddenRow:after,.handsontable th.beforeHiddenRow:before{color:var(--ht-hidden-indicator-color);height:10px!important;left:50%;position:absolute;transform:translate(-50%);width:10px!important}.handsontable th.beforeHiddenRow:after{bottom:-2px;content:"";display:block}.handsontable th.afterHiddenRow:before{content:"";display:block;top:-2px}.handsontable th.ht__active_highlight.afterHiddenRow:after,.handsontable th.ht__active_highlight.afterHiddenRow:before,.handsontable th.ht__active_highlight.beforeHiddenRow:after,.handsontable th.ht__active_highlight.beforeHiddenRow:before{color:var(--ht-icon-active-button-icon-color)}.handsontable .changeType{background-color:var(--ht-icon-button-background-color);border:none;border-radius:var(--ht-icon-button-border-radius);box-shadow:0 0 0 1px var(--ht-icon-button-border-color);box-sizing:border-box;color:inherit;float:right;height:var(--ht-icon-size);margin:0;margin-inline-start:var(--ht-gap-size);order:1;padding:0;position:relative;top:calc((var(--ht-line-height) - var(--ht-icon-size))/2);width:var(--ht-icon-size);z-index:1}.handsontable .changeType:before{color:var(--ht-icon-button-icon-color);content:"";display:block;left:0;position:absolute;top:0}.handsontable .changeType:hover{background-color:var(--ht-icon-button-hover-background-color);box-shadow:0 0 0 1px var(--ht-icon-button-hover-border-color);cursor:pointer}.handsontable .changeType:hover:before{color:var(--ht-icon-button-hover-icon-color)}.handsontable .ht__active_highlight .changeType{background-color:var(--ht-icon-active-button-background-color);box-shadow:0 0 0 1px var(--ht-icon-active-button-border-color)}.handsontable .ht__active_highlight .changeType:before{color:var(--ht-icon-active-button-icon-color)}.handsontable .ht__active_highlight .changeType:hover{background-color:var(--ht-icon-active-button-hover-background-color);box-shadow:0 0 0 1px var(--ht-icon-active-button-hover-border-color)}.handsontable .ht__active_highlight .changeType:hover:before{color:var(--ht-icon-active-button-hover-icon-color)}.handsontable th.htFiltersActive{background-color:var(--ht-header-filter-background-color)}.handsontable th.htFiltersActive.ht__active_highlight{background-color:color-mix(in srgb,var(--ht-header-active-background-color),var(--ht-header-filter-background-color) 20%)}.handsontable th.htFiltersActive.ht__active_highlight .changeType:after{background-color:var(--ht-header-active-foreground-color)}.handsontable.htContextMenu>.ht_master,.handsontable.htDropdownMenu>.ht_master,.handsontable.htFiltersConditionsMenu>.ht_master{background-color:var(--ht-background-color,#fff);border:var(--ht-menu-border-width,1px) solid var(--ht-menu-border-color,#e5e5e9);border-radius:var(--ht-menu-border-radius);box-shadow:var(--ht-menu-shadow-x,0) var(--ht-menu-shadow-y,0) var(--ht-menu-shadow-blur,8px) var(--ht-menu-shadow-color,rgba(0,0,0,.08));overflow:hidden;padding:var(--ht-menu-vertical-padding,8px) var(--ht-menu-horizontal-padding,0)}.handsontable.htContextMenu:not(.htGhostTable),.handsontable.htDropdownMenu:not(.htGhostTable),.handsontable.htFiltersConditionsMenu:not(.htGhostTable){display:none;position:absolute;z-index:1060}.handsontable.htContextMenu .ht_clone_bottom,.handsontable.htContextMenu .ht_clone_bottom_inline_start_corner,.handsontable.htContextMenu .ht_clone_inline_start,.handsontable.htContextMenu .ht_clone_top,.handsontable.htContextMenu .ht_clone_top_inline_start_corner,.handsontable.htDropdownMenu .ht_clone_bottom,.handsontable.htDropdownMenu .ht_clone_bottom_inline_start_corner,.handsontable.htDropdownMenu .ht_clone_inline_start,.handsontable.htDropdownMenu .ht_clone_top,.handsontable.htDropdownMenu .ht_clone_top_inline_start_corner,.handsontable.htFiltersConditionsMenu .ht_clone_bottom,.handsontable.htFiltersConditionsMenu .ht_clone_bottom_inline_start_corner,.handsontable.htFiltersConditionsMenu .ht_clone_inline_start,.handsontable.htFiltersConditionsMenu .ht_clone_top,.handsontable.htFiltersConditionsMenu .ht_clone_top_inline_start_corner{display:none}.handsontable.htContextMenu table.htCore,.handsontable.htDropdownMenu table.htCore,.handsontable.htFiltersConditionsMenu table.htCore{overflow:hidden}.handsontable.htContextMenu .htCustomMenuRenderer .table.htCore,.handsontable.htDropdownMenu .htCustomMenuRenderer .table.htCore,.handsontable.htFiltersConditionsMenu .htCustomMenuRenderer .table.htCore{box-shadow:none}.handsontable.htContextMenu.handsontable:focus,.handsontable.htDropdownMenu.handsontable:focus,.handsontable.htFiltersConditionsMenu.handsontable:focus{outline:none}.handsontable.htContextMenu .wtBorder,.handsontable.htDropdownMenu .wtBorder,.handsontable.htFiltersConditionsMenu .wtBorder{visibility:hidden}.handsontable.htContextMenu table tbody tr td,.handsontable.htDropdownMenu table tbody tr td,.handsontable.htFiltersConditionsMenu table tbody tr td{background:var(--ht-background-color,#fff);border-width:0;cursor:pointer;overflow:hidden;padding:var(--ht-menu-item-vertical-padding,4px) var(--ht-menu-item-horizontal-padding,8px);position:relative;text-overflow:ellipsis;transition:background calc(var(--ht-table-transition)*.5) ease-in-out;white-space:nowrap}.handsontable.htContextMenu table tbody tr td:first-child,.handsontable.htDropdownMenu table tbody tr td:first-child,.handsontable.htFiltersConditionsMenu table tbody tr td:first-child{border-bottom-width:0;border-inline-end-width:0;border-inline-start-width:0;border-top-width:0}.handsontable.htContextMenu table tbody tr td.htDimmed,.handsontable.htDropdownMenu table tbody tr td.htDimmed,.handsontable.htFiltersConditionsMenu table tbody tr td.htDimmed{color:var(--ht-foreground-color);font-style:normal}.handsontable.htContextMenu table tbody tr td:not(.handsontable.htDropdownMenu table tbody tr td.htCustomMenuRenderer,.handsontable.htDropdownMenu table tbody tr td.htDisabled,.handsontable.htContextMenu table tbody tr td.htCustomMenuRenderer,.handsontable.htContextMenu table tbody tr td.htDisabled,.handsontable.htFiltersConditionsMenu table tbody tr td.htCustomMenuRenderer,.handsontable.htFiltersConditionsMenu table tbody tr td.htDisabled).current,.handsontable.htContextMenu table tbody tr td:not(.handsontable.htDropdownMenu table tbody tr td.htCustomMenuRenderer,.handsontable.htDropdownMenu table tbody tr td.htDisabled,.handsontable.htContextMenu table tbody tr td.htCustomMenuRenderer,.handsontable.htContextMenu table tbody tr td.htDisabled,.handsontable.htFiltersConditionsMenu table tbody tr td.htCustomMenuRenderer,.handsontable.htFiltersConditionsMenu table tbody tr td.htDisabled):hover,.handsontable.htDropdownMenu table tbody tr td:not(.handsontable.htDropdownMenu table tbody tr td.htCustomMenuRenderer,.handsontable.htDropdownMenu table tbody tr td.htDisabled,.handsontable.htContextMenu table tbody tr td.htCustomMenuRenderer,.handsontable.htContextMenu table tbody tr td.htDisabled,.handsontable.htFiltersConditionsMenu table tbody tr td.htCustomMenuRenderer,.handsontable.htFiltersConditionsMenu table tbody tr td.htDisabled).current,.handsontable.htDropdownMenu table tbody tr td:not(.handsontable.htDropdownMenu table tbody tr td.htCustomMenuRenderer,.handsontable.htDropdownMenu table tbody tr td.htDisabled,.handsontable.htContextMenu table tbody tr td.htCustomMenuRenderer,.handsontable.htContextMenu table tbody tr td.htDisabled,.handsontable.htFiltersConditionsMenu table tbody tr td.htCustomMenuRenderer,.handsontable.htFiltersConditionsMenu table tbody tr td.htDisabled):hover,.handsontable.htFiltersConditionsMenu table tbody tr td:not(.handsontable.htDropdownMenu table tbody tr td.htCustomMenuRenderer,.handsontable.htDropdownMenu table tbody tr td.htDisabled,.handsontable.htContextMenu table tbody tr td.htCustomMenuRenderer,.handsontable.htContextMenu table tbody tr td.htDisabled,.handsontable.htFiltersConditionsMenu table tbody tr td.htCustomMenuRenderer,.handsontable.htFiltersConditionsMenu table tbody tr td.htDisabled).current,.handsontable.htFiltersConditionsMenu table tbody tr td:not(.handsontable.htDropdownMenu table tbody tr td.htCustomMenuRenderer,.handsontable.htDropdownMenu table tbody tr td.htDisabled,.handsontable.htContextMenu table tbody tr td.htCustomMenuRenderer,.handsontable.htContextMenu table tbody tr td.htDisabled,.handsontable.htFiltersConditionsMenu table tbody tr td.htCustomMenuRenderer,.handsontable.htFiltersConditionsMenu table tbody tr td.htDisabled):hover{background:var(--ht-menu-item-hover-color,#e5e5e9);cursor:pointer}.handsontable.htContextMenu table tbody tr td.htSubmenu .htItemWrapper,.handsontable.htDropdownMenu table tbody tr td.htSubmenu .htItemWrapper,.handsontable.htFiltersConditionsMenu table tbody tr td.htSubmenu .htItemWrapper{margin-inline-end:calc(var(--ht-gap-size, 4px)*2 + var(--ht-icon-size))}.handsontable.htContextMenu table tbody tr td.htSubmenu .htItemWrapper:after,.handsontable.htDropdownMenu table tbody tr td.htSubmenu .htItemWrapper:after,.handsontable.htFiltersConditionsMenu table tbody tr td.htSubmenu .htItemWrapper:after{content:"";display:block;position:absolute;right:calc(var(--ht-menu-item-horizontal-padding) + var(--ht-gap-size)*2);top:50%;transform:translateY(-50%)}.handsontable.htContextMenu table tbody tr td.htSeparator,.handsontable.htDropdownMenu table tbody tr td.htSeparator,.handsontable.htFiltersConditionsMenu table tbody tr td.htSeparator{border-top:1px solid var(--ht-border-color);cursor:default;height:0;padding:0}.handsontable.htContextMenu table tbody tr td.htDisabled,.handsontable.htDropdownMenu table tbody tr td.htDisabled,.handsontable.htFiltersConditionsMenu table tbody tr td.htDisabled{color:var(--ht-disabled-color);cursor:default}.handsontable.htContextMenu table tbody tr td.htDisabled:hover,.handsontable.htDropdownMenu table tbody tr td.htDisabled:hover,.handsontable.htFiltersConditionsMenu table tbody tr td.htDisabled:hover{background:var(--ht-background-color);color:var(--ht-disabled-color)}.handsontable.htContextMenu table tbody tr td.htHidden,.handsontable.htDropdownMenu table tbody tr td.htHidden,.handsontable.htFiltersConditionsMenu table tbody tr td.htHidden{display:none}.handsontable.htContextMenu table tbody tr td:has(.htItemWrapper .selected),.handsontable.htDropdownMenu table tbody tr td:has(.htItemWrapper .selected),.handsontable.htFiltersConditionsMenu table tbody tr td:has(.htItemWrapper .selected){background-color:var(--ht-menu-item-active-color)}.handsontable.htContextMenu table tbody tr td .htItemWrapper,.handsontable.htDropdownMenu table tbody tr td .htItemWrapper,.handsontable.htFiltersConditionsMenu table tbody tr td .htItemWrapper{margin-inline:calc(var(--ht-gap-size, 4px)*2)}.handsontable.htContextMenu table tbody tr td .htItemWrapper:before,.handsontable.htDropdownMenu table tbody tr td .htItemWrapper:before,.handsontable.htFiltersConditionsMenu table tbody tr td .htItemWrapper:before{margin-right:12px;vertical-align:text-bottom}.handsontable.htContextMenu table tbody tr td .htItemWrapper span.selected,.handsontable.htDropdownMenu table tbody tr td .htItemWrapper span.selected,.handsontable.htFiltersConditionsMenu table tbody tr td .htItemWrapper span.selected{font-size:0;inset:0}.handsontable.htContextMenu table tbody tr td .htItemWrapper span.selected:after,.handsontable.htDropdownMenu table tbody tr td .htItemWrapper span.selected:after,.handsontable.htFiltersConditionsMenu table tbody tr td .htItemWrapper span.selected:after{color:var(--ht-accent-color);content:"";display:block;position:absolute;right:calc(var(--ht-menu-item-horizontal-padding) + var(--ht-gap-size)*2);top:50%;transform:translateY(-50%)}.handsontable.htContextMenu table tbody tr td div span.selected,.handsontable.htDropdownMenu table tbody tr td div span.selected,.handsontable.htFiltersConditionsMenu table tbody tr td div span.selected{inset-inline-end:0;position:absolute}.handsontable.htContextMenu table tbody tr td .htUIButton,.handsontable.htDropdownMenu table tbody tr td .htUIButton,.handsontable.htFiltersConditionsMenu table tbody tr td .htUIButton{width:calc(50% - var(--ht-gap-size))}.handsontable.htContextMenu table tbody tr td .htUIButton input,.handsontable.htDropdownMenu table tbody tr td .htUIButton input,.handsontable.htFiltersConditionsMenu table tbody tr td .htUIButton input{width:100%}.handsontable .htUIClearAll a,.handsontable .htUISelectAll a{display:inline-block;padding:var(--ht-gap-size);text-decoration:none}.handsontable .htUIMultipleSelect .ht_master .wtHolder{background:transparent;overflow-y:scroll!important}.handsontable .htUIMultipleSelect .ht_master .wtHolder .htCore{box-shadow:none}[dir=rtl].handsontable.htContextMenu table tbody tr td.htSubmenu .htItemWrapper:after,[dir=rtl].handsontable.htDropdownMenu table tbody tr td.htSubmenu .htItemWrapper:after,[dir=rtl].handsontable.htFiltersConditionsMenu table tbody tr td.htSubmenu .htItemWrapper:after{left:calc(var(--ht-gap-size)*4);right:auto;transform:translateY(-50%) rotate(180deg)}[dir=rtl].handsontable.htContextMenu table tbody tr td .htItemWrapper span.selected:after,[dir=rtl].handsontable.htDropdownMenu table tbody tr td .htItemWrapper span.selected:after,[dir=rtl].handsontable.htFiltersConditionsMenu table tbody tr td .htItemWrapper span.selected:after{left:calc(var(--ht-gap-size)*4);right:auto}[dir=rtl].handsontable .changeType{float:left}.handsontable.htFiltersConditionsMenu:not(.htGhostTable){z-index:1070}.handsontable .ht_master table td.htCustomMenuRenderer{cursor:auto}.handsontable .ht_master table td.htCustomMenuRenderer.htFiltersMenuActionBar{padding-top:calc(var(--ht-menu-item-vertical-padding, 4px)*2)}.handsontable .htFiltersMenuLabel{font-size:calc(var(--ht-font-size) - 2px);font-weight:var(--ht-font-weight);line-height:calc(var(--ht-line-height) - 4px);margin-bottom:calc(var(--ht-gap-size, 2px)*2)}.handsontable .htFiltersMenuLabel:empty{display:none}.handsontable .htFiltersMenuCondition{padding-bottom:0!important;padding-top:calc(var(--ht-gap-size, 2px)*3)!important}.handsontable .htFiltersMenuCondition .border{border-bottom:1px solid var(--ht-border-color)!important}.handsontable .htFiltersMenuCondition .htUIInput{margin-top:calc(var(--ht-gap-size, 2px)*2)}.handsontable .htFiltersMenuValue{border-bottom:1px solid var(--ht-border-color)!important;padding-bottom:0!important;padding-top:calc(var(--ht-gap-size, 2px)*3)!important}.handsontable .htFiltersMenuOperators{padding-bottom:0!important;padding-top:calc(var(--ht-gap-size, 2px)*2)!important}.handsontable .htUISelectionControls{margin:0 calc(var(--ht-gap-size, 2px)*-1);padding-bottom:var(--ht-gap-size,2px)!important;padding-top:calc(var(--ht-gap-size, 2px)*2)!important}.handsontable .htUIMultipleSelectHot{--ht-cell-horizontal-padding:calc(var(--ht-menu-item-horizontal-padding, 2px) + var(--ht-gap-size, 2px)*2);margin:0 calc((var(--ht-menu-item-horizontal-padding, 2px) + var(--ht-gap-size, 2px)*2)*-1);overflow:initial!important;width:calc(100% + var(--ht-menu-item-horizontal-padding, 2px)*2 + var(--ht-gap-size, 2px)*4)}.handsontable .htUIMultipleSelectHot:before{background-color:var(--ht-border-color);content:"";display:block;height:1px;left:0;position:absolute;top:-1px;width:100%}.handsontable .htUIMultipleSelectHot .wtHolder{box-sizing:border-box;overflow-x:hidden;padding:0}.handsontable .htUIMultipleSelectHot .wtHolder .wtSpreader{padding:calc(var(--ht-gap-size)*2) 0}.handsontable .htUIMultipleSelectHot .wtHolder td{height:auto;padding:4px var(--ht-cell-horizontal-padding)}.handsontable .htUIClearAll,.handsontable .htUISelectAll{display:inline-block;margin-bottom:var(--ht-gap-size,4px);margin-right:var(--ht-gap-size,4px)}.handsontable .htUIClearAll a,.handsontable .htUISelectAll a{border-radius:var(--ht-button-border-radius);color:var(--ht-link-color);font-size:var(--ht-font-size);line-height:var(--ht-line-height)}.handsontable .htUIClearAll a:hover,.handsontable .htUISelectAll a:hover{color:var(--ht-link-hover-color)}.handsontable .htUIClearAll a:focus,.handsontable .htUISelectAll a:focus{outline:1px solid var(--ht-link-hover-color)}.handsontable .htUIMultipleSelect .ht_master .wtHolder{overflow:auto}.handsontable .htUIMultipleSelect .ht_master .wtHolder .htCore,.handsontable .htUIMultipleSelect .ht_master .wtHolder .wtHider,.handsontable .htUIMultipleSelect .ht_master .wtHolder td,.handsontable .htUIMultipleSelect .ht_master .wtHolder tr{background:none}.handsontable .htCommentCell{position:relative}.handsontable .htCommentCell:after{border-inline-end:none;border-inline-start:var(--ht-comments-indicator-size,4px) solid transparent;border-top:var(--ht-comments-indicator-size,4px) solid var(--ht-comments-indicator-color,#1a42e8);content:"";display:block;inset-inline-end:0;inset-inline-start:unset;position:absolute;top:0}.htCommentsContainer .htComments{display:none;position:absolute;z-index:1059}.htCommentsContainer .htCommentTextArea{-webkit-appearance:none;background-color:var(--ht-comments-textarea-background-color,#fff);border:var(--ht-comments-textarea-border-width,1px) solid var(--ht-comments-textarea-border-color,transparent);border-inline-start:var(--ht-comments-textarea-border-width,1px) solid var(--ht-comments-textarea-border-color,#1a42e8);box-shadow:var(--ht-menu-shadow-x,0) var(--ht-menu-shadow-y,0) var(--ht-menu-shadow-blur,8px) var(--ht-menu-shadow-color,rgba(0,0,0,.08));box-sizing:border-box;color:var(--ht-comments-textarea-foreground-color);font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen,Ubuntu,Helvetica Neue,Arial,sans-serif;font-size:var(--ht-font-size);font-weight:var(--ht-font-weight);height:88px;line-height:var(--ht-line-height);margin:0 -1px;outline:0!important;padding:var(--ht-comments-textarea-vertical-padding) var(--ht-comments-textarea-horizontal-padding);width:240px}.htCommentsContainer .htCommentTextArea:focus{background-color:var(--ht-comments-textarea-focus-background-color,#fff);border:var(--ht-comments-textarea-focus-border-width,1px) solid var(--ht-comments-textarea-focus-border-color,#1a42e8);color:var(--ht-comments-textarea-focus-foreground-color)}.handsontable .columnSorting:not(.indicatorDisabled){position:relative}.handsontable .columnSorting:not(.indicatorDisabled).sortAction{max-width:calc(100% - var(--ht-icon-size, 16px)*2 - 4px);min-width:calc(var(--ht-icon-size, 16px) + 8px);padding-inline-end:calc(var(--ht-icon-size, 16px) + 2px);padding-inline-start:calc(var(--ht-icon-size, 16px) + 2px)}.handsontable .columnSorting:not(.indicatorDisabled).sortAction:hover{cursor:pointer;text-decoration:none}.handsontable .columnSorting:not(.indicatorDisabled).sortAction:before{content:"";display:block;position:absolute;right:2px;text-align:right;top:50%;transform:translateY(-50%);width:var(--ht-icon-size,16px)}.handsontable .htLeft .columnSorting.sortAction{padding-inline-start:0}.handsontable .htRight .columnSorting.sortAction{padding-inline-end:var(--ht-gap-size,4px);padding-inline-start:var(--ht-icon-size,16px)}.handsontable .htRight .columnSorting.sortAction:before,[dir=rtl] .handsontable .columnSorting.sortAction:before{left:2px;right:auto;text-align:left}[dir=rtl] .handsontable .htRight .columnSorting.sortAction{padding-inline-end:var(--ht-icon-size,16px);padding-inline-start:var(--ht-gap-size,4px)}[dir=rtl] .handsontable .htLeft .columnSorting.sortAction{padding-inline-end:var(--ht-gap-size,4px);padding-inline-start:var(--ht-icon-size,16px)}[dir=rtl] .handsontable .htLeft .columnSorting.sortAction:before{left:auto;right:2px;text-align:right}.htGhostTable .htCore span.colHeader.columnSorting:not(.indicatorDisabled):before{content:"*";display:inline-block;padding-right:20px;position:relative}.handsontable .colHeader.columnSorting:after{content:"";display:block;font-size:8px;margin-top:4px;position:absolute;right:0;text-align:right;top:50%;transform:translateY(-50%);width:8px}.handsontable .colHeader.columnSorting[class*=" sort-"]:after,.handsontable .colHeader.columnSorting[class^=sort-]:after{content:"+"}.handsontable .colHeader.columnSorting.sort-1:after{content:"1"}.handsontable .colHeader.columnSorting.sort-2:after{content:"2"}.handsontable .colHeader.columnSorting.sort-3:after{content:"3"}.handsontable .colHeader.columnSorting.sort-4:after{content:"4"}.handsontable .colHeader.columnSorting.sort-5:after{content:"5"}.handsontable .colHeader.columnSorting.sort-6:after{content:"6"}.handsontable .colHeader.columnSorting.sort-7:after{content:"7"}.handsontable .htRight .colHeader.columnSorting:after,[dir=rtl] .handsontable .colHeader.columnSorting:after{left:0;right:auto;text-align:left}[dir=rtl] .handsontable .htLeft .colHeader.columnSorting:after{left:auto;right:0;text-align:right}.htGhostTable th div button.changeType+span.colHeader.columnSorting:not(.indicatorDisabled){padding-right:var(--ht-gap-size)}.handsontable .htRowHeaders .ht_master.innerBorderInlineStart~.ht_clone_inline_start td:first-of-type,.handsontable .htRowHeaders .ht_master.innerBorderInlineStart~.ht_clone_top_inline_start_corner th:nth-child(2){border-left:0}.handsontable .ht_clone_top_inline_start_corner th.ht__active_highlight{box-shadow:none}.handsontable .manualColumnResizer{background:none;cursor:col-resize;margin:0;opacity:0;position:absolute;top:0;width:10px;z-index:210}.handsontable .manualColumnResizer:after,.handsontable .manualColumnResizer:before{background:var(--ht-resize-indicator-color);border-radius:2px;content:"";display:block;height:calc(100% - 12px);left:1px;max-height:16px;position:absolute;top:50%;transform:translateY(-50%);width:2px}.handsontable .manualColumnResizer:after{left:auto;right:0}.handsontable .manualColumnResizer.active,.handsontable .manualColumnResizer:hover{opacity:1}.handsontable .manualColumnResizerGuide{border-inline-end:1px solid var(--ht-accent-color);border-inline-start:none;display:none;inset-inline-end:unset;margin-inline-end:unset;margin-inline-start:5px;position:absolute;top:0;width:0}.handsontable .manualColumnResizerGuide.active{display:block;z-index:209}.handsontable .manualRowResizer{background:none;cursor:row-resize;height:10px;left:0;margin:0;opacity:0;position:absolute;z-index:210}.handsontable .manualRowResizer:after,.handsontable .manualRowResizer:before{background:var(--ht-resize-indicator-color);border-radius:2px;content:"";display:block;height:2px;left:50%;max-width:16px;position:absolute;top:1px;transform:translate(-50%);width:calc(100% - 12px)}.handsontable .manualRowResizer:after{bottom:0;top:auto}.handsontable .manualRowResizer.active,.handsontable .manualRowResizer:hover{opacity:1}.handsontable .manualRowResizerGuide{border-bottom:1px solid var(--ht-accent-color);border-top:none;bottom:0;display:none;height:0;left:0;margin-top:5px;position:absolute}.handsontable .manualRowResizerGuide.active{display:block;z-index:209}.handsontable tbody td[rowspan][class*=area][class*=highlight]:not([class*=fullySelectedMergedCell]):before{opacity:0}.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-0]:before,.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-multiple]:before{opacity:.14}.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-1]:before{opacity:.21}.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-2]:before{opacity:.28}.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-3]:before{opacity:.35}.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-4]:before{opacity:.42}.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-5]:before{opacity:.49}.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-6]:before{opacity:.56}.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-7]:before{opacity:.63}.handsontable .ht__manualColumnMove.after-selection--columns thead th.ht__highlight,.handsontable.ht__manualColumnMove.after-selection--columns thead th.ht__highlight{cursor:move;cursor:-moz-grab;cursor:-webkit-grab;cursor:grab}.handsontable .ht__manualColumnMove.on-moving--columns *,.handsontable .ht__manualColumnMove.on-moving--columns thead th.ht__highlight,.handsontable.ht__manualColumnMove.on-moving--columns *,.handsontable.ht__manualColumnMove.on-moving--columns thead th.ht__highlight{cursor:move;cursor:-moz-grabbing;cursor:-webkit-grabbing;cursor:grabbing}.handsontable .ht__manualColumnMove.on-moving--columns .manualColumnResizer,.handsontable.ht__manualColumnMove.on-moving--columns .manualColumnResizer{display:none}.handsontable .ht__manualColumnMove--backlight,.handsontable .ht__manualColumnMove--guideline,.handsontable.ht__manualColumnMove--backlight,.handsontable.ht__manualColumnMove--guideline{display:none;height:100%;position:absolute}.handsontable .ht__manualColumnMove--guideline,.handsontable.ht__manualColumnMove--guideline{background:var(--ht-move-indicator-color,#1a42e8);margin-inline-end:0;margin-inline-start:-.5px;top:0;width:1px;z-index:205}.handsontable .ht__manualColumnMove--backlight,.handsontable.ht__manualColumnMove--backlight{background:var(--ht-move-backlight-color);display:none;pointer-events:none;z-index:205}.handsontable .on-moving--columns .ht__manualColumnMove--backlight,.handsontable .on-moving--columns.show-ui .ht__manualColumnMove--guideline,.handsontable.on-moving--columns .ht__manualColumnMove--backlight,.handsontable.on-moving--columns.show-ui .ht__manualColumnMove--guideline{display:block}.handsontable .wtHider{position:relative}.handsontable .ht__manualRowMove.after-selection--rows tbody th.ht__highlight,.handsontable.ht__manualRowMove.after-selection--rows tbody th.ht__highlight{cursor:move;cursor:-moz-grab;cursor:-webkit-grab;cursor:grab}.handsontable .ht__manualRowMove.on-moving--rows *,.handsontable .ht__manualRowMove.on-moving--rows tbody th.ht__highlight,.handsontable.ht__manualRowMove.on-moving--rows *,.handsontable.ht__manualRowMove.on-moving--rows tbody th.ht__highlight{cursor:move;cursor:-moz-grabbing;cursor:-webkit-grabbing;cursor:grabbing}.handsontable .ht__manualRowMove.on-moving--rows .manualRowResizer,.handsontable.ht__manualRowMove.on-moving--rows .manualRowResizer{display:none}.handsontable .ht__manualRowMove--backlight,.handsontable .ht__manualRowMove--guideline,.handsontable.ht__manualRowMove--backlight,.handsontable.ht__manualRowMove--guideline{display:none;position:absolute;width:100%}.handsontable .ht__manualRowMove--guideline,.handsontable.ht__manualRowMove--guideline{background:var(--ht-move-indicator-color);height:1px;left:0;margin-top:-.5px;z-index:205}.handsontable .ht__manualRowMove--backlight,.handsontable.ht__manualRowMove--backlight{background:var(--ht-move-backlight-color);display:none;pointer-events:none;z-index:205}.handsontable .on-moving--rows .ht__manualRowMove--backlight,.handsontable .on-moving--rows.show-ui .ht__manualRowMove--guideline,.handsontable.on-moving--rows .ht__manualRowMove--backlight,.handsontable.on-moving--rows.show-ui .ht__manualRowMove--guideline{display:block}/*!
* Copyright (c) HANDSONCODE sp. z o. o.
*
* HANDSONTABLE is a software distributed by HANDSONCODE sp. z o. o., a Polish corporation based in
* Gdynia, Poland, at Aleja Zwyciestwa 96-98, registered by the District Court in Gdansk under number
* 538651, EU tax ID number: PL5862294002, share capital: PLN 62,800.00.
*
* This software is protected by applicable copyright laws, including international treaties, and dual-
* licensed - depending on whether your use for commercial purposes, meaning intended for or
* resulting in commercial advantage or monetary compensation, or not.
*
* If your use is strictly personal or solely for evaluation purposes, meaning for the purposes of testing
* the suitability, performance, and usefulness of this software outside the production environment,
* you agree to be bound by the terms included in the "handsontable-non-commercial-license.pdf" file.
*
* Your use of this software for commercial purposes is subject to the terms included in an applicable
* license agreement.
*
* In any case, you must not make any such use of this software as to develop software which may be
* considered competitive with this software.
*
* UNLESS EXPRESSLY AGREED OTHERWISE, HANDSONCODE PROVIDES THIS SOFTWARE ON AN "AS IS"
* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, AND IN NO EVENT AND UNDER NO
* LEGAL THEORY, SHALL HANDSONCODE BE LIABLE TO YOU FOR DAMAGES, INCLUDING ANY DIRECT,
* INDIRECT, SPECIAL, INCIDENTAL, OR CONSEQUENTIAL DAMAGES OF ANY CHARACTER ARISING FROM
* USE OR INABILITY TO USE THIS SOFTWARE.
*
* Version: 15.3.0
* Release date: 29/04/2025 (built at 24/04/2025 10:59:23)
*/.ht-theme-main,.ht-theme-main-dark,.ht-theme-main-dark-auto{--ht-font-size:14px;--ht-line-height:20px;--ht-font-weight:400;--ht-gap-size:4px;--ht-icon-size:16px;--ht-table-transition:.15s;--ht-border-color:#e7e7e9;--ht-accent-color:#1a42e8;--ht-foreground-color:#222;--ht-background-color:#fff;--ht-placeholder-color:#999;--ht-read-only-color:#a3a3a3;--ht-disabled-color:#a3a3a3;--ht-cell-horizontal-border-color:#e7e7e9;--ht-cell-vertical-border-color:#e7e7e9;--ht-wrapper-border-width:0;--ht-wrapper-border-radius:4px;--ht-wrapper-border-color:#e7e7e9;--ht-row-header-odd-background-color:#f7f7f9;--ht-row-header-even-background-color:#f7f7f9;--ht-row-cell-odd-background-color:rgba(34,34,34,0);--ht-row-cell-even-background-color:rgba(34,34,34,0);--ht-cell-horizontal-padding:8px;--ht-cell-vertical-padding:4px;--ht-cell-editor-border-width:2px;--ht-cell-editor-border-color:#1a42e8;--ht-cell-editor-foreground-color:#000;--ht-cell-editor-background-color:#fff;--ht-cell-editor-shadow-blur-radius:0;--ht-cell-editor-shadow-color:rgba(17,61,201,.6);--ht-cell-success-background-color:rgba(55,188,108,.2);--ht-cell-error-background-color:rgba(250,77,50,.2);--ht-cell-selection-border-color:#1a42e8;--ht-cell-selection-background-color:#5371ee;--ht-cell-autofill-size:6px;--ht-cell-autofill-border-width:1px;--ht-cell-autofill-border-radius:4px;--ht-cell-autofill-border-color:#fff;--ht-cell-autofill-background-color:#1a42e8;--ht-cell-autofill-fill-border-color:#222;--ht-cell-mobile-handle-size:12px;--ht-cell-mobile-handle-border-width:1px;--ht-cell-mobile-handle-border-radius:6px;--ht-cell-mobile-handle-border-color:#1a42e8;--ht-cell-mobile-handle-background-color:rgba(26,66,232,.4);--ht-resize-indicator-color:rgba(34,34,34,.4);--ht-move-backlight-color:rgba(34,34,34,.06);--ht-move-indicator-color:#1a42e8;--ht-hidden-indicator-color:rgba(34,34,34,.4);--ht-scrollbar-border-radius:8px;--ht-scrollbar-track-color:#f7f7f9;--ht-scrollbar-thumb-color:#999;--ht-checkbox-size:16px;--ht-checkbox-border-radius:4px;--ht-checkbox-border-color:#d1d1d5;--ht-checkbox-background-color:#fff;--ht-checkbox-icon-color:hsla(0,0%,100%,0);--ht-checkbox-focus-border-color:#d1d1d5;--ht-checkbox-focus-background-color:#fff;--ht-checkbox-focus-icon-color:hsla(0,0%,100%,0);--ht-checkbox-focus-ring-color:#1a42e8;--ht-checkbox-disabled-border-color:#e7e7e9;--ht-checkbox-disabled-background-color:#d1d1d5;--ht-checkbox-disabled-icon-color:hsla(0,0%,100%,0);--ht-checkbox-checked-border-color:#3f60ec;--ht-checkbox-checked-background-color:#1a42e8;--ht-checkbox-checked-icon-color:#fff;--ht-checkbox-checked-focus-border-color:#fff;--ht-checkbox-checked-focus-background-color:#1a42e8;--ht-checkbox-checked-focus-icon-color:#fff;--ht-checkbox-checked-disabled-border-color:#e7e7e9;--ht-checkbox-checked-disabled-background-color:#d1d1d5;--ht-checkbox-checked-disabled-icon-color:#a3a3a3;--ht-header-font-weight:400;--ht-header-foreground-color:#222;--ht-header-background-color:#f7f7f9;--ht-header-active-border-color:#2e56fc;--ht-header-active-foreground-color:#fff;--ht-header-active-background-color:#1a42e8;--ht-header-highlighted-shadow-size:0;--ht-header-highlighted-foreground-color:#000;--ht-header-highlighted-background-color:#ebebed;--ht-header-filter-background-color:rgba(55,188,108,.2);--ht-header-row-foreground-color:#222;--ht-header-row-background-color:#f7f7f9;--ht-header-row-highlighted-foreground-color:#000;--ht-header-row-highlighted-background-color:#ebebed;--ht-header-row-active-foreground-color:#fff;--ht-header-row-active-background-color:#1a42e8;--ht-icon-button-border-radius:2px;--ht-icon-button-border-color:#f7f7f9;--ht-icon-button-background-color:#f7f7f9;--ht-icon-button-icon-color:rgba(34,34,34,.4);--ht-icon-button-hover-border-color:#e7e7e8;--ht-icon-button-hover-background-color:#e7e7e8;--ht-icon-button-hover-icon-color:rgba(34,34,34,.4);--ht-icon-active-button-border-color:#2e56fc;--ht-icon-active-button-background-color:#1a42e8;--ht-icon-active-button-icon-color:#fff;--ht-icon-active-button-hover-border-color:#2e56fc;--ht-icon-active-button-hover-background-color:#3357ea;--ht-icon-active-button-hover-icon-color:#fff;--ht-collapse-button-border-radius:4px;--ht-collapse-button-open-border-color:#d1d1d5;--ht-collapse-button-open-background-color:#fff;--ht-collapse-button-open-icon-color:#666;--ht-collapse-button-open-icon-active-color:#666;--ht-collapse-button-open-hover-border-color:#d1d1d5;--ht-collapse-button-open-hover-background-color:#f7f7f9;--ht-collapse-button-open-hover-icon-color:#666;--ht-collapse-button-open-hover-icon-active-color:#666;--ht-collapse-button-close-border-color:#d1d1d5;--ht-collapse-button-close-background-color:#ebebed;--ht-collapse-button-close-icon-color:#666;--ht-collapse-button-close-icon-active-color:#666;--ht-collapse-button-close-hover-border-color:#d1d1d5;--ht-collapse-button-close-hover-background-color:#ebebed;--ht-collapse-button-close-hover-icon-color:#666;--ht-collapse-button-close-hover-icon-active-color:#666;--ht-button-border-radius:4px;--ht-button-horizontal-padding:12px;--ht-button-vertical-padding:6px;--ht-primary-button-border-color:hsla(0,0%,100%,0);--ht-primary-button-foreground-color:#fff;--ht-primary-button-background-color:#1a42e8;--ht-primary-button-disabled-border-color:hsla(0,0%,100%,0);--ht-primary-button-disabled-foreground-color:#a3a3a3;--ht-primary-button-disabled-background-color:hsla(0,0%,60%,.32);--ht-primary-button-hover-border-color:hsla(0,0%,100%,0);--ht-primary-button-hover-foreground-color:#fff;--ht-primary-button-hover-background-color:#1535bc;--ht-primary-button-focus-border-color:#fff;--ht-primary-button-focus-foreground-color:#fff;--ht-primary-button-focus-background-color:#1a42e8;--ht-secondary-button-border-color:#e7e7e9;--ht-secondary-button-foreground-color:#222;--ht-secondary-button-background-color:#fff;--ht-secondary-button-disabled-border-color:#e7e7e9;--ht-secondary-button-disabled-foreground-color:#a3a3a3;--ht-secondary-button-disabled-background-color:#ebebed;--ht-secondary-button-hover-border-color:#e7e7e9;--ht-secondary-button-hover-foreground-color:#222;--ht-secondary-button-hover-background-color:#fff;--ht-secondary-button-focus-border-color:#e7e7e9;--ht-secondary-button-focus-foreground-color:#222;--ht-secondary-button-focus-background-color:#fff;--ht-comments-textarea-horizontal-padding:8px;--ht-comments-textarea-vertical-padding:4px;--ht-comments-indicator-size:6px;--ht-comments-indicator-color:#1a42e8;--ht-comments-textarea-border-width:1px;--ht-comments-textarea-border-color:hsla(0,0%,100%,0);--ht-comments-textarea-foreground-color:#222;--ht-comments-textarea-background-color:#f7f7f9;--ht-comments-textarea-focus-border-width:1px;--ht-comments-textarea-focus-border-color:#1a42e8;--ht-comments-textarea-focus-foreground-color:#222;--ht-comments-textarea-focus-background-color:#fff;--ht-license-horizontal-padding:16px;--ht-license-vertical-padding:8px;--ht-license-foreground-color:#222;--ht-license-background-color:#f7f7f9;--ht-link-color:#1a42e8;--ht-link-hover-color:#3e67ec;--ht-input-border-width:1px;--ht-input-border-radius:4px;--ht-input-horizontal-padding:12px;--ht-input-vertical-padding:6px;--ht-input-border-color:#e7e7e9;--ht-input-foreground-color:#222;--ht-input-background-color:#f7f7f9;--ht-input-hover-border-color:#e7e7e9;--ht-input-hover-foreground-color:#222;--ht-input-hover-background-color:#fff;--ht-input-disabled-border-color:#e7e7e9;--ht-input-disabled-foreground-color:#a3a3a3;--ht-input-disabled-background-color:hsla(0,0%,60%,.32);--ht-input-focus-border-color:#1a42e8;--ht-input-focus-foreground-color:#222;--ht-input-focus-background-color:#fff;--ht-menu-border-width:1px;--ht-menu-border-radius:4px;--ht-menu-horizontal-padding:0;--ht-menu-vertical-padding:8px;--ht-menu-item-horizontal-padding:12px;--ht-menu-item-vertical-padding:4px;--ht-menu-shadow-x:0;--ht-menu-shadow-y:8px;--ht-menu-shadow-blur:16px;--ht-menu-border-color:#e7e7e9;--ht-menu-shadow-color:rgba(0,0,0,.08);--ht-menu-item-hover-color:rgba(0,0,0,.05);--ht-menu-item-active-color:rgba(0,0,0,.08)}.ht-theme-main-dark{--ht-border-color:#313132;--ht-accent-color:#476af7;--ht-foreground-color:#c7c7c7;--ht-background-color:#0f0f10;--ht-placeholder-color:#68696c;--ht-read-only-color:#68696c;--ht-disabled-color:#68696c;--ht-cell-horizontal-border-color:#313132;--ht-cell-vertical-border-color:#313132;--ht-wrapper-border-color:#313132;--ht-row-header-odd-background-color:#1e1e1f;--ht-row-header-even-background-color:#1e1e1f;--ht-row-cell-odd-background-color:rgba(239,239,243,0);--ht-row-cell-even-background-color:rgba(239,239,243,0);--ht-cell-editor-border-color:#476af7;--ht-cell-editor-foreground-color:#fff;--ht-cell-editor-background-color:#0f0f10;--ht-cell-editor-shadow-color:rgba(71,106,247,.6);--ht-cell-success-background-color:rgba(66,190,101,.3);--ht-cell-error-background-color:rgba(255,42,52,.3);--ht-cell-selection-border-color:#476af7;--ht-cell-selection-background-color:#476af7;--ht-cell-autofill-border-color:#0f0f10;--ht-cell-autofill-background-color:#476af7;--ht-cell-autofill-fill-border-color:#c7c7c7;--ht-cell-mobile-handle-border-color:#476af7;--ht-cell-mobile-handle-background-color:rgba(71,106,247,.4);--ht-resize-indicator-color:hsla(0,0%,100%,.4);--ht-move-backlight-color:hsla(0,0%,87%,.12);--ht-move-indicator-color:#476af7;--ht-hidden-indicator-color:hsla(0,0%,100%,.4);--ht-scrollbar-track-color:#19191c;--ht-scrollbar-thumb-color:#68696c;--ht-checkbox-border-color:#404144;--ht-checkbox-background-color:#0f0f10;--ht-checkbox-focus-border-color:#404144;--ht-checkbox-focus-background-color:#0f0f10;--ht-checkbox-focus-ring-color:#476af7;--ht-checkbox-disabled-border-color:#313132;--ht-checkbox-disabled-background-color:#404144;--ht-checkbox-checked-border-color:#3c59cf;--ht-checkbox-checked-background-color:#476af7;--ht-checkbox-checked-icon-color:#c7c7c7;--ht-checkbox-checked-focus-background-color:#476af7;--ht-checkbox-checked-focus-icon-color:#c7c7c7;--ht-checkbox-checked-disabled-border-color:#313132;--ht-checkbox-checked-disabled-background-color:#36373a;--ht-checkbox-checked-disabled-icon-color:#68696c;--ht-header-foreground-color:#c7c7c7;--ht-header-background-color:#1e1e1f;--ht-header-active-border-color:#4767e5;--ht-header-active-foreground-color:#0f0f10;--ht-header-active-background-color:#476af7;--ht-header-highlighted-foreground-color:#f3f3f3;--ht-header-highlighted-background-color:#262627;--ht-header-filter-background-color:rgba(66,190,101,.3);--ht-header-row-foreground-color:#c7c7c7;--ht-header-row-background-color:#1e1e1f;--ht-header-row-highlighted-foreground-color:#f3f3f3;--ht-header-row-highlighted-background-color:#262627;--ht-header-row-active-foreground-color:#0f0f10;--ht-header-row-active-background-color:#476af7;--ht-icon-button-border-color:#1e1e1f;--ht-icon-button-background-color:#1e1e1f;--ht-icon-button-icon-color:hsla(0,0%,100%,.4);--ht-icon-button-hover-border-color:#303031;--ht-icon-button-hover-background-color:#303031;--ht-icon-button-hover-icon-color:hsla(0,0%,100%,.4);--ht-icon-active-button-border-color:#4767e5;--ht-icon-active-button-background-color:#476af7;--ht-icon-active-button-icon-color:#0f0f10;--ht-icon-active-button-hover-border-color:#4767e5;--ht-icon-active-button-hover-background-color:#5576f8;--ht-icon-active-button-hover-icon-color:#0f0f10;--ht-collapse-button-open-border-color:rgba(83,83,85,.64);--ht-collapse-button-open-background-color:#000;--ht-collapse-button-open-icon-color:#999;--ht-collapse-button-open-icon-active-color:#999;--ht-collapse-button-open-hover-border-color:rgba(83,83,85,.64);--ht-collapse-button-open-hover-background-color:#1e1e1f;--ht-collapse-button-open-hover-icon-color:#999;--ht-collapse-button-open-hover-icon-active-color:#999;--ht-collapse-button-close-border-color:rgba(83,83,85,.64);--ht-collapse-button-close-background-color:#262627;--ht-collapse-button-close-icon-color:#999;--ht-collapse-button-close-icon-active-color:#999;--ht-collapse-button-close-hover-border-color:rgba(83,83,85,.64);--ht-collapse-button-close-hover-background-color:#262627;--ht-collapse-button-close-hover-icon-color:#999;--ht-collapse-button-close-hover-icon-active-color:#999;--ht-primary-button-foreground-color:#c7c7c7;--ht-primary-button-background-color:#476af7;--ht-primary-button-disabled-foreground-color:#68696c;--ht-primary-button-disabled-background-color:hsla(225,2%,42%,.32);--ht-primary-button-hover-foreground-color:#c7c7c7;--ht-primary-button-hover-background-color:#6180ff;--ht-primary-button-focus-border-color:#0f0f10;--ht-primary-button-focus-foreground-color:#c7c7c7;--ht-primary-button-focus-background-color:#476af7;--ht-secondary-button-border-color:#313132;--ht-secondary-button-foreground-color:#c7c7c7;--ht-secondary-button-background-color:#0f0f10;--ht-secondary-button-disabled-border-color:#313132;--ht-secondary-button-disabled-foreground-color:#68696c;--ht-secondary-button-disabled-background-color:#262627;--ht-secondary-button-hover-border-color:#313132;--ht-secondary-button-hover-foreground-color:#c7c7c7;--ht-secondary-button-hover-background-color:#0f0f10;--ht-secondary-button-focus-border-color:#313132;--ht-secondary-button-focus-foreground-color:#c7c7c7;--ht-secondary-button-focus-background-color:#0f0f10;--ht-comments-indicator-color:#476af7;--ht-comments-textarea-foreground-color:#c7c7c7;--ht-comments-textarea-background-color:#1e1e1f;--ht-comments-textarea-focus-border-color:#476af7;--ht-comments-textarea-focus-foreground-color:#c7c7c7;--ht-comments-textarea-focus-background-color:#0f0f10;--ht-license-foreground-color:#c7c7c7;--ht-license-background-color:#1e1e1f;--ht-link-color:#5576f8;--ht-link-hover-color:#728eff;--ht-input-border-color:#313132;--ht-input-foreground-color:#c7c7c7;--ht-input-background-color:#1e1e1f;--ht-input-hover-border-color:#313132;--ht-input-hover-foreground-color:#c7c7c7;--ht-input-hover-background-color:#0f0f10;--ht-input-disabled-border-color:#313132;--ht-input-disabled-foreground-color:#68696c;--ht-input-disabled-background-color:hsla(225,2%,42%,.32);--ht-input-focus-border-color:#476af7;--ht-input-focus-foreground-color:#c7c7c7;--ht-input-focus-background-color:#0f0f10;--ht-menu-border-color:#313132;--ht-menu-shadow-color:rgba(0,0,0,.8);--ht-menu-item-hover-color:rgba(239,239,243,.05);--ht-menu-item-active-color:rgba(239,239,243,.12)}@media (prefers-color-scheme:dark){.ht-theme-main-dark-auto{--ht-border-color:#313132;--ht-accent-color:#476af7;--ht-foreground-color:#c7c7c7;--ht-background-color:#0f0f10;--ht-placeholder-color:#68696c;--ht-read-only-color:#68696c;--ht-disabled-color:#68696c;--ht-cell-horizontal-border-color:#313132;--ht-cell-vertical-border-color:#313132;--ht-wrapper-border-color:#313132;--ht-row-header-odd-background-color:#1e1e1f;--ht-row-header-even-background-color:#1e1e1f;--ht-row-cell-odd-background-color:rgba(239,239,243,0);--ht-row-cell-even-background-color:rgba(239,239,243,0);--ht-cell-editor-border-color:#476af7;--ht-cell-editor-foreground-color:#fff;--ht-cell-editor-background-color:#0f0f10;--ht-cell-editor-shadow-color:rgba(71,106,247,.6);--ht-cell-success-background-color:rgba(66,190,101,.3);--ht-cell-error-background-color:rgba(255,42,52,.3);--ht-cell-selection-border-color:#476af7;--ht-cell-selection-background-color:#476af7;--ht-cell-autofill-border-color:#0f0f10;--ht-cell-autofill-background-color:#476af7;--ht-cell-autofill-fill-border-color:#c7c7c7;--ht-cell-mobile-handle-border-color:#476af7;--ht-cell-mobile-handle-background-color:rgba(71,106,247,.4);--ht-resize-indicator-color:hsla(0,0%,100%,.4);--ht-move-backlight-color:hsla(0,0%,87%,.12);--ht-move-indicator-color:#476af7;--ht-hidden-indicator-color:hsla(0,0%,100%,.4);--ht-scrollbar-track-color:#19191c;--ht-scrollbar-thumb-color:#68696c;--ht-checkbox-border-color:#404144;--ht-checkbox-background-color:#0f0f10;--ht-checkbox-focus-border-color:#404144;--ht-checkbox-focus-background-color:#0f0f10;--ht-checkbox-focus-ring-color:#476af7;--ht-checkbox-disabled-border-color:#313132;--ht-checkbox-disabled-background-color:#404144;--ht-checkbox-checked-border-color:#3c59cf;--ht-checkbox-checked-background-color:#476af7;--ht-checkbox-checked-icon-color:#c7c7c7;--ht-checkbox-checked-focus-background-color:#476af7;--ht-checkbox-checked-focus-icon-color:#c7c7c7;--ht-checkbox-checked-disabled-border-color:#313132;--ht-checkbox-checked-disabled-background-color:#36373a;--ht-checkbox-checked-disabled-icon-color:#68696c;--ht-header-foreground-color:#c7c7c7;--ht-header-background-color:#1e1e1f;--ht-header-active-border-color:#4767e5;--ht-header-active-foreground-color:#0f0f10;--ht-header-active-background-color:#476af7;--ht-header-highlighted-foreground-color:#f3f3f3;--ht-header-highlighted-background-color:#262627;--ht-header-filter-background-color:rgba(66,190,101,.3);--ht-header-row-foreground-color:#c7c7c7;--ht-header-row-background-color:#1e1e1f;--ht-header-row-highlighted-foreground-color:#f3f3f3;--ht-header-row-highlighted-background-color:#262627;--ht-header-row-active-foreground-color:#0f0f10;--ht-header-row-active-background-color:#476af7;--ht-icon-button-border-color:#1e1e1f;--ht-icon-button-background-color:#1e1e1f;--ht-icon-button-icon-color:hsla(0,0%,100%,.4);--ht-icon-button-hover-border-color:#303031;--ht-icon-button-hover-background-color:#303031;--ht-icon-button-hover-icon-color:hsla(0,0%,100%,.4);--ht-icon-active-button-border-color:#4767e5;--ht-icon-active-button-background-color:#476af7;--ht-icon-active-button-icon-color:#0f0f10;--ht-icon-active-button-hover-border-color:#4767e5;--ht-icon-active-button-hover-background-color:#5576f8;--ht-icon-active-button-hover-icon-color:#0f0f10;--ht-collapse-button-open-border-color:rgba(83,83,85,.64);--ht-collapse-button-open-background-color:#000;--ht-collapse-button-open-icon-color:#999;--ht-collapse-button-open-icon-active-color:#999;--ht-collapse-button-open-hover-border-color:rgba(83,83,85,.64);--ht-collapse-button-open-hover-background-color:#1e1e1f;--ht-collapse-button-open-hover-icon-color:#999;--ht-collapse-button-open-hover-icon-active-color:#999;--ht-collapse-button-close-border-color:rgba(83,83,85,.64);--ht-collapse-button-close-background-color:#262627;--ht-collapse-button-close-icon-color:#999;--ht-collapse-button-close-icon-active-color:#999;--ht-collapse-button-close-hover-border-color:rgba(83,83,85,.64);--ht-collapse-button-close-hover-background-color:#262627;--ht-collapse-button-close-hover-icon-color:#999;--ht-collapse-button-close-hover-icon-active-color:#999;--ht-primary-button-foreground-color:#c7c7c7;--ht-primary-button-background-color:#476af7;--ht-primary-button-disabled-foreground-color:#68696c;--ht-primary-button-disabled-background-color:hsla(225,2%,42%,.32);--ht-primary-button-hover-foreground-color:#c7c7c7;--ht-primary-button-hover-background-color:#6180ff;--ht-primary-button-focus-border-color:#0f0f10;--ht-primary-button-focus-foreground-color:#c7c7c7;--ht-primary-button-focus-background-color:#476af7;--ht-secondary-button-border-color:#313132;--ht-secondary-button-foreground-color:#c7c7c7;--ht-secondary-button-background-color:#0f0f10;--ht-secondary-button-disabled-border-color:#313132;--ht-secondary-button-disabled-foreground-color:#68696c;--ht-secondary-button-disabled-background-color:#262627;--ht-secondary-button-hover-border-color:#313132;--ht-secondary-button-hover-foreground-color:#c7c7c7;--ht-secondary-button-hover-background-color:#0f0f10;--ht-secondary-button-focus-border-color:#313132;--ht-secondary-button-focus-foreground-color:#c7c7c7;--ht-secondary-button-focus-background-color:#0f0f10;--ht-comments-indicator-color:#476af7;--ht-comments-textarea-foreground-color:#c7c7c7;--ht-comments-textarea-background-color:#1e1e1f;--ht-comments-textarea-focus-border-color:#476af7;--ht-comments-textarea-focus-foreground-color:#c7c7c7;--ht-comments-textarea-focus-background-color:#0f0f10;--ht-license-foreground-color:#c7c7c7;--ht-license-background-color:#1e1e1f;--ht-link-color:#5576f8;--ht-link-hover-color:#728eff;--ht-input-border-color:#313132;--ht-input-foreground-color:#c7c7c7;--ht-input-background-color:#1e1e1f;--ht-input-hover-border-color:#313132;--ht-input-hover-foreground-color:#c7c7c7;--ht-input-hover-background-color:#0f0f10;--ht-input-disabled-border-color:#313132;--ht-input-disabled-foreground-color:#68696c;--ht-input-disabled-background-color:hsla(225,2%,42%,.32);--ht-input-focus-border-color:#476af7;--ht-input-focus-foreground-color:#c7c7c7;--ht-input-focus-background-color:#0f0f10;--ht-menu-border-color:#313132;--ht-menu-shadow-color:rgba(0,0,0,.8);--ht-menu-item-hover-color:rgba(239,239,243,.05);--ht-menu-item-active-color:rgba(239,239,243,.12)}}[class*=ht-theme-main] .pika-single .pika-next,[class*=ht-theme-main].htContextMenu table tbody tr td.htSubmenu .htItemWrapper:after,[class*=ht-theme-main].htDropdownMenu table tbody tr td.htSubmenu .htItemWrapper:after,[class*=ht-theme-main].htFiltersConditionsMenu table tbody tr td.htSubmenu .htItemWrapper:after{background-color:currentColor;height:var(--ht-icon-size);-webkit-mask-image:url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none'%3E%3Cpath stroke='currentColor' stroke-linecap='round' stroke-linejoin='round' d='M6.646 10.94 9.94 7.645 6.646 4.354'/%3E%3C/svg%3E");-webkit-mask-size:contain;width:var(--ht-icon-size)}[class*=ht-theme-main] .pika-single .pika-prev{background-color:currentColor;height:var(--ht-icon-size);-webkit-mask-image:url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none'%3E%3Cpath stroke='currentColor' stroke-linecap='round' stroke-linejoin='round' d='M9.94 10.94 6.645 7.645 9.94 4.354'/%3E%3C/svg%3E");-webkit-mask-size:contain;width:var(--ht-icon-size)}[class*=ht-theme-main] .changeType:before,[class*=ht-theme-main] .htAutocompleteArrow:after,[class*=ht-theme-main] .htUISelectCaption:after{background-color:currentColor;height:var(--ht-icon-size);-webkit-mask-image:url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none'%3E%3Cpath fill='currentColor' d='M11 6.667a.5.5 0 0 1 .392.811l-.038.042-3 3a.5.5 0 0 1-.66.042l-.047-.042-3-3-.042-.047-.027-.038-.027-.048-.008-.018-.014-.034-.016-.054-.005-.026-.005-.03-.002-.029v-.059l.003-.029.004-.03.005-.026.016-.054.014-.033.035-.066.032-.045.037-.04.047-.042.038-.027.048-.027.018-.009.034-.013.054-.016.026-.005.03-.005.029-.002z'/%3E%3C/svg%3E");-webkit-mask-size:contain;width:var(--ht-icon-size)}[class*=ht-theme-main] .columnSorting.sortAction.ascending:before{background-color:currentColor;height:var(--ht-icon-size);-webkit-mask-image:url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none'%3E%3Cpath stroke='currentColor' stroke-linecap='round' stroke-linejoin='round' d='M8 12.667V3.333m0 0L5.333 6M8 3.333 10.667 6'/%3E%3C/svg%3E");-webkit-mask-size:contain;width:var(--ht-icon-size)}[class*=ht-theme-main] .columnSorting.sortAction.descending:before{background-color:currentColor;height:var(--ht-icon-size);-webkit-mask-image:url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none'%3E%3Cpath stroke='currentColor' stroke-linecap='round' stroke-linejoin='round' d='M8 3.333v9.334m0 0L10.667 10M8 12.667 5.333 10'/%3E%3C/svg%3E");-webkit-mask-size:contain;width:var(--ht-icon-size)}[class*=ht-theme-main].htContextMenu table tbody tr td .htItemWrapper span.selected:after,[class*=ht-theme-main].htDropdownMenu table tbody tr td .htItemWrapper span.selected:after,[class*=ht-theme-main].htFiltersConditionsMenu table tbody tr td .htItemWrapper span.selected:after{background-color:currentColor;height:var(--ht-icon-size);-webkit-mask-image:url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none'%3E%3Cpath stroke='currentColor' stroke-linecap='round' stroke-linejoin='round' d='m3.333 8 3.334 3.333 6.666-6.666'/%3E%3C/svg%3E");-webkit-mask-size:contain;width:var(--ht-icon-size)}[class*=ht-theme-main] .htCheckboxRendererInput{appearance:none}[class*=ht-theme-main] .htCheckboxRendererInput:after{-webkit-mask-image:url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none'%3E%3Cpath stroke='currentColor' stroke-linecap='round' stroke-linejoin='round' d='m5 8 2 2 4-4'/%3E%3C/svg%3E")}[class*=ht-theme-main] .htCheckboxRendererInput:after,[class*=ht-theme-main] th.beforeHiddenColumn:after{background-color:currentColor;height:var(--ht-icon-size);-webkit-mask-size:contain;width:var(--ht-icon-size)}[class*=ht-theme-main] th.beforeHiddenColumn:after{-webkit-mask-image:url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='10' fill='none'%3E%3Cpath fill='currentColor' d='m6.085 1.358.03-.003h.061l.03.003.032.004.027.006.056.016.035.014.069.037.047.034.042.038.043.049.028.04.028.05.01.018.013.035.017.057.005.027.005.031.003.03v6.28a.521.521 0 0 1-.845.409l-.043-.04-3.125-3.125a.52.52 0 0 1-.044-.687l.044-.05 3.125-3.124.048-.044.04-.028.05-.028.02-.009.034-.014.056-.016.028-.006z'/%3E%3C/svg%3E")}[class*=ht-theme-main] th.afterHiddenColumn:before{-webkit-mask-image:url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='10' fill='none'%3E%3Cpath fill='currentColor' d='M3.333 1.875c0-.444.514-.676.846-.408l.043.04 3.125 3.125a.52.52 0 0 1 .044.687l-.044.05-3.125 3.124-.049.043-.04.029-.05.028-.018.009-.035.014-.057.016-.027.005-.031.006-.03.002h-.062l-.03-.003-.031-.005-.027-.005-.056-.016-.035-.014-.069-.037-.047-.034-.042-.038-.043-.049-.028-.04-.029-.05-.008-.019-.014-.034-.017-.057-.005-.027-.006-.032-.002-.03z'/%3E%3C/svg%3E")}[class*=ht-theme-main] th.afterHiddenColumn:before,[class*=ht-theme-main] th.beforeHiddenRow:after{background-color:currentColor;height:var(--ht-icon-size);-webkit-mask-size:contain;width:var(--ht-icon-size)}[class*=ht-theme-main] th.beforeHiddenRow:after{-webkit-mask-image:url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' fill='none'%3E%3Cpath fill='currentColor' d='M3.764 2.431a.333.333 0 0 1 .44-.028l.032.028 2 2 .027.031.018.026.018.032.006.012.009.022.01.036.004.018.003.02.002.019v.04l-.002.019-.003.02-.003.017-.011.036-.01.022-.022.044-.022.03-.024.027-.032.028-.025.018-.032.018-.012.006-.023.009-.036.01-.017.004-.02.003-.02.001L6 5H2a.334.334 0 0 1-.26-.541l.025-.028z'/%3E%3C/svg%3E")}[class*=ht-theme-main] th.afterHiddenRow:before{background-color:currentColor;height:var(--ht-icon-size);-webkit-mask-image:url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' fill='none'%3E%3Cpath fill='currentColor' d='M6 3c.284 0 .432.329.26.541l-.024.028-2 2a.333.333 0 0 1-.44.028l-.032-.028-2-2-.027-.031-.018-.026L1.7 3.48l-.006-.012-.01-.022-.01-.036-.003-.018-.003-.02-.002-.019v-.04l.002-.019.003-.02.003-.017.01-.036.01-.022.023-.044.022-.03.024-.027.032-.028.025-.018.032-.018.012-.006.023-.009.036-.01.017-.004.02-.003.02-.001z'/%3E%3C/svg%3E");-webkit-mask-size:contain;width:var(--ht-icon-size)}[class*=ht-theme-main] .collapsibleIndicator:before,[class*=ht-theme-main] .ht_nestingButton:before{background-color:currentColor;height:var(--ht-icon-size);-webkit-mask-image:url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none'%3E%3Cpath stroke='currentColor' stroke-linecap='round' stroke-linejoin='round' d='M4.333 8h7.334'/%3E%3C/svg%3E");-webkit-mask-size:contain;width:var(--ht-icon-size)}[class*=ht-theme-main] .collapsibleIndicator.collapsed:before,[class*=ht-theme-main] .collapsibleIndicator.ht_nestingExpand:before,[class*=ht-theme-main] .ht_nestingButton.collapsed:before,[class*=ht-theme-main] .ht_nestingButton.ht_nestingExpand:before{background-color:currentColor;height:var(--ht-icon-size);-webkit-mask-image:url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none'%3E%3Cpath fill='currentColor' fill-rule='evenodd' d='M7.5 11.667a.5.5 0 0 0 1 0V8.5h3.167a.5.5 0 0 0 0-1H8.5V4.333a.5.5 0 0 0-1 0V7.5H4.333a.5.5 0 0 0 0 1H7.5z' clip-rule='evenodd'/%3E%3C/svg%3E");-webkit-mask-size:contain;width:var(--ht-icon-size)}[class*=ht-theme-main] .htUIRadio>input[type=radio]:after{background-color:currentColor;height:var(--ht-icon-size);-webkit-mask-image:url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none'%3E%3Ccircle cx='8' cy='8' r='4' fill='currentColor'/%3E%3C/svg%3E");-webkit-mask-size:contain;width:var(--ht-icon-size)}
