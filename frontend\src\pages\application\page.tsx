import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { DropdownMenu, DropdownMenuCheckboxItem, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { IconChevronDown, IconDotsVertical } from '@tabler/icons-react';
import React, { useState } from 'react';

interface ApplicationRequest {
  id: string;
  vesselName: string;
  arrivalDate: string;
  departureDate: string;
  itemName: string;
  requestBy: string;
}

const mockApplicationData: ApplicationRequest[] = Array(10).fill(null).map((_, i) => ({
  id: `app_${i + 1}`,
  vesselName: `MV. GOLDEN ACE V. 00${i + 1}`,
  arrivalDate: '2025-05-01',
  departureDate: '2025-05-02',
  itemName: 'STEEL PRODUCT 10MT',
  requestBy: 'USER1',
}));

export default function ApplicationList() {
  const [filter, setFilter] = useState('');
  const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set());
  const [visibleColumns, setVisibleColumns] = useState<Set<keyof ApplicationRequest>>(
    new Set(Object.keys(mockApplicationData[0]) as (keyof ApplicationRequest)[])
  );

  const filteredData = mockApplicationData.filter(app =>
    Object.values(app).some(value =>
      value.toString().toLowerCase().includes(filter.toLowerCase())
    )
  );

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedRows(new Set(filteredData.map(row => row.id)));
    } else {
      setSelectedRows(new Set());
    }
  };

  const handleRowSelect = (id: string, checked: boolean) => {
    const newSelection = new Set(selectedRows);
    if (checked) {
      newSelection.add(id);
    } else {
      newSelection.delete(id);
    }
    setSelectedRows(newSelection);
  };

  const handleToggleColumn = (columnKey: keyof ApplicationRequest, checked: boolean) => {
    const newVisibleColumns = new Set(visibleColumns);
    if (checked) {
      newVisibleColumns.add(columnKey);
    } else {
      newVisibleColumns.delete(columnKey);
    }
    setVisibleColumns(newVisibleColumns);
  };

  const handlePreview = (id: string) => {
    console.log(`Preview document for ID: ${id}`);
    // TODO: Integrate DocumentPreviewDialog here
  };

  const handleApprove = (id: string) => {
    console.log(`Approve application with ID: ${id}`);
  };

  const handleReject = (id: string) => {
    console.log(`Reject application with ID: ${id}`);
  };

  return (
    <AppLayout>
      <div className="container mx-auto p-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-bold">Approval Requests</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between mb-4">
              <Input
                placeholder="Filter lines..."
                value={filter}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFilter(e.target.value)}
                className="max-w-sm"
              />
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="ml-auto">
                    Columns <IconChevronDown className="ml-2 h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {Object.keys(mockApplicationData[0]).map((key) => (
                    <DropdownMenuCheckboxItem
                      key={key}
                      className="capitalize"
                      checked={visibleColumns.has(key as keyof ApplicationRequest)}
                      onCheckedChange={(checked: boolean | 'indeterminate') => handleToggleColumn(key as keyof ApplicationRequest, checked === true)}
                    >
                      {key.replace(/([A-Z])/g, ' $1').trim()}
                    </DropdownMenuCheckboxItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[30px]">
                      <Checkbox
                        checked={selectedRows.size === filteredData.length && filteredData.length > 0}
                        onCheckedChange={(checked: boolean | 'indeterminate') => handleSelectAll(checked === true)}
                      />
                    </TableHead>
                    {Object.keys(mockApplicationData[0]).map((key) => (visibleColumns.has(key as keyof ApplicationRequest) && key !== 'id' &&
                      <TableHead key={key} className="capitalize">
                        {key.replace(/([A-Z])/g, ' $1').trim()}
                      </TableHead>
                    ))}
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredData.map((application) => (
                    <TableRow key={application.id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedRows.has(application.id)}
                          onCheckedChange={(checked: boolean | 'indeterminate') => handleRowSelect(application.id, checked === true)}
                        />
                      </TableCell>
                      {Object.entries(application).map(([key, value]) => (visibleColumns.has(key as keyof ApplicationRequest) && key !== 'id' &&
                        <TableCell key={key}>{value}</TableCell>
                      ))}
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <IconDotsVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => handlePreview(application.id)}>
                              Preview
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleApprove(application.id)}>
                              Approve
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleReject(application.id)}>
                              Reject
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-gray-500">
                {selectedRows.size} of {filteredData.length} row(s) selected.
              </div>
              <div className="space-x-2">
                <Button variant="outline" size="sm">Previous</Button>
                <Button variant="outline" size="sm">Next</Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
