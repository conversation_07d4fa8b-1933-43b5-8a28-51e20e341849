using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Security.Claims;
using System.Text.Json;
using System.Threading.Tasks;
using Imip.JettyApproval.Web.Services;
using Imip.JettyApproval.Web.Services.Interfaces;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.DependencyInjection;

namespace Imip.JettyApproval.Web.Authorization.Permissions;

[Dependency(ReplaceServices = true)]
[ExposeServices(typeof(IPermissionChecker))]
public class CentralizedPermissionChecker : IPermissionChecker, ITransientDependency
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IConfiguration _configuration;
    private readonly ILogger<CentralizedPermissionChecker> _logger;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IWebHostEnvironment _environment;
    private readonly ApplicationConfigurationService _applicationConfigurationService;
    private readonly AppToAppService _appToAppService;
    private readonly ITokenService _tokenService;

    // Shared JsonSerializerOptions instance
    private static readonly JsonSerializerOptions _jsonOptions = new JsonSerializerOptions
    {
        PropertyNameCaseInsensitive = true
    };

    // Per-access-token cache for granted policies and their timestamps
    private static readonly ConcurrentDictionary<string, Dictionary<string, bool>> _grantedPoliciesCache = new();
    private static readonly ConcurrentDictionary<string, DateTime> _grantedPoliciesCacheTime = new();
    private static readonly TimeSpan GrantedPoliciesCacheDuration = TimeSpan.FromSeconds(30);

    public CentralizedPermissionChecker(
        IHttpContextAccessor httpContextAccessor,
        IConfiguration configuration,
        ILogger<CentralizedPermissionChecker> logger,
        IHttpClientFactory httpClientFactory,
        IWebHostEnvironment environment,
        ApplicationConfigurationService applicationConfigurationService,
        ITokenService tokenService,
        AppToAppService appToAppService)
    {
        _httpContextAccessor = httpContextAccessor;
        _configuration = configuration;
        _logger = logger;
        _httpClientFactory = httpClientFactory;
        _environment = environment;
        _applicationConfigurationService = applicationConfigurationService;
        _appToAppService = appToAppService;
        _tokenService = tokenService;
    }

    public async Task<bool> IsGrantedAsync(string name)
    {
        return await IsGrantedAsync(_httpContextAccessor.HttpContext?.User, name);
    }

    public async Task<bool> IsGrantedAsync(ClaimsPrincipal? principal, string name)
    {
        if (principal == null || !(principal.Identity?.IsAuthenticated ?? false))
        {
            _logger.LogWarning("Permission check failed: principal is null or not authenticated for {Permission}", name);
            return false;
        }

        // Special case: Allow access to Quartz endpoints for authenticated users
        var httpContext = _httpContextAccessor.HttpContext;
        if (httpContext != null && httpContext.Request.Path.StartsWithSegments("/quartz"))
        {
            // _logger.LogDebug("Allowing access to Quartz endpoint: {Path} for authenticated user", httpContext.Request.Path);
            return true;
        }

        var accessToken = await _tokenService.GetAccessTokenAsync();
        if (string.IsNullOrEmpty(accessToken))
        {
            _logger.LogWarning("Permission check failed: no access token for {Permission}", name);
            return false;
        }

        var cacheKey = accessToken;
        var now = DateTime.UtcNow;
        bool useCache = false;
        if (_grantedPoliciesCache.TryGetValue(cacheKey, out var grantedPolicies) &&
            _grantedPoliciesCacheTime.TryGetValue(cacheKey, out var cacheTime) &&
            (now - cacheTime) < GrantedPoliciesCacheDuration)
        {
            useCache = true;
        }
        else
        {
            grantedPolicies = await FetchGrantedPoliciesAsync(accessToken);
            _grantedPoliciesCache[cacheKey] = grantedPolicies;
            _grantedPoliciesCacheTime[cacheKey] = now;
        }

        var result = grantedPolicies.TryGetValue(name, out var isGranted) && isGranted;
        return result;
    }

    public async Task<bool> IsGrantedAsync(Guid userId, string name)
    {
        // Not used in this simplified version
        return false;
    }

    public async Task<MultiplePermissionGrantResult> IsGrantedAsync(string[] names)
    {
        var principal = _httpContextAccessor.HttpContext?.User;
        return await IsGrantedAsync(principal, names);
    }

    public async Task<MultiplePermissionGrantResult> IsGrantedAsync(ClaimsPrincipal? principal, string[] names)
    {
        var result = new MultiplePermissionGrantResult();
        foreach (var name in names)
        {
            var granted = await IsGrantedAsync(principal, name);
            result.Result[name] = granted ? PermissionGrantResult.Granted : PermissionGrantResult.Prohibited;
        }
        return result;
    }

    private async Task<Dictionary<string, bool>> FetchGrantedPoliciesAsync(string accessToken)
    {
        var identityServerUrl = _configuration["AuthServer:Authority"];
        var client = _httpClientFactory.CreateClient("IdentityServer");
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
        var endpoint = $"{identityServerUrl}/api/abp/application-configuration";
        var response = await client.GetAsync(endpoint);
        response.EnsureSuccessStatusCode();
        var json = await response.Content.ReadAsStringAsync();
        using var doc = JsonDocument.Parse(json);
        var root = doc.RootElement;
        var grantedPolicies = new Dictionary<string, bool>();
        if (root.TryGetProperty("auth", out var auth) && auth.TryGetProperty("grantedPolicies", out var policies))
        {
            foreach (var prop in policies.EnumerateObject())
            {
                grantedPolicies[prop.Name] = prop.Value.GetBoolean();
            }
        }
        return grantedPolicies;
    }

    private class PermissionCheckResult
    {
        public bool IsGranted { get; set; }
    }

    private class MultiplePermissionCheckResult
    {
        public Dictionary<string, bool> Permissions { get; set; } = [];
    }
}