import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { DropdownMenu, DropdownMenuCheckboxItem, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { IconChevronDown, IconDotsVertical } from '@tabler/icons-react';
import React, { useState } from 'react';

interface DockedVessel {
    id: string;
    vesselName: string;
    jetty: string;
    arrivalDateTime: string;
    departureDateTime: string;
    status: string;
    cargo: string;
    agent: string;
}

const mockDockedVesselData: DockedVessel[] = Array(10).fill(null).map((_, i) => ({
    id: `docked_${i + 1}`,
    vesselName: `MV. SEAWORTHY ${i + 1}`,
    jetty: `Jetty ${i % 3 === 0 ? 'A' : i % 3 === 1 ? 'B' : 'C'}`,
    arrivalDateTime: `2025-05-${10 + i} 08:00`,
    departureDateTime: `2025-05-${12 + i} 17:00`,
    status: i % 2 === 0 ? 'Loading' : 'Unloading',
    cargo: i % 2 === 0 ? 'Containers' : 'Bulk Grain',
    agent: `Agent Corp ${i + 1}`,
}));

export default function DockedVesselList() {
    const [filter, setFilter] = useState('');
    const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set());
    const [visibleColumns, setVisibleColumns] = useState<Set<keyof DockedVessel>>(
        new Set(Object.keys(mockDockedVesselData[0]) as (keyof DockedVessel)[])
    );

    const filteredData = mockDockedVesselData.filter(vessel =>
        Object.values(vessel).some(value =>
            value.toString().toLowerCase().includes(filter.toLowerCase())
        )
    );

    const handleSelectAll = (checked: boolean) => {
        if (checked) {
            setSelectedRows(new Set(filteredData.map(row => row.id)));
        } else {
            setSelectedRows(new Set());
        }
    };

    const handleRowSelect = (id: string, checked: boolean) => {
        const newSelection = new Set(selectedRows);
        if (checked) {
            newSelection.add(id);
        } else {
            newSelection.delete(id);
        }
        setSelectedRows(newSelection);
    };

    const handleToggleColumn = (columnKey: keyof DockedVessel, checked: boolean) => {
        const newVisibleColumns = new Set(visibleColumns);
        if (checked) {
            newVisibleColumns.add(columnKey);
        } else {
            newVisibleColumns.delete(columnKey);
        }
        setVisibleColumns(newVisibleColumns);
    };

    const handleViewDetails = (id: string) => {
        console.log(`Viewing details for docked vessel ID: ${id}`);
        // Implement navigation or dialog for viewing details
    };

    const handleUpdateStatus = (id: string) => {
        console.log(`Updating status for docked vessel ID: ${id}`);
        // Implement logic to update vessel status
    };

    return (
        <AppLayout>
            <div className="container mx-auto p-4">
                <Card>
                    <CardHeader>
                        <CardTitle className="text-2xl font-bold">Docked Vessels</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex items-center justify-between mb-4">
                            <Input
                                placeholder="Filter vessels..."
                                value={filter}
                                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFilter(e.target.value)}
                                className="max-w-sm"
                            />
                            <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                    <Button variant="outline" className="ml-auto">
                                        Columns <IconChevronDown className="ml-2 h-4 w-4" />
                                    </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                    {Object.keys(mockDockedVesselData[0]).map((key) => (
                                        <DropdownMenuCheckboxItem
                                            key={key}
                                            className="capitalize"
                                            checked={visibleColumns.has(key as keyof DockedVessel)}
                                            onCheckedChange={(checked: boolean | 'indeterminate') => handleToggleColumn(key as keyof DockedVessel, checked === true)}
                                        >
                                            {key.replace(/([A-Z])/g, ' $1').trim()}
                                        </DropdownMenuCheckboxItem>
                                    ))}
                                </DropdownMenuContent>
                            </DropdownMenu>
                        </div>

                        <div className="rounded-md border">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead className="w-[30px]">
                                            <Checkbox
                                                checked={selectedRows.size === filteredData.length && filteredData.length > 0}
                                                onCheckedChange={(checked: boolean | 'indeterminate') => handleSelectAll(checked === true)}
                                            />
                                        </TableHead>
                                        {Object.keys(mockDockedVesselData[0]).map((key) => (visibleColumns.has(key as keyof DockedVessel) && key !== 'id' &&
                                            <TableHead key={key} className="capitalize">
                                                {key.replace(/([A-Z])/g, ' $1').trim()}
                                            </TableHead>
                                        ))}
                                        <TableHead className="text-right">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {filteredData.map((vessel) => (
                                        <TableRow key={vessel.id}>
                                            <TableCell>
                                                <Checkbox
                                                    checked={selectedRows.has(vessel.id)}
                                                    onCheckedChange={(checked: boolean | 'indeterminate') => handleRowSelect(vessel.id, checked === true)}
                                                />
                                            </TableCell>
                                            {Object.entries(vessel).map(([key, value]) => (visibleColumns.has(key as keyof DockedVessel) && key !== 'id' &&
                                                <TableCell key={key}>{value}</TableCell>
                                            ))}
                                            <TableCell className="text-right">
                                                <DropdownMenu>
                                                    <DropdownMenuTrigger asChild>
                                                        <Button variant="ghost" className="h-8 w-8 p-0">
                                                            <span className="sr-only">Open menu</span>
                                                            <IconDotsVertical className="h-4 w-4" />
                                                        </Button>
                                                    </DropdownMenuTrigger>
                                                    <DropdownMenuContent align="end">
                                                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                                        <DropdownMenuItem onClick={() => handleViewDetails(vessel.id)}>
                                                            View Details
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem onClick={() => handleUpdateStatus(vessel.id)}>
                                                            Update Status
                                                        </DropdownMenuItem>
                                                    </DropdownMenuContent>
                                                </DropdownMenu>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </div>

                        <div className="flex items-center justify-between mt-4">
                            <div className="text-sm text-gray-500">
                                {selectedRows.size} of {filteredData.length} row(s) selected.
                            </div>
                            <div className="space-x-2">
                                <Button variant="outline" size="sm">Previous</Button>
                                <Button variant="outline" size="sm">Next</Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
