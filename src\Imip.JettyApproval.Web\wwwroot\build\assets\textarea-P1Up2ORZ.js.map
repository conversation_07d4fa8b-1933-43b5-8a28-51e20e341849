{"version": 3, "file": "textarea-P1Up2ORZ.js", "sources": ["../../../../../frontend/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\r\n  return (\r\n    <textarea\r\n      data-slot=\"textarea\"\r\n      className={cn(\r\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Textarea }\r\n"], "names": ["Textarea", "className", "props", "jsx", "cn"], "mappings": "sFAIA,SAASA,EAAS,CAAE,UAAAC,EAAW,GAAGC,GAA2C,CAEzE,OAAAC,EAAA,IAAC,WAAA,CACC,YAAU,WACV,UAAWC,EACT,scACAH,CACF,EACC,GAAGC,CAAA,CACN,CAEJ"}