using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.ApiExplorer;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using Volo.Abp.Swashbuckle;
using Imip.JettyApproval.Web.Swagger;
using Imip.JettyApproval.Web.Services.Attachments;

namespace Imip.JettyApproval.Web.Services;

public static class SwaggerConfigurationService
{
    /// <summary>
    /// Configures Swagger services with custom settings for JettyApproval API
    /// </summary>
    public static void ConfigureSwagger(IServiceCollection services)
    {
        services.AddAbpSwaggerGen(options =>
        {
            ConfigureBasicSwaggerSettings(options);
            ConfigureFileUploadSupport(options);
            ConfigureApiDocumentationFiltering(options);
            ConfigureCustomSchemaIds(options);
        });
    }

    private static void ConfigureBasicSwaggerSettings(SwaggerGenOptions options)
    {
        options.SwaggerDoc("v1", new OpenApiInfo
        {
            Title = "JettyApproval API",
            Version = "v1"
        });

        // Configure Swagger to handle file uploads with IFormFile
        options.OperationFilter<SwaggerFileOperationFilter>();
    }

    private static void ConfigureFileUploadSupport(SwaggerGenOptions options)
    {
        // Add support for multipart/form-data
        options.MapType<IFormFile>(() => new OpenApiSchema
        {
            Type = "string",
            Format = "binary"
        });

        // Add support for FileUploadFormDto
        options.MapType<FileUploadFormDto>(() => new OpenApiSchema
        {
            Type = "object",
            Properties = new Dictionary<string, OpenApiSchema>
            {
                ["File"] = new OpenApiSchema { Type = "string", Format = "binary" },
                ["Description"] = new OpenApiSchema { Type = "string" },
                ["ReferenceId"] = new OpenApiSchema { Type = "string", Format = "uuid" },
                ["ReferenceType"] = new OpenApiSchema { Type = "string" }
            },
            Required = new HashSet<string> { "File" }
        });
    }

    private static void ConfigureApiDocumentationFiltering(SwaggerGenOptions options)
    {
        options.DocInclusionPredicate((docName, description) =>
        {
            // Exclude ABP framework controllers (except specific allowed ones)
            if (IsAbpFrameworkController(description))
            {
                return IsAllowedAbpController(description);
            }

            // Exclude specific ABP modules
            if (IsExcludedAbpModule(description))
            {
                return false;
            }

            // Exclude specific API paths
            if (IsExcludedApiPath(description))
            {
                return false;
            }

            // Exclude specific controller types
            if (IsExcludedControllerType(description))
            {
                return false;
            }

            return true; // Include all other APIs
        });
    }

    private static bool IsAbpFrameworkController(ApiDescription description)
    {
        return description.ActionDescriptor?.RouteValues != null &&
               description.ActionDescriptor.RouteValues.TryGetValue("controller", out var controllerName) &&
               controllerName != null &&
               (controllerName.Contains("Abp") || controllerName.StartsWith("Abp"));
    }

    private static bool IsAllowedAbpController(ApiDescription description)
    {
        var relativePath = description.RelativePath;
        if (relativePath == null) return false;

        // Allow specific ABP endpoints
        return relativePath.Contains("application-configuration") ||
               relativePath.Contains("api/abp/application-configuration") ||
               relativePath.Contains("/tenant") ||
               relativePath.Contains("/tenants") ||
               relativePath.Contains("/account") ||
               relativePath.Contains("/accounts");
    }

    private static bool IsExcludedAbpModule(ApiDescription description)
    {
        var groupName = description.GroupName;
        if (groupName == null) return false;

        var excludedModules = new[]
        {
            "Identity",
            "PermissionManagement",
            "FeatureManagement",
            "SettingManagement"
        };

        return excludedModules.Any(module => groupName.Contains(module));
    }

    private static bool IsExcludedApiPath(ApiDescription description)
    {
        var relativePath = description.RelativePath;
        if (relativePath == null) return false;

        var excludedPaths = new[]
        {
            "/permission", "/permissions",
            "/feature", "/features",
            "/setting", "/settings",
            "/identity", "/identities",
            "/users", "/roles",
            "api-definition", "api/abp/api-definition",
            "application-localization", "api/abp/application-localization"
        };

        return excludedPaths.Any(path => relativePath.Contains(path));
    }

    private static bool IsExcludedControllerType(ApiDescription description)
    {
        var displayName = description.ActionDescriptor?.DisplayName;
        if (displayName == null) return false;

        var excludedControllers = new[]
        {
            "AbpApiDefinition",
            "AbpApplicationLocalization"
        };

        return excludedControllers.Any(controller => displayName.Contains(controller));
    }

    private static void ConfigureCustomSchemaIds(SwaggerGenOptions options)
    {
        options.CustomSchemaIds(type =>
        {
            // Handle generic types
            if (type.IsGenericType)
            {
                var prefix = type.Name.Split('`')[0];
                var genericArgs = string.Join("And", type.GetGenericArguments().Select(t =>
                {
                    if (t.IsGenericType)
                    {
                        var nestedPrefix = t.Name.Split('`')[0];
                        var nestedArgs = string.Join("And", t.GetGenericArguments().Select(nt => nt.Name));
                        return $"{nestedPrefix}Of{nestedArgs}";
                    }

                    return t.Name;
                }));
                return $"{prefix}Of{genericArgs}";
            }

            // Handle non-generic types
            return type.Name;
        });
    }
}
