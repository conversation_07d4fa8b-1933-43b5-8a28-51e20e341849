using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using System;
using System.Threading.Tasks;
using Volo.Abp.AspNetCore.Mvc;

namespace Imip.JettyApproval.Web.Controllers;

[Route("Account")]
public class AccountController : AbpController
{
    private readonly IConfiguration _configuration;

    public AccountController(IConfiguration configuration)
    {
        _configuration = configuration;
    }
    [HttpGet("ExternalLogin")]
    public IActionResult ExternalLogin(string provider, string returnUrl = null)
    {
        // Request a redirect to the external login provider
        var redirectUrl = Url.Action(nameof(ExternalLoginCallback), "Account", new { returnUrl });
        var properties = new AuthenticationProperties { RedirectUri = redirectUrl };

        return Challenge(properties, provider);
    }

    [HttpGet("ExternalLoginCallback")]
    public async Task<IActionResult> ExternalLoginCallback(string returnUrl = null, string remoteError = null)
    {
        if (remoteError != null)
        {
            // Handle error from external provider
            TempData["ErrorMessage"] = $"Error from external provider: {remoteError}";
            return RedirectToPage("/Account/Login");
        }

        // The user should be authenticated at this point
        if (User.Identity.IsAuthenticated)
        {
            // Redirect to the return URL or home page
            if (!string.IsNullOrEmpty(returnUrl) && Url.IsLocalUrl(returnUrl))
            {
                return Redirect(returnUrl);
            }
            return RedirectToAction("Index", "Home");
        }

        // If we got this far, something failed
        TempData["ErrorMessage"] = "Unable to authenticate with external provider.";
        return RedirectToPage("/Account/Login");
    }

    [HttpGet("Login")]
    public IActionResult Login(string returnUrl = "/")
    {
        if (User.Identity.IsAuthenticated)
        {
            return LocalRedirect(returnUrl);
        }

        return Challenge(new AuthenticationProperties
        {
            RedirectUri = returnUrl
        }, "oidc");
    }

    [HttpGet("Logout")]
    [Authorize]
    public async Task<IActionResult> Logout()
    {
        try
        {
            // Sign out from both the application and the external provider
            await HttpContext.SignOutAsync("Cookies");
            await HttpContext.SignOutAsync("oidc");

            // Return a JSON response indicating successful logout
            return Json(new { success = true, message = "Logged out successfully" });
        }
        catch (System.Exception ex)
        {
            // Return error response if logout fails
            return Json(new { success = false, message = "Logout failed", error = ex.Message });
        }
    }

    [HttpGet("LogoutRedirect")]
    [Authorize]
    public async Task<IActionResult> LogoutRedirect()
    {
        try
        {
            // Sign out from both the application and the external provider
            await HttpContext.SignOutAsync("Cookies");
            await HttpContext.SignOutAsync("oidc");

            // Redirect to the identity server logout page directly
            var identityServerUrl = _configuration["AuthServer:Authority"] ?? "https://identity.imip.co.id";
            var logoutUrl = $"{identityServerUrl}/Account/Logout";

            return Redirect(logoutUrl);
        }
        catch (System.Exception ex)
        {
            // If logout fails, just redirect to login page
            return RedirectToAction("Login");
        }
    }

    [HttpGet("signout-callback-oidc")]
    public IActionResult SignOutCallback()
    {
        // This endpoint handles the post-logout redirect from the identity server
        // Redirect to the home page or login page
        return RedirectToAction("Login");
    }
}
