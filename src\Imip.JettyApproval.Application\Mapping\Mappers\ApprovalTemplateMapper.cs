using System;
using System.Collections.Generic;
using Imip.JettyApproval.Approvals.ApprovalTemplates;
using Riok.Mapperly.Abstractions;

namespace Imip.JettyApproval.Mapping.Mappers;

[Mapper]
public partial class ApprovalTemplateMapper : IMapperlyMapper
{
    // Entity to DTO mapping
    [MapProperty(nameof(ApprovalTemplate.Id), nameof(ApprovalTemplateDto.Id))]
    [MapperIgnoreSource(nameof(ApprovalTemplate.IsDeleted))]
    [MapperIgnoreSource(nameof(ApprovalTemplate.DeleterId))]
    [MapperIgnoreSource(nameof(ApprovalTemplate.DeletionTime))]
    [MapperIgnoreSource(nameof(ApprovalTemplate.LastModificationTime))]
    [MapperIgnoreSource(nameof(ApprovalTemplate.LastModifierId))]
    [MapperIgnoreSource(nameof(ApprovalTemplate.CreationTime))]
    [MapperIgnoreSource(nameof(ApprovalTemplate.CreatorId))]
    [MapperIgnoreSource(nameof(ApprovalTemplate.ExtraProperties))]
    [MapperIgnoreSource(nameof(ApprovalTemplate.ConcurrencyStamp))]
    public partial ApprovalTemplateDto MapToDto(ApprovalTemplate entity);

    // DTO to Entity mapping for updates (maps to existing entity)
    [MapperIgnoreTarget(nameof(ApprovalTemplate.Id))] // Don't change existing Id
    public partial void MapToEntity(CreateUpdateApprovalTemplateDto dto, ApprovalTemplate entity);

    // Custom mapping methods for complex scenarios
    public ApprovalTemplate CreateEntityWithId(CreateUpdateApprovalTemplateDto dto, Guid id)
    {
        // Create entity using reflection since constructor is protected
        var entity = (ApprovalTemplate)Activator.CreateInstance(typeof(ApprovalTemplate), true)!;

        // Map properties manually
        MapToEntity(dto, entity);

        return entity;
    }

    // Map list of entities to DTOs
    public partial List<ApprovalTemplateDto> MapToDtoList(List<ApprovalTemplate> entities);

    // Map IEnumerable for LINQ scenarios
    public partial IEnumerable<ApprovalTemplateDto> MapToDtoEnumerable(IEnumerable<ApprovalTemplate> entities);
}
