using System;
using System.Collections.Generic;
using Imip.JettyApproval.Approvals.ApprovalDelegations;
using Riok.Mapperly.Abstractions;

namespace Imip.JettyApproval.Mapping.Mappers;

/// <summary>
/// Mapper for ApprovalDelegation entity
/// </summary>
[Mapper]
public partial class ApprovalDelegationMapper : IMapperlyMapper
{
    // Entity to DTO mapping
    [MapProperty(nameof(ApprovalDelegation.Id), nameof(ApprovalDelegationDto.Id))]
    [MapperIgnoreSource(nameof(ApprovalDelegation.IsDeleted))]
    [MapperIgnoreSource(nameof(ApprovalDelegation.DeleterId))]
    [MapperIgnoreSource(nameof(ApprovalDelegation.DeletionTime))]
    [MapperIgnoreSource(nameof(ApprovalDelegation.LastModificationTime))]
    [MapperIgnoreSource(nameof(ApprovalDelegation.LastModifierId))]
    [MapperIgnoreSource(nameof(ApprovalDelegation.CreationTime))]
    [MapperIgnoreSource(nameof(ApprovalDelegation.CreatorId))]
    [MapperIgnoreSource(nameof(ApprovalDelegation.ExtraProperties))]
    [MapperIgnoreSource(nameof(ApprovalDelegation.ConcurrencyStamp))]
    public partial ApprovalDelegationDto MapToDto(ApprovalDelegation entity);

    // DTO to Entity mapping for updates (maps to existing entity)
    [MapperIgnoreTarget(nameof(ApprovalDelegation.Id))] // Don't change existing Id
    public partial void MapToEntity(CreateUpdateApprovalDelegationDto dto, ApprovalDelegation entity);

    // Custom mapping methods for complex scenarios
    public ApprovalDelegation CreateEntityWithId(CreateUpdateApprovalDelegationDto dto, Guid id)
    {
        // Create entity using reflection since constructor is protected
        var entity = (ApprovalDelegation)Activator.CreateInstance(typeof(ApprovalDelegation), true)!;

        // Map properties manually
        MapToEntity(dto, entity);

        return entity;
    }

    // Map list of entities to DTOs
    public partial List<ApprovalDelegationDto> MapToDtoList(List<ApprovalDelegation> entities);

    // Map IEnumerable for LINQ scenarios
    public partial IEnumerable<ApprovalDelegationDto> MapToDtoEnumerable(IEnumerable<ApprovalDelegation> entities);
}