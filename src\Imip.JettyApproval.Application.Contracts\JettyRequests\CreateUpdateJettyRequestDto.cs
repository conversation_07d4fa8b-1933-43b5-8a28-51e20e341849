using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;

namespace Imip.JettyApproval.JettyRequests;

/// <summary>
/// DTO for creating and updating JettyRequest entity
/// </summary>
public class CreateUpdateJettyRequestDto
{
    /// <summary>
    /// Document number for the jetty request
    /// </summary>
    [Required]
    public int DocNum { get; set; }

    /// <summary>
    /// Type of vessel
    /// </summary>
    [StringLength(100)]
    public string? VesselType { get; set; }

    /// <summary>
    /// Reference ID for the request
    /// </summary>
    public Guid? ReferenceId { get; set; }

    /// <summary>
    /// Name of the vessel
    /// </summary>
    [StringLength(200)]
    public string? VesselName { get; set; }

    /// <summary>
    /// Voyage information
    /// </summary>
    [StringLength(100)]
    public string? Voyage { get; set; }

    /// <summary>
    /// Jetty name/location
    /// </summary>
    [StringLength(200)]
    public string? Jetty { get; set; }

    /// <summary>
    /// Arrival date and time
    /// </summary>
    public DateTime? ArrivalDate { get; set; }

    /// <summary>
    /// Departure date and time
    /// </summary>
    public DateTime? DepartureDate { get; set; }

    /// <summary>
    /// Aside date and time
    /// </summary>
    public DateTime? AsideDate { get; set; }

    /// <summary>
    /// Cast off date and time
    /// </summary>
    public DateTime? CastOfDate { get; set; }

    /// <summary>
    /// Post date and time
    /// </summary>
    public DateTime? PostDate { get; set; }

    /// <summary>
    /// Collection of jetty request items
    /// </summary>
    public List<CreateUpdateJettyRequestItemDto> Items { get; set; } = new List<CreateUpdateJettyRequestItemDto>();
}