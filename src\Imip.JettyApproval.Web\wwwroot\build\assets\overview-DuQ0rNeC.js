import{r as q,j as M,e as Qn,g as se,b as T}from"./vendor-CrSBzUoz.js";import{C as or,a as ur,b as sr,c as ab,d as Qr,e as ei,f as ti}from"./card-BAJCNJxm.js";import{B as ri}from"./badge-BtBZs1VC.js";import{u as mw,f as yh,c as gw,a as bw,b as xw,g as ww}from"./index-CZZWNLgr.js";import{n as cf,O as ob,I as Ow,D as ub,e as sb,B as nn,U as _w,f as cb,g as Sw,i as Aw,j as Ha,aF as Pw,W as ie,M as Nt}from"./app-layout-CNB1Wtrx.js";import{C as mh}from"./checkbox-CayrCcBd.js";import{T as Tw,a as Ew,b as Ga,c as jw,d as Mw,e as gh}from"./table-CAbNlII1.js";import{i as Wt}from"./tiny-invariant-CopsF_GD.js";/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cw=[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]],$w=cf("arrow-up-down",Cw);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Iw=[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]],kw=cf("ellipsis",Iw);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nw=[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]],Rw=cf("trending-up",Nw);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var bh=ob("outline","trending-down","IconTrendingDown",[["path",{d:"M3 7l6 6l4 -4l8 8",key:"svg-0"}],["path",{d:"M21 10l0 7l-7 0",key:"svg-1"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var tr=ob("outline","trending-up","IconTrendingUp",[["path",{d:"M3 17l6 -6l4 4l8 -8",key:"svg-0"}],["path",{d:"M14 7l7 0l0 7",key:"svg-1"}]]);const Dw=[{id:"m5gr84i9",amount:316,status:"success",email:"<EMAIL>"},{id:"3u1reuv4",amount:242,status:"success",email:"<EMAIL>"},{id:"derv1ws0",amount:837,status:"processing",email:"<EMAIL>"},{id:"5kma53ae",amount:874,status:"success",email:"<EMAIL>"},{id:"bhqecj4p",amount:721,status:"failed",email:"<EMAIL>"}],xh=[{id:"select",header:({table:e})=>M.jsx(mh,{checked:e.getIsAllPageRowsSelected()||e.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:t=>e.toggleAllPageRowsSelected(!!t),"aria-label":"Select all"}),cell:({row:e})=>M.jsx(mh,{checked:e.getIsSelected(),onCheckedChange:t=>e.toggleSelected(!!t),"aria-label":"Select row"}),enableSorting:!1,enableHiding:!1},{accessorKey:"status",header:"Status",cell:({row:e})=>M.jsx("div",{className:"capitalize",children:e.getValue("status")})},{accessorKey:"email",header:({column:e})=>M.jsxs(nn,{variant:"ghost",onClick:()=>e.toggleSorting(e.getIsSorted()==="asc"),children:["Email",M.jsx($w,{})]}),cell:({row:e})=>M.jsx("div",{className:"lowercase",children:e.getValue("email")})},{accessorKey:"amount",header:()=>M.jsx("div",{className:"text-right",children:"Amount"}),cell:({row:e})=>{const t=parseFloat(e.getValue("amount")),r=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(t);return M.jsx("div",{className:"text-right font-medium",children:r})}},{id:"actions",enableHiding:!1,cell:({row:e})=>{const t=e.original;return M.jsxs(ub,{children:[M.jsx(sb,{asChild:!0,children:M.jsxs(nn,{variant:"ghost",className:"h-8 w-8 p-0",children:[M.jsx("span",{className:"sr-only",children:"Open menu"}),M.jsx(kw,{})]})}),M.jsxs(cb,{align:"end",children:[M.jsx(Aw,{children:"Actions"}),M.jsx(Ha,{onClick:()=>navigator.clipboard.writeText(t.id),children:"Copy payment ID"}),M.jsx(Pw,{}),M.jsx(Ha,{children:"View customer"}),M.jsx(Ha,{children:"View payment details"})]})]})}}];function qw(){const[e,t]=q.useState([]),[r,n]=q.useState([]),[i,a]=q.useState({}),[o,u]=q.useState({}),s=mw({data:Dw,columns:xh,onSortingChange:t,onColumnFiltersChange:n,getCoreRowModel:ww(),getPaginationRowModel:xw(),getSortedRowModel:bw(),getFilteredRowModel:gw(),onColumnVisibilityChange:a,onRowSelectionChange:u,state:{sorting:e,columnFilters:r,columnVisibility:i,rowSelection:o}});return M.jsxs(or,{children:[M.jsx(ur,{children:M.jsx(sr,{children:"Waiting Approval"})}),M.jsx(ab,{children:M.jsxs("div",{className:"w-full",children:[M.jsxs("div",{className:"flex items-center py-4",children:[M.jsx(Ow,{placeholder:"Filter emails...",value:s.getColumn("email")?.getFilterValue()??"",onChange:c=>s.getColumn("email")?.setFilterValue(c.target.value),className:"max-w-sm"}),M.jsxs(ub,{children:[M.jsx(sb,{asChild:!0,children:M.jsxs(nn,{variant:"outline",className:"ml-auto",children:["Columns ",M.jsx(_w,{})]})}),M.jsx(cb,{align:"end",children:s.getAllColumns().filter(c=>c.getCanHide()).map(c=>M.jsx(Sw,{className:"capitalize",checked:c.getIsVisible(),onCheckedChange:f=>c.toggleVisibility(!!f),children:c.id},c.id))})]})]}),M.jsx("div",{className:"rounded-md border",children:M.jsxs(Tw,{children:[M.jsx(Ew,{children:s.getHeaderGroups().map(c=>M.jsx(Ga,{children:c.headers.map(f=>M.jsx(jw,{children:f.isPlaceholder?null:yh(f.column.columnDef.header,f.getContext())},f.id))},c.id))}),M.jsx(Mw,{children:s.getRowModel().rows?.length?s.getRowModel().rows.map(c=>M.jsx(Ga,{"data-state":c.getIsSelected()&&"selected",children:c.getVisibleCells().map(f=>M.jsx(gh,{children:yh(f.column.columnDef.cell,f.getContext())},f.id))},c.id)):M.jsx(Ga,{children:M.jsx(gh,{colSpan:xh.length,className:"h-24 text-center",children:"No results."})})})]})}),M.jsxs("div",{className:"flex items-center justify-end space-x-2 py-4",children:[M.jsxs("div",{className:"text-muted-foreground flex-1 text-sm",children:[s.getFilteredSelectedRowModel().rows.length," of"," ",s.getFilteredRowModel().rows.length," row(s) selected."]}),M.jsxs("div",{className:"space-x-2",children:[M.jsx(nn,{variant:"outline",size:"sm",onClick:()=>s.previousPage(),disabled:!s.getCanPreviousPage(),children:"Previous"}),M.jsx(nn,{variant:"outline",size:"sm",onClick:()=>s.nextPage(),disabled:!s.getCanNextPage(),children:"Next"})]})]})]})})]})}var Ka,wh;function Ne(){if(wh)return Ka;wh=1;var e=Array.isArray;return Ka=e,Ka}var Va,Oh;function lb(){if(Oh)return Va;Oh=1;var e=typeof Qn=="object"&&Qn&&Qn.Object===Object&&Qn;return Va=e,Va}var Xa,_h;function at(){if(_h)return Xa;_h=1;var e=lb(),t=typeof self=="object"&&self&&self.Object===Object&&self,r=e||t||Function("return this")();return Xa=r,Xa}var Ya,Sh;function Wn(){if(Sh)return Ya;Sh=1;var e=at(),t=e.Symbol;return Ya=t,Ya}var Za,Ah;function Bw(){if(Ah)return Za;Ah=1;var e=Wn(),t=Object.prototype,r=t.hasOwnProperty,n=t.toString,i=e?e.toStringTag:void 0;function a(o){var u=r.call(o,i),s=o[i];try{o[i]=void 0;var c=!0}catch{}var f=n.call(o);return c&&(u?o[i]=s:delete o[i]),f}return Za=a,Za}var Ja,Ph;function Lw(){if(Ph)return Ja;Ph=1;var e=Object.prototype,t=e.toString;function r(n){return t.call(n)}return Ja=r,Ja}var Qa,Th;function mt(){if(Th)return Qa;Th=1;var e=Wn(),t=Bw(),r=Lw(),n="[object Null]",i="[object Undefined]",a=e?e.toStringTag:void 0;function o(u){return u==null?u===void 0?i:n:a&&a in Object(u)?t(u):r(u)}return Qa=o,Qa}var eo,Eh;function gt(){if(Eh)return eo;Eh=1;function e(t){return t!=null&&typeof t=="object"}return eo=e,eo}var to,jh;function Dr(){if(jh)return to;jh=1;var e=mt(),t=gt(),r="[object Symbol]";function n(i){return typeof i=="symbol"||t(i)&&e(i)==r}return to=n,to}var ro,Mh;function lf(){if(Mh)return ro;Mh=1;var e=Ne(),t=Dr(),r=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,n=/^\w*$/;function i(a,o){if(e(a))return!1;var u=typeof a;return u=="number"||u=="symbol"||u=="boolean"||a==null||t(a)?!0:n.test(a)||!r.test(a)||o!=null&&a in Object(o)}return ro=i,ro}var no,Ch;function Pt(){if(Ch)return no;Ch=1;function e(t){var r=typeof t;return t!=null&&(r=="object"||r=="function")}return no=e,no}var io,$h;function ff(){if($h)return io;$h=1;var e=mt(),t=Pt(),r="[object AsyncFunction]",n="[object Function]",i="[object GeneratorFunction]",a="[object Proxy]";function o(u){if(!t(u))return!1;var s=e(u);return s==n||s==i||s==r||s==a}return io=o,io}var ao,Ih;function Fw(){if(Ih)return ao;Ih=1;var e=at(),t=e["__core-js_shared__"];return ao=t,ao}var oo,kh;function zw(){if(kh)return oo;kh=1;var e=Fw(),t=function(){var n=/[^.]+$/.exec(e&&e.keys&&e.keys.IE_PROTO||"");return n?"Symbol(src)_1."+n:""}();function r(n){return!!t&&t in n}return oo=r,oo}var uo,Nh;function fb(){if(Nh)return uo;Nh=1;var e=Function.prototype,t=e.toString;function r(n){if(n!=null){try{return t.call(n)}catch{}try{return n+""}catch{}}return""}return uo=r,uo}var so,Rh;function Uw(){if(Rh)return so;Rh=1;var e=ff(),t=zw(),r=Pt(),n=fb(),i=/[\\^$.*+?()[\]{}|]/g,a=/^\[object .+?Constructor\]$/,o=Function.prototype,u=Object.prototype,s=o.toString,c=u.hasOwnProperty,f=RegExp("^"+s.call(c).replace(i,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function l(h){if(!r(h)||t(h))return!1;var p=e(h)?f:a;return p.test(n(h))}return so=l,so}var co,Dh;function Ww(){if(Dh)return co;Dh=1;function e(t,r){return t?.[r]}return co=e,co}var lo,qh;function Xt(){if(qh)return lo;qh=1;var e=Uw(),t=Ww();function r(n,i){var a=t(n,i);return e(a)?a:void 0}return lo=r,lo}var fo,Bh;function la(){if(Bh)return fo;Bh=1;var e=Xt(),t=e(Object,"create");return fo=t,fo}var ho,Lh;function Hw(){if(Lh)return ho;Lh=1;var e=la();function t(){this.__data__=e?e(null):{},this.size=0}return ho=t,ho}var po,Fh;function Gw(){if(Fh)return po;Fh=1;function e(t){var r=this.has(t)&&delete this.__data__[t];return this.size-=r?1:0,r}return po=e,po}var vo,zh;function Kw(){if(zh)return vo;zh=1;var e=la(),t="__lodash_hash_undefined__",r=Object.prototype,n=r.hasOwnProperty;function i(a){var o=this.__data__;if(e){var u=o[a];return u===t?void 0:u}return n.call(o,a)?o[a]:void 0}return vo=i,vo}var yo,Uh;function Vw(){if(Uh)return yo;Uh=1;var e=la(),t=Object.prototype,r=t.hasOwnProperty;function n(i){var a=this.__data__;return e?a[i]!==void 0:r.call(a,i)}return yo=n,yo}var mo,Wh;function Xw(){if(Wh)return mo;Wh=1;var e=la(),t="__lodash_hash_undefined__";function r(n,i){var a=this.__data__;return this.size+=this.has(n)?0:1,a[n]=e&&i===void 0?t:i,this}return mo=r,mo}var go,Hh;function Yw(){if(Hh)return go;Hh=1;var e=Hw(),t=Gw(),r=Kw(),n=Vw(),i=Xw();function a(o){var u=-1,s=o==null?0:o.length;for(this.clear();++u<s;){var c=o[u];this.set(c[0],c[1])}}return a.prototype.clear=e,a.prototype.delete=t,a.prototype.get=r,a.prototype.has=n,a.prototype.set=i,go=a,go}var bo,Gh;function Zw(){if(Gh)return bo;Gh=1;function e(){this.__data__=[],this.size=0}return bo=e,bo}var xo,Kh;function hf(){if(Kh)return xo;Kh=1;function e(t,r){return t===r||t!==t&&r!==r}return xo=e,xo}var wo,Vh;function fa(){if(Vh)return wo;Vh=1;var e=hf();function t(r,n){for(var i=r.length;i--;)if(e(r[i][0],n))return i;return-1}return wo=t,wo}var Oo,Xh;function Jw(){if(Xh)return Oo;Xh=1;var e=fa(),t=Array.prototype,r=t.splice;function n(i){var a=this.__data__,o=e(a,i);if(o<0)return!1;var u=a.length-1;return o==u?a.pop():r.call(a,o,1),--this.size,!0}return Oo=n,Oo}var _o,Yh;function Qw(){if(Yh)return _o;Yh=1;var e=fa();function t(r){var n=this.__data__,i=e(n,r);return i<0?void 0:n[i][1]}return _o=t,_o}var So,Zh;function e1(){if(Zh)return So;Zh=1;var e=fa();function t(r){return e(this.__data__,r)>-1}return So=t,So}var Ao,Jh;function t1(){if(Jh)return Ao;Jh=1;var e=fa();function t(r,n){var i=this.__data__,a=e(i,r);return a<0?(++this.size,i.push([r,n])):i[a][1]=n,this}return Ao=t,Ao}var Po,Qh;function ha(){if(Qh)return Po;Qh=1;var e=Zw(),t=Jw(),r=Qw(),n=e1(),i=t1();function a(o){var u=-1,s=o==null?0:o.length;for(this.clear();++u<s;){var c=o[u];this.set(c[0],c[1])}}return a.prototype.clear=e,a.prototype.delete=t,a.prototype.get=r,a.prototype.has=n,a.prototype.set=i,Po=a,Po}var To,ed;function df(){if(ed)return To;ed=1;var e=Xt(),t=at(),r=e(t,"Map");return To=r,To}var Eo,td;function r1(){if(td)return Eo;td=1;var e=Yw(),t=ha(),r=df();function n(){this.size=0,this.__data__={hash:new e,map:new(r||t),string:new e}}return Eo=n,Eo}var jo,rd;function n1(){if(rd)return jo;rd=1;function e(t){var r=typeof t;return r=="string"||r=="number"||r=="symbol"||r=="boolean"?t!=="__proto__":t===null}return jo=e,jo}var Mo,nd;function da(){if(nd)return Mo;nd=1;var e=n1();function t(r,n){var i=r.__data__;return e(n)?i[typeof n=="string"?"string":"hash"]:i.map}return Mo=t,Mo}var Co,id;function i1(){if(id)return Co;id=1;var e=da();function t(r){var n=e(this,r).delete(r);return this.size-=n?1:0,n}return Co=t,Co}var $o,ad;function a1(){if(ad)return $o;ad=1;var e=da();function t(r){return e(this,r).get(r)}return $o=t,$o}var Io,od;function o1(){if(od)return Io;od=1;var e=da();function t(r){return e(this,r).has(r)}return Io=t,Io}var ko,ud;function u1(){if(ud)return ko;ud=1;var e=da();function t(r,n){var i=e(this,r),a=i.size;return i.set(r,n),this.size+=i.size==a?0:1,this}return ko=t,ko}var No,sd;function pf(){if(sd)return No;sd=1;var e=r1(),t=i1(),r=a1(),n=o1(),i=u1();function a(o){var u=-1,s=o==null?0:o.length;for(this.clear();++u<s;){var c=o[u];this.set(c[0],c[1])}}return a.prototype.clear=e,a.prototype.delete=t,a.prototype.get=r,a.prototype.has=n,a.prototype.set=i,No=a,No}var Ro,cd;function hb(){if(cd)return Ro;cd=1;var e=pf(),t="Expected a function";function r(n,i){if(typeof n!="function"||i!=null&&typeof i!="function")throw new TypeError(t);var a=function(){var o=arguments,u=i?i.apply(this,o):o[0],s=a.cache;if(s.has(u))return s.get(u);var c=n.apply(this,o);return a.cache=s.set(u,c)||s,c};return a.cache=new(r.Cache||e),a}return r.Cache=e,Ro=r,Ro}var Do,ld;function s1(){if(ld)return Do;ld=1;var e=hb(),t=500;function r(n){var i=e(n,function(o){return a.size===t&&a.clear(),o}),a=i.cache;return i}return Do=r,Do}var qo,fd;function c1(){if(fd)return qo;fd=1;var e=s1(),t=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,r=/\\(\\)?/g,n=e(function(i){var a=[];return i.charCodeAt(0)===46&&a.push(""),i.replace(t,function(o,u,s,c){a.push(s?c.replace(r,"$1"):u||o)}),a});return qo=n,qo}var Bo,hd;function vf(){if(hd)return Bo;hd=1;function e(t,r){for(var n=-1,i=t==null?0:t.length,a=Array(i);++n<i;)a[n]=r(t[n],n,t);return a}return Bo=e,Bo}var Lo,dd;function l1(){if(dd)return Lo;dd=1;var e=Wn(),t=vf(),r=Ne(),n=Dr(),i=e?e.prototype:void 0,a=i?i.toString:void 0;function o(u){if(typeof u=="string")return u;if(r(u))return t(u,o)+"";if(n(u))return a?a.call(u):"";var s=u+"";return s=="0"&&1/u==-1/0?"-0":s}return Lo=o,Lo}var Fo,pd;function db(){if(pd)return Fo;pd=1;var e=l1();function t(r){return r==null?"":e(r)}return Fo=t,Fo}var zo,vd;function pb(){if(vd)return zo;vd=1;var e=Ne(),t=lf(),r=c1(),n=db();function i(a,o){return e(a)?a:t(a,o)?[a]:r(n(a))}return zo=i,zo}var Uo,yd;function pa(){if(yd)return Uo;yd=1;var e=Dr();function t(r){if(typeof r=="string"||e(r))return r;var n=r+"";return n=="0"&&1/r==-1/0?"-0":n}return Uo=t,Uo}var Wo,md;function yf(){if(md)return Wo;md=1;var e=pb(),t=pa();function r(n,i){i=e(i,n);for(var a=0,o=i.length;n!=null&&a<o;)n=n[t(i[a++])];return a&&a==o?n:void 0}return Wo=r,Wo}var Ho,gd;function vb(){if(gd)return Ho;gd=1;var e=yf();function t(r,n,i){var a=r==null?void 0:e(r,n);return a===void 0?i:a}return Ho=t,Ho}var f1=vb();const Ue=se(f1);var Go,bd;function h1(){if(bd)return Go;bd=1;function e(t){return t==null}return Go=e,Go}var d1=h1();const ne=se(d1);var Ko,xd;function p1(){if(xd)return Ko;xd=1;var e=mt(),t=Ne(),r=gt(),n="[object String]";function i(a){return typeof a=="string"||!t(a)&&r(a)&&e(a)==n}return Ko=i,Ko}var v1=p1();const Ht=se(v1);var y1=ff();const Z=se(y1);var m1=Pt();const qr=se(m1);var Vo={exports:{}},te={};/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var wd;function g1(){if(wd)return te;wd=1;var e=Symbol.for("react.element"),t=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),n=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),a=Symbol.for("react.provider"),o=Symbol.for("react.context"),u=Symbol.for("react.server_context"),s=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),f=Symbol.for("react.suspense_list"),l=Symbol.for("react.memo"),h=Symbol.for("react.lazy"),p=Symbol.for("react.offscreen"),y;y=Symbol.for("react.module.reference");function v(d){if(typeof d=="object"&&d!==null){var x=d.$$typeof;switch(x){case e:switch(d=d.type,d){case r:case i:case n:case c:case f:return d;default:switch(d=d&&d.$$typeof,d){case u:case o:case s:case h:case l:case a:return d;default:return x}}case t:return x}}}return te.ContextConsumer=o,te.ContextProvider=a,te.Element=e,te.ForwardRef=s,te.Fragment=r,te.Lazy=h,te.Memo=l,te.Portal=t,te.Profiler=i,te.StrictMode=n,te.Suspense=c,te.SuspenseList=f,te.isAsyncMode=function(){return!1},te.isConcurrentMode=function(){return!1},te.isContextConsumer=function(d){return v(d)===o},te.isContextProvider=function(d){return v(d)===a},te.isElement=function(d){return typeof d=="object"&&d!==null&&d.$$typeof===e},te.isForwardRef=function(d){return v(d)===s},te.isFragment=function(d){return v(d)===r},te.isLazy=function(d){return v(d)===h},te.isMemo=function(d){return v(d)===l},te.isPortal=function(d){return v(d)===t},te.isProfiler=function(d){return v(d)===i},te.isStrictMode=function(d){return v(d)===n},te.isSuspense=function(d){return v(d)===c},te.isSuspenseList=function(d){return v(d)===f},te.isValidElementType=function(d){return typeof d=="string"||typeof d=="function"||d===r||d===i||d===n||d===c||d===f||d===p||typeof d=="object"&&d!==null&&(d.$$typeof===h||d.$$typeof===l||d.$$typeof===a||d.$$typeof===o||d.$$typeof===s||d.$$typeof===y||d.getModuleId!==void 0)},te.typeOf=v,te}var Od;function b1(){return Od||(Od=1,Vo.exports=g1()),Vo.exports}var x1=b1(),Xo,_d;function yb(){if(_d)return Xo;_d=1;var e=mt(),t=gt(),r="[object Number]";function n(i){return typeof i=="number"||t(i)&&e(i)==r}return Xo=n,Xo}var Yo,Sd;function w1(){if(Sd)return Yo;Sd=1;var e=yb();function t(r){return e(r)&&r!=+r}return Yo=t,Yo}var O1=w1();const Hn=se(O1);var _1=yb();const S1=se(_1);var Ye=function(t){return t===0?0:t>0?1:-1},qt=function(t){return Ht(t)&&t.indexOf("%")===t.length-1},B=function(t){return S1(t)&&!Hn(t)},xe=function(t){return B(t)||Ht(t)},A1=0,va=function(t){var r=++A1;return"".concat(t||"").concat(r)},Gt=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(!B(t)&&!Ht(t))return n;var a;if(qt(t)){var o=t.indexOf("%");a=r*parseFloat(t.slice(0,o))/100}else a=+t;return Hn(a)&&(a=n),i&&a>r&&(a=r),a},Ot=function(t){if(!t)return null;var r=Object.keys(t);return r&&r.length?t[r[0]]:null},P1=function(t){if(!Array.isArray(t))return!1;for(var r=t.length,n={},i=0;i<r;i++)if(!n[t[i]])n[t[i]]=!0;else return!0;return!1},rr=function(t,r){return B(t)&&B(r)?function(n){return t+n*(r-t)}:function(){return r}};function Kc(e,t,r){return!e||!e.length?null:e.find(function(n){return n&&(typeof t=="function"?t(n):Ue(n,t))===r})}var T1=function(t,r){return B(t)&&B(r)?t-r:Ht(t)&&Ht(r)?t.localeCompare(r):t instanceof Date&&r instanceof Date?t.getTime()-r.getTime():String(t).localeCompare(String(r))};function hr(e,t){for(var r in e)if({}.hasOwnProperty.call(e,r)&&(!{}.hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if({}.hasOwnProperty.call(t,n)&&!{}.hasOwnProperty.call(e,n))return!1;return!0}function Vc(e){"@babel/helpers - typeof";return Vc=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Vc(e)}var E1=["viewBox","children"],j1=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],Ad=["points","pathLength"],Zo={svg:E1,polygon:Ad,polyline:Ad},mf=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],yi=function(t,r){if(!t||typeof t=="function"||typeof t=="boolean")return null;var n=t;if(q.isValidElement(t)&&(n=t.props),!qr(n))return null;var i={};return Object.keys(n).forEach(function(a){mf.includes(a)&&(i[a]=r||function(o){return n[a](n,o)})}),i},M1=function(t,r,n){return function(i){return t(r,n,i),null}},mi=function(t,r,n){if(!qr(t)||Vc(t)!=="object")return null;var i=null;return Object.keys(t).forEach(function(a){var o=t[a];mf.includes(a)&&typeof o=="function"&&(i||(i={}),i[a]=M1(o,r,n))}),i},C1=["children"],$1=["children"];function Pd(e,t){if(e==null)return{};var r=I1(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function I1(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var Td={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},lt=function(t){return typeof t=="string"?t:t?t.displayName||t.name||"Component":""},Ed=null,Jo=null,gf=function e(t){if(t===Ed&&Array.isArray(Jo))return Jo;var r=[];return q.Children.forEach(t,function(n){ne(n)||(x1.isFragment(n)?r=r.concat(e(n.props.children)):r.push(n))}),Jo=r,Ed=t,r};function Ze(e,t){var r=[],n=[];return Array.isArray(t)?n=t.map(function(i){return lt(i)}):n=[lt(t)],gf(e).forEach(function(i){var a=Ue(i,"type.displayName")||Ue(i,"type.name");n.indexOf(a)!==-1&&r.push(i)}),r}function qe(e,t){var r=Ze(e,t);return r&&r[0]}var jd=function(t){if(!t||!t.props)return!1;var r=t.props,n=r.width,i=r.height;return!(!B(n)||n<=0||!B(i)||i<=0)},k1=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],N1=function(t){return t&&t.type&&Ht(t.type)&&k1.indexOf(t.type)>=0},R1=function(t,r,n,i){var a,o=(a=Zo?.[i])!==null&&a!==void 0?a:[];return r.startsWith("data-")||!Z(t)&&(i&&o.includes(r)||j1.includes(r))||n&&mf.includes(r)},ee=function(t,r,n){if(!t||typeof t=="function"||typeof t=="boolean")return null;var i=t;if(q.isValidElement(t)&&(i=t.props),!qr(i))return null;var a={};return Object.keys(i).forEach(function(o){var u;R1((u=i)===null||u===void 0?void 0:u[o],o,r,n)&&(a[o]=i[o])}),a},Xc=function e(t,r){if(t===r)return!0;var n=q.Children.count(t);if(n!==q.Children.count(r))return!1;if(n===0)return!0;if(n===1)return Md(Array.isArray(t)?t[0]:t,Array.isArray(r)?r[0]:r);for(var i=0;i<n;i++){var a=t[i],o=r[i];if(Array.isArray(a)||Array.isArray(o)){if(!e(a,o))return!1}else if(!Md(a,o))return!1}return!0},Md=function(t,r){if(ne(t)&&ne(r))return!0;if(!ne(t)&&!ne(r)){var n=t.props||{},i=n.children,a=Pd(n,C1),o=r.props||{},u=o.children,s=Pd(o,$1);return i&&u?hr(a,s)&&Xc(i,u):!i&&!u?hr(a,s):!1}return!1},Cd=function(t,r){var n=[],i={};return gf(t).forEach(function(a,o){if(N1(a))n.push(a);else if(a){var u=lt(a.type),s=r[u]||{},c=s.handler,f=s.once;if(c&&(!f||!i[u])){var l=c(a,u,o);n.push(l),i[u]=!0}}}),n},D1=function(t){var r=t&&t.type;return r&&Td[r]?Td[r]:null},q1=function(t,r){return gf(r).indexOf(t)},B1=["children","width","height","viewBox","className","style","title","desc"];function Yc(){return Yc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Yc.apply(this,arguments)}function L1(e,t){if(e==null)return{};var r=F1(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function F1(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Zc(e){var t=e.children,r=e.width,n=e.height,i=e.viewBox,a=e.className,o=e.style,u=e.title,s=e.desc,c=L1(e,B1),f=i||{width:r,height:n,x:0,y:0},l=ie("recharts-surface",a);return T.createElement("svg",Yc({},ee(c,!0,"svg"),{className:l,width:r,height:n,style:o,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),T.createElement("title",null,u),T.createElement("desc",null,s),t)}var z1=["children","className"];function Jc(){return Jc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Jc.apply(this,arguments)}function U1(e,t){if(e==null)return{};var r=W1(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function W1(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var _e=T.forwardRef(function(e,t){var r=e.children,n=e.className,i=U1(e,z1),a=ie("recharts-layer",n);return T.createElement("g",Jc({className:a},ee(i,!0),{ref:t}),r)}),ft=function(t,r){for(var n=arguments.length,i=new Array(n>2?n-2:0),a=2;a<n;a++)i[a-2]=arguments[a]},Qo,$d;function H1(){if($d)return Qo;$d=1;function e(t,r,n){var i=-1,a=t.length;r<0&&(r=-r>a?0:a+r),n=n>a?a:n,n<0&&(n+=a),a=r>n?0:n-r>>>0,r>>>=0;for(var o=Array(a);++i<a;)o[i]=t[i+r];return o}return Qo=e,Qo}var eu,Id;function G1(){if(Id)return eu;Id=1;var e=H1();function t(r,n,i){var a=r.length;return i=i===void 0?a:i,!n&&i>=a?r:e(r,n,i)}return eu=t,eu}var tu,kd;function mb(){if(kd)return tu;kd=1;var e="\\ud800-\\udfff",t="\\u0300-\\u036f",r="\\ufe20-\\ufe2f",n="\\u20d0-\\u20ff",i=t+r+n,a="\\ufe0e\\ufe0f",o="\\u200d",u=RegExp("["+o+e+i+a+"]");function s(c){return u.test(c)}return tu=s,tu}var ru,Nd;function K1(){if(Nd)return ru;Nd=1;function e(t){return t.split("")}return ru=e,ru}var nu,Rd;function V1(){if(Rd)return nu;Rd=1;var e="\\ud800-\\udfff",t="\\u0300-\\u036f",r="\\ufe20-\\ufe2f",n="\\u20d0-\\u20ff",i=t+r+n,a="\\ufe0e\\ufe0f",o="["+e+"]",u="["+i+"]",s="\\ud83c[\\udffb-\\udfff]",c="(?:"+u+"|"+s+")",f="[^"+e+"]",l="(?:\\ud83c[\\udde6-\\uddff]){2}",h="[\\ud800-\\udbff][\\udc00-\\udfff]",p="\\u200d",y=c+"?",v="["+a+"]?",d="(?:"+p+"(?:"+[f,l,h].join("|")+")"+v+y+")*",x=v+y+d,w="(?:"+[f+u+"?",u,l,h,o].join("|")+")",b=RegExp(s+"(?="+s+")|"+w+x,"g");function O(m){return m.match(b)||[]}return nu=O,nu}var iu,Dd;function X1(){if(Dd)return iu;Dd=1;var e=K1(),t=mb(),r=V1();function n(i){return t(i)?r(i):e(i)}return iu=n,iu}var au,qd;function Y1(){if(qd)return au;qd=1;var e=G1(),t=mb(),r=X1(),n=db();function i(a){return function(o){o=n(o);var u=t(o)?r(o):void 0,s=u?u[0]:o.charAt(0),c=u?e(u,1).join(""):o.slice(1);return s[a]()+c}}return au=i,au}var ou,Bd;function Z1(){if(Bd)return ou;Bd=1;var e=Y1(),t=e("toUpperCase");return ou=t,ou}var J1=Z1();const ya=se(J1);function ue(e){return function(){return e}}const gb=Math.cos,gi=Math.sin,Je=Math.sqrt,bi=Math.PI,ma=2*bi,Qc=Math.PI,el=2*Qc,Rt=1e-6,Q1=el-Rt;function bb(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}function eO(e){let t=Math.floor(e);if(!(t>=0))throw new Error(`invalid digits: ${e}`);if(t>15)return bb;const r=10**t;return function(n){this._+=n[0];for(let i=1,a=n.length;i<a;++i)this._+=Math.round(arguments[i]*r)/r+n[i]}}class tO{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=t==null?bb:eO(t)}moveTo(t,r){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}`}closePath(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,r){this._append`L${this._x1=+t},${this._y1=+r}`}quadraticCurveTo(t,r,n,i){this._append`Q${+t},${+r},${this._x1=+n},${this._y1=+i}`}bezierCurveTo(t,r,n,i,a,o){this._append`C${+t},${+r},${+n},${+i},${this._x1=+a},${this._y1=+o}`}arcTo(t,r,n,i,a){if(t=+t,r=+r,n=+n,i=+i,a=+a,a<0)throw new Error(`negative radius: ${a}`);let o=this._x1,u=this._y1,s=n-t,c=i-r,f=o-t,l=u-r,h=f*f+l*l;if(this._x1===null)this._append`M${this._x1=t},${this._y1=r}`;else if(h>Rt)if(!(Math.abs(l*s-c*f)>Rt)||!a)this._append`L${this._x1=t},${this._y1=r}`;else{let p=n-o,y=i-u,v=s*s+c*c,d=p*p+y*y,x=Math.sqrt(v),w=Math.sqrt(h),b=a*Math.tan((Qc-Math.acos((v+h-d)/(2*x*w)))/2),O=b/w,m=b/x;Math.abs(O-1)>Rt&&this._append`L${t+O*f},${r+O*l}`,this._append`A${a},${a},0,0,${+(l*p>f*y)},${this._x1=t+m*s},${this._y1=r+m*c}`}}arc(t,r,n,i,a,o){if(t=+t,r=+r,n=+n,o=!!o,n<0)throw new Error(`negative radius: ${n}`);let u=n*Math.cos(i),s=n*Math.sin(i),c=t+u,f=r+s,l=1^o,h=o?i-a:a-i;this._x1===null?this._append`M${c},${f}`:(Math.abs(this._x1-c)>Rt||Math.abs(this._y1-f)>Rt)&&this._append`L${c},${f}`,n&&(h<0&&(h=h%el+el),h>Q1?this._append`A${n},${n},0,1,${l},${t-u},${r-s}A${n},${n},0,1,${l},${this._x1=c},${this._y1=f}`:h>Rt&&this._append`A${n},${n},0,${+(h>=Qc)},${l},${this._x1=t+n*Math.cos(a)},${this._y1=r+n*Math.sin(a)}`)}rect(t,r,n,i){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}h${n=+n}v${+i}h${-n}Z`}toString(){return this._}}function bf(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(r==null)t=null;else{const n=Math.floor(r);if(!(n>=0))throw new RangeError(`invalid digits: ${r}`);t=n}return e},()=>new tO(t)}function xf(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function xb(e){this._context=e}xb.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t);break}}};function ga(e){return new xb(e)}function wb(e){return e[0]}function Ob(e){return e[1]}function _b(e,t){var r=ue(!0),n=null,i=ga,a=null,o=bf(u);e=typeof e=="function"?e:e===void 0?wb:ue(e),t=typeof t=="function"?t:t===void 0?Ob:ue(t);function u(s){var c,f=(s=xf(s)).length,l,h=!1,p;for(n==null&&(a=i(p=o())),c=0;c<=f;++c)!(c<f&&r(l=s[c],c,s))===h&&((h=!h)?a.lineStart():a.lineEnd()),h&&a.point(+e(l,c,s),+t(l,c,s));if(p)return a=null,p+""||null}return u.x=function(s){return arguments.length?(e=typeof s=="function"?s:ue(+s),u):e},u.y=function(s){return arguments.length?(t=typeof s=="function"?s:ue(+s),u):t},u.defined=function(s){return arguments.length?(r=typeof s=="function"?s:ue(!!s),u):r},u.curve=function(s){return arguments.length?(i=s,n!=null&&(a=i(n)),u):i},u.context=function(s){return arguments.length?(s==null?n=a=null:a=i(n=s),u):n},u}function ni(e,t,r){var n=null,i=ue(!0),a=null,o=ga,u=null,s=bf(c);e=typeof e=="function"?e:e===void 0?wb:ue(+e),t=typeof t=="function"?t:ue(t===void 0?0:+t),r=typeof r=="function"?r:r===void 0?Ob:ue(+r);function c(l){var h,p,y,v=(l=xf(l)).length,d,x=!1,w,b=new Array(v),O=new Array(v);for(a==null&&(u=o(w=s())),h=0;h<=v;++h){if(!(h<v&&i(d=l[h],h,l))===x)if(x=!x)p=h,u.areaStart(),u.lineStart();else{for(u.lineEnd(),u.lineStart(),y=h-1;y>=p;--y)u.point(b[y],O[y]);u.lineEnd(),u.areaEnd()}x&&(b[h]=+e(d,h,l),O[h]=+t(d,h,l),u.point(n?+n(d,h,l):b[h],r?+r(d,h,l):O[h]))}if(w)return u=null,w+""||null}function f(){return _b().defined(i).curve(o).context(a)}return c.x=function(l){return arguments.length?(e=typeof l=="function"?l:ue(+l),n=null,c):e},c.x0=function(l){return arguments.length?(e=typeof l=="function"?l:ue(+l),c):e},c.x1=function(l){return arguments.length?(n=l==null?null:typeof l=="function"?l:ue(+l),c):n},c.y=function(l){return arguments.length?(t=typeof l=="function"?l:ue(+l),r=null,c):t},c.y0=function(l){return arguments.length?(t=typeof l=="function"?l:ue(+l),c):t},c.y1=function(l){return arguments.length?(r=l==null?null:typeof l=="function"?l:ue(+l),c):r},c.lineX0=c.lineY0=function(){return f().x(e).y(t)},c.lineY1=function(){return f().x(e).y(r)},c.lineX1=function(){return f().x(n).y(t)},c.defined=function(l){return arguments.length?(i=typeof l=="function"?l:ue(!!l),c):i},c.curve=function(l){return arguments.length?(o=l,a!=null&&(u=o(a)),c):o},c.context=function(l){return arguments.length?(l==null?a=u=null:u=o(a=l),c):a},c}class Sb{constructor(t,r){this._context=t,this._x=r}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line}point(t,r){switch(t=+t,r=+r,this._point){case 0:{this._point=1,this._line?this._context.lineTo(t,r):this._context.moveTo(t,r);break}case 1:this._point=2;default:{this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,r,t,r):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+r)/2,t,this._y0,t,r);break}}this._x0=t,this._y0=r}}function rO(e){return new Sb(e,!0)}function nO(e){return new Sb(e,!1)}const wf={draw(e,t){const r=Je(t/bi);e.moveTo(r,0),e.arc(0,0,r,0,ma)}},iO={draw(e,t){const r=Je(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},Ab=Je(1/3),aO=Ab*2,oO={draw(e,t){const r=Je(t/aO),n=r*Ab;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},uO={draw(e,t){const r=Je(t),n=-r/2;e.rect(n,n,r,r)}},sO=.8908130915292852,Pb=gi(bi/10)/gi(7*bi/10),cO=gi(ma/10)*Pb,lO=-gb(ma/10)*Pb,fO={draw(e,t){const r=Je(t*sO),n=cO*r,i=lO*r;e.moveTo(0,-r),e.lineTo(n,i);for(let a=1;a<5;++a){const o=ma*a/5,u=gb(o),s=gi(o);e.lineTo(s*r,-u*r),e.lineTo(u*n-s*i,s*n+u*i)}e.closePath()}},uu=Je(3),hO={draw(e,t){const r=-Je(t/(uu*3));e.moveTo(0,r*2),e.lineTo(-uu*r,-r),e.lineTo(uu*r,-r),e.closePath()}},Le=-.5,Fe=Je(3)/2,tl=1/Je(12),dO=(tl/2+1)*3,pO={draw(e,t){const r=Je(t/dO),n=r/2,i=r*tl,a=n,o=r*tl+r,u=-a,s=o;e.moveTo(n,i),e.lineTo(a,o),e.lineTo(u,s),e.lineTo(Le*n-Fe*i,Fe*n+Le*i),e.lineTo(Le*a-Fe*o,Fe*a+Le*o),e.lineTo(Le*u-Fe*s,Fe*u+Le*s),e.lineTo(Le*n+Fe*i,Le*i-Fe*n),e.lineTo(Le*a+Fe*o,Le*o-Fe*a),e.lineTo(Le*u+Fe*s,Le*s-Fe*u),e.closePath()}};function vO(e,t){let r=null,n=bf(i);e=typeof e=="function"?e:ue(e||wf),t=typeof t=="function"?t:ue(t===void 0?64:+t);function i(){let a;if(r||(r=a=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),a)return r=null,a+""||null}return i.type=function(a){return arguments.length?(e=typeof a=="function"?a:ue(a),i):e},i.size=function(a){return arguments.length?(t=typeof a=="function"?a:ue(+a),i):t},i.context=function(a){return arguments.length?(r=a??null,i):r},i}function xi(){}function wi(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function Tb(e){this._context=e}Tb.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:wi(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:wi(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function yO(e){return new Tb(e)}function Eb(e){this._context=e}Eb.prototype={areaStart:xi,areaEnd:xi,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x2,this._y2),this._context.closePath();break}case 2:{this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break}case 3:{this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4);break}}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:wi(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function mO(e){return new Eb(e)}function jb(e){this._context=e}jb.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:wi(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function gO(e){return new jb(e)}function Mb(e){this._context=e}Mb.prototype={areaStart:xi,areaEnd:xi,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e=+e,t=+t,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function bO(e){return new Mb(e)}function Ld(e){return e<0?-1:1}function Fd(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0),u=(a*i+o*n)/(n+i);return(Ld(a)+Ld(o))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs(u))||0}function zd(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function su(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,u=(a-n)/3;e._context.bezierCurveTo(n+u,i+u*t,a-u,o-u*r,a,o)}function Oi(e){this._context=e}Oi.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:su(this,this._t0,zd(this,this._t0));break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(e=+e,t=+t,!(e===this._x1&&t===this._y1)){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,su(this,zd(this,r=Fd(this,e,t)),r);break;default:su(this,this._t0,r=Fd(this,e,t));break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}};function Cb(e){this._context=new $b(e)}(Cb.prototype=Object.create(Oi.prototype)).point=function(e,t){Oi.prototype.point.call(this,t,e)};function $b(e){this._context=e}$b.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}};function xO(e){return new Oi(e)}function wO(e){return new Cb(e)}function Ib(e){this._context=e}Ib.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),r===2)this._context.lineTo(e[1],t[1]);else for(var n=Ud(e),i=Ud(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o]);(this._line||this._line!==0&&r===1)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}};function Ud(e){var t,r=e.length-1,n,i=new Array(r),a=new Array(r),o=new Array(r);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<r-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[r-1]=2,a[r-1]=7,o[r-1]=8*e[r-1]+e[r],t=1;t<r;++t)n=i[t]/a[t-1],a[t]-=n,o[t]-=n*o[t-1];for(i[r-1]=o[r-1]/a[r-1],t=r-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(a[r-1]=(e[r]+i[r-1])/2,t=0;t<r-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function OO(e){return new Ib(e)}function ba(e,t){this._context=e,this._t=t}ba.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&this._point===2&&this._context.lineTo(this._x,this._y),(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:{if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}break}}this._x=e,this._y=t}};function _O(e){return new ba(e,.5)}function SO(e){return new ba(e,0)}function AO(e){return new ba(e,1)}function yr(e,t){if((o=e.length)>1)for(var r=1,n,i,a=e[t[0]],o,u=a.length;r<o;++r)for(i=a,a=e[t[r]],n=0;n<u;++n)a[n][1]+=a[n][0]=isNaN(i[n][1])?i[n][0]:i[n][1]}function rl(e){for(var t=e.length,r=new Array(t);--t>=0;)r[t]=t;return r}function PO(e,t){return e[t]}function TO(e){const t=[];return t.key=e,t}function EO(){var e=ue([]),t=rl,r=yr,n=PO;function i(a){var o=Array.from(e.apply(this,arguments),TO),u,s=o.length,c=-1,f;for(const l of a)for(u=0,++c;u<s;++u)(o[u][c]=[0,+n(l,o[u].key,c,a)]).data=l;for(u=0,f=xf(t(o));u<s;++u)o[f[u]].index=u;return r(o,f),o}return i.keys=function(a){return arguments.length?(e=typeof a=="function"?a:ue(Array.from(a)),i):e},i.value=function(a){return arguments.length?(n=typeof a=="function"?a:ue(+a),i):n},i.order=function(a){return arguments.length?(t=a==null?rl:typeof a=="function"?a:ue(Array.from(a)),i):t},i.offset=function(a){return arguments.length?(r=a??yr,i):r},i}function jO(e,t){if((n=e.length)>0){for(var r,n,i=0,a=e[0].length,o;i<a;++i){for(o=r=0;r<n;++r)o+=e[r][i][1]||0;if(o)for(r=0;r<n;++r)e[r][i][1]/=o}yr(e,t)}}function MO(e,t){if((i=e.length)>0){for(var r=0,n=e[t[0]],i,a=n.length;r<a;++r){for(var o=0,u=0;o<i;++o)u+=e[o][r][1]||0;n[r][1]+=n[r][0]=-u/2}yr(e,t)}}function CO(e,t){if(!(!((o=e.length)>0)||!((a=(i=e[t[0]]).length)>0))){for(var r=0,n=1,i,a,o;n<a;++n){for(var u=0,s=0,c=0;u<o;++u){for(var f=e[t[u]],l=f[n][1]||0,h=f[n-1][1]||0,p=(l-h)/2,y=0;y<u;++y){var v=e[t[y]],d=v[n][1]||0,x=v[n-1][1]||0;p+=d-x}s+=l,c+=p*l}i[n-1][1]+=i[n-1][0]=r,s&&(r-=c/s)}i[n-1][1]+=i[n-1][0]=r,yr(e,t)}}function cn(e){"@babel/helpers - typeof";return cn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},cn(e)}var $O=["type","size","sizeType"];function nl(){return nl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},nl.apply(this,arguments)}function Wd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Hd(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Wd(Object(r),!0).forEach(function(n){IO(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Wd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function IO(e,t,r){return t=kO(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function kO(e){var t=NO(e,"string");return cn(t)=="symbol"?t:t+""}function NO(e,t){if(cn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(cn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function RO(e,t){if(e==null)return{};var r=DO(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function DO(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var kb={symbolCircle:wf,symbolCross:iO,symbolDiamond:oO,symbolSquare:uO,symbolStar:fO,symbolTriangle:hO,symbolWye:pO},qO=Math.PI/180,BO=function(t){var r="symbol".concat(ya(t));return kb[r]||wf},LO=function(t,r,n){if(r==="area")return t;switch(n){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":{var i=18*qO;return 1.25*t*t*(Math.tan(i)-Math.tan(i*2)*Math.pow(Math.tan(i),2))}case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},FO=function(t,r){kb["symbol".concat(ya(t))]=r},Of=function(t){var r=t.type,n=r===void 0?"circle":r,i=t.size,a=i===void 0?64:i,o=t.sizeType,u=o===void 0?"area":o,s=RO(t,$O),c=Hd(Hd({},s),{},{type:n,size:a,sizeType:u}),f=function(){var d=BO(n),x=vO().type(d).size(LO(a,u,n));return x()},l=c.className,h=c.cx,p=c.cy,y=ee(c,!0);return h===+h&&p===+p&&a===+a?T.createElement("path",nl({},y,{className:ie("recharts-symbols",l),transform:"translate(".concat(h,", ").concat(p,")"),d:f()})):null};Of.registerSymbol=FO;function mr(e){"@babel/helpers - typeof";return mr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},mr(e)}function il(){return il=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},il.apply(this,arguments)}function Gd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function zO(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Gd(Object(r),!0).forEach(function(n){ln(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Gd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function UO(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function WO(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Rb(n.key),n)}}function HO(e,t,r){return t&&WO(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function GO(e,t,r){return t=_i(t),KO(e,Nb()?Reflect.construct(t,r||[],_i(e).constructor):t.apply(e,r))}function KO(e,t){if(t&&(mr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return VO(e)}function VO(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Nb(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Nb=function(){return!!e})()}function _i(e){return _i=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},_i(e)}function XO(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&al(e,t)}function al(e,t){return al=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},al(e,t)}function ln(e,t,r){return t=Rb(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Rb(e){var t=YO(e,"string");return mr(t)=="symbol"?t:t+""}function YO(e,t){if(mr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(mr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Ge=32,_f=function(e){function t(){return UO(this,t),GO(this,t,arguments)}return XO(t,e),HO(t,[{key:"renderIcon",value:function(n){var i=this.props.inactiveColor,a=Ge/2,o=Ge/6,u=Ge/3,s=n.inactive?i:n.color;if(n.type==="plainline")return T.createElement("line",{strokeWidth:4,fill:"none",stroke:s,strokeDasharray:n.payload.strokeDasharray,x1:0,y1:a,x2:Ge,y2:a,className:"recharts-legend-icon"});if(n.type==="line")return T.createElement("path",{strokeWidth:4,fill:"none",stroke:s,d:"M0,".concat(a,"h").concat(u,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(2*u,",").concat(a,`
            H`).concat(Ge,"M").concat(2*u,",").concat(a,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(u,",").concat(a),className:"recharts-legend-icon"});if(n.type==="rect")return T.createElement("path",{stroke:"none",fill:s,d:"M0,".concat(Ge/8,"h").concat(Ge,"v").concat(Ge*3/4,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(T.isValidElement(n.legendIcon)){var c=zO({},n);return delete c.legendIcon,T.cloneElement(n.legendIcon,c)}return T.createElement(Of,{fill:s,cx:a,cy:a,size:Ge,sizeType:"diameter",type:n.type})}},{key:"renderItems",value:function(){var n=this,i=this.props,a=i.payload,o=i.iconSize,u=i.layout,s=i.formatter,c=i.inactiveColor,f={x:0,y:0,width:Ge,height:Ge},l={display:u==="horizontal"?"inline-block":"block",marginRight:10},h={display:"inline-block",verticalAlign:"middle",marginRight:4};return a.map(function(p,y){var v=p.formatter||s,d=ie(ln(ln({"recharts-legend-item":!0},"legend-item-".concat(y),!0),"inactive",p.inactive));if(p.type==="none")return null;var x=Z(p.value)?null:p.value;ft(!Z(p.value),`The name property is also required when using a function for the dataKey of a chart's cartesian components. Ex: <Bar name="Name of my Data"/>`);var w=p.inactive?c:p.color;return T.createElement("li",il({className:d,style:l,key:"legend-item-".concat(y)},mi(n.props,p,y)),T.createElement(Zc,{width:o,height:o,viewBox:f,style:h},n.renderIcon(p)),T.createElement("span",{className:"recharts-legend-item-text",style:{color:w}},v?v(x,p,y):x))})}},{key:"render",value:function(){var n=this.props,i=n.payload,a=n.layout,o=n.align;if(!i||!i.length)return null;var u={padding:0,margin:0,textAlign:a==="horizontal"?o:"left"};return T.createElement("ul",{className:"recharts-default-legend",style:u},this.renderItems())}}])}(q.PureComponent);ln(_f,"displayName","Legend");ln(_f,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var cu,Kd;function ZO(){if(Kd)return cu;Kd=1;var e=ha();function t(){this.__data__=new e,this.size=0}return cu=t,cu}var lu,Vd;function JO(){if(Vd)return lu;Vd=1;function e(t){var r=this.__data__,n=r.delete(t);return this.size=r.size,n}return lu=e,lu}var fu,Xd;function QO(){if(Xd)return fu;Xd=1;function e(t){return this.__data__.get(t)}return fu=e,fu}var hu,Yd;function e_(){if(Yd)return hu;Yd=1;function e(t){return this.__data__.has(t)}return hu=e,hu}var du,Zd;function t_(){if(Zd)return du;Zd=1;var e=ha(),t=df(),r=pf(),n=200;function i(a,o){var u=this.__data__;if(u instanceof e){var s=u.__data__;if(!t||s.length<n-1)return s.push([a,o]),this.size=++u.size,this;u=this.__data__=new r(s)}return u.set(a,o),this.size=u.size,this}return du=i,du}var pu,Jd;function Db(){if(Jd)return pu;Jd=1;var e=ha(),t=ZO(),r=JO(),n=QO(),i=e_(),a=t_();function o(u){var s=this.__data__=new e(u);this.size=s.size}return o.prototype.clear=t,o.prototype.delete=r,o.prototype.get=n,o.prototype.has=i,o.prototype.set=a,pu=o,pu}var vu,Qd;function r_(){if(Qd)return vu;Qd=1;var e="__lodash_hash_undefined__";function t(r){return this.__data__.set(r,e),this}return vu=t,vu}var yu,ep;function n_(){if(ep)return yu;ep=1;function e(t){return this.__data__.has(t)}return yu=e,yu}var mu,tp;function qb(){if(tp)return mu;tp=1;var e=pf(),t=r_(),r=n_();function n(i){var a=-1,o=i==null?0:i.length;for(this.__data__=new e;++a<o;)this.add(i[a])}return n.prototype.add=n.prototype.push=t,n.prototype.has=r,mu=n,mu}var gu,rp;function Bb(){if(rp)return gu;rp=1;function e(t,r){for(var n=-1,i=t==null?0:t.length;++n<i;)if(r(t[n],n,t))return!0;return!1}return gu=e,gu}var bu,np;function Lb(){if(np)return bu;np=1;function e(t,r){return t.has(r)}return bu=e,bu}var xu,ip;function Fb(){if(ip)return xu;ip=1;var e=qb(),t=Bb(),r=Lb(),n=1,i=2;function a(o,u,s,c,f,l){var h=s&n,p=o.length,y=u.length;if(p!=y&&!(h&&y>p))return!1;var v=l.get(o),d=l.get(u);if(v&&d)return v==u&&d==o;var x=-1,w=!0,b=s&i?new e:void 0;for(l.set(o,u),l.set(u,o);++x<p;){var O=o[x],m=u[x];if(c)var g=h?c(m,O,x,u,o,l):c(O,m,x,o,u,l);if(g!==void 0){if(g)continue;w=!1;break}if(b){if(!t(u,function(_,S){if(!r(b,S)&&(O===_||f(O,_,s,c,l)))return b.push(S)})){w=!1;break}}else if(!(O===m||f(O,m,s,c,l))){w=!1;break}}return l.delete(o),l.delete(u),w}return xu=a,xu}var wu,ap;function i_(){if(ap)return wu;ap=1;var e=at(),t=e.Uint8Array;return wu=t,wu}var Ou,op;function a_(){if(op)return Ou;op=1;function e(t){var r=-1,n=Array(t.size);return t.forEach(function(i,a){n[++r]=[a,i]}),n}return Ou=e,Ou}var _u,up;function Sf(){if(up)return _u;up=1;function e(t){var r=-1,n=Array(t.size);return t.forEach(function(i){n[++r]=i}),n}return _u=e,_u}var Su,sp;function o_(){if(sp)return Su;sp=1;var e=Wn(),t=i_(),r=hf(),n=Fb(),i=a_(),a=Sf(),o=1,u=2,s="[object Boolean]",c="[object Date]",f="[object Error]",l="[object Map]",h="[object Number]",p="[object RegExp]",y="[object Set]",v="[object String]",d="[object Symbol]",x="[object ArrayBuffer]",w="[object DataView]",b=e?e.prototype:void 0,O=b?b.valueOf:void 0;function m(g,_,S,P,C,A,E){switch(S){case w:if(g.byteLength!=_.byteLength||g.byteOffset!=_.byteOffset)return!1;g=g.buffer,_=_.buffer;case x:return!(g.byteLength!=_.byteLength||!A(new t(g),new t(_)));case s:case c:case h:return r(+g,+_);case f:return g.name==_.name&&g.message==_.message;case p:case v:return g==_+"";case l:var j=i;case y:var k=P&o;if(j||(j=a),g.size!=_.size&&!k)return!1;var I=E.get(g);if(I)return I==_;P|=u,E.set(g,_);var N=n(j(g),j(_),P,C,A,E);return E.delete(g),N;case d:if(O)return O.call(g)==O.call(_)}return!1}return Su=m,Su}var Au,cp;function zb(){if(cp)return Au;cp=1;function e(t,r){for(var n=-1,i=r.length,a=t.length;++n<i;)t[a+n]=r[n];return t}return Au=e,Au}var Pu,lp;function u_(){if(lp)return Pu;lp=1;var e=zb(),t=Ne();function r(n,i,a){var o=i(n);return t(n)?o:e(o,a(n))}return Pu=r,Pu}var Tu,fp;function s_(){if(fp)return Tu;fp=1;function e(t,r){for(var n=-1,i=t==null?0:t.length,a=0,o=[];++n<i;){var u=t[n];r(u,n,t)&&(o[a++]=u)}return o}return Tu=e,Tu}var Eu,hp;function c_(){if(hp)return Eu;hp=1;function e(){return[]}return Eu=e,Eu}var ju,dp;function l_(){if(dp)return ju;dp=1;var e=s_(),t=c_(),r=Object.prototype,n=r.propertyIsEnumerable,i=Object.getOwnPropertySymbols,a=i?function(o){return o==null?[]:(o=Object(o),e(i(o),function(u){return n.call(o,u)}))}:t;return ju=a,ju}var Mu,pp;function f_(){if(pp)return Mu;pp=1;function e(t,r){for(var n=-1,i=Array(t);++n<t;)i[n]=r(n);return i}return Mu=e,Mu}var Cu,vp;function h_(){if(vp)return Cu;vp=1;var e=mt(),t=gt(),r="[object Arguments]";function n(i){return t(i)&&e(i)==r}return Cu=n,Cu}var $u,yp;function Af(){if(yp)return $u;yp=1;var e=h_(),t=gt(),r=Object.prototype,n=r.hasOwnProperty,i=r.propertyIsEnumerable,a=e(function(){return arguments}())?e:function(o){return t(o)&&n.call(o,"callee")&&!i.call(o,"callee")};return $u=a,$u}var en={exports:{}},Iu,mp;function d_(){if(mp)return Iu;mp=1;function e(){return!1}return Iu=e,Iu}en.exports;var gp;function Ub(){return gp||(gp=1,function(e,t){var r=at(),n=d_(),i=t&&!t.nodeType&&t,a=i&&!0&&e&&!e.nodeType&&e,o=a&&a.exports===i,u=o?r.Buffer:void 0,s=u?u.isBuffer:void 0,c=s||n;e.exports=c}(en,en.exports)),en.exports}var ku,bp;function Pf(){if(bp)return ku;bp=1;var e=9007199254740991,t=/^(?:0|[1-9]\d*)$/;function r(n,i){var a=typeof n;return i=i??e,!!i&&(a=="number"||a!="symbol"&&t.test(n))&&n>-1&&n%1==0&&n<i}return ku=r,ku}var Nu,xp;function Tf(){if(xp)return Nu;xp=1;var e=9007199254740991;function t(r){return typeof r=="number"&&r>-1&&r%1==0&&r<=e}return Nu=t,Nu}var Ru,wp;function p_(){if(wp)return Ru;wp=1;var e=mt(),t=Tf(),r=gt(),n="[object Arguments]",i="[object Array]",a="[object Boolean]",o="[object Date]",u="[object Error]",s="[object Function]",c="[object Map]",f="[object Number]",l="[object Object]",h="[object RegExp]",p="[object Set]",y="[object String]",v="[object WeakMap]",d="[object ArrayBuffer]",x="[object DataView]",w="[object Float32Array]",b="[object Float64Array]",O="[object Int8Array]",m="[object Int16Array]",g="[object Int32Array]",_="[object Uint8Array]",S="[object Uint8ClampedArray]",P="[object Uint16Array]",C="[object Uint32Array]",A={};A[w]=A[b]=A[O]=A[m]=A[g]=A[_]=A[S]=A[P]=A[C]=!0,A[n]=A[i]=A[d]=A[a]=A[x]=A[o]=A[u]=A[s]=A[c]=A[f]=A[l]=A[h]=A[p]=A[y]=A[v]=!1;function E(j){return r(j)&&t(j.length)&&!!A[e(j)]}return Ru=E,Ru}var Du,Op;function Wb(){if(Op)return Du;Op=1;function e(t){return function(r){return t(r)}}return Du=e,Du}var tn={exports:{}};tn.exports;var _p;function v_(){return _p||(_p=1,function(e,t){var r=lb(),n=t&&!t.nodeType&&t,i=n&&!0&&e&&!e.nodeType&&e,a=i&&i.exports===n,o=a&&r.process,u=function(){try{var s=i&&i.require&&i.require("util").types;return s||o&&o.binding&&o.binding("util")}catch{}}();e.exports=u}(tn,tn.exports)),tn.exports}var qu,Sp;function Hb(){if(Sp)return qu;Sp=1;var e=p_(),t=Wb(),r=v_(),n=r&&r.isTypedArray,i=n?t(n):e;return qu=i,qu}var Bu,Ap;function y_(){if(Ap)return Bu;Ap=1;var e=f_(),t=Af(),r=Ne(),n=Ub(),i=Pf(),a=Hb(),o=Object.prototype,u=o.hasOwnProperty;function s(c,f){var l=r(c),h=!l&&t(c),p=!l&&!h&&n(c),y=!l&&!h&&!p&&a(c),v=l||h||p||y,d=v?e(c.length,String):[],x=d.length;for(var w in c)(f||u.call(c,w))&&!(v&&(w=="length"||p&&(w=="offset"||w=="parent")||y&&(w=="buffer"||w=="byteLength"||w=="byteOffset")||i(w,x)))&&d.push(w);return d}return Bu=s,Bu}var Lu,Pp;function m_(){if(Pp)return Lu;Pp=1;var e=Object.prototype;function t(r){var n=r&&r.constructor,i=typeof n=="function"&&n.prototype||e;return r===i}return Lu=t,Lu}var Fu,Tp;function Gb(){if(Tp)return Fu;Tp=1;function e(t,r){return function(n){return t(r(n))}}return Fu=e,Fu}var zu,Ep;function g_(){if(Ep)return zu;Ep=1;var e=Gb(),t=e(Object.keys,Object);return zu=t,zu}var Uu,jp;function b_(){if(jp)return Uu;jp=1;var e=m_(),t=g_(),r=Object.prototype,n=r.hasOwnProperty;function i(a){if(!e(a))return t(a);var o=[];for(var u in Object(a))n.call(a,u)&&u!="constructor"&&o.push(u);return o}return Uu=i,Uu}var Wu,Mp;function Gn(){if(Mp)return Wu;Mp=1;var e=ff(),t=Tf();function r(n){return n!=null&&t(n.length)&&!e(n)}return Wu=r,Wu}var Hu,Cp;function xa(){if(Cp)return Hu;Cp=1;var e=y_(),t=b_(),r=Gn();function n(i){return r(i)?e(i):t(i)}return Hu=n,Hu}var Gu,$p;function x_(){if($p)return Gu;$p=1;var e=u_(),t=l_(),r=xa();function n(i){return e(i,r,t)}return Gu=n,Gu}var Ku,Ip;function w_(){if(Ip)return Ku;Ip=1;var e=x_(),t=1,r=Object.prototype,n=r.hasOwnProperty;function i(a,o,u,s,c,f){var l=u&t,h=e(a),p=h.length,y=e(o),v=y.length;if(p!=v&&!l)return!1;for(var d=p;d--;){var x=h[d];if(!(l?x in o:n.call(o,x)))return!1}var w=f.get(a),b=f.get(o);if(w&&b)return w==o&&b==a;var O=!0;f.set(a,o),f.set(o,a);for(var m=l;++d<p;){x=h[d];var g=a[x],_=o[x];if(s)var S=l?s(_,g,x,o,a,f):s(g,_,x,a,o,f);if(!(S===void 0?g===_||c(g,_,u,s,f):S)){O=!1;break}m||(m=x=="constructor")}if(O&&!m){var P=a.constructor,C=o.constructor;P!=C&&"constructor"in a&&"constructor"in o&&!(typeof P=="function"&&P instanceof P&&typeof C=="function"&&C instanceof C)&&(O=!1)}return f.delete(a),f.delete(o),O}return Ku=i,Ku}var Vu,kp;function O_(){if(kp)return Vu;kp=1;var e=Xt(),t=at(),r=e(t,"DataView");return Vu=r,Vu}var Xu,Np;function __(){if(Np)return Xu;Np=1;var e=Xt(),t=at(),r=e(t,"Promise");return Xu=r,Xu}var Yu,Rp;function Kb(){if(Rp)return Yu;Rp=1;var e=Xt(),t=at(),r=e(t,"Set");return Yu=r,Yu}var Zu,Dp;function S_(){if(Dp)return Zu;Dp=1;var e=Xt(),t=at(),r=e(t,"WeakMap");return Zu=r,Zu}var Ju,qp;function A_(){if(qp)return Ju;qp=1;var e=O_(),t=df(),r=__(),n=Kb(),i=S_(),a=mt(),o=fb(),u="[object Map]",s="[object Object]",c="[object Promise]",f="[object Set]",l="[object WeakMap]",h="[object DataView]",p=o(e),y=o(t),v=o(r),d=o(n),x=o(i),w=a;return(e&&w(new e(new ArrayBuffer(1)))!=h||t&&w(new t)!=u||r&&w(r.resolve())!=c||n&&w(new n)!=f||i&&w(new i)!=l)&&(w=function(b){var O=a(b),m=O==s?b.constructor:void 0,g=m?o(m):"";if(g)switch(g){case p:return h;case y:return u;case v:return c;case d:return f;case x:return l}return O}),Ju=w,Ju}var Qu,Bp;function P_(){if(Bp)return Qu;Bp=1;var e=Db(),t=Fb(),r=o_(),n=w_(),i=A_(),a=Ne(),o=Ub(),u=Hb(),s=1,c="[object Arguments]",f="[object Array]",l="[object Object]",h=Object.prototype,p=h.hasOwnProperty;function y(v,d,x,w,b,O){var m=a(v),g=a(d),_=m?f:i(v),S=g?f:i(d);_=_==c?l:_,S=S==c?l:S;var P=_==l,C=S==l,A=_==S;if(A&&o(v)){if(!o(d))return!1;m=!0,P=!1}if(A&&!P)return O||(O=new e),m||u(v)?t(v,d,x,w,b,O):r(v,d,_,x,w,b,O);if(!(x&s)){var E=P&&p.call(v,"__wrapped__"),j=C&&p.call(d,"__wrapped__");if(E||j){var k=E?v.value():v,I=j?d.value():d;return O||(O=new e),b(k,I,x,w,O)}}return A?(O||(O=new e),n(v,d,x,w,b,O)):!1}return Qu=y,Qu}var es,Lp;function Ef(){if(Lp)return es;Lp=1;var e=P_(),t=gt();function r(n,i,a,o,u){return n===i?!0:n==null||i==null||!t(n)&&!t(i)?n!==n&&i!==i:e(n,i,a,o,r,u)}return es=r,es}var ts,Fp;function T_(){if(Fp)return ts;Fp=1;var e=Db(),t=Ef(),r=1,n=2;function i(a,o,u,s){var c=u.length,f=c,l=!s;if(a==null)return!f;for(a=Object(a);c--;){var h=u[c];if(l&&h[2]?h[1]!==a[h[0]]:!(h[0]in a))return!1}for(;++c<f;){h=u[c];var p=h[0],y=a[p],v=h[1];if(l&&h[2]){if(y===void 0&&!(p in a))return!1}else{var d=new e;if(s)var x=s(y,v,p,a,o,d);if(!(x===void 0?t(v,y,r|n,s,d):x))return!1}}return!0}return ts=i,ts}var rs,zp;function Vb(){if(zp)return rs;zp=1;var e=Pt();function t(r){return r===r&&!e(r)}return rs=t,rs}var ns,Up;function E_(){if(Up)return ns;Up=1;var e=Vb(),t=xa();function r(n){for(var i=t(n),a=i.length;a--;){var o=i[a],u=n[o];i[a]=[o,u,e(u)]}return i}return ns=r,ns}var is,Wp;function Xb(){if(Wp)return is;Wp=1;function e(t,r){return function(n){return n==null?!1:n[t]===r&&(r!==void 0||t in Object(n))}}return is=e,is}var as,Hp;function j_(){if(Hp)return as;Hp=1;var e=T_(),t=E_(),r=Xb();function n(i){var a=t(i);return a.length==1&&a[0][2]?r(a[0][0],a[0][1]):function(o){return o===i||e(o,i,a)}}return as=n,as}var os,Gp;function M_(){if(Gp)return os;Gp=1;function e(t,r){return t!=null&&r in Object(t)}return os=e,os}var us,Kp;function C_(){if(Kp)return us;Kp=1;var e=pb(),t=Af(),r=Ne(),n=Pf(),i=Tf(),a=pa();function o(u,s,c){s=e(s,u);for(var f=-1,l=s.length,h=!1;++f<l;){var p=a(s[f]);if(!(h=u!=null&&c(u,p)))break;u=u[p]}return h||++f!=l?h:(l=u==null?0:u.length,!!l&&i(l)&&n(p,l)&&(r(u)||t(u)))}return us=o,us}var ss,Vp;function $_(){if(Vp)return ss;Vp=1;var e=M_(),t=C_();function r(n,i){return n!=null&&t(n,i,e)}return ss=r,ss}var cs,Xp;function I_(){if(Xp)return cs;Xp=1;var e=Ef(),t=vb(),r=$_(),n=lf(),i=Vb(),a=Xb(),o=pa(),u=1,s=2;function c(f,l){return n(f)&&i(l)?a(o(f),l):function(h){var p=t(h,f);return p===void 0&&p===l?r(h,f):e(l,p,u|s)}}return cs=c,cs}var ls,Yp;function Br(){if(Yp)return ls;Yp=1;function e(t){return t}return ls=e,ls}var fs,Zp;function k_(){if(Zp)return fs;Zp=1;function e(t){return function(r){return r?.[t]}}return fs=e,fs}var hs,Jp;function N_(){if(Jp)return hs;Jp=1;var e=yf();function t(r){return function(n){return e(n,r)}}return hs=t,hs}var ds,Qp;function R_(){if(Qp)return ds;Qp=1;var e=k_(),t=N_(),r=lf(),n=pa();function i(a){return r(a)?e(n(a)):t(a)}return ds=i,ds}var ps,ev;function Tt(){if(ev)return ps;ev=1;var e=j_(),t=I_(),r=Br(),n=Ne(),i=R_();function a(o){return typeof o=="function"?o:o==null?r:typeof o=="object"?n(o)?t(o[0],o[1]):e(o):i(o)}return ps=a,ps}var vs,tv;function Yb(){if(tv)return vs;tv=1;function e(t,r,n,i){for(var a=t.length,o=n+(i?1:-1);i?o--:++o<a;)if(r(t[o],o,t))return o;return-1}return vs=e,vs}var ys,rv;function D_(){if(rv)return ys;rv=1;function e(t){return t!==t}return ys=e,ys}var ms,nv;function q_(){if(nv)return ms;nv=1;function e(t,r,n){for(var i=n-1,a=t.length;++i<a;)if(t[i]===r)return i;return-1}return ms=e,ms}var gs,iv;function B_(){if(iv)return gs;iv=1;var e=Yb(),t=D_(),r=q_();function n(i,a,o){return a===a?r(i,a,o):e(i,t,o)}return gs=n,gs}var bs,av;function L_(){if(av)return bs;av=1;var e=B_();function t(r,n){var i=r==null?0:r.length;return!!i&&e(r,n,0)>-1}return bs=t,bs}var xs,ov;function F_(){if(ov)return xs;ov=1;function e(t,r,n){for(var i=-1,a=t==null?0:t.length;++i<a;)if(n(r,t[i]))return!0;return!1}return xs=e,xs}var ws,uv;function z_(){if(uv)return ws;uv=1;function e(){}return ws=e,ws}var Os,sv;function U_(){if(sv)return Os;sv=1;var e=Kb(),t=z_(),r=Sf(),n=1/0,i=e&&1/r(new e([,-0]))[1]==n?function(a){return new e(a)}:t;return Os=i,Os}var _s,cv;function W_(){if(cv)return _s;cv=1;var e=qb(),t=L_(),r=F_(),n=Lb(),i=U_(),a=Sf(),o=200;function u(s,c,f){var l=-1,h=t,p=s.length,y=!0,v=[],d=v;if(f)y=!1,h=r;else if(p>=o){var x=c?null:i(s);if(x)return a(x);y=!1,h=n,d=new e}else d=c?[]:v;e:for(;++l<p;){var w=s[l],b=c?c(w):w;if(w=f||w!==0?w:0,y&&b===b){for(var O=d.length;O--;)if(d[O]===b)continue e;c&&d.push(b),v.push(w)}else h(d,b,f)||(d!==v&&d.push(b),v.push(w))}return v}return _s=u,_s}var Ss,lv;function H_(){if(lv)return Ss;lv=1;var e=Tt(),t=W_();function r(n,i){return n&&n.length?t(n,e(i,2)):[]}return Ss=r,Ss}var G_=H_();const fv=se(G_);function Zb(e,t,r){return t===!0?fv(e,r):Z(t)?fv(e,t):e}function gr(e){"@babel/helpers - typeof";return gr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},gr(e)}var K_=["ref"];function hv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ot(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?hv(Object(r),!0).forEach(function(n){wa(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function V_(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function dv(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Qb(n.key),n)}}function X_(e,t,r){return t&&dv(e.prototype,t),r&&dv(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Y_(e,t,r){return t=Si(t),Z_(e,Jb()?Reflect.construct(t,r||[],Si(e).constructor):t.apply(e,r))}function Z_(e,t){if(t&&(gr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return J_(e)}function J_(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Jb(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Jb=function(){return!!e})()}function Si(e){return Si=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Si(e)}function Q_(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ol(e,t)}function ol(e,t){return ol=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},ol(e,t)}function wa(e,t,r){return t=Qb(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Qb(e){var t=eS(e,"string");return gr(t)=="symbol"?t:t+""}function eS(e,t){if(gr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(gr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function tS(e,t){if(e==null)return{};var r=rS(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function rS(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function nS(e){return e.value}function iS(e,t){if(T.isValidElement(e))return T.cloneElement(e,t);if(typeof e=="function")return T.createElement(e,t);t.ref;var r=tS(t,K_);return T.createElement(_f,r)}var pv=1,dr=function(e){function t(){var r;V_(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=Y_(this,t,[].concat(i)),wa(r,"lastBoundingBox",{width:-1,height:-1}),r}return Q_(t,e),X_(t,[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();return n.height=this.wrapperNode.offsetHeight,n.width=this.wrapperNode.offsetWidth,n}return null}},{key:"updateBBox",value:function(){var n=this.props.onBBoxUpdate,i=this.getBBox();i?(Math.abs(i.width-this.lastBoundingBox.width)>pv||Math.abs(i.height-this.lastBoundingBox.height)>pv)&&(this.lastBoundingBox.width=i.width,this.lastBoundingBox.height=i.height,n&&n(i)):(this.lastBoundingBox.width!==-1||this.lastBoundingBox.height!==-1)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,n&&n(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?ot({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(n){var i=this.props,a=i.layout,o=i.align,u=i.verticalAlign,s=i.margin,c=i.chartWidth,f=i.chartHeight,l,h;if(!n||(n.left===void 0||n.left===null)&&(n.right===void 0||n.right===null))if(o==="center"&&a==="vertical"){var p=this.getBBoxSnapshot();l={left:((c||0)-p.width)/2}}else l=o==="right"?{right:s&&s.right||0}:{left:s&&s.left||0};if(!n||(n.top===void 0||n.top===null)&&(n.bottom===void 0||n.bottom===null))if(u==="middle"){var y=this.getBBoxSnapshot();h={top:((f||0)-y.height)/2}}else h=u==="bottom"?{bottom:s&&s.bottom||0}:{top:s&&s.top||0};return ot(ot({},l),h)}},{key:"render",value:function(){var n=this,i=this.props,a=i.content,o=i.width,u=i.height,s=i.wrapperStyle,c=i.payloadUniqBy,f=i.payload,l=ot(ot({position:"absolute",width:o||"auto",height:u||"auto"},this.getDefaultPosition(s)),s);return T.createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(p){n.wrapperNode=p}},iS(a,ot(ot({},this.props),{},{payload:Zb(f,c,nS)})))}}],[{key:"getWithHeight",value:function(n,i){var a=ot(ot({},this.defaultProps),n.props),o=a.layout;return o==="vertical"&&B(n.props.height)?{height:n.props.height}:o==="horizontal"?{width:n.props.width||i}:null}}])}(q.PureComponent);wa(dr,"displayName","Legend");wa(dr,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var As,vv;function aS(){if(vv)return As;vv=1;var e=Wn(),t=Af(),r=Ne(),n=e?e.isConcatSpreadable:void 0;function i(a){return r(a)||t(a)||!!(n&&a&&a[n])}return As=i,As}var Ps,yv;function e0(){if(yv)return Ps;yv=1;var e=zb(),t=aS();function r(n,i,a,o,u){var s=-1,c=n.length;for(a||(a=t),u||(u=[]);++s<c;){var f=n[s];i>0&&a(f)?i>1?r(f,i-1,a,o,u):e(u,f):o||(u[u.length]=f)}return u}return Ps=r,Ps}var Ts,mv;function oS(){if(mv)return Ts;mv=1;function e(t){return function(r,n,i){for(var a=-1,o=Object(r),u=i(r),s=u.length;s--;){var c=u[t?s:++a];if(n(o[c],c,o)===!1)break}return r}}return Ts=e,Ts}var Es,gv;function uS(){if(gv)return Es;gv=1;var e=oS(),t=e();return Es=t,Es}var js,bv;function t0(){if(bv)return js;bv=1;var e=uS(),t=xa();function r(n,i){return n&&e(n,i,t)}return js=r,js}var Ms,xv;function sS(){if(xv)return Ms;xv=1;var e=Gn();function t(r,n){return function(i,a){if(i==null)return i;if(!e(i))return r(i,a);for(var o=i.length,u=n?o:-1,s=Object(i);(n?u--:++u<o)&&a(s[u],u,s)!==!1;);return i}}return Ms=t,Ms}var Cs,wv;function jf(){if(wv)return Cs;wv=1;var e=t0(),t=sS(),r=t(e);return Cs=r,Cs}var $s,Ov;function r0(){if(Ov)return $s;Ov=1;var e=jf(),t=Gn();function r(n,i){var a=-1,o=t(n)?Array(n.length):[];return e(n,function(u,s,c){o[++a]=i(u,s,c)}),o}return $s=r,$s}var Is,_v;function cS(){if(_v)return Is;_v=1;function e(t,r){var n=t.length;for(t.sort(r);n--;)t[n]=t[n].value;return t}return Is=e,Is}var ks,Sv;function lS(){if(Sv)return ks;Sv=1;var e=Dr();function t(r,n){if(r!==n){var i=r!==void 0,a=r===null,o=r===r,u=e(r),s=n!==void 0,c=n===null,f=n===n,l=e(n);if(!c&&!l&&!u&&r>n||u&&s&&f&&!c&&!l||a&&s&&f||!i&&f||!o)return 1;if(!a&&!u&&!l&&r<n||l&&i&&o&&!a&&!u||c&&i&&o||!s&&o||!f)return-1}return 0}return ks=t,ks}var Ns,Av;function fS(){if(Av)return Ns;Av=1;var e=lS();function t(r,n,i){for(var a=-1,o=r.criteria,u=n.criteria,s=o.length,c=i.length;++a<s;){var f=e(o[a],u[a]);if(f){if(a>=c)return f;var l=i[a];return f*(l=="desc"?-1:1)}}return r.index-n.index}return Ns=t,Ns}var Rs,Pv;function hS(){if(Pv)return Rs;Pv=1;var e=vf(),t=yf(),r=Tt(),n=r0(),i=cS(),a=Wb(),o=fS(),u=Br(),s=Ne();function c(f,l,h){l.length?l=e(l,function(v){return s(v)?function(d){return t(d,v.length===1?v[0]:v)}:v}):l=[u];var p=-1;l=e(l,a(r));var y=n(f,function(v,d,x){var w=e(l,function(b){return b(v)});return{criteria:w,index:++p,value:v}});return i(y,function(v,d){return o(v,d,h)})}return Rs=c,Rs}var Ds,Tv;function dS(){if(Tv)return Ds;Tv=1;function e(t,r,n){switch(n.length){case 0:return t.call(r);case 1:return t.call(r,n[0]);case 2:return t.call(r,n[0],n[1]);case 3:return t.call(r,n[0],n[1],n[2])}return t.apply(r,n)}return Ds=e,Ds}var qs,Ev;function pS(){if(Ev)return qs;Ev=1;var e=dS(),t=Math.max;function r(n,i,a){return i=t(i===void 0?n.length-1:i,0),function(){for(var o=arguments,u=-1,s=t(o.length-i,0),c=Array(s);++u<s;)c[u]=o[i+u];u=-1;for(var f=Array(i+1);++u<i;)f[u]=o[u];return f[i]=a(c),e(n,this,f)}}return qs=r,qs}var Bs,jv;function vS(){if(jv)return Bs;jv=1;function e(t){return function(){return t}}return Bs=e,Bs}var Ls,Mv;function n0(){if(Mv)return Ls;Mv=1;var e=Xt(),t=function(){try{var r=e(Object,"defineProperty");return r({},"",{}),r}catch{}}();return Ls=t,Ls}var Fs,Cv;function yS(){if(Cv)return Fs;Cv=1;var e=vS(),t=n0(),r=Br(),n=t?function(i,a){return t(i,"toString",{configurable:!0,enumerable:!1,value:e(a),writable:!0})}:r;return Fs=n,Fs}var zs,$v;function mS(){if($v)return zs;$v=1;var e=800,t=16,r=Date.now;function n(i){var a=0,o=0;return function(){var u=r(),s=t-(u-o);if(o=u,s>0){if(++a>=e)return arguments[0]}else a=0;return i.apply(void 0,arguments)}}return zs=n,zs}var Us,Iv;function gS(){if(Iv)return Us;Iv=1;var e=yS(),t=mS(),r=t(e);return Us=r,Us}var Ws,kv;function bS(){if(kv)return Ws;kv=1;var e=Br(),t=pS(),r=gS();function n(i,a){return r(t(i,a,e),i+"")}return Ws=n,Ws}var Hs,Nv;function Oa(){if(Nv)return Hs;Nv=1;var e=hf(),t=Gn(),r=Pf(),n=Pt();function i(a,o,u){if(!n(u))return!1;var s=typeof o;return(s=="number"?t(u)&&r(o,u.length):s=="string"&&o in u)?e(u[o],a):!1}return Hs=i,Hs}var Gs,Rv;function xS(){if(Rv)return Gs;Rv=1;var e=e0(),t=hS(),r=bS(),n=Oa(),i=r(function(a,o){if(a==null)return[];var u=o.length;return u>1&&n(a,o[0],o[1])?o=[]:u>2&&n(o[0],o[1],o[2])&&(o=[o[0]]),t(a,e(o,1),[])});return Gs=i,Gs}var wS=xS();const Mf=se(wS);function fn(e){"@babel/helpers - typeof";return fn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},fn(e)}function ul(){return ul=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ul.apply(this,arguments)}function OS(e,t){return PS(e)||AS(e,t)||SS(e,t)||_S()}function _S(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function SS(e,t){if(e){if(typeof e=="string")return Dv(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Dv(e,t)}}function Dv(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function AS(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],s=!0,c=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);s=!0);}catch(f){c=!0,i=f}finally{try{if(!s&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return u}}function PS(e){if(Array.isArray(e))return e}function qv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ks(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?qv(Object(r),!0).forEach(function(n){TS(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):qv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function TS(e,t,r){return t=ES(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ES(e){var t=jS(e,"string");return fn(t)=="symbol"?t:t+""}function jS(e,t){if(fn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(fn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function MS(e){return Array.isArray(e)&&xe(e[0])&&xe(e[1])?e.join(" ~ "):e}var CS=function(t){var r=t.separator,n=r===void 0?" : ":r,i=t.contentStyle,a=i===void 0?{}:i,o=t.itemStyle,u=o===void 0?{}:o,s=t.labelStyle,c=s===void 0?{}:s,f=t.payload,l=t.formatter,h=t.itemSorter,p=t.wrapperClassName,y=t.labelClassName,v=t.label,d=t.labelFormatter,x=t.accessibilityLayer,w=x===void 0?!1:x,b=function(){if(f&&f.length){var E={padding:0,margin:0},j=(h?Mf(f,h):f).map(function(k,I){if(k.type==="none")return null;var N=Ks({display:"block",paddingTop:4,paddingBottom:4,color:k.color||"#000"},u),D=k.formatter||l||MS,L=k.value,F=k.name,H=L,K=F;if(D&&H!=null&&K!=null){var U=D(L,F,k,I,f);if(Array.isArray(U)){var V=OS(U,2);H=V[0],K=V[1]}else H=U}return T.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(I),style:N},xe(K)?T.createElement("span",{className:"recharts-tooltip-item-name"},K):null,xe(K)?T.createElement("span",{className:"recharts-tooltip-item-separator"},n):null,T.createElement("span",{className:"recharts-tooltip-item-value"},H),T.createElement("span",{className:"recharts-tooltip-item-unit"},k.unit||""))});return T.createElement("ul",{className:"recharts-tooltip-item-list",style:E},j)}return null},O=Ks({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},a),m=Ks({margin:0},c),g=!ne(v),_=g?v:"",S=ie("recharts-default-tooltip",p),P=ie("recharts-tooltip-label",y);g&&d&&f!==void 0&&f!==null&&(_=d(v,f));var C=w?{role:"status","aria-live":"assertive"}:{};return T.createElement("div",ul({className:S,style:O},C),T.createElement("p",{className:P,style:m},T.isValidElement(_)?_:"".concat(_)),b())};function hn(e){"@babel/helpers - typeof";return hn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},hn(e)}function ii(e,t,r){return t=$S(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function $S(e){var t=IS(e,"string");return hn(t)=="symbol"?t:t+""}function IS(e,t){if(hn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(hn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Wr="recharts-tooltip-wrapper",kS={visibility:"hidden"};function NS(e){var t=e.coordinate,r=e.translateX,n=e.translateY;return ie(Wr,ii(ii(ii(ii({},"".concat(Wr,"-right"),B(r)&&t&&B(t.x)&&r>=t.x),"".concat(Wr,"-left"),B(r)&&t&&B(t.x)&&r<t.x),"".concat(Wr,"-bottom"),B(n)&&t&&B(t.y)&&n>=t.y),"".concat(Wr,"-top"),B(n)&&t&&B(t.y)&&n<t.y))}function Bv(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.key,i=e.offsetTopLeft,a=e.position,o=e.reverseDirection,u=e.tooltipDimension,s=e.viewBox,c=e.viewBoxDimension;if(a&&B(a[n]))return a[n];var f=r[n]-u-i,l=r[n]+i;if(t[n])return o[n]?f:l;if(o[n]){var h=f,p=s[n];return h<p?Math.max(l,s[n]):Math.max(f,s[n])}var y=l+u,v=s[n]+c;return y>v?Math.max(f,s[n]):Math.max(l,s[n])}function RS(e){var t=e.translateX,r=e.translateY,n=e.useTranslate3d;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}function DS(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.offsetTopLeft,i=e.position,a=e.reverseDirection,o=e.tooltipBox,u=e.useTranslate3d,s=e.viewBox,c,f,l;return o.height>0&&o.width>0&&r?(f=Bv({allowEscapeViewBox:t,coordinate:r,key:"x",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.width,viewBox:s,viewBoxDimension:s.width}),l=Bv({allowEscapeViewBox:t,coordinate:r,key:"y",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.height,viewBox:s,viewBoxDimension:s.height}),c=RS({translateX:f,translateY:l,useTranslate3d:u})):c=kS,{cssProperties:c,cssClasses:NS({translateX:f,translateY:l,coordinate:r})}}function br(e){"@babel/helpers - typeof";return br=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},br(e)}function Lv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Fv(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Lv(Object(r),!0).forEach(function(n){cl(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Lv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function qS(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function BS(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,a0(n.key),n)}}function LS(e,t,r){return t&&BS(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function FS(e,t,r){return t=Ai(t),zS(e,i0()?Reflect.construct(t,r||[],Ai(e).constructor):t.apply(e,r))}function zS(e,t){if(t&&(br(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return US(e)}function US(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function i0(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(i0=function(){return!!e})()}function Ai(e){return Ai=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ai(e)}function WS(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&sl(e,t)}function sl(e,t){return sl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},sl(e,t)}function cl(e,t,r){return t=a0(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function a0(e){var t=HS(e,"string");return br(t)=="symbol"?t:t+""}function HS(e,t){if(br(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(br(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var zv=1,GS=function(e){function t(){var r;qS(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=FS(this,t,[].concat(i)),cl(r,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),cl(r,"handleKeyDown",function(o){if(o.key==="Escape"){var u,s,c,f;r.setState({dismissed:!0,dismissedAtCoordinate:{x:(u=(s=r.props.coordinate)===null||s===void 0?void 0:s.x)!==null&&u!==void 0?u:0,y:(c=(f=r.props.coordinate)===null||f===void 0?void 0:f.y)!==null&&c!==void 0?c:0}})}}),r}return WS(t,e),LS(t,[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();(Math.abs(n.width-this.state.lastBoundingBox.width)>zv||Math.abs(n.height-this.state.lastBoundingBox.height)>zv)&&this.setState({lastBoundingBox:{width:n.width,height:n.height}})}else(this.state.lastBoundingBox.width!==-1||this.state.lastBoundingBox.height!==-1)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var n,i;this.props.active&&this.updateBBox(),this.state.dismissed&&(((n=this.props.coordinate)===null||n===void 0?void 0:n.x)!==this.state.dismissedAtCoordinate.x||((i=this.props.coordinate)===null||i===void 0?void 0:i.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var n=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,u=i.animationDuration,s=i.animationEasing,c=i.children,f=i.coordinate,l=i.hasPayload,h=i.isAnimationActive,p=i.offset,y=i.position,v=i.reverseDirection,d=i.useTranslate3d,x=i.viewBox,w=i.wrapperStyle,b=DS({allowEscapeViewBox:o,coordinate:f,offsetTopLeft:p,position:y,reverseDirection:v,tooltipBox:this.state.lastBoundingBox,useTranslate3d:d,viewBox:x}),O=b.cssClasses,m=b.cssProperties,g=Fv(Fv({transition:h&&a?"transform ".concat(u,"ms ").concat(s):void 0},m),{},{pointerEvents:"none",visibility:!this.state.dismissed&&a&&l?"visible":"hidden",position:"absolute",top:0,left:0},w);return T.createElement("div",{tabIndex:-1,className:O,style:g,ref:function(S){n.wrapperNode=S}},c)}}])}(q.PureComponent),KS=function(){return!(typeof window<"u"&&window.document&&window.document.createElement&&window.setTimeout)},Kn={isSsr:KS()};function xr(e){"@babel/helpers - typeof";return xr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},xr(e)}function Uv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Wv(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Uv(Object(r),!0).forEach(function(n){Cf(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Uv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function VS(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function XS(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,u0(n.key),n)}}function YS(e,t,r){return t&&XS(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function ZS(e,t,r){return t=Pi(t),JS(e,o0()?Reflect.construct(t,r||[],Pi(e).constructor):t.apply(e,r))}function JS(e,t){if(t&&(xr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return QS(e)}function QS(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function o0(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(o0=function(){return!!e})()}function Pi(e){return Pi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Pi(e)}function eA(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ll(e,t)}function ll(e,t){return ll=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},ll(e,t)}function Cf(e,t,r){return t=u0(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function u0(e){var t=tA(e,"string");return xr(t)=="symbol"?t:t+""}function tA(e,t){if(xr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(xr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function rA(e){return e.dataKey}function nA(e,t){return T.isValidElement(e)?T.cloneElement(e,t):typeof e=="function"?T.createElement(e,t):T.createElement(CS,t)}var Qe=function(e){function t(){return VS(this,t),ZS(this,t,arguments)}return eA(t,e),YS(t,[{key:"render",value:function(){var n=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,u=i.animationDuration,s=i.animationEasing,c=i.content,f=i.coordinate,l=i.filterNull,h=i.isAnimationActive,p=i.offset,y=i.payload,v=i.payloadUniqBy,d=i.position,x=i.reverseDirection,w=i.useTranslate3d,b=i.viewBox,O=i.wrapperStyle,m=y??[];l&&m.length&&(m=Zb(y.filter(function(_){return _.value!=null&&(_.hide!==!0||n.props.includeHidden)}),v,rA));var g=m.length>0;return T.createElement(GS,{allowEscapeViewBox:o,animationDuration:u,animationEasing:s,isAnimationActive:h,active:a,coordinate:f,hasPayload:g,offset:p,position:d,reverseDirection:x,useTranslate3d:w,viewBox:b,wrapperStyle:O},nA(c,Wv(Wv({},this.props),{},{payload:m})))}}])}(q.PureComponent);Cf(Qe,"displayName","Tooltip");Cf(Qe,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!Kn.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var Vs,Hv;function iA(){if(Hv)return Vs;Hv=1;var e=at(),t=function(){return e.Date.now()};return Vs=t,Vs}var Xs,Gv;function aA(){if(Gv)return Xs;Gv=1;var e=/\s/;function t(r){for(var n=r.length;n--&&e.test(r.charAt(n)););return n}return Xs=t,Xs}var Ys,Kv;function oA(){if(Kv)return Ys;Kv=1;var e=aA(),t=/^\s+/;function r(n){return n&&n.slice(0,e(n)+1).replace(t,"")}return Ys=r,Ys}var Zs,Vv;function s0(){if(Vv)return Zs;Vv=1;var e=oA(),t=Pt(),r=Dr(),n=NaN,i=/^[-+]0x[0-9a-f]+$/i,a=/^0b[01]+$/i,o=/^0o[0-7]+$/i,u=parseInt;function s(c){if(typeof c=="number")return c;if(r(c))return n;if(t(c)){var f=typeof c.valueOf=="function"?c.valueOf():c;c=t(f)?f+"":f}if(typeof c!="string")return c===0?c:+c;c=e(c);var l=a.test(c);return l||o.test(c)?u(c.slice(2),l?2:8):i.test(c)?n:+c}return Zs=s,Zs}var Js,Xv;function uA(){if(Xv)return Js;Xv=1;var e=Pt(),t=iA(),r=s0(),n="Expected a function",i=Math.max,a=Math.min;function o(u,s,c){var f,l,h,p,y,v,d=0,x=!1,w=!1,b=!0;if(typeof u!="function")throw new TypeError(n);s=r(s)||0,e(c)&&(x=!!c.leading,w="maxWait"in c,h=w?i(r(c.maxWait)||0,s):h,b="trailing"in c?!!c.trailing:b);function O(j){var k=f,I=l;return f=l=void 0,d=j,p=u.apply(I,k),p}function m(j){return d=j,y=setTimeout(S,s),x?O(j):p}function g(j){var k=j-v,I=j-d,N=s-k;return w?a(N,h-I):N}function _(j){var k=j-v,I=j-d;return v===void 0||k>=s||k<0||w&&I>=h}function S(){var j=t();if(_(j))return P(j);y=setTimeout(S,g(j))}function P(j){return y=void 0,b&&f?O(j):(f=l=void 0,p)}function C(){y!==void 0&&clearTimeout(y),d=0,f=v=l=y=void 0}function A(){return y===void 0?p:P(t())}function E(){var j=t(),k=_(j);if(f=arguments,l=this,v=j,k){if(y===void 0)return m(v);if(w)return clearTimeout(y),y=setTimeout(S,s),O(v)}return y===void 0&&(y=setTimeout(S,s)),p}return E.cancel=C,E.flush=A,E}return Js=o,Js}var Qs,Yv;function sA(){if(Yv)return Qs;Yv=1;var e=uA(),t=Pt(),r="Expected a function";function n(i,a,o){var u=!0,s=!0;if(typeof i!="function")throw new TypeError(r);return t(o)&&(u="leading"in o?!!o.leading:u,s="trailing"in o?!!o.trailing:s),e(i,a,{leading:u,maxWait:a,trailing:s})}return Qs=n,Qs}var cA=sA();const c0=se(cA);function dn(e){"@babel/helpers - typeof";return dn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},dn(e)}function Zv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ai(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Zv(Object(r),!0).forEach(function(n){lA(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Zv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function lA(e,t,r){return t=fA(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function fA(e){var t=hA(e,"string");return dn(t)=="symbol"?t:t+""}function hA(e,t){if(dn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(dn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function dA(e,t){return mA(e)||yA(e,t)||vA(e,t)||pA()}function pA(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function vA(e,t){if(e){if(typeof e=="string")return Jv(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Jv(e,t)}}function Jv(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function yA(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],s=!0,c=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);s=!0);}catch(f){c=!0,i=f}finally{try{if(!s&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return u}}function mA(e){if(Array.isArray(e))return e}var gA=q.forwardRef(function(e,t){var r=e.aspect,n=e.initialDimension,i=n===void 0?{width:-1,height:-1}:n,a=e.width,o=a===void 0?"100%":a,u=e.height,s=u===void 0?"100%":u,c=e.minWidth,f=c===void 0?0:c,l=e.minHeight,h=e.maxHeight,p=e.children,y=e.debounce,v=y===void 0?0:y,d=e.id,x=e.className,w=e.onResize,b=e.style,O=b===void 0?{}:b,m=q.useRef(null),g=q.useRef();g.current=w,q.useImperativeHandle(t,function(){return Object.defineProperty(m.current,"current",{get:function(){return m.current},configurable:!0})});var _=q.useState({containerWidth:i.width,containerHeight:i.height}),S=dA(_,2),P=S[0],C=S[1],A=q.useCallback(function(j,k){C(function(I){var N=Math.round(j),D=Math.round(k);return I.containerWidth===N&&I.containerHeight===D?I:{containerWidth:N,containerHeight:D}})},[]);q.useEffect(function(){var j=function(F){var H,K=F[0].contentRect,U=K.width,V=K.height;A(U,V),(H=g.current)===null||H===void 0||H.call(g,U,V)};v>0&&(j=c0(j,v,{trailing:!0,leading:!1}));var k=new ResizeObserver(j),I=m.current.getBoundingClientRect(),N=I.width,D=I.height;return A(N,D),k.observe(m.current),function(){k.disconnect()}},[A,v]);var E=q.useMemo(function(){var j=P.containerWidth,k=P.containerHeight;if(j<0||k<0)return null;ft(qt(o)||qt(s),`The width(%s) and height(%s) are both fixed numbers,
       maybe you don't need to use a ResponsiveContainer.`,o,s),ft(!r||r>0,"The aspect(%s) must be greater than zero.",r);var I=qt(o)?j:o,N=qt(s)?k:s;r&&r>0&&(I?N=I/r:N&&(I=N*r),h&&N>h&&(N=h)),ft(I>0||N>0,`The width(%s) and height(%s) of chart should be greater than 0,
       please check the style of container, or the props width(%s) and height(%s),
       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the
       height and width.`,I,N,o,s,f,l,r);var D=!Array.isArray(p)&&lt(p.type).endsWith("Chart");return T.Children.map(p,function(L){return T.isValidElement(L)?q.cloneElement(L,ai({width:I,height:N},D?{style:ai({height:"100%",width:"100%",maxHeight:N,maxWidth:I},L.props.style)}:{})):L})},[r,p,s,h,l,f,P,o]);return T.createElement("div",{id:d?"".concat(d):void 0,className:ie("recharts-responsive-container",x),style:ai(ai({},O),{},{width:o,height:s,minWidth:f,minHeight:l,maxHeight:h}),ref:m},E)}),l0=function(t){return null};l0.displayName="Cell";function pn(e){"@babel/helpers - typeof";return pn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},pn(e)}function Qv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function fl(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Qv(Object(r),!0).forEach(function(n){bA(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Qv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function bA(e,t,r){return t=xA(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function xA(e){var t=wA(e,"string");return pn(t)=="symbol"?t:t+""}function wA(e,t){if(pn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(pn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var nr={widthCache:{},cacheCount:0},OA=2e3,_A={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},ey="recharts_measurement_span";function SA(e){var t=fl({},e);return Object.keys(t).forEach(function(r){t[r]||delete t[r]}),t}var an=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(t==null||Kn.isSsr)return{width:0,height:0};var n=SA(r),i=JSON.stringify({text:t,copyStyle:n});if(nr.widthCache[i])return nr.widthCache[i];try{var a=document.getElementById(ey);a||(a=document.createElement("span"),a.setAttribute("id",ey),a.setAttribute("aria-hidden","true"),document.body.appendChild(a));var o=fl(fl({},_A),n);Object.assign(a.style,o),a.textContent="".concat(t);var u=a.getBoundingClientRect(),s={width:u.width,height:u.height};return nr.widthCache[i]=s,++nr.cacheCount>OA&&(nr.cacheCount=0,nr.widthCache={}),s}catch{return{width:0,height:0}}},AA=function(t){return{top:t.top+window.scrollY-document.documentElement.clientTop,left:t.left+window.scrollX-document.documentElement.clientLeft}};function vn(e){"@babel/helpers - typeof";return vn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},vn(e)}function Ti(e,t){return jA(e)||EA(e,t)||TA(e,t)||PA()}function PA(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function TA(e,t){if(e){if(typeof e=="string")return ty(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ty(e,t)}}function ty(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function EA(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],s=!0,c=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);s=!0);}catch(f){c=!0,i=f}finally{try{if(!s&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return u}}function jA(e){if(Array.isArray(e))return e}function MA(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ry(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,$A(n.key),n)}}function CA(e,t,r){return t&&ry(e.prototype,t),r&&ry(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function $A(e){var t=IA(e,"string");return vn(t)=="symbol"?t:t+""}function IA(e,t){if(vn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(vn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var ny=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,iy=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,kA=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,NA=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,f0={cm:96/2.54,mm:96/25.4,pt:96/72,pc:96/6,in:96,Q:96/(2.54*40),px:1},RA=Object.keys(f0),cr="NaN";function DA(e,t){return e*f0[t]}var oi=function(){function e(t,r){MA(this,e),this.num=t,this.unit=r,this.num=t,this.unit=r,Number.isNaN(t)&&(this.unit=""),r!==""&&!kA.test(r)&&(this.num=NaN,this.unit=""),RA.includes(r)&&(this.num=DA(t,r),this.unit="px")}return CA(e,[{key:"add",value:function(r){return this.unit!==r.unit?new e(NaN,""):new e(this.num+r.num,this.unit)}},{key:"subtract",value:function(r){return this.unit!==r.unit?new e(NaN,""):new e(this.num-r.num,this.unit)}},{key:"multiply",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new e(NaN,""):new e(this.num*r.num,this.unit||r.unit)}},{key:"divide",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new e(NaN,""):new e(this.num/r.num,this.unit||r.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],[{key:"parse",value:function(r){var n,i=(n=NA.exec(r))!==null&&n!==void 0?n:[],a=Ti(i,3),o=a[1],u=a[2];return new e(parseFloat(o),u??"")}}])}();function h0(e){if(e.includes(cr))return cr;for(var t=e;t.includes("*")||t.includes("/");){var r,n=(r=ny.exec(t))!==null&&r!==void 0?r:[],i=Ti(n,4),a=i[1],o=i[2],u=i[3],s=oi.parse(a??""),c=oi.parse(u??""),f=o==="*"?s.multiply(c):s.divide(c);if(f.isNaN())return cr;t=t.replace(ny,f.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var l,h=(l=iy.exec(t))!==null&&l!==void 0?l:[],p=Ti(h,4),y=p[1],v=p[2],d=p[3],x=oi.parse(y??""),w=oi.parse(d??""),b=v==="+"?x.add(w):x.subtract(w);if(b.isNaN())return cr;t=t.replace(iy,b.toString())}return t}var ay=/\(([^()]*)\)/;function qA(e){for(var t=e;t.includes("(");){var r=ay.exec(t),n=Ti(r,2),i=n[1];t=t.replace(ay,h0(i))}return t}function BA(e){var t=e.replace(/\s+/g,"");return t=qA(t),t=h0(t),t}function LA(e){try{return BA(e)}catch{return cr}}function ec(e){var t=LA(e.slice(5,-1));return t===cr?"":t}var FA=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],zA=["dx","dy","angle","className","breakAll"];function hl(){return hl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},hl.apply(this,arguments)}function oy(e,t){if(e==null)return{};var r=UA(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function UA(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function uy(e,t){return KA(e)||GA(e,t)||HA(e,t)||WA()}function WA(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function HA(e,t){if(e){if(typeof e=="string")return sy(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sy(e,t)}}function sy(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function GA(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],s=!0,c=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);s=!0);}catch(f){c=!0,i=f}finally{try{if(!s&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return u}}function KA(e){if(Array.isArray(e))return e}var d0=/[ \f\n\r\t\v\u2028\u2029]+/,p0=function(t){var r=t.children,n=t.breakAll,i=t.style;try{var a=[];ne(r)||(n?a=r.toString().split(""):a=r.toString().split(d0));var o=a.map(function(s){return{word:s,width:an(s,i).width}}),u=n?0:an(" ",i).width;return{wordsWithComputedWidth:o,spaceWidth:u}}catch{return null}},VA=function(t,r,n,i,a){var o=t.maxLines,u=t.children,s=t.style,c=t.breakAll,f=B(o),l=u,h=function(){var I=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return I.reduce(function(N,D){var L=D.word,F=D.width,H=N[N.length-1];if(H&&(i==null||a||H.width+F+n<Number(i)))H.words.push(L),H.width+=F+n;else{var K={words:[L],width:F};N.push(K)}return N},[])},p=h(r),y=function(I){return I.reduce(function(N,D){return N.width>D.width?N:D})};if(!f)return p;for(var v="…",d=function(I){var N=l.slice(0,I),D=p0({breakAll:c,style:s,children:N+v}).wordsWithComputedWidth,L=h(D),F=L.length>o||y(L).width>Number(i);return[F,L]},x=0,w=l.length-1,b=0,O;x<=w&&b<=l.length-1;){var m=Math.floor((x+w)/2),g=m-1,_=d(g),S=uy(_,2),P=S[0],C=S[1],A=d(m),E=uy(A,1),j=E[0];if(!P&&!j&&(x=m+1),P&&j&&(w=m-1),!P&&j){O=C;break}b++}return O||p},cy=function(t){var r=ne(t)?[]:t.toString().split(d0);return[{words:r}]},XA=function(t){var r=t.width,n=t.scaleToFit,i=t.children,a=t.style,o=t.breakAll,u=t.maxLines;if((r||n)&&!Kn.isSsr){var s,c,f=p0({breakAll:o,children:i,style:a});if(f){var l=f.wordsWithComputedWidth,h=f.spaceWidth;s=l,c=h}else return cy(i);return VA({breakAll:o,children:i,maxLines:u,style:a},s,c,r,n)}return cy(i)},ly="#808080",Ei=function(t){var r=t.x,n=r===void 0?0:r,i=t.y,a=i===void 0?0:i,o=t.lineHeight,u=o===void 0?"1em":o,s=t.capHeight,c=s===void 0?"0.71em":s,f=t.scaleToFit,l=f===void 0?!1:f,h=t.textAnchor,p=h===void 0?"start":h,y=t.verticalAnchor,v=y===void 0?"end":y,d=t.fill,x=d===void 0?ly:d,w=oy(t,FA),b=q.useMemo(function(){return XA({breakAll:w.breakAll,children:w.children,maxLines:w.maxLines,scaleToFit:l,style:w.style,width:w.width})},[w.breakAll,w.children,w.maxLines,l,w.style,w.width]),O=w.dx,m=w.dy,g=w.angle,_=w.className,S=w.breakAll,P=oy(w,zA);if(!xe(n)||!xe(a))return null;var C=n+(B(O)?O:0),A=a+(B(m)?m:0),E;switch(v){case"start":E=ec("calc(".concat(c,")"));break;case"middle":E=ec("calc(".concat((b.length-1)/2," * -").concat(u," + (").concat(c," / 2))"));break;default:E=ec("calc(".concat(b.length-1," * -").concat(u,")"));break}var j=[];if(l){var k=b[0].width,I=w.width;j.push("scale(".concat((B(I)?I/k:1)/k,")"))}return g&&j.push("rotate(".concat(g,", ").concat(C,", ").concat(A,")")),j.length&&(P.transform=j.join(" ")),T.createElement("text",hl({},ee(P,!0),{x:C,y:A,className:ie("recharts-text",_),textAnchor:p,fill:x.includes("url")?ly:x}),b.map(function(N,D){var L=N.words.join(S?"":" ");return T.createElement("tspan",{x:C,dy:D===0?E:u,key:"".concat(L,"-").concat(D)},L)}))};function St(e,t){return e==null||t==null?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function YA(e,t){return e==null||t==null?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function $f(e){let t,r,n;e.length!==2?(t=St,r=(u,s)=>St(e(u),s),n=(u,s)=>e(u)-s):(t=e===St||e===YA?e:ZA,r=e,n=e);function i(u,s,c=0,f=u.length){if(c<f){if(t(s,s)!==0)return f;do{const l=c+f>>>1;r(u[l],s)<0?c=l+1:f=l}while(c<f)}return c}function a(u,s,c=0,f=u.length){if(c<f){if(t(s,s)!==0)return f;do{const l=c+f>>>1;r(u[l],s)<=0?c=l+1:f=l}while(c<f)}return c}function o(u,s,c=0,f=u.length){const l=i(u,s,c,f-1);return l>c&&n(u[l-1],s)>-n(u[l],s)?l-1:l}return{left:i,center:o,right:a}}function ZA(){return 0}function v0(e){return e===null?NaN:+e}function*JA(e,t){for(let r of e)r!=null&&(r=+r)>=r&&(yield r)}const QA=$f(St),Vn=QA.right;$f(v0).center;class fy extends Map{constructor(t,r=rP){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:r}}),t!=null)for(const[n,i]of t)this.set(n,i)}get(t){return super.get(hy(this,t))}has(t){return super.has(hy(this,t))}set(t,r){return super.set(eP(this,t),r)}delete(t){return super.delete(tP(this,t))}}function hy({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):r}function eP({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}function tP({_intern:e,_key:t},r){const n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}function rP(e){return e!==null&&typeof e=="object"?e.valueOf():e}function nP(e=St){if(e===St)return y0;if(typeof e!="function")throw new TypeError("compare is not a function");return(t,r)=>{const n=e(t,r);return n||n===0?n:(e(r,r)===0)-(e(t,t)===0)}}function y0(e,t){return(e==null||!(e>=e))-(t==null||!(t>=t))||(e<t?-1:e>t?1:0)}const iP=Math.sqrt(50),aP=Math.sqrt(10),oP=Math.sqrt(2);function ji(e,t,r){const n=(t-e)/Math.max(0,r),i=Math.floor(Math.log10(n)),a=n/Math.pow(10,i),o=a>=iP?10:a>=aP?5:a>=oP?2:1;let u,s,c;return i<0?(c=Math.pow(10,-i)/o,u=Math.round(e*c),s=Math.round(t*c),u/c<e&&++u,s/c>t&&--s,c=-c):(c=Math.pow(10,i)*o,u=Math.round(e/c),s=Math.round(t/c),u*c<e&&++u,s*c>t&&--s),s<u&&.5<=r&&r<2?ji(e,t,r*2):[u,s,c]}function dl(e,t,r){if(t=+t,e=+e,r=+r,!(r>0))return[];if(e===t)return[e];const n=t<e,[i,a,o]=n?ji(t,e,r):ji(e,t,r);if(!(a>=i))return[];const u=a-i+1,s=new Array(u);if(n)if(o<0)for(let c=0;c<u;++c)s[c]=(a-c)/-o;else for(let c=0;c<u;++c)s[c]=(a-c)*o;else if(o<0)for(let c=0;c<u;++c)s[c]=(i+c)/-o;else for(let c=0;c<u;++c)s[c]=(i+c)*o;return s}function pl(e,t,r){return t=+t,e=+e,r=+r,ji(e,t,r)[2]}function vl(e,t,r){t=+t,e=+e,r=+r;const n=t<e,i=n?pl(t,e,r):pl(e,t,r);return(n?-1:1)*(i<0?1/-i:i)}function dy(e,t){let r;for(const n of e)n!=null&&(r<n||r===void 0&&n>=n)&&(r=n);return r}function py(e,t){let r;for(const n of e)n!=null&&(r>n||r===void 0&&n>=n)&&(r=n);return r}function m0(e,t,r=0,n=1/0,i){if(t=Math.floor(t),r=Math.floor(Math.max(0,r)),n=Math.floor(Math.min(e.length-1,n)),!(r<=t&&t<=n))return e;for(i=i===void 0?y0:nP(i);n>r;){if(n-r>600){const s=n-r+1,c=t-r+1,f=Math.log(s),l=.5*Math.exp(2*f/3),h=.5*Math.sqrt(f*l*(s-l)/s)*(c-s/2<0?-1:1),p=Math.max(r,Math.floor(t-c*l/s+h)),y=Math.min(n,Math.floor(t+(s-c)*l/s+h));m0(e,t,p,y,i)}const a=e[t];let o=r,u=n;for(Hr(e,r,t),i(e[n],a)>0&&Hr(e,r,n);o<u;){for(Hr(e,o,u),++o,--u;i(e[o],a)<0;)++o;for(;i(e[u],a)>0;)--u}i(e[r],a)===0?Hr(e,r,u):(++u,Hr(e,u,n)),u<=t&&(r=u+1),t<=u&&(n=u-1)}return e}function Hr(e,t,r){const n=e[t];e[t]=e[r],e[r]=n}function uP(e,t,r){if(e=Float64Array.from(JA(e)),!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return py(e);if(t>=1)return dy(e);var n,i=(n-1)*t,a=Math.floor(i),o=dy(m0(e,a).subarray(0,a+1)),u=py(e.subarray(a+1));return o+(u-o)*(i-a)}}function sP(e,t,r=v0){if(!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e),u=+r(e[a+1],a+1,e);return o+(u-o)*(i-a)}}function cP(e,t,r){e=+e,t=+t,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=Math.max(0,Math.ceil((t-e)/r))|0,a=new Array(i);++n<i;)a[n]=e+n*r;return a}function He(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e);break}return this}function bt(e,t){switch(arguments.length){case 0:break;case 1:{typeof e=="function"?this.interpolator(e):this.range(e);break}default:{this.domain(e),typeof t=="function"?this.interpolator(t):this.range(t);break}}return this}const yl=Symbol("implicit");function If(){var e=new fy,t=[],r=[],n=yl;function i(a){let o=e.get(a);if(o===void 0){if(n!==yl)return n;e.set(a,o=t.push(a)-1)}return r[o%r.length]}return i.domain=function(a){if(!arguments.length)return t.slice();t=[],e=new fy;for(const o of a)e.has(o)||e.set(o,t.push(o)-1);return i},i.range=function(a){return arguments.length?(r=Array.from(a),i):r.slice()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return If(t,r).unknown(n)},He.apply(i,arguments),i}function yn(){var e=If().unknown(void 0),t=e.domain,r=e.range,n=0,i=1,a,o,u=!1,s=0,c=0,f=.5;delete e.unknown;function l(){var h=t().length,p=i<n,y=p?i:n,v=p?n:i;a=(v-y)/Math.max(1,h-s+c*2),u&&(a=Math.floor(a)),y+=(v-y-a*(h-s))*f,o=a*(1-s),u&&(y=Math.round(y),o=Math.round(o));var d=cP(h).map(function(x){return y+a*x});return r(p?d.reverse():d)}return e.domain=function(h){return arguments.length?(t(h),l()):t()},e.range=function(h){return arguments.length?([n,i]=h,n=+n,i=+i,l()):[n,i]},e.rangeRound=function(h){return[n,i]=h,n=+n,i=+i,u=!0,l()},e.bandwidth=function(){return o},e.step=function(){return a},e.round=function(h){return arguments.length?(u=!!h,l()):u},e.padding=function(h){return arguments.length?(s=Math.min(1,c=+h),l()):s},e.paddingInner=function(h){return arguments.length?(s=Math.min(1,h),l()):s},e.paddingOuter=function(h){return arguments.length?(c=+h,l()):c},e.align=function(h){return arguments.length?(f=Math.max(0,Math.min(1,h)),l()):f},e.copy=function(){return yn(t(),[n,i]).round(u).paddingInner(s).paddingOuter(c).align(f)},He.apply(l(),arguments)}function g0(e){var t=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return g0(t())},e}function on(){return g0(yn.apply(null,arguments).paddingInner(1))}function kf(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function b0(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function Xn(){}var mn=.7,Mi=1/mn,pr="\\s*([+-]?\\d+)\\s*",gn="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",tt="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",lP=/^#([0-9a-f]{3,8})$/,fP=new RegExp(`^rgb\\(${pr},${pr},${pr}\\)$`),hP=new RegExp(`^rgb\\(${tt},${tt},${tt}\\)$`),dP=new RegExp(`^rgba\\(${pr},${pr},${pr},${gn}\\)$`),pP=new RegExp(`^rgba\\(${tt},${tt},${tt},${gn}\\)$`),vP=new RegExp(`^hsl\\(${gn},${tt},${tt}\\)$`),yP=new RegExp(`^hsla\\(${gn},${tt},${tt},${gn}\\)$`),vy={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};kf(Xn,bn,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:yy,formatHex:yy,formatHex8:mP,formatHsl:gP,formatRgb:my,toString:my});function yy(){return this.rgb().formatHex()}function mP(){return this.rgb().formatHex8()}function gP(){return x0(this).formatHsl()}function my(){return this.rgb().formatRgb()}function bn(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=lP.exec(e))?(r=t[1].length,t=parseInt(t[1],16),r===6?gy(t):r===3?new ke(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):r===8?ui(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):r===4?ui(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=fP.exec(e))?new ke(t[1],t[2],t[3],1):(t=hP.exec(e))?new ke(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=dP.exec(e))?ui(t[1],t[2],t[3],t[4]):(t=pP.exec(e))?ui(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=vP.exec(e))?wy(t[1],t[2]/100,t[3]/100,1):(t=yP.exec(e))?wy(t[1],t[2]/100,t[3]/100,t[4]):vy.hasOwnProperty(e)?gy(vy[e]):e==="transparent"?new ke(NaN,NaN,NaN,0):null}function gy(e){return new ke(e>>16&255,e>>8&255,e&255,1)}function ui(e,t,r,n){return n<=0&&(e=t=r=NaN),new ke(e,t,r,n)}function bP(e){return e instanceof Xn||(e=bn(e)),e?(e=e.rgb(),new ke(e.r,e.g,e.b,e.opacity)):new ke}function ml(e,t,r,n){return arguments.length===1?bP(e):new ke(e,t,r,n??1)}function ke(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}kf(ke,ml,b0(Xn,{brighter(e){return e=e==null?Mi:Math.pow(Mi,e),new ke(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?mn:Math.pow(mn,e),new ke(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new ke(zt(this.r),zt(this.g),zt(this.b),Ci(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:by,formatHex:by,formatHex8:xP,formatRgb:xy,toString:xy}));function by(){return`#${Bt(this.r)}${Bt(this.g)}${Bt(this.b)}`}function xP(){return`#${Bt(this.r)}${Bt(this.g)}${Bt(this.b)}${Bt((isNaN(this.opacity)?1:this.opacity)*255)}`}function xy(){const e=Ci(this.opacity);return`${e===1?"rgb(":"rgba("}${zt(this.r)}, ${zt(this.g)}, ${zt(this.b)}${e===1?")":`, ${e})`}`}function Ci(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function zt(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function Bt(e){return e=zt(e),(e<16?"0":"")+e.toString(16)}function wy(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new Xe(e,t,r,n)}function x0(e){if(e instanceof Xe)return new Xe(e.h,e.s,e.l,e.opacity);if(e instanceof Xn||(e=bn(e)),!e)return new Xe;if(e instanceof Xe)return e;e=e.rgb();var t=e.r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,u=a-i,s=(a+i)/2;return u?(t===a?o=(r-n)/u+(r<n)*6:r===a?o=(n-t)/u+2:o=(t-r)/u+4,u/=s<.5?a+i:2-a-i,o*=60):u=s>0&&s<1?0:o,new Xe(o,u,s,e.opacity)}function wP(e,t,r,n){return arguments.length===1?x0(e):new Xe(e,t,r,n??1)}function Xe(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}kf(Xe,wP,b0(Xn,{brighter(e){return e=e==null?Mi:Math.pow(Mi,e),new Xe(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?mn:Math.pow(mn,e),new Xe(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new ke(tc(e>=240?e-240:e+120,i,n),tc(e,i,n),tc(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new Xe(Oy(this.h),si(this.s),si(this.l),Ci(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=Ci(this.opacity);return`${e===1?"hsl(":"hsla("}${Oy(this.h)}, ${si(this.s)*100}%, ${si(this.l)*100}%${e===1?")":`, ${e})`}`}}));function Oy(e){return e=(e||0)%360,e<0?e+360:e}function si(e){return Math.max(0,Math.min(1,e||0))}function tc(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}const Nf=e=>()=>e;function OP(e,t){return function(r){return e+r*t}}function _P(e,t,r){return e=Math.pow(e,r),t=Math.pow(t,r)-e,r=1/r,function(n){return Math.pow(e+n*t,r)}}function SP(e){return(e=+e)==1?w0:function(t,r){return r-t?_P(t,r,e):Nf(isNaN(t)?r:t)}}function w0(e,t){var r=t-e;return r?OP(e,r):Nf(isNaN(e)?t:e)}const _y=function e(t){var r=SP(t);function n(i,a){var o=r((i=ml(i)).r,(a=ml(a)).r),u=r(i.g,a.g),s=r(i.b,a.b),c=w0(i.opacity,a.opacity);return function(f){return i.r=o(f),i.g=u(f),i.b=s(f),i.opacity=c(f),i+""}}return n.gamma=e,n}(1);function AP(e,t){t||(t=[]);var r=e?Math.min(t.length,e.length):0,n=t.slice(),i;return function(a){for(i=0;i<r;++i)n[i]=e[i]*(1-a)+t[i]*a;return n}}function PP(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}function TP(e,t){var r=t?t.length:0,n=e?Math.min(r,e.length):0,i=new Array(n),a=new Array(r),o;for(o=0;o<n;++o)i[o]=Lr(e[o],t[o]);for(;o<r;++o)a[o]=t[o];return function(u){for(o=0;o<n;++o)a[o]=i[o](u);return a}}function EP(e,t){var r=new Date;return e=+e,t=+t,function(n){return r.setTime(e*(1-n)+t*n),r}}function $i(e,t){return e=+e,t=+t,function(r){return e*(1-r)+t*r}}function jP(e,t){var r={},n={},i;(e===null||typeof e!="object")&&(e={}),(t===null||typeof t!="object")&&(t={});for(i in t)i in e?r[i]=Lr(e[i],t[i]):n[i]=t[i];return function(a){for(i in r)n[i]=r[i](a);return n}}var gl=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,rc=new RegExp(gl.source,"g");function MP(e){return function(){return e}}function CP(e){return function(t){return e(t)+""}}function $P(e,t){var r=gl.lastIndex=rc.lastIndex=0,n,i,a,o=-1,u=[],s=[];for(e=e+"",t=t+"";(n=gl.exec(e))&&(i=rc.exec(t));)(a=i.index)>r&&(a=t.slice(r,a),u[o]?u[o]+=a:u[++o]=a),(n=n[0])===(i=i[0])?u[o]?u[o]+=i:u[++o]=i:(u[++o]=null,s.push({i:o,x:$i(n,i)})),r=rc.lastIndex;return r<t.length&&(a=t.slice(r),u[o]?u[o]+=a:u[++o]=a),u.length<2?s[0]?CP(s[0].x):MP(t):(t=s.length,function(c){for(var f=0,l;f<t;++f)u[(l=s[f]).i]=l.x(c);return u.join("")})}function Lr(e,t){var r=typeof t,n;return t==null||r==="boolean"?Nf(t):(r==="number"?$i:r==="string"?(n=bn(t))?(t=n,_y):$P:t instanceof bn?_y:t instanceof Date?EP:PP(t)?AP:Array.isArray(t)?TP:typeof t.valueOf!="function"&&typeof t.toString!="function"||isNaN(t)?jP:$i)(e,t)}function Rf(e,t){return e=+e,t=+t,function(r){return Math.round(e*(1-r)+t*r)}}function IP(e,t){t===void 0&&(t=e,e=Lr);for(var r=0,n=t.length-1,i=t[0],a=new Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(o){var u=Math.max(0,Math.min(n-1,Math.floor(o*=n)));return a[u](o-u)}}function kP(e){return function(){return e}}function Ii(e){return+e}var Sy=[0,1];function Ce(e){return e}function bl(e,t){return(t-=e=+e)?function(r){return(r-e)/t}:kP(isNaN(t)?NaN:.5)}function NP(e,t){var r;return e>t&&(r=e,e=t,t=r),function(n){return Math.max(e,Math.min(t,n))}}function RP(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=bl(i,n),a=r(o,a)):(n=bl(n,i),a=r(a,o)),function(u){return a(n(u))}}function DP(e,t,r){var n=Math.min(e.length,t.length)-1,i=new Array(n),a=new Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=bl(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(u){var s=Vn(e,u,1,n)-1;return a[s](i[s](u))}}function Yn(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function _a(){var e=Sy,t=Sy,r=Lr,n,i,a,o=Ce,u,s,c;function f(){var h=Math.min(e.length,t.length);return o!==Ce&&(o=NP(e[0],e[h-1])),u=h>2?DP:RP,s=c=null,l}function l(h){return h==null||isNaN(h=+h)?a:(s||(s=u(e.map(n),t,r)))(n(o(h)))}return l.invert=function(h){return o(i((c||(c=u(t,e.map(n),$i)))(h)))},l.domain=function(h){return arguments.length?(e=Array.from(h,Ii),f()):e.slice()},l.range=function(h){return arguments.length?(t=Array.from(h),f()):t.slice()},l.rangeRound=function(h){return t=Array.from(h),r=Rf,f()},l.clamp=function(h){return arguments.length?(o=h?!0:Ce,f()):o!==Ce},l.interpolate=function(h){return arguments.length?(r=h,f()):r},l.unknown=function(h){return arguments.length?(a=h,l):a},function(h,p){return n=h,i=p,f()}}function Df(){return _a()(Ce,Ce)}function qP(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)}function ki(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function wr(e){return e=ki(Math.abs(e)),e?e[1]:NaN}function BP(e,t){return function(r,n){for(var i=r.length,a=[],o=0,u=e[0],s=0;i>0&&u>0&&(s+u+1>n&&(u=Math.max(1,n-s)),a.push(r.substring(i-=u,i+u)),!((s+=u+1)>n));)u=e[o=(o+1)%e.length];return a.reverse().join(t)}}function LP(e){return function(t){return t.replace(/[0-9]/g,function(r){return e[+r]})}}var FP=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function xn(e){if(!(t=FP.exec(e)))throw new Error("invalid format: "+e);var t;return new qf({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}xn.prototype=qf.prototype;function qf(e){this.fill=e.fill===void 0?" ":e.fill+"",this.align=e.align===void 0?">":e.align+"",this.sign=e.sign===void 0?"-":e.sign+"",this.symbol=e.symbol===void 0?"":e.symbol+"",this.zero=!!e.zero,this.width=e.width===void 0?void 0:+e.width,this.comma=!!e.comma,this.precision=e.precision===void 0?void 0:+e.precision,this.trim=!!e.trim,this.type=e.type===void 0?"":e.type+""}qf.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===void 0?"":Math.max(1,this.width|0))+(this.comma?",":"")+(this.precision===void 0?"":"."+Math.max(0,this.precision|0))+(this.trim?"~":"")+this.type};function zP(e){e:for(var t=e.length,r=1,n=-1,i;r<t;++r)switch(e[r]){case".":n=i=r;break;case"0":n===0&&(n=r),i=r;break;default:if(!+e[r])break e;n>0&&(n=0);break}return n>0?e.slice(0,n)+e.slice(i+1):e}var O0;function UP(e,t){var r=ki(e,t);if(!r)return e+"";var n=r[0],i=r[1],a=i-(O0=Math.max(-8,Math.min(8,Math.floor(i/3)))*3)+1,o=n.length;return a===o?n:a>o?n+new Array(a-o+1).join("0"):a>0?n.slice(0,a)+"."+n.slice(a):"0."+new Array(1-a).join("0")+ki(e,Math.max(0,t+a-1))[0]}function Ay(e,t){var r=ki(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+new Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+new Array(i-n.length+2).join("0")}const Py={"%":(e,t)=>(e*100).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:qP,e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>Ay(e*100,t),r:Ay,s:UP,X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function Ty(e){return e}var Ey=Array.prototype.map,jy=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function WP(e){var t=e.grouping===void 0||e.thousands===void 0?Ty:BP(Ey.call(e.grouping,Number),e.thousands+""),r=e.currency===void 0?"":e.currency[0]+"",n=e.currency===void 0?"":e.currency[1]+"",i=e.decimal===void 0?".":e.decimal+"",a=e.numerals===void 0?Ty:LP(Ey.call(e.numerals,String)),o=e.percent===void 0?"%":e.percent+"",u=e.minus===void 0?"−":e.minus+"",s=e.nan===void 0?"NaN":e.nan+"";function c(l){l=xn(l);var h=l.fill,p=l.align,y=l.sign,v=l.symbol,d=l.zero,x=l.width,w=l.comma,b=l.precision,O=l.trim,m=l.type;m==="n"?(w=!0,m="g"):Py[m]||(b===void 0&&(b=12),O=!0,m="g"),(d||h==="0"&&p==="=")&&(d=!0,h="0",p="=");var g=v==="$"?r:v==="#"&&/[boxX]/.test(m)?"0"+m.toLowerCase():"",_=v==="$"?n:/[%p]/.test(m)?o:"",S=Py[m],P=/[defgprs%]/.test(m);b=b===void 0?6:/[gprs]/.test(m)?Math.max(1,Math.min(21,b)):Math.max(0,Math.min(20,b));function C(A){var E=g,j=_,k,I,N;if(m==="c")j=S(A)+j,A="";else{A=+A;var D=A<0||1/A<0;if(A=isNaN(A)?s:S(Math.abs(A),b),O&&(A=zP(A)),D&&+A==0&&y!=="+"&&(D=!1),E=(D?y==="("?y:u:y==="-"||y==="("?"":y)+E,j=(m==="s"?jy[8+O0/3]:"")+j+(D&&y==="("?")":""),P){for(k=-1,I=A.length;++k<I;)if(N=A.charCodeAt(k),48>N||N>57){j=(N===46?i+A.slice(k+1):A.slice(k))+j,A=A.slice(0,k);break}}}w&&!d&&(A=t(A,1/0));var L=E.length+A.length+j.length,F=L<x?new Array(x-L+1).join(h):"";switch(w&&d&&(A=t(F+A,F.length?x-j.length:1/0),F=""),p){case"<":A=E+A+j+F;break;case"=":A=E+F+A+j;break;case"^":A=F.slice(0,L=F.length>>1)+E+A+j+F.slice(L);break;default:A=F+E+A+j;break}return a(A)}return C.toString=function(){return l+""},C}function f(l,h){var p=c((l=xn(l),l.type="f",l)),y=Math.max(-8,Math.min(8,Math.floor(wr(h)/3)))*3,v=Math.pow(10,-y),d=jy[8+y/3];return function(x){return p(v*x)+d}}return{format:c,formatPrefix:f}}var ci,Bf,_0;HP({thousands:",",grouping:[3],currency:["$",""]});function HP(e){return ci=WP(e),Bf=ci.format,_0=ci.formatPrefix,ci}function GP(e){return Math.max(0,-wr(Math.abs(e)))}function KP(e,t){return Math.max(0,Math.max(-8,Math.min(8,Math.floor(wr(t)/3)))*3-wr(Math.abs(e)))}function VP(e,t){return e=Math.abs(e),t=Math.abs(t)-e,Math.max(0,wr(t)-wr(e))+1}function S0(e,t,r,n){var i=vl(e,t,r),a;switch(n=xn(n??",f"),n.type){case"s":{var o=Math.max(Math.abs(e),Math.abs(t));return n.precision==null&&!isNaN(a=KP(i,o))&&(n.precision=a),_0(n,o)}case"":case"e":case"g":case"p":case"r":{n.precision==null&&!isNaN(a=VP(i,Math.max(Math.abs(e),Math.abs(t))))&&(n.precision=a-(n.type==="e"));break}case"f":case"%":{n.precision==null&&!isNaN(a=GP(i))&&(n.precision=a-(n.type==="%")*2);break}}return Bf(n)}function Et(e){var t=e.domain;return e.ticks=function(r){var n=t();return dl(n[0],n[n.length-1],r??10)},e.tickFormat=function(r,n){var i=t();return S0(i[0],i[i.length-1],r??10,n)},e.nice=function(r){r==null&&(r=10);var n=t(),i=0,a=n.length-1,o=n[i],u=n[a],s,c,f=10;for(u<o&&(c=o,o=u,u=c,c=i,i=a,a=c);f-- >0;){if(c=pl(o,u,r),c===s)return n[i]=o,n[a]=u,t(n);if(c>0)o=Math.floor(o/c)*c,u=Math.ceil(u/c)*c;else if(c<0)o=Math.ceil(o*c)/c,u=Math.floor(u*c)/c;else break;s=c}return e},e}function Ni(){var e=Df();return e.copy=function(){return Yn(e,Ni())},He.apply(e,arguments),Et(e)}function A0(e){var t;function r(n){return n==null||isNaN(n=+n)?t:n}return r.invert=r,r.domain=r.range=function(n){return arguments.length?(e=Array.from(n,Ii),r):e.slice()},r.unknown=function(n){return arguments.length?(t=n,r):t},r.copy=function(){return A0(e).unknown(t)},e=arguments.length?Array.from(e,Ii):[0,1],Et(r)}function P0(e,t){e=e.slice();var r=0,n=e.length-1,i=e[r],a=e[n],o;return a<i&&(o=r,r=n,n=o,o=i,i=a,a=o),e[r]=t.floor(i),e[n]=t.ceil(a),e}function My(e){return Math.log(e)}function Cy(e){return Math.exp(e)}function XP(e){return-Math.log(-e)}function YP(e){return-Math.exp(-e)}function ZP(e){return isFinite(e)?+("1e"+e):e<0?0:e}function JP(e){return e===10?ZP:e===Math.E?Math.exp:t=>Math.pow(e,t)}function QP(e){return e===Math.E?Math.log:e===10&&Math.log10||e===2&&Math.log2||(e=Math.log(e),t=>Math.log(t)/e)}function $y(e){return(t,r)=>-e(-t,r)}function Lf(e){const t=e(My,Cy),r=t.domain;let n=10,i,a;function o(){return i=QP(n),a=JP(n),r()[0]<0?(i=$y(i),a=$y(a),e(XP,YP)):e(My,Cy),t}return t.base=function(u){return arguments.length?(n=+u,o()):n},t.domain=function(u){return arguments.length?(r(u),o()):r()},t.ticks=u=>{const s=r();let c=s[0],f=s[s.length-1];const l=f<c;l&&([c,f]=[f,c]);let h=i(c),p=i(f),y,v;const d=u==null?10:+u;let x=[];if(!(n%1)&&p-h<d){if(h=Math.floor(h),p=Math.ceil(p),c>0){for(;h<=p;++h)for(y=1;y<n;++y)if(v=h<0?y/a(-h):y*a(h),!(v<c)){if(v>f)break;x.push(v)}}else for(;h<=p;++h)for(y=n-1;y>=1;--y)if(v=h>0?y/a(-h):y*a(h),!(v<c)){if(v>f)break;x.push(v)}x.length*2<d&&(x=dl(c,f,d))}else x=dl(h,p,Math.min(p-h,d)).map(a);return l?x.reverse():x},t.tickFormat=(u,s)=>{if(u==null&&(u=10),s==null&&(s=n===10?"s":","),typeof s!="function"&&(!(n%1)&&(s=xn(s)).precision==null&&(s.trim=!0),s=Bf(s)),u===1/0)return s;const c=Math.max(1,n*u/t.ticks().length);return f=>{let l=f/a(Math.round(i(f)));return l*n<n-.5&&(l*=n),l<=c?s(f):""}},t.nice=()=>r(P0(r(),{floor:u=>a(Math.floor(i(u))),ceil:u=>a(Math.ceil(i(u)))})),t}function T0(){const e=Lf(_a()).domain([1,10]);return e.copy=()=>Yn(e,T0()).base(e.base()),He.apply(e,arguments),e}function Iy(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function ky(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function Ff(e){var t=1,r=e(Iy(t),ky(t));return r.constant=function(n){return arguments.length?e(Iy(t=+n),ky(t)):t},Et(r)}function E0(){var e=Ff(_a());return e.copy=function(){return Yn(e,E0()).constant(e.constant())},He.apply(e,arguments)}function Ny(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function eT(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function tT(e){return e<0?-e*e:e*e}function zf(e){var t=e(Ce,Ce),r=1;function n(){return r===1?e(Ce,Ce):r===.5?e(eT,tT):e(Ny(r),Ny(1/r))}return t.exponent=function(i){return arguments.length?(r=+i,n()):r},Et(t)}function Uf(){var e=zf(_a());return e.copy=function(){return Yn(e,Uf()).exponent(e.exponent())},He.apply(e,arguments),e}function rT(){return Uf.apply(null,arguments).exponent(.5)}function Ry(e){return Math.sign(e)*e*e}function nT(e){return Math.sign(e)*Math.sqrt(Math.abs(e))}function j0(){var e=Df(),t=[0,1],r=!1,n;function i(a){var o=nT(e(a));return isNaN(o)?n:r?Math.round(o):o}return i.invert=function(a){return e.invert(Ry(a))},i.domain=function(a){return arguments.length?(e.domain(a),i):e.domain()},i.range=function(a){return arguments.length?(e.range((t=Array.from(a,Ii)).map(Ry)),i):t.slice()},i.rangeRound=function(a){return i.range(a).round(!0)},i.round=function(a){return arguments.length?(r=!!a,i):r},i.clamp=function(a){return arguments.length?(e.clamp(a),i):e.clamp()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return j0(e.domain(),t).round(r).clamp(e.clamp()).unknown(n)},He.apply(i,arguments),Et(i)}function M0(){var e=[],t=[],r=[],n;function i(){var o=0,u=Math.max(1,t.length);for(r=new Array(u-1);++o<u;)r[o-1]=sP(e,o/u);return a}function a(o){return o==null||isNaN(o=+o)?n:t[Vn(r,o)]}return a.invertExtent=function(o){var u=t.indexOf(o);return u<0?[NaN,NaN]:[u>0?r[u-1]:e[0],u<r.length?r[u]:e[e.length-1]]},a.domain=function(o){if(!arguments.length)return e.slice();e=[];for(let u of o)u!=null&&!isNaN(u=+u)&&e.push(u);return e.sort(St),i()},a.range=function(o){return arguments.length?(t=Array.from(o),i()):t.slice()},a.unknown=function(o){return arguments.length?(n=o,a):n},a.quantiles=function(){return r.slice()},a.copy=function(){return M0().domain(e).range(t).unknown(n)},He.apply(a,arguments)}function C0(){var e=0,t=1,r=1,n=[.5],i=[0,1],a;function o(s){return s!=null&&s<=s?i[Vn(n,s,0,r)]:a}function u(){var s=-1;for(n=new Array(r);++s<r;)n[s]=((s+1)*t-(s-r)*e)/(r+1);return o}return o.domain=function(s){return arguments.length?([e,t]=s,e=+e,t=+t,u()):[e,t]},o.range=function(s){return arguments.length?(r=(i=Array.from(s)).length-1,u()):i.slice()},o.invertExtent=function(s){var c=i.indexOf(s);return c<0?[NaN,NaN]:c<1?[e,n[0]]:c>=r?[n[r-1],t]:[n[c-1],n[c]]},o.unknown=function(s){return arguments.length&&(a=s),o},o.thresholds=function(){return n.slice()},o.copy=function(){return C0().domain([e,t]).range(i).unknown(a)},He.apply(Et(o),arguments)}function $0(){var e=[.5],t=[0,1],r,n=1;function i(a){return a!=null&&a<=a?t[Vn(e,a,0,n)]:r}return i.domain=function(a){return arguments.length?(e=Array.from(a),n=Math.min(e.length,t.length-1),i):e.slice()},i.range=function(a){return arguments.length?(t=Array.from(a),n=Math.min(e.length,t.length-1),i):t.slice()},i.invertExtent=function(a){var o=t.indexOf(a);return[e[o-1],e[o]]},i.unknown=function(a){return arguments.length?(r=a,i):r},i.copy=function(){return $0().domain(e).range(t).unknown(r)},He.apply(i,arguments)}const nc=new Date,ic=new Date;function we(e,t,r,n){function i(a){return e(a=arguments.length===0?new Date:new Date(+a)),a}return i.floor=a=>(e(a=new Date(+a)),a),i.ceil=a=>(e(a=new Date(a-1)),t(a,1),e(a),a),i.round=a=>{const o=i(a),u=i.ceil(a);return a-o<u-a?o:u},i.offset=(a,o)=>(t(a=new Date(+a),o==null?1:Math.floor(o)),a),i.range=(a,o,u)=>{const s=[];if(a=i.ceil(a),u=u==null?1:Math.floor(u),!(a<o)||!(u>0))return s;let c;do s.push(c=new Date(+a)),t(a,u),e(a);while(c<a&&a<o);return s},i.filter=a=>we(o=>{if(o>=o)for(;e(o),!a(o);)o.setTime(o-1)},(o,u)=>{if(o>=o)if(u<0)for(;++u<=0;)for(;t(o,-1),!a(o););else for(;--u>=0;)for(;t(o,1),!a(o););}),r&&(i.count=(a,o)=>(nc.setTime(+a),ic.setTime(+o),e(nc),e(ic),Math.floor(r(nc,ic))),i.every=a=>(a=Math.floor(a),!isFinite(a)||!(a>0)?null:a>1?i.filter(n?o=>n(o)%a===0:o=>i.count(0,o)%a===0):i)),i}const Ri=we(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);Ri.every=e=>(e=Math.floor(e),!isFinite(e)||!(e>0)?null:e>1?we(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):Ri);Ri.range;const ut=1e3,ze=ut*60,st=ze*60,pt=st*24,Wf=pt*7,Dy=pt*30,ac=pt*365,Lt=we(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+t*ut)},(e,t)=>(t-e)/ut,e=>e.getUTCSeconds());Lt.range;const Hf=we(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*ut)},(e,t)=>{e.setTime(+e+t*ze)},(e,t)=>(t-e)/ze,e=>e.getMinutes());Hf.range;const Gf=we(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+t*ze)},(e,t)=>(t-e)/ze,e=>e.getUTCMinutes());Gf.range;const Kf=we(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*ut-e.getMinutes()*ze)},(e,t)=>{e.setTime(+e+t*st)},(e,t)=>(t-e)/st,e=>e.getHours());Kf.range;const Vf=we(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+t*st)},(e,t)=>(t-e)/st,e=>e.getUTCHours());Vf.range;const Zn=we(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*ze)/pt,e=>e.getDate()-1);Zn.range;const Sa=we(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/pt,e=>e.getUTCDate()-1);Sa.range;const I0=we(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/pt,e=>Math.floor(e/pt));I0.range;function Yt(e){return we(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(t,r)=>{t.setDate(t.getDate()+r*7)},(t,r)=>(r-t-(r.getTimezoneOffset()-t.getTimezoneOffset())*ze)/Wf)}const Aa=Yt(0),Di=Yt(1),iT=Yt(2),aT=Yt(3),Or=Yt(4),oT=Yt(5),uT=Yt(6);Aa.range;Di.range;iT.range;aT.range;Or.range;oT.range;uT.range;function Zt(e){return we(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCDate(t.getUTCDate()+r*7)},(t,r)=>(r-t)/Wf)}const Pa=Zt(0),qi=Zt(1),sT=Zt(2),cT=Zt(3),_r=Zt(4),lT=Zt(5),fT=Zt(6);Pa.range;qi.range;sT.range;cT.range;_r.range;lT.range;fT.range;const Xf=we(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());Xf.range;const Yf=we(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());Yf.range;const vt=we(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());vt.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:we(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)});vt.range;const yt=we(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());yt.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:we(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)});yt.range;function k0(e,t,r,n,i,a){const o=[[Lt,1,ut],[Lt,5,5*ut],[Lt,15,15*ut],[Lt,30,30*ut],[a,1,ze],[a,5,5*ze],[a,15,15*ze],[a,30,30*ze],[i,1,st],[i,3,3*st],[i,6,6*st],[i,12,12*st],[n,1,pt],[n,2,2*pt],[r,1,Wf],[t,1,Dy],[t,3,3*Dy],[e,1,ac]];function u(c,f,l){const h=f<c;h&&([c,f]=[f,c]);const p=l&&typeof l.range=="function"?l:s(c,f,l),y=p?p.range(c,+f+1):[];return h?y.reverse():y}function s(c,f,l){const h=Math.abs(f-c)/l,p=$f(([,,d])=>d).right(o,h);if(p===o.length)return e.every(vl(c/ac,f/ac,l));if(p===0)return Ri.every(Math.max(vl(c,f,l),1));const[y,v]=o[h/o[p-1][2]<o[p][2]/h?p-1:p];return y.every(v)}return[u,s]}const[hT,dT]=k0(yt,Yf,Pa,I0,Vf,Gf),[pT,vT]=k0(vt,Xf,Aa,Zn,Kf,Hf);function oc(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function uc(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function Gr(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}function yT(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,u=e.months,s=e.shortMonths,c=Kr(i),f=Vr(i),l=Kr(a),h=Vr(a),p=Kr(o),y=Vr(o),v=Kr(u),d=Vr(u),x=Kr(s),w=Vr(s),b={a:D,A:L,b:F,B:H,c:null,d:Uy,e:Uy,f:BT,g:XT,G:ZT,H:RT,I:DT,j:qT,L:N0,m:LT,M:FT,p:K,q:U,Q:Gy,s:Ky,S:zT,u:UT,U:WT,V:HT,w:GT,W:KT,x:null,X:null,y:VT,Y:YT,Z:JT,"%":Hy},O={a:V,A:ce,b:pe,B:Re,c:null,d:Wy,e:Wy,f:rE,g:hE,G:pE,H:QT,I:eE,j:tE,L:D0,m:nE,M:iE,p:Ct,q:$e,Q:Gy,s:Ky,S:aE,u:oE,U:uE,V:sE,w:cE,W:lE,x:null,X:null,y:fE,Y:dE,Z:vE,"%":Hy},m={a:C,A,b:E,B:j,c:k,d:Fy,e:Fy,f:$T,g:Ly,G:By,H:zy,I:zy,j:ET,L:CT,m:TT,M:jT,p:P,q:PT,Q:kT,s:NT,S:MT,u:wT,U:OT,V:_T,w:xT,W:ST,x:I,X:N,y:Ly,Y:By,Z:AT,"%":IT};b.x=g(r,b),b.X=g(n,b),b.c=g(t,b),O.x=g(r,O),O.X=g(n,O),O.c=g(t,O);function g(z,X){return function(Y){var R=[],he=-1,J=0,me=z.length,ge,Ie,xt;for(Y instanceof Date||(Y=new Date(+Y));++he<me;)z.charCodeAt(he)===37&&(R.push(z.slice(J,he)),(Ie=qy[ge=z.charAt(++he)])!=null?ge=z.charAt(++he):Ie=ge==="e"?" ":"0",(xt=X[ge])&&(ge=xt(Y,Ie)),R.push(ge),J=he+1);return R.push(z.slice(J,he)),R.join("")}}function _(z,X){return function(Y){var R=Gr(1900,void 0,1),he=S(R,z,Y+="",0),J,me;if(he!=Y.length)return null;if("Q"in R)return new Date(R.Q);if("s"in R)return new Date(R.s*1e3+("L"in R?R.L:0));if(X&&!("Z"in R)&&(R.Z=0),"p"in R&&(R.H=R.H%12+R.p*12),R.m===void 0&&(R.m="q"in R?R.q:0),"V"in R){if(R.V<1||R.V>53)return null;"w"in R||(R.w=1),"Z"in R?(J=uc(Gr(R.y,0,1)),me=J.getUTCDay(),J=me>4||me===0?qi.ceil(J):qi(J),J=Sa.offset(J,(R.V-1)*7),R.y=J.getUTCFullYear(),R.m=J.getUTCMonth(),R.d=J.getUTCDate()+(R.w+6)%7):(J=oc(Gr(R.y,0,1)),me=J.getDay(),J=me>4||me===0?Di.ceil(J):Di(J),J=Zn.offset(J,(R.V-1)*7),R.y=J.getFullYear(),R.m=J.getMonth(),R.d=J.getDate()+(R.w+6)%7)}else("W"in R||"U"in R)&&("w"in R||(R.w="u"in R?R.u%7:"W"in R?1:0),me="Z"in R?uc(Gr(R.y,0,1)).getUTCDay():oc(Gr(R.y,0,1)).getDay(),R.m=0,R.d="W"in R?(R.w+6)%7+R.W*7-(me+5)%7:R.w+R.U*7-(me+6)%7);return"Z"in R?(R.H+=R.Z/100|0,R.M+=R.Z%100,uc(R)):oc(R)}}function S(z,X,Y,R){for(var he=0,J=X.length,me=Y.length,ge,Ie;he<J;){if(R>=me)return-1;if(ge=X.charCodeAt(he++),ge===37){if(ge=X.charAt(he++),Ie=m[ge in qy?X.charAt(he++):ge],!Ie||(R=Ie(z,Y,R))<0)return-1}else if(ge!=Y.charCodeAt(R++))return-1}return R}function P(z,X,Y){var R=c.exec(X.slice(Y));return R?(z.p=f.get(R[0].toLowerCase()),Y+R[0].length):-1}function C(z,X,Y){var R=p.exec(X.slice(Y));return R?(z.w=y.get(R[0].toLowerCase()),Y+R[0].length):-1}function A(z,X,Y){var R=l.exec(X.slice(Y));return R?(z.w=h.get(R[0].toLowerCase()),Y+R[0].length):-1}function E(z,X,Y){var R=x.exec(X.slice(Y));return R?(z.m=w.get(R[0].toLowerCase()),Y+R[0].length):-1}function j(z,X,Y){var R=v.exec(X.slice(Y));return R?(z.m=d.get(R[0].toLowerCase()),Y+R[0].length):-1}function k(z,X,Y){return S(z,t,X,Y)}function I(z,X,Y){return S(z,r,X,Y)}function N(z,X,Y){return S(z,n,X,Y)}function D(z){return o[z.getDay()]}function L(z){return a[z.getDay()]}function F(z){return s[z.getMonth()]}function H(z){return u[z.getMonth()]}function K(z){return i[+(z.getHours()>=12)]}function U(z){return 1+~~(z.getMonth()/3)}function V(z){return o[z.getUTCDay()]}function ce(z){return a[z.getUTCDay()]}function pe(z){return s[z.getUTCMonth()]}function Re(z){return u[z.getUTCMonth()]}function Ct(z){return i[+(z.getUTCHours()>=12)]}function $e(z){return 1+~~(z.getUTCMonth()/3)}return{format:function(z){var X=g(z+="",b);return X.toString=function(){return z},X},parse:function(z){var X=_(z+="",!1);return X.toString=function(){return z},X},utcFormat:function(z){var X=g(z+="",O);return X.toString=function(){return z},X},utcParse:function(z){var X=_(z+="",!0);return X.toString=function(){return z},X}}}var qy={"-":"",_:" ",0:"0"},Se=/^\s*\d+/,mT=/^%/,gT=/[\\^$*+?|[\]().{}]/g;function Q(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?new Array(r-a+1).join(t)+i:i)}function bT(e){return e.replace(gT,"\\$&")}function Kr(e){return new RegExp("^(?:"+e.map(bT).join("|")+")","i")}function Vr(e){return new Map(e.map((t,r)=>[t.toLowerCase(),r]))}function xT(e,t,r){var n=Se.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function wT(e,t,r){var n=Se.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function OT(e,t,r){var n=Se.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function _T(e,t,r){var n=Se.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function ST(e,t,r){var n=Se.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function By(e,t,r){var n=Se.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function Ly(e,t,r){var n=Se.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function AT(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function PT(e,t,r){var n=Se.exec(t.slice(r,r+1));return n?(e.q=n[0]*3-3,r+n[0].length):-1}function TT(e,t,r){var n=Se.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function Fy(e,t,r){var n=Se.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function ET(e,t,r){var n=Se.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function zy(e,t,r){var n=Se.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function jT(e,t,r){var n=Se.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function MT(e,t,r){var n=Se.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function CT(e,t,r){var n=Se.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function $T(e,t,r){var n=Se.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function IT(e,t,r){var n=mT.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function kT(e,t,r){var n=Se.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function NT(e,t,r){var n=Se.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function Uy(e,t){return Q(e.getDate(),t,2)}function RT(e,t){return Q(e.getHours(),t,2)}function DT(e,t){return Q(e.getHours()%12||12,t,2)}function qT(e,t){return Q(1+Zn.count(vt(e),e),t,3)}function N0(e,t){return Q(e.getMilliseconds(),t,3)}function BT(e,t){return N0(e,t)+"000"}function LT(e,t){return Q(e.getMonth()+1,t,2)}function FT(e,t){return Q(e.getMinutes(),t,2)}function zT(e,t){return Q(e.getSeconds(),t,2)}function UT(e){var t=e.getDay();return t===0?7:t}function WT(e,t){return Q(Aa.count(vt(e)-1,e),t,2)}function R0(e){var t=e.getDay();return t>=4||t===0?Or(e):Or.ceil(e)}function HT(e,t){return e=R0(e),Q(Or.count(vt(e),e)+(vt(e).getDay()===4),t,2)}function GT(e){return e.getDay()}function KT(e,t){return Q(Di.count(vt(e)-1,e),t,2)}function VT(e,t){return Q(e.getFullYear()%100,t,2)}function XT(e,t){return e=R0(e),Q(e.getFullYear()%100,t,2)}function YT(e,t){return Q(e.getFullYear()%1e4,t,4)}function ZT(e,t){var r=e.getDay();return e=r>=4||r===0?Or(e):Or.ceil(e),Q(e.getFullYear()%1e4,t,4)}function JT(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+Q(t/60|0,"0",2)+Q(t%60,"0",2)}function Wy(e,t){return Q(e.getUTCDate(),t,2)}function QT(e,t){return Q(e.getUTCHours(),t,2)}function eE(e,t){return Q(e.getUTCHours()%12||12,t,2)}function tE(e,t){return Q(1+Sa.count(yt(e),e),t,3)}function D0(e,t){return Q(e.getUTCMilliseconds(),t,3)}function rE(e,t){return D0(e,t)+"000"}function nE(e,t){return Q(e.getUTCMonth()+1,t,2)}function iE(e,t){return Q(e.getUTCMinutes(),t,2)}function aE(e,t){return Q(e.getUTCSeconds(),t,2)}function oE(e){var t=e.getUTCDay();return t===0?7:t}function uE(e,t){return Q(Pa.count(yt(e)-1,e),t,2)}function q0(e){var t=e.getUTCDay();return t>=4||t===0?_r(e):_r.ceil(e)}function sE(e,t){return e=q0(e),Q(_r.count(yt(e),e)+(yt(e).getUTCDay()===4),t,2)}function cE(e){return e.getUTCDay()}function lE(e,t){return Q(qi.count(yt(e)-1,e),t,2)}function fE(e,t){return Q(e.getUTCFullYear()%100,t,2)}function hE(e,t){return e=q0(e),Q(e.getUTCFullYear()%100,t,2)}function dE(e,t){return Q(e.getUTCFullYear()%1e4,t,4)}function pE(e,t){var r=e.getUTCDay();return e=r>=4||r===0?_r(e):_r.ceil(e),Q(e.getUTCFullYear()%1e4,t,4)}function vE(){return"+0000"}function Hy(){return"%"}function Gy(e){return+e}function Ky(e){return Math.floor(+e/1e3)}var ir,B0,L0;yE({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});function yE(e){return ir=yT(e),B0=ir.format,ir.parse,L0=ir.utcFormat,ir.utcParse,ir}function mE(e){return new Date(e)}function gE(e){return e instanceof Date?+e:+new Date(+e)}function Zf(e,t,r,n,i,a,o,u,s,c){var f=Df(),l=f.invert,h=f.domain,p=c(".%L"),y=c(":%S"),v=c("%I:%M"),d=c("%I %p"),x=c("%a %d"),w=c("%b %d"),b=c("%B"),O=c("%Y");function m(g){return(s(g)<g?p:u(g)<g?y:o(g)<g?v:a(g)<g?d:n(g)<g?i(g)<g?x:w:r(g)<g?b:O)(g)}return f.invert=function(g){return new Date(l(g))},f.domain=function(g){return arguments.length?h(Array.from(g,gE)):h().map(mE)},f.ticks=function(g){var _=h();return e(_[0],_[_.length-1],g??10)},f.tickFormat=function(g,_){return _==null?m:c(_)},f.nice=function(g){var _=h();return(!g||typeof g.range!="function")&&(g=t(_[0],_[_.length-1],g??10)),g?h(P0(_,g)):f},f.copy=function(){return Yn(f,Zf(e,t,r,n,i,a,o,u,s,c))},f}function bE(){return He.apply(Zf(pT,vT,vt,Xf,Aa,Zn,Kf,Hf,Lt,B0).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function xE(){return He.apply(Zf(hT,dT,yt,Yf,Pa,Sa,Vf,Gf,Lt,L0).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function Ta(){var e=0,t=1,r,n,i,a,o=Ce,u=!1,s;function c(l){return l==null||isNaN(l=+l)?s:o(i===0?.5:(l=(a(l)-r)*i,u?Math.max(0,Math.min(1,l)):l))}c.domain=function(l){return arguments.length?([e,t]=l,r=a(e=+e),n=a(t=+t),i=r===n?0:1/(n-r),c):[e,t]},c.clamp=function(l){return arguments.length?(u=!!l,c):u},c.interpolator=function(l){return arguments.length?(o=l,c):o};function f(l){return function(h){var p,y;return arguments.length?([p,y]=h,o=l(p,y),c):[o(0),o(1)]}}return c.range=f(Lr),c.rangeRound=f(Rf),c.unknown=function(l){return arguments.length?(s=l,c):s},function(l){return a=l,r=l(e),n=l(t),i=r===n?0:1/(n-r),c}}function jt(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function F0(){var e=Et(Ta()(Ce));return e.copy=function(){return jt(e,F0())},bt.apply(e,arguments)}function z0(){var e=Lf(Ta()).domain([1,10]);return e.copy=function(){return jt(e,z0()).base(e.base())},bt.apply(e,arguments)}function U0(){var e=Ff(Ta());return e.copy=function(){return jt(e,U0()).constant(e.constant())},bt.apply(e,arguments)}function Jf(){var e=zf(Ta());return e.copy=function(){return jt(e,Jf()).exponent(e.exponent())},bt.apply(e,arguments)}function wE(){return Jf.apply(null,arguments).exponent(.5)}function W0(){var e=[],t=Ce;function r(n){if(n!=null&&!isNaN(n=+n))return t((Vn(e,n,1)-1)/(e.length-1))}return r.domain=function(n){if(!arguments.length)return e.slice();e=[];for(let i of n)i!=null&&!isNaN(i=+i)&&e.push(i);return e.sort(St),r},r.interpolator=function(n){return arguments.length?(t=n,r):t},r.range=function(){return e.map((n,i)=>t(i/(e.length-1)))},r.quantiles=function(n){return Array.from({length:n+1},(i,a)=>uP(e,a/n))},r.copy=function(){return W0(t).domain(e)},bt.apply(r,arguments)}function Ea(){var e=0,t=.5,r=1,n=1,i,a,o,u,s,c=Ce,f,l=!1,h;function p(v){return isNaN(v=+v)?h:(v=.5+((v=+f(v))-a)*(n*v<n*a?u:s),c(l?Math.max(0,Math.min(1,v)):v))}p.domain=function(v){return arguments.length?([e,t,r]=v,i=f(e=+e),a=f(t=+t),o=f(r=+r),u=i===a?0:.5/(a-i),s=a===o?0:.5/(o-a),n=a<i?-1:1,p):[e,t,r]},p.clamp=function(v){return arguments.length?(l=!!v,p):l},p.interpolator=function(v){return arguments.length?(c=v,p):c};function y(v){return function(d){var x,w,b;return arguments.length?([x,w,b]=d,c=IP(v,[x,w,b]),p):[c(0),c(.5),c(1)]}}return p.range=y(Lr),p.rangeRound=y(Rf),p.unknown=function(v){return arguments.length?(h=v,p):h},function(v){return f=v,i=v(e),a=v(t),o=v(r),u=i===a?0:.5/(a-i),s=a===o?0:.5/(o-a),n=a<i?-1:1,p}}function H0(){var e=Et(Ea()(Ce));return e.copy=function(){return jt(e,H0())},bt.apply(e,arguments)}function G0(){var e=Lf(Ea()).domain([.1,1,10]);return e.copy=function(){return jt(e,G0()).base(e.base())},bt.apply(e,arguments)}function K0(){var e=Ff(Ea());return e.copy=function(){return jt(e,K0()).constant(e.constant())},bt.apply(e,arguments)}function Qf(){var e=zf(Ea());return e.copy=function(){return jt(e,Qf()).exponent(e.exponent())},bt.apply(e,arguments)}function OE(){return Qf.apply(null,arguments).exponent(.5)}const Vy=Object.freeze(Object.defineProperty({__proto__:null,scaleBand:yn,scaleDiverging:H0,scaleDivergingLog:G0,scaleDivergingPow:Qf,scaleDivergingSqrt:OE,scaleDivergingSymlog:K0,scaleIdentity:A0,scaleImplicit:yl,scaleLinear:Ni,scaleLog:T0,scaleOrdinal:If,scalePoint:on,scalePow:Uf,scaleQuantile:M0,scaleQuantize:C0,scaleRadial:j0,scaleSequential:F0,scaleSequentialLog:z0,scaleSequentialPow:Jf,scaleSequentialQuantile:W0,scaleSequentialSqrt:wE,scaleSequentialSymlog:U0,scaleSqrt:rT,scaleSymlog:E0,scaleThreshold:$0,scaleTime:bE,scaleUtc:xE,tickFormat:S0},Symbol.toStringTag,{value:"Module"}));var sc,Xy;function V0(){if(Xy)return sc;Xy=1;var e=Dr();function t(r,n,i){for(var a=-1,o=r.length;++a<o;){var u=r[a],s=n(u);if(s!=null&&(c===void 0?s===s&&!e(s):i(s,c)))var c=s,f=u}return f}return sc=t,sc}var cc,Yy;function _E(){if(Yy)return cc;Yy=1;function e(t,r){return t>r}return cc=e,cc}var lc,Zy;function SE(){if(Zy)return lc;Zy=1;var e=V0(),t=_E(),r=Br();function n(i){return i&&i.length?e(i,r,t):void 0}return lc=n,lc}var AE=SE();const ja=se(AE);var fc,Jy;function PE(){if(Jy)return fc;Jy=1;function e(t,r){return t<r}return fc=e,fc}var hc,Qy;function TE(){if(Qy)return hc;Qy=1;var e=V0(),t=PE(),r=Br();function n(i){return i&&i.length?e(i,r,t):void 0}return hc=n,hc}var EE=TE();const Ma=se(EE);var dc,em;function jE(){if(em)return dc;em=1;var e=vf(),t=Tt(),r=r0(),n=Ne();function i(a,o){var u=n(a)?e:r;return u(a,t(o,3))}return dc=i,dc}var pc,tm;function ME(){if(tm)return pc;tm=1;var e=e0(),t=jE();function r(n,i){return e(t(n,i),1)}return pc=r,pc}var CE=ME();const $E=se(CE);var vc,rm;function IE(){if(rm)return vc;rm=1;var e=Ef();function t(r,n){return e(r,n)}return vc=t,vc}var kE=IE();const eh=se(kE);var Fr=1e9,NE={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},rh,fe=!0,We="[DecimalError] ",Ut=We+"Invalid argument: ",th=We+"Exponent out of range: ",zr=Math.floor,Dt=Math.pow,RE=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,Be,Oe=1e7,le=7,X0=9007199254740991,Bi=zr(X0/le),W={};W.absoluteValue=W.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e};W.comparedTo=W.cmp=function(e){var t,r,n,i,a=this;if(e=new a.constructor(e),a.s!==e.s)return a.s||-e.s;if(a.e!==e.e)return a.e>e.e^a.s<0?1:-1;for(n=a.d.length,i=e.d.length,t=0,r=n<i?n:i;t<r;++t)if(a.d[t]!==e.d[t])return a.d[t]>e.d[t]^a.s<0?1:-1;return n===i?0:n>i^a.s<0?1:-1};W.decimalPlaces=W.dp=function(){var e=this,t=e.d.length-1,r=(t-e.e)*le;if(t=e.d[t],t)for(;t%10==0;t/=10)r--;return r<0?0:r};W.dividedBy=W.div=function(e){return ht(this,new this.constructor(e))};W.dividedToIntegerBy=W.idiv=function(e){var t=this,r=t.constructor;return oe(ht(t,new r(e),0,1),r.precision)};W.equals=W.eq=function(e){return!this.cmp(e)};W.exponent=function(){return ye(this)};W.greaterThan=W.gt=function(e){return this.cmp(e)>0};W.greaterThanOrEqualTo=W.gte=function(e){return this.cmp(e)>=0};W.isInteger=W.isint=function(){return this.e>this.d.length-2};W.isNegative=W.isneg=function(){return this.s<0};W.isPositive=W.ispos=function(){return this.s>0};W.isZero=function(){return this.s===0};W.lessThan=W.lt=function(e){return this.cmp(e)<0};W.lessThanOrEqualTo=W.lte=function(e){return this.cmp(e)<1};W.logarithm=W.log=function(e){var t,r=this,n=r.constructor,i=n.precision,a=i+5;if(e===void 0)e=new n(10);else if(e=new n(e),e.s<1||e.eq(Be))throw Error(We+"NaN");if(r.s<1)throw Error(We+(r.s?"NaN":"-Infinity"));return r.eq(Be)?new n(0):(fe=!1,t=ht(wn(r,a),wn(e,a),a),fe=!0,oe(t,i))};W.minus=W.sub=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?J0(t,e):Y0(t,(e.s=-e.s,e))};W.modulo=W.mod=function(e){var t,r=this,n=r.constructor,i=n.precision;if(e=new n(e),!e.s)throw Error(We+"NaN");return r.s?(fe=!1,t=ht(r,e,0,1).times(e),fe=!0,r.minus(t)):oe(new n(r),i)};W.naturalExponential=W.exp=function(){return Z0(this)};W.naturalLogarithm=W.ln=function(){return wn(this)};W.negated=W.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e};W.plus=W.add=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?Y0(t,e):J0(t,(e.s=-e.s,e))};W.precision=W.sd=function(e){var t,r,n,i=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error(Ut+e);if(t=ye(i)+1,n=i.d.length-1,r=n*le+1,n=i.d[n],n){for(;n%10==0;n/=10)r--;for(n=i.d[0];n>=10;n/=10)r++}return e&&t>r?t:r};W.squareRoot=W.sqrt=function(){var e,t,r,n,i,a,o,u=this,s=u.constructor;if(u.s<1){if(!u.s)return new s(0);throw Error(We+"NaN")}for(e=ye(u),fe=!1,i=Math.sqrt(+u),i==0||i==1/0?(t=et(u.d),(t.length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=zr((e+1)/2)-(e<0||e%2),i==1/0?t="5e"+e:(t=i.toExponential(),t=t.slice(0,t.indexOf("e")+1)+e),n=new s(t)):n=new s(i.toString()),r=s.precision,i=o=r+3;;)if(a=n,n=a.plus(ht(u,a,o+2)).times(.5),et(a.d).slice(0,o)===(t=et(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&t=="4999"){if(oe(a,r+1,0),a.times(a).eq(u)){n=a;break}}else if(t!="9999")break;o+=4}return fe=!0,oe(n,r)};W.times=W.mul=function(e){var t,r,n,i,a,o,u,s,c,f=this,l=f.constructor,h=f.d,p=(e=new l(e)).d;if(!f.s||!e.s)return new l(0);for(e.s*=f.s,r=f.e+e.e,s=h.length,c=p.length,s<c&&(a=h,h=p,p=a,o=s,s=c,c=o),a=[],o=s+c,n=o;n--;)a.push(0);for(n=c;--n>=0;){for(t=0,i=s+n;i>n;)u=a[i]+p[n]*h[i-n-1]+t,a[i--]=u%Oe|0,t=u/Oe|0;a[i]=(a[i]+t)%Oe|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,fe?oe(e,l.precision):e};W.toDecimalPlaces=W.todp=function(e,t){var r=this,n=r.constructor;return r=new n(r),e===void 0?r:(nt(e,0,Fr),t===void 0?t=n.rounding:nt(t,0,8),oe(r,e+ye(r)+1,t))};W.toExponential=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=Kt(n,!0):(nt(e,0,Fr),t===void 0?t=i.rounding:nt(t,0,8),n=oe(new i(n),e+1,t),r=Kt(n,!0,e+1)),r};W.toFixed=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?Kt(i):(nt(e,0,Fr),t===void 0?t=a.rounding:nt(t,0,8),n=oe(new a(i),e+ye(i)+1,t),r=Kt(n.abs(),!1,e+ye(n)+1),i.isneg()&&!i.isZero()?"-"+r:r)};W.toInteger=W.toint=function(){var e=this,t=e.constructor;return oe(new t(e),ye(e)+1,t.rounding)};W.toNumber=function(){return+this};W.toPower=W.pow=function(e){var t,r,n,i,a,o,u=this,s=u.constructor,c=12,f=+(e=new s(e));if(!e.s)return new s(Be);if(u=new s(u),!u.s){if(e.s<1)throw Error(We+"Infinity");return u}if(u.eq(Be))return u;if(n=s.precision,e.eq(Be))return oe(u,n);if(t=e.e,r=e.d.length-1,o=t>=r,a=u.s,o){if((r=f<0?-f:f)<=X0){for(i=new s(Be),t=Math.ceil(n/le+4),fe=!1;r%2&&(i=i.times(u),im(i.d,t)),r=zr(r/2),r!==0;)u=u.times(u),im(u.d,t);return fe=!0,e.s<0?new s(Be).div(i):oe(i,n)}}else if(a<0)throw Error(We+"NaN");return a=a<0&&e.d[Math.max(t,r)]&1?-1:1,u.s=1,fe=!1,i=e.times(wn(u,n+c)),fe=!0,i=Z0(i),i.s=a,i};W.toPrecision=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?(r=ye(i),n=Kt(i,r<=a.toExpNeg||r>=a.toExpPos)):(nt(e,1,Fr),t===void 0?t=a.rounding:nt(t,0,8),i=oe(new a(i),e,t),r=ye(i),n=Kt(i,e<=r||r<=a.toExpNeg,e)),n};W.toSignificantDigits=W.tosd=function(e,t){var r=this,n=r.constructor;return e===void 0?(e=n.precision,t=n.rounding):(nt(e,1,Fr),t===void 0?t=n.rounding:nt(t,0,8)),oe(new n(r),e,t)};W.toString=W.valueOf=W.val=W.toJSON=W[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=this,t=ye(e),r=e.constructor;return Kt(e,t<=r.toExpNeg||t>=r.toExpPos)};function Y0(e,t){var r,n,i,a,o,u,s,c,f=e.constructor,l=f.precision;if(!e.s||!t.s)return t.s||(t=new f(e)),fe?oe(t,l):t;if(s=e.d,c=t.d,o=e.e,i=t.e,s=s.slice(),a=o-i,a){for(a<0?(n=s,a=-a,u=c.length):(n=c,i=o,u=s.length),o=Math.ceil(l/le),u=o>u?o+1:u+1,a>u&&(a=u,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for(u=s.length,a=c.length,u-a<0&&(a=u,n=c,c=s,s=n),r=0;a;)r=(s[--a]=s[a]+c[a]+r)/Oe|0,s[a]%=Oe;for(r&&(s.unshift(r),++i),u=s.length;s[--u]==0;)s.pop();return t.d=s,t.e=i,fe?oe(t,l):t}function nt(e,t,r){if(e!==~~e||e<t||e>r)throw Error(Ut+e)}function et(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)n=e[t]+"",r=le-n.length,r&&(a+=wt(r)),a+=n;o=e[t],n=o+"",r=le-n.length,r&&(a+=wt(r))}else if(o===0)return"0";for(;o%10===0;)o/=10;return a+o}var ht=function(){function e(n,i){var a,o=0,u=n.length;for(n=n.slice();u--;)a=n[u]*i+o,n[u]=a%Oe|0,o=a/Oe|0;return o&&n.unshift(o),n}function t(n,i,a,o){var u,s;if(a!=o)s=a>o?1:-1;else for(u=s=0;u<a;u++)if(n[u]!=i[u]){s=n[u]>i[u]?1:-1;break}return s}function r(n,i,a){for(var o=0;a--;)n[a]-=o,o=n[a]<i[a]?1:0,n[a]=o*Oe+n[a]-i[a];for(;!n[0]&&n.length>1;)n.shift()}return function(n,i,a,o){var u,s,c,f,l,h,p,y,v,d,x,w,b,O,m,g,_,S,P=n.constructor,C=n.s==i.s?1:-1,A=n.d,E=i.d;if(!n.s)return new P(n);if(!i.s)throw Error(We+"Division by zero");for(s=n.e-i.e,_=E.length,m=A.length,p=new P(C),y=p.d=[],c=0;E[c]==(A[c]||0);)++c;if(E[c]>(A[c]||0)&&--s,a==null?w=a=P.precision:o?w=a+(ye(n)-ye(i))+1:w=a,w<0)return new P(0);if(w=w/le+2|0,c=0,_==1)for(f=0,E=E[0],w++;(c<m||f)&&w--;c++)b=f*Oe+(A[c]||0),y[c]=b/E|0,f=b%E|0;else{for(f=Oe/(E[0]+1)|0,f>1&&(E=e(E,f),A=e(A,f),_=E.length,m=A.length),O=_,v=A.slice(0,_),d=v.length;d<_;)v[d++]=0;S=E.slice(),S.unshift(0),g=E[0],E[1]>=Oe/2&&++g;do f=0,u=t(E,v,_,d),u<0?(x=v[0],_!=d&&(x=x*Oe+(v[1]||0)),f=x/g|0,f>1?(f>=Oe&&(f=Oe-1),l=e(E,f),h=l.length,d=v.length,u=t(l,v,h,d),u==1&&(f--,r(l,_<h?S:E,h))):(f==0&&(u=f=1),l=E.slice()),h=l.length,h<d&&l.unshift(0),r(v,l,d),u==-1&&(d=v.length,u=t(E,v,_,d),u<1&&(f++,r(v,_<d?S:E,d))),d=v.length):u===0&&(f++,v=[0]),y[c++]=f,u&&v[0]?v[d++]=A[O]||0:(v=[A[O]],d=1);while((O++<m||v[0]!==void 0)&&w--)}return y[0]||y.shift(),p.e=s,oe(p,o?a+ye(p)+1:a)}}();function Z0(e,t){var r,n,i,a,o,u,s=0,c=0,f=e.constructor,l=f.precision;if(ye(e)>16)throw Error(th+ye(e));if(!e.s)return new f(Be);for(fe=!1,u=l,o=new f(.03125);e.abs().gte(.1);)e=e.times(o),c+=5;for(n=Math.log(Dt(2,c))/Math.LN10*2+5|0,u+=n,r=i=a=new f(Be),f.precision=u;;){if(i=oe(i.times(e),u),r=r.times(++s),o=a.plus(ht(i,r,u)),et(o.d).slice(0,u)===et(a.d).slice(0,u)){for(;c--;)a=oe(a.times(a),u);return f.precision=l,t==null?(fe=!0,oe(a,l)):a}a=o}}function ye(e){for(var t=e.e*le,r=e.d[0];r>=10;r/=10)t++;return t}function yc(e,t,r){if(t>e.LN10.sd())throw fe=!0,r&&(e.precision=r),Error(We+"LN10 precision limit exceeded");return oe(new e(e.LN10),t)}function wt(e){for(var t="";e--;)t+="0";return t}function wn(e,t){var r,n,i,a,o,u,s,c,f,l=1,h=10,p=e,y=p.d,v=p.constructor,d=v.precision;if(p.s<1)throw Error(We+(p.s?"NaN":"-Infinity"));if(p.eq(Be))return new v(0);if(t==null?(fe=!1,c=d):c=t,p.eq(10))return t==null&&(fe=!0),yc(v,c);if(c+=h,v.precision=c,r=et(y),n=r.charAt(0),a=ye(p),Math.abs(a)<15e14){for(;n<7&&n!=1||n==1&&r.charAt(1)>3;)p=p.times(e),r=et(p.d),n=r.charAt(0),l++;a=ye(p),n>1?(p=new v("0."+r),a++):p=new v(n+"."+r.slice(1))}else return s=yc(v,c+2,d).times(a+""),p=wn(new v(n+"."+r.slice(1)),c-h).plus(s),v.precision=d,t==null?(fe=!0,oe(p,d)):p;for(u=o=p=ht(p.minus(Be),p.plus(Be),c),f=oe(p.times(p),c),i=3;;){if(o=oe(o.times(f),c),s=u.plus(ht(o,new v(i),c)),et(s.d).slice(0,c)===et(u.d).slice(0,c))return u=u.times(2),a!==0&&(u=u.plus(yc(v,c+2,d).times(a+""))),u=ht(u,new v(l),c),v.precision=d,t==null?(fe=!0,oe(u,d)):u;u=s,i+=2}}function nm(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;t.charCodeAt(n)===48;)++n;for(i=t.length;t.charCodeAt(i-1)===48;)--i;if(t=t.slice(n,i),t){if(i-=n,r=r-n-1,e.e=zr(r/le),e.d=[],n=(r+1)%le,r<0&&(n+=le),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=le;n<i;)e.d.push(+t.slice(n,n+=le));t=t.slice(n),n=le-t.length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),fe&&(e.e>Bi||e.e<-Bi))throw Error(th+r)}else e.s=0,e.e=0,e.d=[0];return e}function oe(e,t,r){var n,i,a,o,u,s,c,f,l=e.d;for(o=1,a=l[0];a>=10;a/=10)o++;if(n=t-o,n<0)n+=le,i=t,c=l[f=0];else{if(f=Math.ceil((n+1)/le),a=l.length,f>=a)return e;for(c=a=l[f],o=1;a>=10;a/=10)o++;n%=le,i=n-le+o}if(r!==void 0&&(a=Dt(10,o-i-1),u=c/a%10|0,s=t<0||l[f+1]!==void 0||c%a,s=r<4?(u||s)&&(r==0||r==(e.s<0?3:2)):u>5||u==5&&(r==4||s||r==6&&(n>0?i>0?c/Dt(10,o-i):0:l[f-1])%10&1||r==(e.s<0?8:7))),t<1||!l[0])return s?(a=ye(e),l.length=1,t=t-a-1,l[0]=Dt(10,(le-t%le)%le),e.e=zr(-t/le)||0):(l.length=1,l[0]=e.e=e.s=0),e;if(n==0?(l.length=f,a=1,f--):(l.length=f+1,a=Dt(10,le-n),l[f]=i>0?(c/Dt(10,o-i)%Dt(10,i)|0)*a:0),s)for(;;)if(f==0){(l[0]+=a)==Oe&&(l[0]=1,++e.e);break}else{if(l[f]+=a,l[f]!=Oe)break;l[f--]=0,a=1}for(n=l.length;l[--n]===0;)l.pop();if(fe&&(e.e>Bi||e.e<-Bi))throw Error(th+ye(e));return e}function J0(e,t){var r,n,i,a,o,u,s,c,f,l,h=e.constructor,p=h.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new h(e),fe?oe(t,p):t;if(s=e.d,l=t.d,n=t.e,c=e.e,s=s.slice(),o=c-n,o){for(f=o<0,f?(r=s,o=-o,u=l.length):(r=l,n=c,u=s.length),i=Math.max(Math.ceil(p/le),u)+2,o>i&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for(i=s.length,u=l.length,f=i<u,f&&(u=i),i=0;i<u;i++)if(s[i]!=l[i]){f=s[i]<l[i];break}o=0}for(f&&(r=s,s=l,l=r,t.s=-t.s),u=s.length,i=l.length-u;i>0;--i)s[u++]=0;for(i=l.length;i>o;){if(s[--i]<l[i]){for(a=i;a&&s[--a]===0;)s[a]=Oe-1;--s[a],s[i]+=Oe}s[i]-=l[i]}for(;s[--u]===0;)s.pop();for(;s[0]===0;s.shift())--n;return s[0]?(t.d=s,t.e=n,fe?oe(t,p):t):new h(0)}function Kt(e,t,r){var n,i=ye(e),a=et(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+wt(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+wt(-i-1)+a,r&&(n=r-o)>0&&(a+=wt(n))):i>=o?(a+=wt(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+wt(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=wt(n))),e.s<0?"-"+a:a}function im(e,t){if(e.length>t)return e.length=t,!0}function Q0(e){var t,r,n;function i(a){var o=this;if(!(o instanceof i))return new i(a);if(o.constructor=i,a instanceof i){o.s=a.s,o.e=a.e,o.d=(a=a.d)?a.slice():a;return}if(typeof a=="number"){if(a*0!==0)throw Error(Ut+a);if(a>0)o.s=1;else if(a<0)a=-a,o.s=-1;else{o.s=0,o.e=0,o.d=[0];return}if(a===~~a&&a<1e7){o.e=0,o.d=[a];return}return nm(o,a.toString())}else if(typeof a!="string")throw Error(Ut+a);if(a.charCodeAt(0)===45?(a=a.slice(1),o.s=-1):o.s=1,RE.test(a))nm(o,a);else throw Error(Ut+a)}if(i.prototype=W,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=Q0,i.config=i.set=DE,e===void 0&&(e={}),e)for(n=["precision","rounding","toExpNeg","toExpPos","LN10"],t=0;t<n.length;)e.hasOwnProperty(r=n[t++])||(e[r]=this[r]);return i.config(e),i}function DE(e){if(!e||typeof e!="object")throw Error(We+"Object expected");var t,r,n,i=["precision",1,Fr,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if((n=e[r=i[t]])!==void 0)if(zr(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(Ut+r+": "+n);if((n=e[r="LN10"])!==void 0)if(n==Math.LN10)this[r]=new this(n);else throw Error(Ut+r+": "+n);return this}var rh=Q0(NE);Be=new rh(1);const ae=rh;function qE(e){return zE(e)||FE(e)||LE(e)||BE()}function BE(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function LE(e,t){if(e){if(typeof e=="string")return xl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return xl(e,t)}}function FE(e){if(typeof Symbol<"u"&&Symbol.iterator in Object(e))return Array.from(e)}function zE(e){if(Array.isArray(e))return xl(e)}function xl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var UE=function(t){return t},ex={},tx=function(t){return t===ex},am=function(t){return function r(){return arguments.length===0||arguments.length===1&&tx(arguments.length<=0?void 0:arguments[0])?r:t.apply(void 0,arguments)}},WE=function e(t,r){return t===1?r:am(function(){for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];var o=i.filter(function(u){return u!==ex}).length;return o>=t?r.apply(void 0,i):e(t-o,am(function(){for(var u=arguments.length,s=new Array(u),c=0;c<u;c++)s[c]=arguments[c];var f=i.map(function(l){return tx(l)?s.shift():l});return r.apply(void 0,qE(f).concat(s))}))})},Ca=function(t){return WE(t.length,t)},wl=function(t,r){for(var n=[],i=t;i<r;++i)n[i-t]=i;return n},HE=Ca(function(e,t){return Array.isArray(t)?t.map(e):Object.keys(t).map(function(r){return t[r]}).map(e)}),GE=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];if(!r.length)return UE;var i=r.reverse(),a=i[0],o=i.slice(1);return function(){return o.reduce(function(u,s){return s(u)},a.apply(void 0,arguments))}},Ol=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},rx=function(t){var r=null,n=null;return function(){for(var i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];return r&&a.every(function(u,s){return u===r[s]})||(r=a,n=t.apply(void 0,a)),n}};function KE(e){var t;return e===0?t=1:t=Math.floor(new ae(e).abs().log(10).toNumber())+1,t}function VE(e,t,r){for(var n=new ae(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}var XE=Ca(function(e,t,r){var n=+e,i=+t;return n+r*(i-n)}),YE=Ca(function(e,t,r){var n=t-+e;return n=n||1/0,(r-e)/n}),ZE=Ca(function(e,t,r){var n=t-+e;return n=n||1/0,Math.max(0,Math.min(1,(r-e)/n))});const $a={rangeStep:VE,getDigitCount:KE,interpolateNumber:XE,uninterpolateNumber:YE,uninterpolateTruncation:ZE};function _l(e){return ej(e)||QE(e)||nx(e)||JE()}function JE(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function QE(e){if(typeof Symbol<"u"&&Symbol.iterator in Object(e))return Array.from(e)}function ej(e){if(Array.isArray(e))return Sl(e)}function On(e,t){return nj(e)||rj(e,t)||nx(e,t)||tj()}function tj(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function nx(e,t){if(e){if(typeof e=="string")return Sl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Sl(e,t)}}function Sl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function rj(e,t){if(!(typeof Symbol>"u"||!(Symbol.iterator in Object(e)))){var r=[],n=!0,i=!1,a=void 0;try{for(var o=e[Symbol.iterator](),u;!(n=(u=o.next()).done)&&(r.push(u.value),!(t&&r.length===t));n=!0);}catch(s){i=!0,a=s}finally{try{!n&&o.return!=null&&o.return()}finally{if(i)throw a}}return r}}function nj(e){if(Array.isArray(e))return e}function ix(e){var t=On(e,2),r=t[0],n=t[1],i=r,a=n;return r>n&&(i=n,a=r),[i,a]}function ax(e,t,r){if(e.lte(0))return new ae(0);var n=$a.getDigitCount(e.toNumber()),i=new ae(10).pow(n),a=e.div(i),o=n!==1?.05:.1,u=new ae(Math.ceil(a.div(o).toNumber())).add(r).mul(o),s=u.mul(i);return t?s:new ae(Math.ceil(s))}function ij(e,t,r){var n=1,i=new ae(e);if(!i.isint()&&r){var a=Math.abs(e);a<1?(n=new ae(10).pow($a.getDigitCount(e)-1),i=new ae(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new ae(Math.floor(e)))}else e===0?i=new ae(Math.floor((t-1)/2)):r||(i=new ae(Math.floor(e)));var o=Math.floor((t-1)/2),u=GE(HE(function(s){return i.add(new ae(s-o).mul(n)).toNumber()}),wl);return u(0,t)}function ox(e,t,r,n){var i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new ae(0),tickMin:new ae(0),tickMax:new ae(0)};var a=ax(new ae(t).sub(e).div(r-1),n,i),o;e<=0&&t>=0?o=new ae(0):(o=new ae(e).add(t).div(2),o=o.sub(new ae(o).mod(a)));var u=Math.ceil(o.sub(e).div(a).toNumber()),s=Math.ceil(new ae(t).sub(o).div(a).toNumber()),c=u+s+1;return c>r?ox(e,t,r,n,i+1):(c<r&&(s=t>0?s+(r-c):s,u=t>0?u:u+(r-c)),{step:a,tickMin:o.sub(new ae(u).mul(a)),tickMax:o.add(new ae(s).mul(a))})}function aj(e){var t=On(e,2),r=t[0],n=t[1],i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:6,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=Math.max(i,2),u=ix([r,n]),s=On(u,2),c=s[0],f=s[1];if(c===-1/0||f===1/0){var l=f===1/0?[c].concat(_l(wl(0,i-1).map(function(){return 1/0}))):[].concat(_l(wl(0,i-1).map(function(){return-1/0})),[f]);return r>n?Ol(l):l}if(c===f)return ij(c,i,a);var h=ox(c,f,o,a),p=h.step,y=h.tickMin,v=h.tickMax,d=$a.rangeStep(y,v.add(new ae(.1).mul(p)),p);return r>n?Ol(d):d}function oj(e,t){var r=On(e,2),n=r[0],i=r[1],a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=ix([n,i]),u=On(o,2),s=u[0],c=u[1];if(s===-1/0||c===1/0)return[n,i];if(s===c)return[s];var f=Math.max(t,2),l=ax(new ae(c).sub(s).div(f-1),a,0),h=[].concat(_l($a.rangeStep(new ae(s),new ae(c).sub(new ae(.99).mul(l)),l)),[c]);return n>i?Ol(h):h}var uj=rx(aj),sj=rx(oj),cj=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function Sr(e){"@babel/helpers - typeof";return Sr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Sr(e)}function Li(){return Li=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Li.apply(this,arguments)}function lj(e,t){return pj(e)||dj(e,t)||hj(e,t)||fj()}function fj(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function hj(e,t){if(e){if(typeof e=="string")return om(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return om(e,t)}}function om(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function dj(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],s=!0,c=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);s=!0);}catch(f){c=!0,i=f}finally{try{if(!s&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return u}}function pj(e){if(Array.isArray(e))return e}function vj(e,t){if(e==null)return{};var r=yj(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function yj(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function mj(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function gj(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,cx(n.key),n)}}function bj(e,t,r){return t&&gj(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function xj(e,t,r){return t=Fi(t),wj(e,ux()?Reflect.construct(t,r||[],Fi(e).constructor):t.apply(e,r))}function wj(e,t){if(t&&(Sr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Oj(e)}function Oj(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ux(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(ux=function(){return!!e})()}function Fi(e){return Fi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Fi(e)}function _j(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Al(e,t)}function Al(e,t){return Al=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Al(e,t)}function sx(e,t,r){return t=cx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function cx(e){var t=Sj(e,"string");return Sr(t)=="symbol"?t:t+""}function Sj(e,t){if(Sr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Sr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Ia=function(e){function t(){return mj(this,t),xj(this,t,arguments)}return _j(t,e),bj(t,[{key:"render",value:function(){var n=this.props,i=n.offset,a=n.layout,o=n.width,u=n.dataKey,s=n.data,c=n.dataPointFormatter,f=n.xAxis,l=n.yAxis,h=vj(n,cj),p=ee(h,!1);this.props.direction==="x"&&f.type!=="number"&&Wt(!1);var y=s.map(function(v){var d=c(v,u),x=d.x,w=d.y,b=d.value,O=d.errorVal;if(!O)return null;var m=[],g,_;if(Array.isArray(O)){var S=lj(O,2);g=S[0],_=S[1]}else g=_=O;if(a==="vertical"){var P=f.scale,C=w+i,A=C+o,E=C-o,j=P(b-g),k=P(b+_);m.push({x1:k,y1:A,x2:k,y2:E}),m.push({x1:j,y1:C,x2:k,y2:C}),m.push({x1:j,y1:A,x2:j,y2:E})}else if(a==="horizontal"){var I=l.scale,N=x+i,D=N-o,L=N+o,F=I(b-g),H=I(b+_);m.push({x1:D,y1:H,x2:L,y2:H}),m.push({x1:N,y1:F,x2:N,y2:H}),m.push({x1:D,y1:F,x2:L,y2:F})}return T.createElement(_e,Li({className:"recharts-errorBar",key:"bar-".concat(m.map(function(K){return"".concat(K.x1,"-").concat(K.x2,"-").concat(K.y1,"-").concat(K.y2)}))},p),m.map(function(K){return T.createElement("line",Li({},K,{key:"line-".concat(K.x1,"-").concat(K.x2,"-").concat(K.y1,"-").concat(K.y2)}))}))});return T.createElement(_e,{className:"recharts-errorBars"},y)}}])}(T.Component);sx(Ia,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"});sx(Ia,"displayName","ErrorBar");function _n(e){"@babel/helpers - typeof";return _n=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_n(e)}function um(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function kt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?um(Object(r),!0).forEach(function(n){Aj(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):um(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Aj(e,t,r){return t=Pj(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Pj(e){var t=Tj(e,"string");return _n(t)=="symbol"?t:t+""}function Tj(e,t){if(_n(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(_n(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var lx=function(t){var r=t.children,n=t.formattedGraphicalItems,i=t.legendWidth,a=t.legendContent,o=qe(r,dr);if(!o)return null;var u=dr.defaultProps,s=u!==void 0?kt(kt({},u),o.props):{},c;return o.props&&o.props.payload?c=o.props&&o.props.payload:a==="children"?c=(n||[]).reduce(function(f,l){var h=l.item,p=l.props,y=p.sectors||p.data||[];return f.concat(y.map(function(v){return{type:o.props.iconType||h.props.legendType,value:v.name,color:v.fill,payload:v}}))},[]):c=(n||[]).map(function(f){var l=f.item,h=l.type.defaultProps,p=h!==void 0?kt(kt({},h),l.props):{},y=p.dataKey,v=p.name,d=p.legendType,x=p.hide;return{inactive:x,dataKey:y,type:s.iconType||d||"square",color:nh(l),value:v||y,payload:p}}),kt(kt(kt({},s),dr.getWithHeight(o,i)),{},{payload:c,item:o})};function Sn(e){"@babel/helpers - typeof";return Sn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Sn(e)}function sm(e){return Cj(e)||Mj(e)||jj(e)||Ej()}function Ej(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function jj(e,t){if(e){if(typeof e=="string")return Pl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Pl(e,t)}}function Mj(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Cj(e){if(Array.isArray(e))return Pl(e)}function Pl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function cm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function de(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?cm(Object(r),!0).forEach(function(n){vr(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):cm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function vr(e,t,r){return t=$j(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function $j(e){var t=Ij(e,"string");return Sn(t)=="symbol"?t:t+""}function Ij(e,t){if(Sn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Sn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function it(e,t,r){return ne(e)||ne(t)?r:xe(t)?Ue(e,t,r):Z(t)?t(e):r}function un(e,t,r,n){var i=$E(e,function(u){return it(u,t)});if(r==="number"){var a=i.filter(function(u){return B(u)||parseFloat(u)});return a.length?[Ma(a),ja(a)]:[1/0,-1/0]}var o=n?i.filter(function(u){return!ne(u)}):i;return o.map(function(u){return xe(u)||u instanceof Date?u:""})}var kj=function(t){var r,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],i=arguments.length>2?arguments[2]:void 0,a=arguments.length>3?arguments[3]:void 0,o=-1,u=(r=n?.length)!==null&&r!==void 0?r:0;if(u<=1)return 0;if(a&&a.axisType==="angleAxis"&&Math.abs(Math.abs(a.range[1]-a.range[0])-360)<=1e-6)for(var s=a.range,c=0;c<u;c++){var f=c>0?i[c-1].coordinate:i[u-1].coordinate,l=i[c].coordinate,h=c>=u-1?i[0].coordinate:i[c+1].coordinate,p=void 0;if(Ye(l-f)!==Ye(h-l)){var y=[];if(Ye(h-l)===Ye(s[1]-s[0])){p=h;var v=l+s[1]-s[0];y[0]=Math.min(v,(v+f)/2),y[1]=Math.max(v,(v+f)/2)}else{p=f;var d=h+s[1]-s[0];y[0]=Math.min(l,(d+l)/2),y[1]=Math.max(l,(d+l)/2)}var x=[Math.min(l,(p+l)/2),Math.max(l,(p+l)/2)];if(t>x[0]&&t<=x[1]||t>=y[0]&&t<=y[1]){o=i[c].index;break}}else{var w=Math.min(f,h),b=Math.max(f,h);if(t>(w+l)/2&&t<=(b+l)/2){o=i[c].index;break}}}else for(var O=0;O<u;O++)if(O===0&&t<=(n[O].coordinate+n[O+1].coordinate)/2||O>0&&O<u-1&&t>(n[O].coordinate+n[O-1].coordinate)/2&&t<=(n[O].coordinate+n[O+1].coordinate)/2||O===u-1&&t>(n[O].coordinate+n[O-1].coordinate)/2){o=n[O].index;break}return o},nh=function(t){var r,n=t,i=n.type.displayName,a=(r=t.type)!==null&&r!==void 0&&r.defaultProps?de(de({},t.type.defaultProps),t.props):t.props,o=a.stroke,u=a.fill,s;switch(i){case"Line":s=o;break;case"Area":case"Radar":s=o&&o!=="none"?o:u;break;default:s=u;break}return s},Nj=function(t){var r=t.barSize,n=t.totalSize,i=t.stackGroups,a=i===void 0?{}:i;if(!a)return{};for(var o={},u=Object.keys(a),s=0,c=u.length;s<c;s++)for(var f=a[u[s]].stackGroups,l=Object.keys(f),h=0,p=l.length;h<p;h++){var y=f[l[h]],v=y.items,d=y.cateAxisId,x=v.filter(function(_){return lt(_.type).indexOf("Bar")>=0});if(x&&x.length){var w=x[0].type.defaultProps,b=w!==void 0?de(de({},w),x[0].props):x[0].props,O=b.barSize,m=b[d];o[m]||(o[m]=[]);var g=ne(O)?r:O;o[m].push({item:x[0],stackList:x.slice(1),barSize:ne(g)?void 0:Gt(g,n,0)})}}return o},Rj=function(t){var r=t.barGap,n=t.barCategoryGap,i=t.bandSize,a=t.sizeList,o=a===void 0?[]:a,u=t.maxBarSize,s=o.length;if(s<1)return null;var c=Gt(r,i,0,!0),f,l=[];if(o[0].barSize===+o[0].barSize){var h=!1,p=i/s,y=o.reduce(function(O,m){return O+m.barSize||0},0);y+=(s-1)*c,y>=i&&(y-=(s-1)*c,c=0),y>=i&&p>0&&(h=!0,p*=.9,y=s*p);var v=(i-y)/2>>0,d={offset:v-c,size:0};f=o.reduce(function(O,m){var g={item:m.item,position:{offset:d.offset+d.size+c,size:h?p:m.barSize}},_=[].concat(sm(O),[g]);return d=_[_.length-1].position,m.stackList&&m.stackList.length&&m.stackList.forEach(function(S){_.push({item:S,position:d})}),_},l)}else{var x=Gt(n,i,0,!0);i-2*x-(s-1)*c<=0&&(c=0);var w=(i-2*x-(s-1)*c)/s;w>1&&(w>>=0);var b=u===+u?Math.min(w,u):w;f=o.reduce(function(O,m,g){var _=[].concat(sm(O),[{item:m.item,position:{offset:x+(w+c)*g+(w-b)/2,size:b}}]);return m.stackList&&m.stackList.length&&m.stackList.forEach(function(S){_.push({item:S,position:_[_.length-1].position})}),_},l)}return f},Dj=function(t,r,n,i){var a=n.children,o=n.width,u=n.margin,s=o-(u.left||0)-(u.right||0),c=lx({children:a,legendWidth:s});if(c){var f=i||{},l=f.width,h=f.height,p=c.align,y=c.verticalAlign,v=c.layout;if((v==="vertical"||v==="horizontal"&&y==="middle")&&p!=="center"&&B(t[p]))return de(de({},t),{},vr({},p,t[p]+(l||0)));if((v==="horizontal"||v==="vertical"&&p==="center")&&y!=="middle"&&B(t[y]))return de(de({},t),{},vr({},y,t[y]+(h||0)))}return t},qj=function(t,r,n){return ne(r)?!0:t==="horizontal"?r==="yAxis":t==="vertical"||n==="x"?r==="xAxis":n==="y"?r==="yAxis":!0},fx=function(t,r,n,i,a){var o=r.props.children,u=Ze(o,Ia).filter(function(c){return qj(i,a,c.props.direction)});if(u&&u.length){var s=u.map(function(c){return c.props.dataKey});return t.reduce(function(c,f){var l=it(f,n);if(ne(l))return c;var h=Array.isArray(l)?[Ma(l),ja(l)]:[l,l],p=s.reduce(function(y,v){var d=it(f,v,0),x=h[0]-Math.abs(Array.isArray(d)?d[0]:d),w=h[1]+Math.abs(Array.isArray(d)?d[1]:d);return[Math.min(x,y[0]),Math.max(w,y[1])]},[1/0,-1/0]);return[Math.min(p[0],c[0]),Math.max(p[1],c[1])]},[1/0,-1/0])}return null},Bj=function(t,r,n,i,a){var o=r.map(function(u){return fx(t,u,n,a,i)}).filter(function(u){return!ne(u)});return o&&o.length?o.reduce(function(u,s){return[Math.min(u[0],s[0]),Math.max(u[1],s[1])]},[1/0,-1/0]):null},hx=function(t,r,n,i,a){var o=r.map(function(s){var c=s.props.dataKey;return n==="number"&&c&&fx(t,s,c,i)||un(t,c,n,a)});if(n==="number")return o.reduce(function(s,c){return[Math.min(s[0],c[0]),Math.max(s[1],c[1])]},[1/0,-1/0]);var u={};return o.reduce(function(s,c){for(var f=0,l=c.length;f<l;f++)u[c[f]]||(u[c[f]]=!0,s.push(c[f]));return s},[])},dx=function(t,r){return t==="horizontal"&&r==="xAxis"||t==="vertical"&&r==="yAxis"||t==="centric"&&r==="angleAxis"||t==="radial"&&r==="radiusAxis"},px=function(t,r,n,i){if(i)return t.map(function(s){return s.coordinate});var a,o,u=t.map(function(s){return s.coordinate===r&&(a=!0),s.coordinate===n&&(o=!0),s.coordinate});return a||u.push(r),o||u.push(n),u},ct=function(t,r,n){if(!t)return null;var i=t.scale,a=t.duplicateDomain,o=t.type,u=t.range,s=t.realScaleType==="scaleBand"?i.bandwidth()/2:2,c=(r||n)&&o==="category"&&i.bandwidth?i.bandwidth()/s:0;if(c=t.axisType==="angleAxis"&&u?.length>=2?Ye(u[0]-u[1])*2*c:c,r&&(t.ticks||t.niceTicks)){var f=(t.ticks||t.niceTicks).map(function(l){var h=a?a.indexOf(l):l;return{coordinate:i(h)+c,value:l,offset:c}});return f.filter(function(l){return!Hn(l.coordinate)})}return t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(l,h){return{coordinate:i(l)+c,value:l,index:h,offset:c}}):i.ticks&&!n?i.ticks(t.tickCount).map(function(l){return{coordinate:i(l)+c,value:l,offset:c}}):i.domain().map(function(l,h){return{coordinate:i(l)+c,value:a?a[l]:l,index:h,offset:c}})},mc=new WeakMap,li=function(t,r){if(typeof r!="function")return t;mc.has(t)||mc.set(t,new WeakMap);var n=mc.get(t);if(n.has(r))return n.get(r);var i=function(){t.apply(void 0,arguments),r.apply(void 0,arguments)};return n.set(r,i),i},Lj=function(t,r,n){var i=t.scale,a=t.type,o=t.layout,u=t.axisType;if(i==="auto")return o==="radial"&&u==="radiusAxis"?{scale:yn(),realScaleType:"band"}:o==="radial"&&u==="angleAxis"?{scale:Ni(),realScaleType:"linear"}:a==="category"&&r&&(r.indexOf("LineChart")>=0||r.indexOf("AreaChart")>=0||r.indexOf("ComposedChart")>=0&&!n)?{scale:on(),realScaleType:"point"}:a==="category"?{scale:yn(),realScaleType:"band"}:{scale:Ni(),realScaleType:"linear"};if(Ht(i)){var s="scale".concat(ya(i));return{scale:(Vy[s]||on)(),realScaleType:Vy[s]?s:"point"}}return Z(i)?{scale:i}:{scale:on(),realScaleType:"point"}},lm=1e-4,Fj=function(t){var r=t.domain();if(!(!r||r.length<=2)){var n=r.length,i=t.range(),a=Math.min(i[0],i[1])-lm,o=Math.max(i[0],i[1])+lm,u=t(r[0]),s=t(r[n-1]);(u<a||u>o||s<a||s>o)&&t.domain([r[0],r[n-1]])}},zj=function(t,r){if(!t)return null;for(var n=0,i=t.length;n<i;n++)if(t[n].item===r)return t[n].position;return null},Uj=function(t,r){if(!r||r.length!==2||!B(r[0])||!B(r[1]))return t;var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]),a=[t[0],t[1]];return(!B(t[0])||t[0]<n)&&(a[0]=n),(!B(t[1])||t[1]>i)&&(a[1]=i),a[0]>i&&(a[0]=i),a[1]<n&&(a[1]=n),a},Wj=function(t){var r=t.length;if(!(r<=0))for(var n=0,i=t[0].length;n<i;++n)for(var a=0,o=0,u=0;u<r;++u){var s=Hn(t[u][n][1])?t[u][n][0]:t[u][n][1];s>=0?(t[u][n][0]=a,t[u][n][1]=a+s,a=t[u][n][1]):(t[u][n][0]=o,t[u][n][1]=o+s,o=t[u][n][1])}},Hj=function(t){var r=t.length;if(!(r<=0))for(var n=0,i=t[0].length;n<i;++n)for(var a=0,o=0;o<r;++o){var u=Hn(t[o][n][1])?t[o][n][0]:t[o][n][1];u>=0?(t[o][n][0]=a,t[o][n][1]=a+u,a=t[o][n][1]):(t[o][n][0]=0,t[o][n][1]=0)}},Gj={sign:Wj,expand:jO,none:yr,silhouette:MO,wiggle:CO,positive:Hj},Kj=function(t,r,n){var i=r.map(function(u){return u.props.dataKey}),a=Gj[n],o=EO().keys(i).value(function(u,s){return+it(u,s,0)}).order(rl).offset(a);return o(t)},Vj=function(t,r,n,i,a,o){if(!t)return null;var u=o?r.reverse():r,s={},c=u.reduce(function(l,h){var p,y=(p=h.type)!==null&&p!==void 0&&p.defaultProps?de(de({},h.type.defaultProps),h.props):h.props,v=y.stackId,d=y.hide;if(d)return l;var x=y[n],w=l[x]||{hasStack:!1,stackGroups:{}};if(xe(v)){var b=w.stackGroups[v]||{numericAxisId:n,cateAxisId:i,items:[]};b.items.push(h),w.hasStack=!0,w.stackGroups[v]=b}else w.stackGroups[va("_stackId_")]={numericAxisId:n,cateAxisId:i,items:[h]};return de(de({},l),{},vr({},x,w))},s),f={};return Object.keys(c).reduce(function(l,h){var p=c[h];if(p.hasStack){var y={};p.stackGroups=Object.keys(p.stackGroups).reduce(function(v,d){var x=p.stackGroups[d];return de(de({},v),{},vr({},d,{numericAxisId:n,cateAxisId:i,items:x.items,stackedData:Kj(t,x.items,a)}))},y)}return de(de({},l),{},vr({},h,p))},f)},Xj=function(t,r){var n=r.realScaleType,i=r.type,a=r.tickCount,o=r.originalDomain,u=r.allowDecimals,s=n||r.scale;if(s!=="auto"&&s!=="linear")return null;if(a&&i==="number"&&o&&(o[0]==="auto"||o[1]==="auto")){var c=t.domain();if(!c.length)return null;var f=uj(c,a,u);return t.domain([Ma(f),ja(f)]),{niceTicks:f}}if(a&&i==="number"){var l=t.domain(),h=sj(l,a,u);return{niceTicks:h}}return null},fm=function(t){var r=t.axis,n=t.ticks,i=t.offset,a=t.bandSize,o=t.entry,u=t.index;if(r.type==="category")return n[u]?n[u].coordinate+i:null;var s=it(o,r.dataKey,r.domain[u]);return ne(s)?null:r.scale(s)-a/2+i},Yj=function(t){var r=t.numericAxis,n=r.scale.domain();if(r.type==="number"){var i=Math.min(n[0],n[1]),a=Math.max(n[0],n[1]);return i<=0&&a>=0?0:a<0?a:i}return n[0]},Zj=function(t,r){var n,i=(n=t.type)!==null&&n!==void 0&&n.defaultProps?de(de({},t.type.defaultProps),t.props):t.props,a=i.stackId;if(xe(a)){var o=r[a];if(o){var u=o.items.indexOf(t);return u>=0?o.stackedData[u]:null}}return null},Jj=function(t){return t.reduce(function(r,n){return[Ma(n.concat([r[0]]).filter(B)),ja(n.concat([r[1]]).filter(B))]},[1/0,-1/0])},vx=function(t,r,n){return Object.keys(t).reduce(function(i,a){var o=t[a],u=o.stackedData,s=u.reduce(function(c,f){var l=Jj(f.slice(r,n+1));return[Math.min(c[0],l[0]),Math.max(c[1],l[1])]},[1/0,-1/0]);return[Math.min(s[0],i[0]),Math.max(s[1],i[1])]},[1/0,-1/0]).map(function(i){return i===1/0||i===-1/0?0:i})},hm=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,dm=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Tl=function(t,r,n){if(Z(t))return t(r,n);if(!Array.isArray(t))return r;var i=[];if(B(t[0]))i[0]=n?t[0]:Math.min(t[0],r[0]);else if(hm.test(t[0])){var a=+hm.exec(t[0])[1];i[0]=r[0]-a}else Z(t[0])?i[0]=t[0](r[0]):i[0]=r[0];if(B(t[1]))i[1]=n?t[1]:Math.max(t[1],r[1]);else if(dm.test(t[1])){var o=+dm.exec(t[1])[1];i[1]=r[1]+o}else Z(t[1])?i[1]=t[1](r[1]):i[1]=r[1];return i},zi=function(t,r,n){if(t&&t.scale&&t.scale.bandwidth){var i=t.scale.bandwidth();if(!n||i>0)return i}if(t&&r&&r.length>=2){for(var a=Mf(r,function(l){return l.coordinate}),o=1/0,u=1,s=a.length;u<s;u++){var c=a[u],f=a[u-1];o=Math.min((c.coordinate||0)-(f.coordinate||0),o)}return o===1/0?0:o}return n?void 0:0},pm=function(t,r,n){return!t||!t.length||eh(t,Ue(n,"type.defaultProps.domain"))?r:t},yx=function(t,r){var n=t.type.defaultProps?de(de({},t.type.defaultProps),t.props):t.props,i=n.dataKey,a=n.name,o=n.unit,u=n.formatter,s=n.tooltipType,c=n.chartType,f=n.hide;return de(de({},ee(t,!1)),{},{dataKey:i,unit:o,formatter:u,name:a||i,color:nh(t),value:it(r,i),type:s,payload:r,chartType:c,hide:f})};function An(e){"@babel/helpers - typeof";return An=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},An(e)}function vm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ym(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?vm(Object(r),!0).forEach(function(n){Qj(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Qj(e,t,r){return t=eM(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function eM(e){var t=tM(e,"string");return An(t)=="symbol"?t:t+""}function tM(e,t){if(An(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(An(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Ui=Math.PI/180,rM=function(t){return t*180/Math.PI},Pe=function(t,r,n,i){return{x:t+Math.cos(-Ui*i)*n,y:r+Math.sin(-Ui*i)*n}},nM=function(t,r){var n=t.x,i=t.y,a=r.x,o=r.y;return Math.sqrt(Math.pow(n-a,2)+Math.pow(i-o,2))},iM=function(t,r){var n=t.x,i=t.y,a=r.cx,o=r.cy,u=nM({x:n,y:i},{x:a,y:o});if(u<=0)return{radius:u};var s=(n-a)/u,c=Math.acos(s);return i>o&&(c=2*Math.PI-c),{radius:u,angle:rM(c),angleInRadian:c}},aM=function(t){var r=t.startAngle,n=t.endAngle,i=Math.floor(r/360),a=Math.floor(n/360),o=Math.min(i,a);return{startAngle:r-o*360,endAngle:n-o*360}},oM=function(t,r){var n=r.startAngle,i=r.endAngle,a=Math.floor(n/360),o=Math.floor(i/360),u=Math.min(a,o);return t+u*360},mm=function(t,r){var n=t.x,i=t.y,a=iM({x:n,y:i},r),o=a.radius,u=a.angle,s=r.innerRadius,c=r.outerRadius;if(o<s||o>c)return!1;if(o===0)return!0;var f=aM(r),l=f.startAngle,h=f.endAngle,p=u,y;if(l<=h){for(;p>h;)p-=360;for(;p<l;)p+=360;y=p>=l&&p<=h}else{for(;p>l;)p-=360;for(;p<h;)p+=360;y=p>=h&&p<=l}return y?ym(ym({},r),{},{radius:o,angle:oM(p,r)}):null};function Pn(e){"@babel/helpers - typeof";return Pn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Pn(e)}var uM=["offset"];function sM(e){return hM(e)||fM(e)||lM(e)||cM()}function cM(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function lM(e,t){if(e){if(typeof e=="string")return El(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return El(e,t)}}function fM(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function hM(e){if(Array.isArray(e))return El(e)}function El(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function dM(e,t){if(e==null)return{};var r=pM(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function pM(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function gm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function be(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?gm(Object(r),!0).forEach(function(n){vM(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function vM(e,t,r){return t=yM(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yM(e){var t=mM(e,"string");return Pn(t)=="symbol"?t:t+""}function mM(e,t){if(Pn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Pn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Tn(){return Tn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Tn.apply(this,arguments)}var gM=function(t){var r=t.value,n=t.formatter,i=ne(t.children)?r:t.children;return Z(n)?n(i):i},bM=function(t,r){var n=Ye(r-t),i=Math.min(Math.abs(r-t),360);return n*i},xM=function(t,r,n){var i=t.position,a=t.viewBox,o=t.offset,u=t.className,s=a,c=s.cx,f=s.cy,l=s.innerRadius,h=s.outerRadius,p=s.startAngle,y=s.endAngle,v=s.clockWise,d=(l+h)/2,x=bM(p,y),w=x>=0?1:-1,b,O;i==="insideStart"?(b=p+w*o,O=v):i==="insideEnd"?(b=y-w*o,O=!v):i==="end"&&(b=y+w*o,O=v),O=x<=0?O:!O;var m=Pe(c,f,d,b),g=Pe(c,f,d,b+(O?1:-1)*359),_="M".concat(m.x,",").concat(m.y,`
    A`).concat(d,",").concat(d,",0,1,").concat(O?0:1,`,
    `).concat(g.x,",").concat(g.y),S=ne(t.id)?va("recharts-radial-line-"):t.id;return T.createElement("text",Tn({},n,{dominantBaseline:"central",className:ie("recharts-radial-bar-label",u)}),T.createElement("defs",null,T.createElement("path",{id:S,d:_})),T.createElement("textPath",{xlinkHref:"#".concat(S)},r))},wM=function(t){var r=t.viewBox,n=t.offset,i=t.position,a=r,o=a.cx,u=a.cy,s=a.innerRadius,c=a.outerRadius,f=a.startAngle,l=a.endAngle,h=(f+l)/2;if(i==="outside"){var p=Pe(o,u,c+n,h),y=p.x,v=p.y;return{x:y,y:v,textAnchor:y>=o?"start":"end",verticalAnchor:"middle"}}if(i==="center")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"middle"};if(i==="centerTop")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"start"};if(i==="centerBottom")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"end"};var d=(s+c)/2,x=Pe(o,u,d,h),w=x.x,b=x.y;return{x:w,y:b,textAnchor:"middle",verticalAnchor:"middle"}},OM=function(t){var r=t.viewBox,n=t.parentViewBox,i=t.offset,a=t.position,o=r,u=o.x,s=o.y,c=o.width,f=o.height,l=f>=0?1:-1,h=l*i,p=l>0?"end":"start",y=l>0?"start":"end",v=c>=0?1:-1,d=v*i,x=v>0?"end":"start",w=v>0?"start":"end";if(a==="top"){var b={x:u+c/2,y:s-l*i,textAnchor:"middle",verticalAnchor:p};return be(be({},b),n?{height:Math.max(s-n.y,0),width:c}:{})}if(a==="bottom"){var O={x:u+c/2,y:s+f+h,textAnchor:"middle",verticalAnchor:y};return be(be({},O),n?{height:Math.max(n.y+n.height-(s+f),0),width:c}:{})}if(a==="left"){var m={x:u-d,y:s+f/2,textAnchor:x,verticalAnchor:"middle"};return be(be({},m),n?{width:Math.max(m.x-n.x,0),height:f}:{})}if(a==="right"){var g={x:u+c+d,y:s+f/2,textAnchor:w,verticalAnchor:"middle"};return be(be({},g),n?{width:Math.max(n.x+n.width-g.x,0),height:f}:{})}var _=n?{width:c,height:f}:{};return a==="insideLeft"?be({x:u+d,y:s+f/2,textAnchor:w,verticalAnchor:"middle"},_):a==="insideRight"?be({x:u+c-d,y:s+f/2,textAnchor:x,verticalAnchor:"middle"},_):a==="insideTop"?be({x:u+c/2,y:s+h,textAnchor:"middle",verticalAnchor:y},_):a==="insideBottom"?be({x:u+c/2,y:s+f-h,textAnchor:"middle",verticalAnchor:p},_):a==="insideTopLeft"?be({x:u+d,y:s+h,textAnchor:w,verticalAnchor:y},_):a==="insideTopRight"?be({x:u+c-d,y:s+h,textAnchor:x,verticalAnchor:y},_):a==="insideBottomLeft"?be({x:u+d,y:s+f-h,textAnchor:w,verticalAnchor:p},_):a==="insideBottomRight"?be({x:u+c-d,y:s+f-h,textAnchor:x,verticalAnchor:p},_):qr(a)&&(B(a.x)||qt(a.x))&&(B(a.y)||qt(a.y))?be({x:u+Gt(a.x,c),y:s+Gt(a.y,f),textAnchor:"end",verticalAnchor:"end"},_):be({x:u+c/2,y:s+f/2,textAnchor:"middle",verticalAnchor:"middle"},_)},_M=function(t){return"cx"in t&&B(t.cx)};function Ee(e){var t=e.offset,r=t===void 0?5:t,n=dM(e,uM),i=be({offset:r},n),a=i.viewBox,o=i.position,u=i.value,s=i.children,c=i.content,f=i.className,l=f===void 0?"":f,h=i.textBreakAll;if(!a||ne(u)&&ne(s)&&!q.isValidElement(c)&&!Z(c))return null;if(q.isValidElement(c))return q.cloneElement(c,i);var p;if(Z(c)){if(p=q.createElement(c,i),q.isValidElement(p))return p}else p=gM(i);var y=_M(a),v=ee(i,!0);if(y&&(o==="insideStart"||o==="insideEnd"||o==="end"))return xM(i,p,v);var d=y?wM(i):OM(i);return T.createElement(Ei,Tn({className:ie("recharts-label",l)},v,d,{breakAll:h}),p)}Ee.displayName="Label";var mx=function(t){var r=t.cx,n=t.cy,i=t.angle,a=t.startAngle,o=t.endAngle,u=t.r,s=t.radius,c=t.innerRadius,f=t.outerRadius,l=t.x,h=t.y,p=t.top,y=t.left,v=t.width,d=t.height,x=t.clockWise,w=t.labelViewBox;if(w)return w;if(B(v)&&B(d)){if(B(l)&&B(h))return{x:l,y:h,width:v,height:d};if(B(p)&&B(y))return{x:p,y,width:v,height:d}}return B(l)&&B(h)?{x:l,y:h,width:0,height:0}:B(r)&&B(n)?{cx:r,cy:n,startAngle:a||i||0,endAngle:o||i||0,innerRadius:c||0,outerRadius:f||s||u||0,clockWise:x}:t.viewBox?t.viewBox:{}},SM=function(t,r){return t?t===!0?T.createElement(Ee,{key:"label-implicit",viewBox:r}):xe(t)?T.createElement(Ee,{key:"label-implicit",viewBox:r,value:t}):q.isValidElement(t)?t.type===Ee?q.cloneElement(t,{key:"label-implicit",viewBox:r}):T.createElement(Ee,{key:"label-implicit",content:t,viewBox:r}):Z(t)?T.createElement(Ee,{key:"label-implicit",content:t,viewBox:r}):qr(t)?T.createElement(Ee,Tn({viewBox:r},t,{key:"label-implicit"})):null:null},AM=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!t||!t.children&&n&&!t.label)return null;var i=t.children,a=mx(t),o=Ze(i,Ee).map(function(s,c){return q.cloneElement(s,{viewBox:r||a,key:"label-".concat(c)})});if(!n)return o;var u=SM(t.label,r||a);return[u].concat(sM(o))};Ee.parseViewBox=mx;Ee.renderCallByParent=AM;var gc,bm;function PM(){if(bm)return gc;bm=1;function e(t){var r=t==null?0:t.length;return r?t[r-1]:void 0}return gc=e,gc}var TM=PM();const EM=se(TM);function En(e){"@babel/helpers - typeof";return En=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},En(e)}var jM=["valueAccessor"],MM=["data","dataKey","clockWise","id","textBreakAll"];function CM(e){return NM(e)||kM(e)||IM(e)||$M()}function $M(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function IM(e,t){if(e){if(typeof e=="string")return jl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return jl(e,t)}}function kM(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function NM(e){if(Array.isArray(e))return jl(e)}function jl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Wi(){return Wi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Wi.apply(this,arguments)}function xm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function wm(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?xm(Object(r),!0).forEach(function(n){RM(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):xm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function RM(e,t,r){return t=DM(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function DM(e){var t=qM(e,"string");return En(t)=="symbol"?t:t+""}function qM(e,t){if(En(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(En(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Om(e,t){if(e==null)return{};var r=BM(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function BM(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var LM=function(t){return Array.isArray(t.value)?EM(t.value):t.value};function dt(e){var t=e.valueAccessor,r=t===void 0?LM:t,n=Om(e,jM),i=n.data,a=n.dataKey,o=n.clockWise,u=n.id,s=n.textBreakAll,c=Om(n,MM);return!i||!i.length?null:T.createElement(_e,{className:"recharts-label-list"},i.map(function(f,l){var h=ne(a)?r(f,l):it(f&&f.payload,a),p=ne(u)?{}:{id:"".concat(u,"-").concat(l)};return T.createElement(Ee,Wi({},ee(f,!0),c,p,{parentViewBox:f.parentViewBox,value:h,textBreakAll:s,viewBox:Ee.parseViewBox(ne(o)?f:wm(wm({},f),{},{clockWise:o})),key:"label-".concat(l),index:l}))}))}dt.displayName="LabelList";function FM(e,t){return e?e===!0?T.createElement(dt,{key:"labelList-implicit",data:t}):T.isValidElement(e)||Z(e)?T.createElement(dt,{key:"labelList-implicit",data:t,content:e}):qr(e)?T.createElement(dt,Wi({data:t},e,{key:"labelList-implicit"})):null:null}function zM(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!e||!e.children&&r&&!e.label)return null;var n=e.children,i=Ze(n,dt).map(function(o,u){return q.cloneElement(o,{data:t,key:"labelList-".concat(u)})});if(!r)return i;var a=FM(e.label,t);return[a].concat(CM(i))}dt.renderCallByParent=zM;function jn(e){"@babel/helpers - typeof";return jn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},jn(e)}function Ml(){return Ml=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ml.apply(this,arguments)}function _m(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Sm(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?_m(Object(r),!0).forEach(function(n){UM(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_m(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function UM(e,t,r){return t=WM(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function WM(e){var t=HM(e,"string");return jn(t)=="symbol"?t:t+""}function HM(e,t){if(jn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(jn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var GM=function(t,r){var n=Ye(r-t),i=Math.min(Math.abs(r-t),359.999);return n*i},fi=function(t){var r=t.cx,n=t.cy,i=t.radius,a=t.angle,o=t.sign,u=t.isExternal,s=t.cornerRadius,c=t.cornerIsExternal,f=s*(u?1:-1)+i,l=Math.asin(s/f)/Ui,h=c?a:a+o*l,p=Pe(r,n,f,h),y=Pe(r,n,i,h),v=c?a-o*l:a,d=Pe(r,n,f*Math.cos(l*Ui),v);return{center:p,circleTangency:y,lineTangency:d,theta:l}},gx=function(t){var r=t.cx,n=t.cy,i=t.innerRadius,a=t.outerRadius,o=t.startAngle,u=t.endAngle,s=GM(o,u),c=o+s,f=Pe(r,n,a,o),l=Pe(r,n,a,c),h="M ".concat(f.x,",").concat(f.y,`
    A `).concat(a,",").concat(a,`,0,
    `).concat(+(Math.abs(s)>180),",").concat(+(o>c),`,
    `).concat(l.x,",").concat(l.y,`
  `);if(i>0){var p=Pe(r,n,i,o),y=Pe(r,n,i,c);h+="L ".concat(y.x,",").concat(y.y,`
            A `).concat(i,",").concat(i,`,0,
            `).concat(+(Math.abs(s)>180),",").concat(+(o<=c),`,
            `).concat(p.x,",").concat(p.y," Z")}else h+="L ".concat(r,",").concat(n," Z");return h},KM=function(t){var r=t.cx,n=t.cy,i=t.innerRadius,a=t.outerRadius,o=t.cornerRadius,u=t.forceCornerRadius,s=t.cornerIsExternal,c=t.startAngle,f=t.endAngle,l=Ye(f-c),h=fi({cx:r,cy:n,radius:a,angle:c,sign:l,cornerRadius:o,cornerIsExternal:s}),p=h.circleTangency,y=h.lineTangency,v=h.theta,d=fi({cx:r,cy:n,radius:a,angle:f,sign:-l,cornerRadius:o,cornerIsExternal:s}),x=d.circleTangency,w=d.lineTangency,b=d.theta,O=s?Math.abs(c-f):Math.abs(c-f)-v-b;if(O<0)return u?"M ".concat(y.x,",").concat(y.y,`
        a`).concat(o,",").concat(o,",0,0,1,").concat(o*2,`,0
        a`).concat(o,",").concat(o,",0,0,1,").concat(-o*2,`,0
      `):gx({cx:r,cy:n,innerRadius:i,outerRadius:a,startAngle:c,endAngle:f});var m="M ".concat(y.x,",").concat(y.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(p.x,",").concat(p.y,`
    A`).concat(a,",").concat(a,",0,").concat(+(O>180),",").concat(+(l<0),",").concat(x.x,",").concat(x.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(w.x,",").concat(w.y,`
  `);if(i>0){var g=fi({cx:r,cy:n,radius:i,angle:c,sign:l,isExternal:!0,cornerRadius:o,cornerIsExternal:s}),_=g.circleTangency,S=g.lineTangency,P=g.theta,C=fi({cx:r,cy:n,radius:i,angle:f,sign:-l,isExternal:!0,cornerRadius:o,cornerIsExternal:s}),A=C.circleTangency,E=C.lineTangency,j=C.theta,k=s?Math.abs(c-f):Math.abs(c-f)-P-j;if(k<0&&o===0)return"".concat(m,"L").concat(r,",").concat(n,"Z");m+="L".concat(E.x,",").concat(E.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(A.x,",").concat(A.y,`
      A`).concat(i,",").concat(i,",0,").concat(+(k>180),",").concat(+(l>0),",").concat(_.x,",").concat(_.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(S.x,",").concat(S.y,"Z")}else m+="L".concat(r,",").concat(n,"Z");return m},VM={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},bx=function(t){var r=Sm(Sm({},VM),t),n=r.cx,i=r.cy,a=r.innerRadius,o=r.outerRadius,u=r.cornerRadius,s=r.forceCornerRadius,c=r.cornerIsExternal,f=r.startAngle,l=r.endAngle,h=r.className;if(o<a||f===l)return null;var p=ie("recharts-sector",h),y=o-a,v=Gt(u,y,0,!0),d;return v>0&&Math.abs(f-l)<360?d=KM({cx:n,cy:i,innerRadius:a,outerRadius:o,cornerRadius:Math.min(v,y/2),forceCornerRadius:s,cornerIsExternal:c,startAngle:f,endAngle:l}):d=gx({cx:n,cy:i,innerRadius:a,outerRadius:o,startAngle:f,endAngle:l}),T.createElement("path",Ml({},ee(r,!0),{className:p,d,role:"img"}))};function Mn(e){"@babel/helpers - typeof";return Mn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Mn(e)}function Cl(){return Cl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Cl.apply(this,arguments)}function Am(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Pm(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Am(Object(r),!0).forEach(function(n){XM(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Am(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function XM(e,t,r){return t=YM(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function YM(e){var t=ZM(e,"string");return Mn(t)=="symbol"?t:t+""}function ZM(e,t){if(Mn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Mn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Tm={curveBasisClosed:mO,curveBasisOpen:gO,curveBasis:yO,curveBumpX:rO,curveBumpY:nO,curveLinearClosed:bO,curveLinear:ga,curveMonotoneX:xO,curveMonotoneY:wO,curveNatural:OO,curveStep:_O,curveStepAfter:AO,curveStepBefore:SO},hi=function(t){return t.x===+t.x&&t.y===+t.y},Xr=function(t){return t.x},Yr=function(t){return t.y},JM=function(t,r){if(Z(t))return t;var n="curve".concat(ya(t));return(n==="curveMonotone"||n==="curveBump")&&r?Tm["".concat(n).concat(r==="vertical"?"Y":"X")]:Tm[n]||ga},QM=function(t){var r=t.type,n=r===void 0?"linear":r,i=t.points,a=i===void 0?[]:i,o=t.baseLine,u=t.layout,s=t.connectNulls,c=s===void 0?!1:s,f=JM(n,u),l=c?a.filter(function(v){return hi(v)}):a,h;if(Array.isArray(o)){var p=c?o.filter(function(v){return hi(v)}):o,y=l.map(function(v,d){return Pm(Pm({},v),{},{base:p[d]})});return u==="vertical"?h=ni().y(Yr).x1(Xr).x0(function(v){return v.base.x}):h=ni().x(Xr).y1(Yr).y0(function(v){return v.base.y}),h.defined(hi).curve(f),h(y)}return u==="vertical"&&B(o)?h=ni().y(Yr).x1(Xr).x0(o):B(o)?h=ni().x(Xr).y1(Yr).y0(o):h=_b().x(Xr).y(Yr),h.defined(hi).curve(f),h(l)},Em=function(t){var r=t.className,n=t.points,i=t.path,a=t.pathRef;if((!n||!n.length)&&!i)return null;var o=n&&n.length?QM(t):i;return T.createElement("path",Cl({},ee(t,!1),yi(t),{className:ie("recharts-curve",r),d:o,ref:a}))},bc={exports:{}},xc,jm;function eC(){if(jm)return xc;jm=1;var e="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";return xc=e,xc}var wc,Mm;function tC(){if(Mm)return wc;Mm=1;var e=eC();function t(){}function r(){}return r.resetWarningCache=t,wc=function(){function n(o,u,s,c,f,l){if(l!==e){var h=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw h.name="Invariant Violation",h}}n.isRequired=n;function i(){return n}var a={array:n,bigint:n,bool:n,func:n,number:n,object:n,string:n,symbol:n,any:n,arrayOf:i,element:n,elementType:n,instanceOf:i,node:n,objectOf:i,oneOf:i,oneOfType:i,shape:i,exact:i,checkPropTypes:r,resetWarningCache:t};return a.PropTypes=a,a},wc}var Cm;function rC(){return Cm||(Cm=1,bc.exports=tC()()),bc.exports}var nC=rC();const re=se(nC);var iC=Object.getOwnPropertyNames,aC=Object.getOwnPropertySymbols,oC=Object.prototype.hasOwnProperty;function $m(e,t){return function(n,i,a){return e(n,i,a)&&t(n,i,a)}}function di(e){return function(r,n,i){if(!r||!n||typeof r!="object"||typeof n!="object")return e(r,n,i);var a=i.cache,o=a.get(r),u=a.get(n);if(o&&u)return o===n&&u===r;a.set(r,n),a.set(n,r);var s=e(r,n,i);return a.delete(r),a.delete(n),s}}function Im(e){return iC(e).concat(aC(e))}var uC=Object.hasOwn||function(e,t){return oC.call(e,t)};function Jt(e,t){return e===t||!e&&!t&&e!==e&&t!==t}var sC="__v",cC="__o",lC="_owner",km=Object.getOwnPropertyDescriptor,Nm=Object.keys;function fC(e,t,r){var n=e.length;if(t.length!==n)return!1;for(;n-- >0;)if(!r.equals(e[n],t[n],n,n,e,t,r))return!1;return!0}function hC(e,t){return Jt(e.getTime(),t.getTime())}function dC(e,t){return e.name===t.name&&e.message===t.message&&e.cause===t.cause&&e.stack===t.stack}function pC(e,t){return e===t}function Rm(e,t,r){var n=e.size;if(n!==t.size)return!1;if(!n)return!0;for(var i=new Array(n),a=e.entries(),o,u,s=0;(o=a.next())&&!o.done;){for(var c=t.entries(),f=!1,l=0;(u=c.next())&&!u.done;){if(i[l]){l++;continue}var h=o.value,p=u.value;if(r.equals(h[0],p[0],s,l,e,t,r)&&r.equals(h[1],p[1],h[0],p[0],e,t,r)){f=i[l]=!0;break}l++}if(!f)return!1;s++}return!0}var vC=Jt;function yC(e,t,r){var n=Nm(e),i=n.length;if(Nm(t).length!==i)return!1;for(;i-- >0;)if(!xx(e,t,r,n[i]))return!1;return!0}function Zr(e,t,r){var n=Im(e),i=n.length;if(Im(t).length!==i)return!1;for(var a,o,u;i-- >0;)if(a=n[i],!xx(e,t,r,a)||(o=km(e,a),u=km(t,a),(o||u)&&(!o||!u||o.configurable!==u.configurable||o.enumerable!==u.enumerable||o.writable!==u.writable)))return!1;return!0}function mC(e,t){return Jt(e.valueOf(),t.valueOf())}function gC(e,t){return e.source===t.source&&e.flags===t.flags}function Dm(e,t,r){var n=e.size;if(n!==t.size)return!1;if(!n)return!0;for(var i=new Array(n),a=e.values(),o,u;(o=a.next())&&!o.done;){for(var s=t.values(),c=!1,f=0;(u=s.next())&&!u.done;){if(!i[f]&&r.equals(o.value,u.value,o.value,u.value,e,t,r)){c=i[f]=!0;break}f++}if(!c)return!1}return!0}function bC(e,t){var r=e.length;if(t.length!==r)return!1;for(;r-- >0;)if(e[r]!==t[r])return!1;return!0}function xC(e,t){return e.hostname===t.hostname&&e.pathname===t.pathname&&e.protocol===t.protocol&&e.port===t.port&&e.hash===t.hash&&e.username===t.username&&e.password===t.password}function xx(e,t,r,n){return(n===lC||n===cC||n===sC)&&(e.$$typeof||t.$$typeof)?!0:uC(t,n)&&r.equals(e[n],t[n],n,n,e,t,r)}var wC="[object Arguments]",OC="[object Boolean]",_C="[object Date]",SC="[object Error]",AC="[object Map]",PC="[object Number]",TC="[object Object]",EC="[object RegExp]",jC="[object Set]",MC="[object String]",CC="[object URL]",$C=Array.isArray,qm=typeof ArrayBuffer=="function"&&ArrayBuffer.isView?ArrayBuffer.isView:null,Bm=Object.assign,IC=Object.prototype.toString.call.bind(Object.prototype.toString);function kC(e){var t=e.areArraysEqual,r=e.areDatesEqual,n=e.areErrorsEqual,i=e.areFunctionsEqual,a=e.areMapsEqual,o=e.areNumbersEqual,u=e.areObjectsEqual,s=e.arePrimitiveWrappersEqual,c=e.areRegExpsEqual,f=e.areSetsEqual,l=e.areTypedArraysEqual,h=e.areUrlsEqual;return function(y,v,d){if(y===v)return!0;if(y==null||v==null)return!1;var x=typeof y;if(x!==typeof v)return!1;if(x!=="object")return x==="number"?o(y,v,d):x==="function"?i(y,v,d):!1;var w=y.constructor;if(w!==v.constructor)return!1;if(w===Object)return u(y,v,d);if($C(y))return t(y,v,d);if(qm!=null&&qm(y))return l(y,v,d);if(w===Date)return r(y,v,d);if(w===RegExp)return c(y,v,d);if(w===Map)return a(y,v,d);if(w===Set)return f(y,v,d);var b=IC(y);return b===_C?r(y,v,d):b===EC?c(y,v,d):b===AC?a(y,v,d):b===jC?f(y,v,d):b===TC?typeof y.then!="function"&&typeof v.then!="function"&&u(y,v,d):b===CC?h(y,v,d):b===SC?n(y,v,d):b===wC?u(y,v,d):b===OC||b===PC||b===MC?s(y,v,d):!1}}function NC(e){var t=e.circular,r=e.createCustomConfig,n=e.strict,i={areArraysEqual:n?Zr:fC,areDatesEqual:hC,areErrorsEqual:dC,areFunctionsEqual:pC,areMapsEqual:n?$m(Rm,Zr):Rm,areNumbersEqual:vC,areObjectsEqual:n?Zr:yC,arePrimitiveWrappersEqual:mC,areRegExpsEqual:gC,areSetsEqual:n?$m(Dm,Zr):Dm,areTypedArraysEqual:n?Zr:bC,areUrlsEqual:xC};if(r&&(i=Bm({},i,r(i))),t){var a=di(i.areArraysEqual),o=di(i.areMapsEqual),u=di(i.areObjectsEqual),s=di(i.areSetsEqual);i=Bm({},i,{areArraysEqual:a,areMapsEqual:o,areObjectsEqual:u,areSetsEqual:s})}return i}function RC(e){return function(t,r,n,i,a,o,u){return e(t,r,u)}}function DC(e){var t=e.circular,r=e.comparator,n=e.createState,i=e.equals,a=e.strict;if(n)return function(s,c){var f=n(),l=f.cache,h=l===void 0?t?new WeakMap:void 0:l,p=f.meta;return r(s,c,{cache:h,equals:i,meta:p,strict:a})};if(t)return function(s,c){return r(s,c,{cache:new WeakMap,equals:i,meta:void 0,strict:a})};var o={cache:void 0,equals:i,meta:void 0,strict:a};return function(s,c){return r(s,c,o)}}var qC=Mt();Mt({strict:!0});Mt({circular:!0});Mt({circular:!0,strict:!0});Mt({createInternalComparator:function(){return Jt}});Mt({strict:!0,createInternalComparator:function(){return Jt}});Mt({circular:!0,createInternalComparator:function(){return Jt}});Mt({circular:!0,createInternalComparator:function(){return Jt},strict:!0});function Mt(e){e===void 0&&(e={});var t=e.circular,r=t===void 0?!1:t,n=e.createInternalComparator,i=e.createState,a=e.strict,o=a===void 0?!1:a,u=NC(e),s=kC(u),c=n?n(s):RC(s);return DC({circular:r,comparator:s,createState:i,equals:c,strict:o})}function BC(e){typeof requestAnimationFrame<"u"&&requestAnimationFrame(e)}function Lm(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,r=-1,n=function i(a){r<0&&(r=a),a-r>t?(e(a),r=-1):BC(i)};requestAnimationFrame(n)}function $l(e){"@babel/helpers - typeof";return $l=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},$l(e)}function LC(e){return WC(e)||UC(e)||zC(e)||FC()}function FC(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function zC(e,t){if(e){if(typeof e=="string")return Fm(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Fm(e,t)}}function Fm(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function UC(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function WC(e){if(Array.isArray(e))return e}function HC(){var e={},t=function(){return null},r=!1,n=function i(a){if(!r){if(Array.isArray(a)){if(!a.length)return;var o=a,u=LC(o),s=u[0],c=u.slice(1);if(typeof s=="number"){Lm(i.bind(null,c),s);return}i(s),Lm(i.bind(null,c));return}$l(a)==="object"&&(e=a,t(e)),typeof a=="function"&&a()}};return{stop:function(){r=!0},start:function(a){r=!1,n(a)},subscribe:function(a){return t=a,function(){t=function(){return null}}}}}function Cn(e){"@babel/helpers - typeof";return Cn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Cn(e)}function zm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Um(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?zm(Object(r),!0).forEach(function(n){wx(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):zm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function wx(e,t,r){return t=GC(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function GC(e){var t=KC(e,"string");return Cn(t)==="symbol"?t:String(t)}function KC(e,t){if(Cn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Cn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var VC=function(t,r){return[Object.keys(t),Object.keys(r)].reduce(function(n,i){return n.filter(function(a){return i.includes(a)})})},XC=function(t){return t},YC=function(t){return t.replace(/([A-Z])/g,function(r){return"-".concat(r.toLowerCase())})},sn=function(t,r){return Object.keys(r).reduce(function(n,i){return Um(Um({},n),{},wx({},i,t(i,r[i])))},{})},Wm=function(t,r,n){return t.map(function(i){return"".concat(YC(i)," ").concat(r,"ms ").concat(n)}).join(",")};function ZC(e,t){return e$(e)||QC(e,t)||Ox(e,t)||JC()}function JC(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function QC(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],s=!0,c=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);s=!0);}catch(f){c=!0,i=f}finally{try{if(!s&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return u}}function e$(e){if(Array.isArray(e))return e}function t$(e){return i$(e)||n$(e)||Ox(e)||r$()}function r$(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ox(e,t){if(e){if(typeof e=="string")return Il(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Il(e,t)}}function n$(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function i$(e){if(Array.isArray(e))return Il(e)}function Il(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Hi=1e-4,_x=function(t,r){return[0,3*t,3*r-6*t,3*t-3*r+1]},Sx=function(t,r){return t.map(function(n,i){return n*Math.pow(r,i)}).reduce(function(n,i){return n+i})},Hm=function(t,r){return function(n){var i=_x(t,r);return Sx(i,n)}},a$=function(t,r){return function(n){var i=_x(t,r),a=[].concat(t$(i.map(function(o,u){return o*u}).slice(1)),[0]);return Sx(a,n)}},Gm=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r[0],a=r[1],o=r[2],u=r[3];if(r.length===1)switch(r[0]){case"linear":i=0,a=0,o=1,u=1;break;case"ease":i=.25,a=.1,o=.25,u=1;break;case"ease-in":i=.42,a=0,o=1,u=1;break;case"ease-out":i=.42,a=0,o=.58,u=1;break;case"ease-in-out":i=0,a=0,o=.58,u=1;break;default:{var s=r[0].split("(");if(s[0]==="cubic-bezier"&&s[1].split(")")[0].split(",").length===4){var c=s[1].split(")")[0].split(",").map(function(d){return parseFloat(d)}),f=ZC(c,4);i=f[0],a=f[1],o=f[2],u=f[3]}}}var l=Hm(i,o),h=Hm(a,u),p=a$(i,o),y=function(x){return x>1?1:x<0?0:x},v=function(x){for(var w=x>1?1:x,b=w,O=0;O<8;++O){var m=l(b)-w,g=p(b);if(Math.abs(m-w)<Hi||g<Hi)return h(b);b=y(b-m/g)}return h(b)};return v.isStepper=!1,v},o$=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=t.stiff,n=r===void 0?100:r,i=t.damping,a=i===void 0?8:i,o=t.dt,u=o===void 0?17:o,s=function(f,l,h){var p=-(f-l)*n,y=h*a,v=h+(p-y)*u/1e3,d=h*u/1e3+f;return Math.abs(d-l)<Hi&&Math.abs(v)<Hi?[l,0]:[d,v]};return s.isStepper=!0,s.dt=u,s},u$=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r[0];if(typeof i=="string")switch(i){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return Gm(i);case"spring":return o$();default:if(i.split("(")[0]==="cubic-bezier")return Gm(i)}return typeof i=="function"?i:null};function $n(e){"@babel/helpers - typeof";return $n=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},$n(e)}function Km(e){return l$(e)||c$(e)||Ax(e)||s$()}function s$(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function c$(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function l$(e){if(Array.isArray(e))return Nl(e)}function Vm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ae(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Vm(Object(r),!0).forEach(function(n){kl(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Vm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function kl(e,t,r){return t=f$(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function f$(e){var t=h$(e,"string");return $n(t)==="symbol"?t:String(t)}function h$(e,t){if($n(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if($n(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function d$(e,t){return y$(e)||v$(e,t)||Ax(e,t)||p$()}function p$(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ax(e,t){if(e){if(typeof e=="string")return Nl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Nl(e,t)}}function Nl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function v$(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],s=!0,c=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);s=!0);}catch(f){c=!0,i=f}finally{try{if(!s&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return u}}function y$(e){if(Array.isArray(e))return e}var Gi=function(t,r,n){return t+(r-t)*n},Rl=function(t){var r=t.from,n=t.to;return r!==n},m$=function e(t,r,n){var i=sn(function(a,o){if(Rl(o)){var u=t(o.from,o.to,o.velocity),s=d$(u,2),c=s[0],f=s[1];return Ae(Ae({},o),{},{from:c,velocity:f})}return o},r);return n<1?sn(function(a,o){return Rl(o)?Ae(Ae({},o),{},{velocity:Gi(o.velocity,i[a].velocity,n),from:Gi(o.from,i[a].from,n)}):o},r):e(t,i,n-1)};const g$=function(e,t,r,n,i){var a=VC(e,t),o=a.reduce(function(d,x){return Ae(Ae({},d),{},kl({},x,[e[x],t[x]]))},{}),u=a.reduce(function(d,x){return Ae(Ae({},d),{},kl({},x,{from:e[x],velocity:0,to:t[x]}))},{}),s=-1,c,f,l=function(){return null},h=function(){return sn(function(x,w){return w.from},u)},p=function(){return!Object.values(u).filter(Rl).length},y=function(x){c||(c=x);var w=x-c,b=w/r.dt;u=m$(r,u,b),i(Ae(Ae(Ae({},e),t),h())),c=x,p()||(s=requestAnimationFrame(l))},v=function(x){f||(f=x);var w=(x-f)/n,b=sn(function(m,g){return Gi.apply(void 0,Km(g).concat([r(w)]))},o);if(i(Ae(Ae(Ae({},e),t),b)),w<1)s=requestAnimationFrame(l);else{var O=sn(function(m,g){return Gi.apply(void 0,Km(g).concat([r(1)]))},o);i(Ae(Ae(Ae({},e),t),O))}};return l=r.isStepper?y:v,function(){return requestAnimationFrame(l),function(){cancelAnimationFrame(s)}}};function Ar(e){"@babel/helpers - typeof";return Ar=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ar(e)}var b$=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function x$(e,t){if(e==null)return{};var r=w$(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function w$(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,a;for(a=0;a<n.length;a++)i=n[a],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}function Oc(e){return A$(e)||S$(e)||_$(e)||O$()}function O$(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function _$(e,t){if(e){if(typeof e=="string")return Dl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Dl(e,t)}}function S$(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function A$(e){if(Array.isArray(e))return Dl(e)}function Dl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Xm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ke(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Xm(Object(r),!0).forEach(function(n){rn(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Xm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function rn(e,t,r){return t=Px(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function P$(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function T$(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Px(n.key),n)}}function E$(e,t,r){return t&&T$(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function Px(e){var t=j$(e,"string");return Ar(t)==="symbol"?t:String(t)}function j$(e,t){if(Ar(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Ar(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function M$(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ql(e,t)}function ql(e,t){return ql=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},ql(e,t)}function C$(e){var t=$$();return function(){var n=Ki(e),i;if(t){var a=Ki(this).constructor;i=Reflect.construct(n,arguments,a)}else i=n.apply(this,arguments);return Bl(this,i)}}function Bl(e,t){if(t&&(Ar(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Ll(e)}function Ll(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function $$(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function Ki(e){return Ki=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ki(e)}var At=function(e){M$(r,e);var t=C$(r);function r(n,i){var a;P$(this,r),a=t.call(this,n,i);var o=a.props,u=o.isActive,s=o.attributeName,c=o.from,f=o.to,l=o.steps,h=o.children,p=o.duration;if(a.handleStyleChange=a.handleStyleChange.bind(Ll(a)),a.changeStyle=a.changeStyle.bind(Ll(a)),!u||p<=0)return a.state={style:{}},typeof h=="function"&&(a.state={style:f}),Bl(a);if(l&&l.length)a.state={style:l[0].style};else if(c){if(typeof h=="function")return a.state={style:c},Bl(a);a.state={style:s?rn({},s,c):c}}else a.state={style:{}};return a}return E$(r,[{key:"componentDidMount",value:function(){var i=this.props,a=i.isActive,o=i.canBegin;this.mounted=!0,!(!a||!o)&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(i){var a=this.props,o=a.isActive,u=a.canBegin,s=a.attributeName,c=a.shouldReAnimate,f=a.to,l=a.from,h=this.state.style;if(u){if(!o){var p={style:s?rn({},s,f):f};this.state&&h&&(s&&h[s]!==f||!s&&h!==f)&&this.setState(p);return}if(!(qC(i.to,f)&&i.canBegin&&i.isActive)){var y=!i.canBegin||!i.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var v=y||c?l:i.to;if(this.state&&h){var d={style:s?rn({},s,v):v};(s&&h[s]!==v||!s&&h!==v)&&this.setState(d)}this.runAnimation(Ke(Ke({},this.props),{},{from:v,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var i=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),i&&i()}},{key:"handleStyleChange",value:function(i){this.changeStyle(i)}},{key:"changeStyle",value:function(i){this.mounted&&this.setState({style:i})}},{key:"runJSAnimation",value:function(i){var a=this,o=i.from,u=i.to,s=i.duration,c=i.easing,f=i.begin,l=i.onAnimationEnd,h=i.onAnimationStart,p=g$(o,u,u$(c),s,this.changeStyle),y=function(){a.stopJSAnimation=p()};this.manager.start([h,f,y,s,l])}},{key:"runStepAnimation",value:function(i){var a=this,o=i.steps,u=i.begin,s=i.onAnimationStart,c=o[0],f=c.style,l=c.duration,h=l===void 0?0:l,p=function(v,d,x){if(x===0)return v;var w=d.duration,b=d.easing,O=b===void 0?"ease":b,m=d.style,g=d.properties,_=d.onAnimationEnd,S=x>0?o[x-1]:d,P=g||Object.keys(m);if(typeof O=="function"||O==="spring")return[].concat(Oc(v),[a.runJSAnimation.bind(a,{from:S.style,to:m,duration:w,easing:O}),w]);var C=Wm(P,w,O),A=Ke(Ke(Ke({},S.style),m),{},{transition:C});return[].concat(Oc(v),[A,w,_]).filter(XC)};return this.manager.start([s].concat(Oc(o.reduce(p,[f,Math.max(h,u)])),[i.onAnimationEnd]))}},{key:"runAnimation",value:function(i){this.manager||(this.manager=HC());var a=i.begin,o=i.duration,u=i.attributeName,s=i.to,c=i.easing,f=i.onAnimationStart,l=i.onAnimationEnd,h=i.steps,p=i.children,y=this.manager;if(this.unSubscribe=y.subscribe(this.handleStyleChange),typeof c=="function"||typeof p=="function"||c==="spring"){this.runJSAnimation(i);return}if(h.length>1){this.runStepAnimation(i);return}var v=u?rn({},u,s):s,d=Wm(Object.keys(v),o,c);y.start([f,a,Ke(Ke({},v),{},{transition:d}),o,l])}},{key:"render",value:function(){var i=this.props,a=i.children;i.begin;var o=i.duration;i.attributeName,i.easing;var u=i.isActive;i.steps,i.from,i.to,i.canBegin,i.onAnimationEnd,i.shouldReAnimate,i.onAnimationReStart;var s=x$(i,b$),c=q.Children.count(a),f=this.state.style;if(typeof a=="function")return a(f);if(!u||c===0||o<=0)return a;var l=function(p){var y=p.props,v=y.style,d=v===void 0?{}:v,x=y.className,w=q.cloneElement(p,Ke(Ke({},s),{},{style:Ke(Ke({},d),f),className:x}));return w};return c===1?l(q.Children.only(a)):T.createElement("div",null,q.Children.map(a,function(h){return l(h)}))}}]),r}(q.PureComponent);At.displayName="Animate";At.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}};At.propTypes={from:re.oneOfType([re.object,re.string]),to:re.oneOfType([re.object,re.string]),attributeName:re.string,duration:re.number,begin:re.number,easing:re.oneOfType([re.string,re.func]),steps:re.arrayOf(re.shape({duration:re.number.isRequired,style:re.object.isRequired,easing:re.oneOfType([re.oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),re.func]),properties:re.arrayOf("string"),onAnimationEnd:re.func})),children:re.oneOfType([re.node,re.func]),isActive:re.bool,canBegin:re.bool,onAnimationEnd:re.func,shouldReAnimate:re.bool,onAnimationStart:re.func,onAnimationReStart:re.func};function In(e){"@babel/helpers - typeof";return In=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},In(e)}function Vi(){return Vi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Vi.apply(this,arguments)}function I$(e,t){return D$(e)||R$(e,t)||N$(e,t)||k$()}function k$(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function N$(e,t){if(e){if(typeof e=="string")return Ym(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ym(e,t)}}function Ym(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function R$(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],s=!0,c=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);s=!0);}catch(f){c=!0,i=f}finally{try{if(!s&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return u}}function D$(e){if(Array.isArray(e))return e}function Zm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Jm(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Zm(Object(r),!0).forEach(function(n){q$(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Zm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function q$(e,t,r){return t=B$(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function B$(e){var t=L$(e,"string");return In(t)=="symbol"?t:t+""}function L$(e,t){if(In(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(In(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Qm=function(t,r,n,i,a){var o=Math.min(Math.abs(n)/2,Math.abs(i)/2),u=i>=0?1:-1,s=n>=0?1:-1,c=i>=0&&n>=0||i<0&&n<0?1:0,f;if(o>0&&a instanceof Array){for(var l=[0,0,0,0],h=0,p=4;h<p;h++)l[h]=a[h]>o?o:a[h];f="M".concat(t,",").concat(r+u*l[0]),l[0]>0&&(f+="A ".concat(l[0],",").concat(l[0],",0,0,").concat(c,",").concat(t+s*l[0],",").concat(r)),f+="L ".concat(t+n-s*l[1],",").concat(r),l[1]>0&&(f+="A ".concat(l[1],",").concat(l[1],",0,0,").concat(c,`,
        `).concat(t+n,",").concat(r+u*l[1])),f+="L ".concat(t+n,",").concat(r+i-u*l[2]),l[2]>0&&(f+="A ".concat(l[2],",").concat(l[2],",0,0,").concat(c,`,
        `).concat(t+n-s*l[2],",").concat(r+i)),f+="L ".concat(t+s*l[3],",").concat(r+i),l[3]>0&&(f+="A ".concat(l[3],",").concat(l[3],",0,0,").concat(c,`,
        `).concat(t,",").concat(r+i-u*l[3])),f+="Z"}else if(o>0&&a===+a&&a>0){var y=Math.min(o,a);f="M ".concat(t,",").concat(r+u*y,`
            A `).concat(y,",").concat(y,",0,0,").concat(c,",").concat(t+s*y,",").concat(r,`
            L `).concat(t+n-s*y,",").concat(r,`
            A `).concat(y,",").concat(y,",0,0,").concat(c,",").concat(t+n,",").concat(r+u*y,`
            L `).concat(t+n,",").concat(r+i-u*y,`
            A `).concat(y,",").concat(y,",0,0,").concat(c,",").concat(t+n-s*y,",").concat(r+i,`
            L `).concat(t+s*y,",").concat(r+i,`
            A `).concat(y,",").concat(y,",0,0,").concat(c,",").concat(t,",").concat(r+i-u*y," Z")}else f="M ".concat(t,",").concat(r," h ").concat(n," v ").concat(i," h ").concat(-n," Z");return f},F$=function(t,r){if(!t||!r)return!1;var n=t.x,i=t.y,a=r.x,o=r.y,u=r.width,s=r.height;if(Math.abs(u)>0&&Math.abs(s)>0){var c=Math.min(a,a+u),f=Math.max(a,a+u),l=Math.min(o,o+s),h=Math.max(o,o+s);return n>=c&&n<=f&&i>=l&&i<=h}return!1},z$={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},ih=function(t){var r=Jm(Jm({},z$),t),n=q.useRef(),i=q.useState(-1),a=I$(i,2),o=a[0],u=a[1];q.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var O=n.current.getTotalLength();O&&u(O)}catch{}},[]);var s=r.x,c=r.y,f=r.width,l=r.height,h=r.radius,p=r.className,y=r.animationEasing,v=r.animationDuration,d=r.animationBegin,x=r.isAnimationActive,w=r.isUpdateAnimationActive;if(s!==+s||c!==+c||f!==+f||l!==+l||f===0||l===0)return null;var b=ie("recharts-rectangle",p);return w?T.createElement(At,{canBegin:o>0,from:{width:f,height:l,x:s,y:c},to:{width:f,height:l,x:s,y:c},duration:v,animationEasing:y,isActive:w},function(O){var m=O.width,g=O.height,_=O.x,S=O.y;return T.createElement(At,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:d,duration:v,isActive:x,easing:y},T.createElement("path",Vi({},ee(r,!0),{className:b,d:Qm(_,S,m,g,h),ref:n})))}):T.createElement("path",Vi({},ee(r,!0),{className:b,d:Qm(s,c,f,l,h)}))};function Fl(){return Fl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Fl.apply(this,arguments)}var Tx=function(t){var r=t.cx,n=t.cy,i=t.r,a=t.className,o=ie("recharts-dot",a);return r===+r&&n===+n&&i===+i?T.createElement("circle",Fl({},ee(t,!1),yi(t),{className:o,cx:r,cy:n,r:i})):null};function kn(e){"@babel/helpers - typeof";return kn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},kn(e)}var U$=["x","y","top","left","width","height","className"];function zl(){return zl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},zl.apply(this,arguments)}function eg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function W$(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?eg(Object(r),!0).forEach(function(n){H$(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function H$(e,t,r){return t=G$(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function G$(e){var t=K$(e,"string");return kn(t)=="symbol"?t:t+""}function K$(e,t){if(kn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(kn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function V$(e,t){if(e==null)return{};var r=X$(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function X$(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var Y$=function(t,r,n,i,a,o){return"M".concat(t,",").concat(a,"v").concat(i,"M").concat(o,",").concat(r,"h").concat(n)},Z$=function(t){var r=t.x,n=r===void 0?0:r,i=t.y,a=i===void 0?0:i,o=t.top,u=o===void 0?0:o,s=t.left,c=s===void 0?0:s,f=t.width,l=f===void 0?0:f,h=t.height,p=h===void 0?0:h,y=t.className,v=V$(t,U$),d=W$({x:n,y:a,top:u,left:c,width:l,height:p},v);return!B(n)||!B(a)||!B(l)||!B(p)||!B(u)||!B(c)?null:T.createElement("path",zl({},ee(d,!0),{className:ie("recharts-cross",y),d:Y$(n,a,l,p,u,c)}))},_c,tg;function J$(){if(tg)return _c;tg=1;var e=Gb(),t=e(Object.getPrototypeOf,Object);return _c=t,_c}var Sc,rg;function Q$(){if(rg)return Sc;rg=1;var e=mt(),t=J$(),r=gt(),n="[object Object]",i=Function.prototype,a=Object.prototype,o=i.toString,u=a.hasOwnProperty,s=o.call(Object);function c(f){if(!r(f)||e(f)!=n)return!1;var l=t(f);if(l===null)return!0;var h=u.call(l,"constructor")&&l.constructor;return typeof h=="function"&&h instanceof h&&o.call(h)==s}return Sc=c,Sc}var eI=Q$();const tI=se(eI);var Ac,ng;function rI(){if(ng)return Ac;ng=1;var e=mt(),t=gt(),r="[object Boolean]";function n(i){return i===!0||i===!1||t(i)&&e(i)==r}return Ac=n,Ac}var nI=rI();const iI=se(nI);function Nn(e){"@babel/helpers - typeof";return Nn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Nn(e)}function Xi(){return Xi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Xi.apply(this,arguments)}function aI(e,t){return cI(e)||sI(e,t)||uI(e,t)||oI()}function oI(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function uI(e,t){if(e){if(typeof e=="string")return ig(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ig(e,t)}}function ig(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function sI(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],s=!0,c=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);s=!0);}catch(f){c=!0,i=f}finally{try{if(!s&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return u}}function cI(e){if(Array.isArray(e))return e}function ag(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function og(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ag(Object(r),!0).forEach(function(n){lI(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ag(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function lI(e,t,r){return t=fI(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function fI(e){var t=hI(e,"string");return Nn(t)=="symbol"?t:t+""}function hI(e,t){if(Nn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Nn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var ug=function(t,r,n,i,a){var o=n-i,u;return u="M ".concat(t,",").concat(r),u+="L ".concat(t+n,",").concat(r),u+="L ".concat(t+n-o/2,",").concat(r+a),u+="L ".concat(t+n-o/2-i,",").concat(r+a),u+="L ".concat(t,",").concat(r," Z"),u},dI={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},pI=function(t){var r=og(og({},dI),t),n=q.useRef(),i=q.useState(-1),a=aI(i,2),o=a[0],u=a[1];q.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var b=n.current.getTotalLength();b&&u(b)}catch{}},[]);var s=r.x,c=r.y,f=r.upperWidth,l=r.lowerWidth,h=r.height,p=r.className,y=r.animationEasing,v=r.animationDuration,d=r.animationBegin,x=r.isUpdateAnimationActive;if(s!==+s||c!==+c||f!==+f||l!==+l||h!==+h||f===0&&l===0||h===0)return null;var w=ie("recharts-trapezoid",p);return x?T.createElement(At,{canBegin:o>0,from:{upperWidth:0,lowerWidth:0,height:h,x:s,y:c},to:{upperWidth:f,lowerWidth:l,height:h,x:s,y:c},duration:v,animationEasing:y,isActive:x},function(b){var O=b.upperWidth,m=b.lowerWidth,g=b.height,_=b.x,S=b.y;return T.createElement(At,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:d,duration:v,easing:y},T.createElement("path",Xi({},ee(r,!0),{className:w,d:ug(_,S,O,m,g),ref:n})))}):T.createElement("g",null,T.createElement("path",Xi({},ee(r,!0),{className:w,d:ug(s,c,f,l,h)})))},vI=["option","shapeType","propTransformer","activeClassName","isActive"];function Rn(e){"@babel/helpers - typeof";return Rn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Rn(e)}function yI(e,t){if(e==null)return{};var r=mI(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function mI(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function sg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Yi(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?sg(Object(r),!0).forEach(function(n){gI(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function gI(e,t,r){return t=bI(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function bI(e){var t=xI(e,"string");return Rn(t)=="symbol"?t:t+""}function xI(e,t){if(Rn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Rn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function wI(e,t){return Yi(Yi({},t),e)}function OI(e,t){return e==="symbols"}function cg(e){var t=e.shapeType,r=e.elementProps;switch(t){case"rectangle":return T.createElement(ih,r);case"trapezoid":return T.createElement(pI,r);case"sector":return T.createElement(bx,r);case"symbols":if(OI(t))return T.createElement(Of,r);break;default:return null}}function _I(e){return q.isValidElement(e)?e.props:e}function SI(e){var t=e.option,r=e.shapeType,n=e.propTransformer,i=n===void 0?wI:n,a=e.activeClassName,o=a===void 0?"recharts-active-shape":a,u=e.isActive,s=yI(e,vI),c;if(q.isValidElement(t))c=q.cloneElement(t,Yi(Yi({},s),_I(t)));else if(Z(t))c=t(s);else if(tI(t)&&!iI(t)){var f=i(t,s);c=T.createElement(cg,{shapeType:r,elementProps:f})}else{var l=s;c=T.createElement(cg,{shapeType:r,elementProps:l})}return u?T.createElement(_e,{className:o},c):c}function ka(e,t){return t!=null&&"trapezoids"in e.props}function Na(e,t){return t!=null&&"sectors"in e.props}function Dn(e,t){return t!=null&&"points"in e.props}function AI(e,t){var r,n,i=e.x===(t==null||(r=t.labelViewBox)===null||r===void 0?void 0:r.x)||e.x===t.x,a=e.y===(t==null||(n=t.labelViewBox)===null||n===void 0?void 0:n.y)||e.y===t.y;return i&&a}function PI(e,t){var r=e.endAngle===t.endAngle,n=e.startAngle===t.startAngle;return r&&n}function TI(e,t){var r=e.x===t.x,n=e.y===t.y,i=e.z===t.z;return r&&n&&i}function EI(e,t){var r;return ka(e,t)?r=AI:Na(e,t)?r=PI:Dn(e,t)&&(r=TI),r}function jI(e,t){var r;return ka(e,t)?r="trapezoids":Na(e,t)?r="sectors":Dn(e,t)&&(r="points"),r}function MI(e,t){if(ka(e,t)){var r;return(r=t.tooltipPayload)===null||r===void 0||(r=r[0])===null||r===void 0||(r=r.payload)===null||r===void 0?void 0:r.payload}if(Na(e,t)){var n;return(n=t.tooltipPayload)===null||n===void 0||(n=n[0])===null||n===void 0||(n=n.payload)===null||n===void 0?void 0:n.payload}return Dn(e,t)?t.payload:{}}function CI(e){var t=e.activeTooltipItem,r=e.graphicalItem,n=e.itemData,i=jI(r,t),a=MI(r,t),o=n.filter(function(s,c){var f=eh(a,s),l=r.props[i].filter(function(y){var v=EI(r,t);return v(y,t)}),h=r.props[i].indexOf(l[l.length-1]),p=c===h;return f&&p}),u=n.indexOf(o[o.length-1]);return u}var Pc,lg;function $I(){if(lg)return Pc;lg=1;var e=Math.ceil,t=Math.max;function r(n,i,a,o){for(var u=-1,s=t(e((i-n)/(a||1)),0),c=Array(s);s--;)c[o?s:++u]=n,n+=a;return c}return Pc=r,Pc}var Tc,fg;function Ex(){if(fg)return Tc;fg=1;var e=s0(),t=1/0,r=17976931348623157e292;function n(i){if(!i)return i===0?i:0;if(i=e(i),i===t||i===-1/0){var a=i<0?-1:1;return a*r}return i===i?i:0}return Tc=n,Tc}var Ec,hg;function II(){if(hg)return Ec;hg=1;var e=$I(),t=Oa(),r=Ex();function n(i){return function(a,o,u){return u&&typeof u!="number"&&t(a,o,u)&&(o=u=void 0),a=r(a),o===void 0?(o=a,a=0):o=r(o),u=u===void 0?a<o?1:-1:r(u),e(a,o,u,i)}}return Ec=n,Ec}var jc,dg;function kI(){if(dg)return jc;dg=1;var e=II(),t=e();return jc=t,jc}var NI=kI();const Zi=se(NI);function qn(e){"@babel/helpers - typeof";return qn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},qn(e)}function pg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function vg(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?pg(Object(r),!0).forEach(function(n){jx(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function jx(e,t,r){return t=RI(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function RI(e){var t=DI(e,"string");return qn(t)=="symbol"?t:t+""}function DI(e,t){if(qn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(qn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var qI=["Webkit","Moz","O","ms"],BI=function(t,r){var n=t.replace(/(\w)/,function(a){return a.toUpperCase()}),i=qI.reduce(function(a,o){return vg(vg({},a),{},jx({},o+n,r))},{});return i[t]=r,i};function Pr(e){"@babel/helpers - typeof";return Pr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Pr(e)}function Ji(){return Ji=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ji.apply(this,arguments)}function yg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Mc(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?yg(Object(r),!0).forEach(function(n){De(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function LI(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function mg(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Cx(n.key),n)}}function FI(e,t,r){return t&&mg(e.prototype,t),r&&mg(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function zI(e,t,r){return t=Qi(t),UI(e,Mx()?Reflect.construct(t,r||[],Qi(e).constructor):t.apply(e,r))}function UI(e,t){if(t&&(Pr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return WI(e)}function WI(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Mx(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Mx=function(){return!!e})()}function Qi(e){return Qi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Qi(e)}function HI(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ul(e,t)}function Ul(e,t){return Ul=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Ul(e,t)}function De(e,t,r){return t=Cx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Cx(e){var t=GI(e,"string");return Pr(t)=="symbol"?t:t+""}function GI(e,t){if(Pr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Pr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var KI=function(t){var r=t.data,n=t.startIndex,i=t.endIndex,a=t.x,o=t.width,u=t.travellerWidth;if(!r||!r.length)return{};var s=r.length,c=on().domain(Zi(0,s)).range([a,a+o-u]),f=c.domain().map(function(l){return c(l)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:c(n),endX:c(i),scale:c,scaleValues:f}},gg=function(t){return t.changedTouches&&!!t.changedTouches.length},Tr=function(e){function t(r){var n;return LI(this,t),n=zI(this,t,[r]),De(n,"handleDrag",function(i){n.leaveTimer&&(clearTimeout(n.leaveTimer),n.leaveTimer=null),n.state.isTravellerMoving?n.handleTravellerMove(i):n.state.isSlideMoving&&n.handleSlideDrag(i)}),De(n,"handleTouchMove",function(i){i.changedTouches!=null&&i.changedTouches.length>0&&n.handleDrag(i.changedTouches[0])}),De(n,"handleDragEnd",function(){n.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var i=n.props,a=i.endIndex,o=i.onDragEnd,u=i.startIndex;o?.({endIndex:a,startIndex:u})}),n.detachDragEndListener()}),De(n,"handleLeaveWrapper",function(){(n.state.isTravellerMoving||n.state.isSlideMoving)&&(n.leaveTimer=window.setTimeout(n.handleDragEnd,n.props.leaveTimeOut))}),De(n,"handleEnterSlideOrTraveller",function(){n.setState({isTextActive:!0})}),De(n,"handleLeaveSlideOrTraveller",function(){n.setState({isTextActive:!1})}),De(n,"handleSlideDragStart",function(i){var a=gg(i)?i.changedTouches[0]:i;n.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:a.pageX}),n.attachDragEndListener()}),n.travellerDragStartHandlers={startX:n.handleTravellerDragStart.bind(n,"startX"),endX:n.handleTravellerDragStart.bind(n,"endX")},n.state={},n}return HI(t,e),FI(t,[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(n){var i=n.startX,a=n.endX,o=this.state.scaleValues,u=this.props,s=u.gap,c=u.data,f=c.length-1,l=Math.min(i,a),h=Math.max(i,a),p=t.getIndexInRange(o,l),y=t.getIndexInRange(o,h);return{startIndex:p-p%s,endIndex:y===f?f:y-y%s}}},{key:"getTextOfTick",value:function(n){var i=this.props,a=i.data,o=i.tickFormatter,u=i.dataKey,s=it(a[n],u,n);return Z(o)?o(s,n):s}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(n){var i=this.state,a=i.slideMoveStartX,o=i.startX,u=i.endX,s=this.props,c=s.x,f=s.width,l=s.travellerWidth,h=s.startIndex,p=s.endIndex,y=s.onChange,v=n.pageX-a;v>0?v=Math.min(v,c+f-l-u,c+f-l-o):v<0&&(v=Math.max(v,c-o,c-u));var d=this.getIndex({startX:o+v,endX:u+v});(d.startIndex!==h||d.endIndex!==p)&&y&&y(d),this.setState({startX:o+v,endX:u+v,slideMoveStartX:n.pageX})}},{key:"handleTravellerDragStart",value:function(n,i){var a=gg(i)?i.changedTouches[0]:i;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:n,brushMoveStartX:a.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(n){var i=this.state,a=i.brushMoveStartX,o=i.movingTravellerId,u=i.endX,s=i.startX,c=this.state[o],f=this.props,l=f.x,h=f.width,p=f.travellerWidth,y=f.onChange,v=f.gap,d=f.data,x={startX:this.state.startX,endX:this.state.endX},w=n.pageX-a;w>0?w=Math.min(w,l+h-p-c):w<0&&(w=Math.max(w,l-c)),x[o]=c+w;var b=this.getIndex(x),O=b.startIndex,m=b.endIndex,g=function(){var S=d.length-1;return o==="startX"&&(u>s?O%v===0:m%v===0)||u<s&&m===S||o==="endX"&&(u>s?m%v===0:O%v===0)||u>s&&m===S};this.setState(De(De({},o,c+w),"brushMoveStartX",n.pageX),function(){y&&g()&&y(b)})}},{key:"handleTravellerMoveKeyboard",value:function(n,i){var a=this,o=this.state,u=o.scaleValues,s=o.startX,c=o.endX,f=this.state[i],l=u.indexOf(f);if(l!==-1){var h=l+n;if(!(h===-1||h>=u.length)){var p=u[h];i==="startX"&&p>=c||i==="endX"&&p<=s||this.setState(De({},i,p),function(){a.props.onChange(a.getIndex({startX:a.state.startX,endX:a.state.endX}))})}}}},{key:"renderBackground",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,s=n.fill,c=n.stroke;return T.createElement("rect",{stroke:c,fill:s,x:i,y:a,width:o,height:u})}},{key:"renderPanorama",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,s=n.data,c=n.children,f=n.padding,l=q.Children.only(c);return l?T.cloneElement(l,{x:i,y:a,width:o,height:u,margin:f,compact:!0,data:s}):null}},{key:"renderTravellerLayer",value:function(n,i){var a,o,u=this,s=this.props,c=s.y,f=s.travellerWidth,l=s.height,h=s.traveller,p=s.ariaLabel,y=s.data,v=s.startIndex,d=s.endIndex,x=Math.max(n,this.props.x),w=Mc(Mc({},ee(this.props,!1)),{},{x,y:c,width:f,height:l}),b=p||"Min value: ".concat((a=y[v])===null||a===void 0?void 0:a.name,", Max value: ").concat((o=y[d])===null||o===void 0?void 0:o.name);return T.createElement(_e,{tabIndex:0,role:"slider","aria-label":b,"aria-valuenow":n,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[i],onTouchStart:this.travellerDragStartHandlers[i],onKeyDown:function(m){["ArrowLeft","ArrowRight"].includes(m.key)&&(m.preventDefault(),m.stopPropagation(),u.handleTravellerMoveKeyboard(m.key==="ArrowRight"?1:-1,i))},onFocus:function(){u.setState({isTravellerFocused:!0})},onBlur:function(){u.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},t.renderTraveller(h,w))}},{key:"renderSlide",value:function(n,i){var a=this.props,o=a.y,u=a.height,s=a.stroke,c=a.travellerWidth,f=Math.min(n,i)+c,l=Math.max(Math.abs(i-n)-c,0);return T.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:s,fillOpacity:.2,x:f,y:o,width:l,height:u})}},{key:"renderText",value:function(){var n=this.props,i=n.startIndex,a=n.endIndex,o=n.y,u=n.height,s=n.travellerWidth,c=n.stroke,f=this.state,l=f.startX,h=f.endX,p=5,y={pointerEvents:"none",fill:c};return T.createElement(_e,{className:"recharts-brush-texts"},T.createElement(Ei,Ji({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,h)-p,y:o+u/2},y),this.getTextOfTick(i)),T.createElement(Ei,Ji({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,h)+s+p,y:o+u/2},y),this.getTextOfTick(a)))}},{key:"render",value:function(){var n=this.props,i=n.data,a=n.className,o=n.children,u=n.x,s=n.y,c=n.width,f=n.height,l=n.alwaysShowText,h=this.state,p=h.startX,y=h.endX,v=h.isTextActive,d=h.isSlideMoving,x=h.isTravellerMoving,w=h.isTravellerFocused;if(!i||!i.length||!B(u)||!B(s)||!B(c)||!B(f)||c<=0||f<=0)return null;var b=ie("recharts-brush",a),O=T.Children.count(o)===1,m=BI("userSelect","none");return T.createElement(_e,{className:b,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:m},this.renderBackground(),O&&this.renderPanorama(),this.renderSlide(p,y),this.renderTravellerLayer(p,"startX"),this.renderTravellerLayer(y,"endX"),(v||d||x||w||l)&&this.renderText())}}],[{key:"renderDefaultTraveller",value:function(n){var i=n.x,a=n.y,o=n.width,u=n.height,s=n.stroke,c=Math.floor(a+u/2)-1;return T.createElement(T.Fragment,null,T.createElement("rect",{x:i,y:a,width:o,height:u,fill:s,stroke:"none"}),T.createElement("line",{x1:i+1,y1:c,x2:i+o-1,y2:c,fill:"none",stroke:"#fff"}),T.createElement("line",{x1:i+1,y1:c+2,x2:i+o-1,y2:c+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(n,i){var a;return T.isValidElement(n)?a=T.cloneElement(n,i):Z(n)?a=n(i):a=t.renderDefaultTraveller(i),a}},{key:"getDerivedStateFromProps",value:function(n,i){var a=n.data,o=n.width,u=n.x,s=n.travellerWidth,c=n.updateId,f=n.startIndex,l=n.endIndex;if(a!==i.prevData||c!==i.prevUpdateId)return Mc({prevData:a,prevTravellerWidth:s,prevUpdateId:c,prevX:u,prevWidth:o},a&&a.length?KI({data:a,width:o,x:u,travellerWidth:s,startIndex:f,endIndex:l}):{scale:null,scaleValues:null});if(i.scale&&(o!==i.prevWidth||u!==i.prevX||s!==i.prevTravellerWidth)){i.scale.range([u,u+o-s]);var h=i.scale.domain().map(function(p){return i.scale(p)});return{prevData:a,prevTravellerWidth:s,prevUpdateId:c,prevX:u,prevWidth:o,startX:i.scale(n.startIndex),endX:i.scale(n.endIndex),scaleValues:h}}return null}},{key:"getIndexInRange",value:function(n,i){for(var a=n.length,o=0,u=a-1;u-o>1;){var s=Math.floor((o+u)/2);n[s]>i?u=s:o=s}return i>=n[u]?u:o}}])}(q.PureComponent);De(Tr,"displayName","Brush");De(Tr,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var Cc,bg;function VI(){if(bg)return Cc;bg=1;var e=jf();function t(r,n){var i;return e(r,function(a,o,u){return i=n(a,o,u),!i}),!!i}return Cc=t,Cc}var $c,xg;function XI(){if(xg)return $c;xg=1;var e=Bb(),t=Tt(),r=VI(),n=Ne(),i=Oa();function a(o,u,s){var c=n(o)?e:r;return s&&i(o,u,s)&&(u=void 0),c(o,t(u,3))}return $c=a,$c}var YI=XI();const ZI=se(YI);var rt=function(t,r){var n=t.alwaysShow,i=t.ifOverflow;return n&&(i="extendDomain"),i===r},Ic,wg;function JI(){if(wg)return Ic;wg=1;var e=n0();function t(r,n,i){n=="__proto__"&&e?e(r,n,{configurable:!0,enumerable:!0,value:i,writable:!0}):r[n]=i}return Ic=t,Ic}var kc,Og;function QI(){if(Og)return kc;Og=1;var e=JI(),t=t0(),r=Tt();function n(i,a){var o={};return a=r(a,3),t(i,function(u,s,c){e(o,s,a(u,s,c))}),o}return kc=n,kc}var ek=QI();const tk=se(ek);var Nc,_g;function rk(){if(_g)return Nc;_g=1;function e(t,r){for(var n=-1,i=t==null?0:t.length;++n<i;)if(!r(t[n],n,t))return!1;return!0}return Nc=e,Nc}var Rc,Sg;function nk(){if(Sg)return Rc;Sg=1;var e=jf();function t(r,n){var i=!0;return e(r,function(a,o,u){return i=!!n(a,o,u),i}),i}return Rc=t,Rc}var Dc,Ag;function ik(){if(Ag)return Dc;Ag=1;var e=rk(),t=nk(),r=Tt(),n=Ne(),i=Oa();function a(o,u,s){var c=n(o)?e:t;return s&&i(o,u,s)&&(u=void 0),c(o,r(u,3))}return Dc=a,Dc}var ak=ik();const $x=se(ak);var ok=["x","y"];function Bn(e){"@babel/helpers - typeof";return Bn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Bn(e)}function Wl(){return Wl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Wl.apply(this,arguments)}function Pg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Jr(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Pg(Object(r),!0).forEach(function(n){uk(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Pg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function uk(e,t,r){return t=sk(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function sk(e){var t=ck(e,"string");return Bn(t)=="symbol"?t:t+""}function ck(e,t){if(Bn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Bn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function lk(e,t){if(e==null)return{};var r=fk(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function fk(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function hk(e,t){var r=e.x,n=e.y,i=lk(e,ok),a="".concat(r),o=parseInt(a,10),u="".concat(n),s=parseInt(u,10),c="".concat(t.height||i.height),f=parseInt(c,10),l="".concat(t.width||i.width),h=parseInt(l,10);return Jr(Jr(Jr(Jr(Jr({},t),i),o?{x:o}:{}),s?{y:s}:{}),{},{height:f,width:h,name:t.name,radius:t.radius})}function Tg(e){return T.createElement(SI,Wl({shapeType:"rectangle",propTransformer:hk,activeClassName:"recharts-active-bar"},e))}var dk=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return function(n,i){if(typeof t=="number")return t;var a=typeof n=="number";return a?t(n,i):(a||Wt(!1),r)}},pk=["value","background"],Ix;function Er(e){"@babel/helpers - typeof";return Er=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Er(e)}function vk(e,t){if(e==null)return{};var r=yk(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function yk(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function ea(){return ea=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ea.apply(this,arguments)}function Eg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ve(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Eg(Object(r),!0).forEach(function(n){_t(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Eg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function mk(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function jg(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Nx(n.key),n)}}function gk(e,t,r){return t&&jg(e.prototype,t),r&&jg(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function bk(e,t,r){return t=ta(t),xk(e,kx()?Reflect.construct(t,r||[],ta(e).constructor):t.apply(e,r))}function xk(e,t){if(t&&(Er(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return wk(e)}function wk(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function kx(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(kx=function(){return!!e})()}function ta(e){return ta=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ta(e)}function Ok(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Hl(e,t)}function Hl(e,t){return Hl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Hl(e,t)}function _t(e,t,r){return t=Nx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Nx(e){var t=_k(e,"string");return Er(t)=="symbol"?t:t+""}function _k(e,t){if(Er(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Er(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Qt=function(e){function t(){var r;mk(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=bk(this,t,[].concat(i)),_t(r,"state",{isAnimationFinished:!1}),_t(r,"id",va("recharts-bar-")),_t(r,"handleAnimationEnd",function(){var o=r.props.onAnimationEnd;r.setState({isAnimationFinished:!0}),o&&o()}),_t(r,"handleAnimationStart",function(){var o=r.props.onAnimationStart;r.setState({isAnimationFinished:!1}),o&&o()}),r}return Ok(t,e),gk(t,[{key:"renderRectanglesStatically",value:function(n){var i=this,a=this.props,o=a.shape,u=a.dataKey,s=a.activeIndex,c=a.activeBar,f=ee(this.props,!1);return n&&n.map(function(l,h){var p=h===s,y=p?c:o,v=ve(ve(ve({},f),l),{},{isActive:p,option:y,index:h,dataKey:u,onAnimationStart:i.handleAnimationStart,onAnimationEnd:i.handleAnimationEnd});return T.createElement(_e,ea({className:"recharts-bar-rectangle"},mi(i.props,l,h),{key:"rectangle-".concat(l?.x,"-").concat(l?.y,"-").concat(l?.value,"-").concat(h)}),T.createElement(Tg,v))})}},{key:"renderRectanglesWithAnimation",value:function(){var n=this,i=this.props,a=i.data,o=i.layout,u=i.isAnimationActive,s=i.animationBegin,c=i.animationDuration,f=i.animationEasing,l=i.animationId,h=this.state.prevData;return T.createElement(At,{begin:s,duration:c,isActive:u,easing:f,from:{t:0},to:{t:1},key:"bar-".concat(l),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(p){var y=p.t,v=a.map(function(d,x){var w=h&&h[x];if(w){var b=rr(w.x,d.x),O=rr(w.y,d.y),m=rr(w.width,d.width),g=rr(w.height,d.height);return ve(ve({},d),{},{x:b(y),y:O(y),width:m(y),height:g(y)})}if(o==="horizontal"){var _=rr(0,d.height),S=_(y);return ve(ve({},d),{},{y:d.y+d.height-S,height:S})}var P=rr(0,d.width),C=P(y);return ve(ve({},d),{},{width:C})});return T.createElement(_e,null,n.renderRectanglesStatically(v))})}},{key:"renderRectangles",value:function(){var n=this.props,i=n.data,a=n.isAnimationActive,o=this.state.prevData;return a&&i&&i.length&&(!o||!eh(o,i))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(i)}},{key:"renderBackground",value:function(){var n=this,i=this.props,a=i.data,o=i.dataKey,u=i.activeIndex,s=ee(this.props.background,!1);return a.map(function(c,f){c.value;var l=c.background,h=vk(c,pk);if(!l)return null;var p=ve(ve(ve(ve(ve({},h),{},{fill:"#eee"},l),s),mi(n.props,c,f)),{},{onAnimationStart:n.handleAnimationStart,onAnimationEnd:n.handleAnimationEnd,dataKey:o,index:f,className:"recharts-bar-background-rectangle"});return T.createElement(Tg,ea({key:"background-bar-".concat(f),option:n.props.background,isActive:f===u},p))})}},{key:"renderErrorBar",value:function(n,i){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.data,u=a.xAxis,s=a.yAxis,c=a.layout,f=a.children,l=Ze(f,Ia);if(!l)return null;var h=c==="vertical"?o[0].height/2:o[0].width/2,p=function(d,x){var w=Array.isArray(d.value)?d.value[1]:d.value;return{x:d.x,y:d.y,value:w,errorVal:it(d,x)}},y={clipPath:n?"url(#clipPath-".concat(i,")"):null};return T.createElement(_e,y,l.map(function(v){return T.cloneElement(v,{key:"error-bar-".concat(i,"-").concat(v.props.dataKey),data:o,xAxis:u,yAxis:s,layout:c,offset:h,dataPointFormatter:p})}))}},{key:"render",value:function(){var n=this.props,i=n.hide,a=n.data,o=n.className,u=n.xAxis,s=n.yAxis,c=n.left,f=n.top,l=n.width,h=n.height,p=n.isAnimationActive,y=n.background,v=n.id;if(i||!a||!a.length)return null;var d=this.state.isAnimationFinished,x=ie("recharts-bar",o),w=u&&u.allowDataOverflow,b=s&&s.allowDataOverflow,O=w||b,m=ne(v)?this.id:v;return T.createElement(_e,{className:x},w||b?T.createElement("defs",null,T.createElement("clipPath",{id:"clipPath-".concat(m)},T.createElement("rect",{x:w?c:c-l/2,y:b?f:f-h/2,width:w?l:l*2,height:b?h:h*2}))):null,T.createElement(_e,{className:"recharts-bar-rectangles",clipPath:O?"url(#clipPath-".concat(m,")"):null},y?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(O,m),(!p||d)&&dt.renderCallByParent(this.props,a))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curData:n.data,prevData:i.curData}:n.data!==i.curData?{curData:n.data}:null}}])}(q.PureComponent);Ix=Qt;_t(Qt,"displayName","Bar");_t(Qt,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!Kn.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"});_t(Qt,"getComposedData",function(e){var t=e.props,r=e.item,n=e.barPosition,i=e.bandSize,a=e.xAxis,o=e.yAxis,u=e.xAxisTicks,s=e.yAxisTicks,c=e.stackedData,f=e.dataStartIndex,l=e.displayedData,h=e.offset,p=zj(n,r);if(!p)return null;var y=t.layout,v=r.type.defaultProps,d=v!==void 0?ve(ve({},v),r.props):r.props,x=d.dataKey,w=d.children,b=d.minPointSize,O=y==="horizontal"?o:a,m=c?O.scale.domain():null,g=Yj({numericAxis:O}),_=Ze(w,l0),S=l.map(function(P,C){var A,E,j,k,I,N;c?A=Uj(c[f+C],m):(A=it(P,x),Array.isArray(A)||(A=[g,A]));var D=dk(b,Ix.defaultProps.minPointSize)(A[1],C);if(y==="horizontal"){var L,F=[o.scale(A[0]),o.scale(A[1])],H=F[0],K=F[1];E=fm({axis:a,ticks:u,bandSize:i,offset:p.offset,entry:P,index:C}),j=(L=K??H)!==null&&L!==void 0?L:void 0,k=p.size;var U=H-K;if(I=Number.isNaN(U)?0:U,N={x:E,y:o.y,width:k,height:o.height},Math.abs(D)>0&&Math.abs(I)<Math.abs(D)){var V=Ye(I||D)*(Math.abs(D)-Math.abs(I));j-=V,I+=V}}else{var ce=[a.scale(A[0]),a.scale(A[1])],pe=ce[0],Re=ce[1];if(E=pe,j=fm({axis:o,ticks:s,bandSize:i,offset:p.offset,entry:P,index:C}),k=Re-pe,I=p.size,N={x:a.x,y:j,width:a.width,height:I},Math.abs(D)>0&&Math.abs(k)<Math.abs(D)){var Ct=Ye(k||D)*(Math.abs(D)-Math.abs(k));k+=Ct}}return ve(ve(ve({},P),{},{x:E,y:j,width:k,height:I,value:c?A:A[1],payload:P,background:N},_&&_[C]&&_[C].props),{},{tooltipPayload:[yx(r,P)],tooltipPosition:{x:E+k/2,y:j+I/2}})});return ve({data:S,layout:y},h)});function Ln(e){"@babel/helpers - typeof";return Ln=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ln(e)}function Sk(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Mg(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Rx(n.key),n)}}function Ak(e,t,r){return t&&Mg(e.prototype,t),r&&Mg(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Cg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ve(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Cg(Object(r),!0).forEach(function(n){Ra(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Cg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Ra(e,t,r){return t=Rx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Rx(e){var t=Pk(e,"string");return Ln(t)=="symbol"?t:t+""}function Pk(e,t){if(Ln(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Ln(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Tk=function(t,r,n,i,a){var o=t.width,u=t.height,s=t.layout,c=t.children,f=Object.keys(r),l={left:n.left,leftMirror:n.left,right:o-n.right,rightMirror:o-n.right,top:n.top,topMirror:n.top,bottom:u-n.bottom,bottomMirror:u-n.bottom},h=!!qe(c,Qt);return f.reduce(function(p,y){var v=r[y],d=v.orientation,x=v.domain,w=v.padding,b=w===void 0?{}:w,O=v.mirror,m=v.reversed,g="".concat(d).concat(O?"Mirror":""),_,S,P,C,A;if(v.type==="number"&&(v.padding==="gap"||v.padding==="no-gap")){var E=x[1]-x[0],j=1/0,k=v.categoricalDomain.sort(T1);if(k.forEach(function(ce,pe){pe>0&&(j=Math.min((ce||0)-(k[pe-1]||0),j))}),Number.isFinite(j)){var I=j/E,N=v.layout==="vertical"?n.height:n.width;if(v.padding==="gap"&&(_=I*N/2),v.padding==="no-gap"){var D=Gt(t.barCategoryGap,I*N),L=I*N/2;_=L-D-(L-D)/N*D}}}i==="xAxis"?S=[n.left+(b.left||0)+(_||0),n.left+n.width-(b.right||0)-(_||0)]:i==="yAxis"?S=s==="horizontal"?[n.top+n.height-(b.bottom||0),n.top+(b.top||0)]:[n.top+(b.top||0)+(_||0),n.top+n.height-(b.bottom||0)-(_||0)]:S=v.range,m&&(S=[S[1],S[0]]);var F=Lj(v,a,h),H=F.scale,K=F.realScaleType;H.domain(x).range(S),Fj(H);var U=Xj(H,Ve(Ve({},v),{},{realScaleType:K}));i==="xAxis"?(A=d==="top"&&!O||d==="bottom"&&O,P=n.left,C=l[g]-A*v.height):i==="yAxis"&&(A=d==="left"&&!O||d==="right"&&O,P=l[g]-A*v.width,C=n.top);var V=Ve(Ve(Ve({},v),U),{},{realScaleType:K,x:P,y:C,scale:H,width:i==="xAxis"?n.width:v.width,height:i==="yAxis"?n.height:v.height});return V.bandSize=zi(V,U),!v.hide&&i==="xAxis"?l[g]+=(A?-1:1)*V.height:v.hide||(l[g]+=(A?-1:1)*V.width),Ve(Ve({},p),{},Ra({},y,V))},{})},Dx=function(t,r){var n=t.x,i=t.y,a=r.x,o=r.y;return{x:Math.min(n,a),y:Math.min(i,o),width:Math.abs(a-n),height:Math.abs(o-i)}},Ek=function(t){var r=t.x1,n=t.y1,i=t.x2,a=t.y2;return Dx({x:r,y:n},{x:i,y:a})},qx=function(){function e(t){Sk(this,e),this.scale=t}return Ak(e,[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(r){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=n.bandAware,a=n.position;if(r!==void 0){if(a)switch(a){case"start":return this.scale(r);case"middle":{var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+o}case"end":{var u=this.bandwidth?this.bandwidth():0;return this.scale(r)+u}default:return this.scale(r)}if(i){var s=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+s}return this.scale(r)}}},{key:"isInRange",value:function(r){var n=this.range(),i=n[0],a=n[n.length-1];return i<=a?r>=i&&r<=a:r>=a&&r<=i}}],[{key:"create",value:function(r){return new e(r)}}])}();Ra(qx,"EPS",1e-4);var ah=function(t){var r=Object.keys(t).reduce(function(n,i){return Ve(Ve({},n),{},Ra({},i,qx.create(t[i])))},{});return Ve(Ve({},r),{},{apply:function(i){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=a.bandAware,u=a.position;return tk(i,function(s,c){return r[c].apply(s,{bandAware:o,position:u})})},isInRange:function(i){return $x(i,function(a,o){return r[o].isInRange(a)})}})};function jk(e){return(e%180+180)%180}var Mk=function(t){var r=t.width,n=t.height,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=jk(i),o=a*Math.PI/180,u=Math.atan(n/r),s=o>u&&o<Math.PI-u?n/Math.sin(o):r/Math.cos(o);return Math.abs(s)},qc,$g;function Ck(){if($g)return qc;$g=1;var e=Tt(),t=Gn(),r=xa();function n(i){return function(a,o,u){var s=Object(a);if(!t(a)){var c=e(o,3);a=r(a),o=function(l){return c(s[l],l,s)}}var f=i(a,o,u);return f>-1?s[c?a[f]:f]:void 0}}return qc=n,qc}var Bc,Ig;function $k(){if(Ig)return Bc;Ig=1;var e=Ex();function t(r){var n=e(r),i=n%1;return n===n?i?n-i:n:0}return Bc=t,Bc}var Lc,kg;function Ik(){if(kg)return Lc;kg=1;var e=Yb(),t=Tt(),r=$k(),n=Math.max;function i(a,o,u){var s=a==null?0:a.length;if(!s)return-1;var c=u==null?0:r(u);return c<0&&(c=n(s+c,0)),e(a,t(o,3),c)}return Lc=i,Lc}var Fc,Ng;function kk(){if(Ng)return Fc;Ng=1;var e=Ck(),t=Ik(),r=e(t);return Fc=r,Fc}var Nk=kk();const Rk=se(Nk);var Dk=hb();const qk=se(Dk);var Bk=qk(function(e){return{x:e.left,y:e.top,width:e.width,height:e.height}},function(e){return["l",e.left,"t",e.top,"w",e.width,"h",e.height].join("")}),oh=q.createContext(void 0),uh=q.createContext(void 0),Bx=q.createContext(void 0),Lx=q.createContext({}),Fx=q.createContext(void 0),zx=q.createContext(0),Ux=q.createContext(0),Rg=function(t){var r=t.state,n=r.xAxisMap,i=r.yAxisMap,a=r.offset,o=t.clipPathId,u=t.children,s=t.width,c=t.height,f=Bk(a);return T.createElement(oh.Provider,{value:n},T.createElement(uh.Provider,{value:i},T.createElement(Lx.Provider,{value:a},T.createElement(Bx.Provider,{value:f},T.createElement(Fx.Provider,{value:o},T.createElement(zx.Provider,{value:c},T.createElement(Ux.Provider,{value:s},u)))))))},Lk=function(){return q.useContext(Fx)},Wx=function(t){var r=q.useContext(oh);r==null&&Wt(!1);var n=r[t];return n==null&&Wt(!1),n},Fk=function(){var t=q.useContext(oh);return Ot(t)},zk=function(){var t=q.useContext(uh),r=Rk(t,function(n){return $x(n.domain,Number.isFinite)});return r||Ot(t)},Hx=function(t){var r=q.useContext(uh);r==null&&Wt(!1);var n=r[t];return n==null&&Wt(!1),n},Uk=function(){var t=q.useContext(Bx);return t},Wk=function(){return q.useContext(Lx)},sh=function(){return q.useContext(Ux)},ch=function(){return q.useContext(zx)};function jr(e){"@babel/helpers - typeof";return jr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},jr(e)}function Hk(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Gk(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Kx(n.key),n)}}function Kk(e,t,r){return t&&Gk(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function Vk(e,t,r){return t=ra(t),Xk(e,Gx()?Reflect.construct(t,r||[],ra(e).constructor):t.apply(e,r))}function Xk(e,t){if(t&&(jr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Yk(e)}function Yk(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Gx(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Gx=function(){return!!e})()}function ra(e){return ra=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ra(e)}function Zk(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Gl(e,t)}function Gl(e,t){return Gl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Gl(e,t)}function Dg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function qg(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Dg(Object(r),!0).forEach(function(n){lh(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Dg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function lh(e,t,r){return t=Kx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Kx(e){var t=Jk(e,"string");return jr(t)=="symbol"?t:t+""}function Jk(e,t){if(jr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(jr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function Qk(e,t){return nN(e)||rN(e,t)||tN(e,t)||eN()}function eN(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function tN(e,t){if(e){if(typeof e=="string")return Bg(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Bg(e,t)}}function Bg(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function rN(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],s=!0,c=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);s=!0);}catch(f){c=!0,i=f}finally{try{if(!s&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return u}}function nN(e){if(Array.isArray(e))return e}function Kl(){return Kl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Kl.apply(this,arguments)}var iN=function(t,r){var n;return T.isValidElement(t)?n=T.cloneElement(t,r):Z(t)?n=t(r):n=T.createElement("line",Kl({},r,{className:"recharts-reference-line-line"})),n},aN=function(t,r,n,i,a,o,u,s,c){var f=a.x,l=a.y,h=a.width,p=a.height;if(n){var y=c.y,v=t.y.apply(y,{position:o});if(rt(c,"discard")&&!t.y.isInRange(v))return null;var d=[{x:f+h,y:v},{x:f,y:v}];return s==="left"?d.reverse():d}if(r){var x=c.x,w=t.x.apply(x,{position:o});if(rt(c,"discard")&&!t.x.isInRange(w))return null;var b=[{x:w,y:l+p},{x:w,y:l}];return u==="top"?b.reverse():b}if(i){var O=c.segment,m=O.map(function(g){return t.apply(g,{position:o})});return rt(c,"discard")&&ZI(m,function(g){return!t.isInRange(g)})?null:m}return null};function oN(e){var t=e.x,r=e.y,n=e.segment,i=e.xAxisId,a=e.yAxisId,o=e.shape,u=e.className,s=e.alwaysShow,c=Lk(),f=Wx(i),l=Hx(a),h=Uk();if(!c||!h)return null;ft(s===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var p=ah({x:f.scale,y:l.scale}),y=xe(t),v=xe(r),d=n&&n.length===2,x=aN(p,y,v,d,h,e.position,f.orientation,l.orientation,e);if(!x)return null;var w=Qk(x,2),b=w[0],O=b.x,m=b.y,g=w[1],_=g.x,S=g.y,P=rt(e,"hidden")?"url(#".concat(c,")"):void 0,C=qg(qg({clipPath:P},ee(e,!0)),{},{x1:O,y1:m,x2:_,y2:S});return T.createElement(_e,{className:ie("recharts-reference-line",u)},iN(o,C),Ee.renderCallByParent(e,Ek({x1:O,y1:m,x2:_,y2:S})))}var fh=function(e){function t(){return Hk(this,t),Vk(this,t,arguments)}return Zk(t,e),Kk(t,[{key:"render",value:function(){return T.createElement(oN,this.props)}}])}(T.Component);lh(fh,"displayName","ReferenceLine");lh(fh,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function Vl(){return Vl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Vl.apply(this,arguments)}function Mr(e){"@babel/helpers - typeof";return Mr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Mr(e)}function Lg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Fg(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Lg(Object(r),!0).forEach(function(n){Da(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Lg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function uN(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function sN(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Xx(n.key),n)}}function cN(e,t,r){return t&&sN(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function lN(e,t,r){return t=na(t),fN(e,Vx()?Reflect.construct(t,r||[],na(e).constructor):t.apply(e,r))}function fN(e,t){if(t&&(Mr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return hN(e)}function hN(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Vx(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Vx=function(){return!!e})()}function na(e){return na=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},na(e)}function dN(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Xl(e,t)}function Xl(e,t){return Xl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Xl(e,t)}function Da(e,t,r){return t=Xx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Xx(e){var t=pN(e,"string");return Mr(t)=="symbol"?t:t+""}function pN(e,t){if(Mr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Mr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var vN=function(t){var r=t.x,n=t.y,i=t.xAxis,a=t.yAxis,o=ah({x:i.scale,y:a.scale}),u=o.apply({x:r,y:n},{bandAware:!0});return rt(t,"discard")&&!o.isInRange(u)?null:u},qa=function(e){function t(){return uN(this,t),lN(this,t,arguments)}return dN(t,e),cN(t,[{key:"render",value:function(){var n=this.props,i=n.x,a=n.y,o=n.r,u=n.alwaysShow,s=n.clipPathId,c=xe(i),f=xe(a);if(ft(u===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!c||!f)return null;var l=vN(this.props);if(!l)return null;var h=l.x,p=l.y,y=this.props,v=y.shape,d=y.className,x=rt(this.props,"hidden")?"url(#".concat(s,")"):void 0,w=Fg(Fg({clipPath:x},ee(this.props,!0)),{},{cx:h,cy:p});return T.createElement(_e,{className:ie("recharts-reference-dot",d)},t.renderDot(v,w),Ee.renderCallByParent(this.props,{x:h-o,y:p-o,width:2*o,height:2*o}))}}])}(T.Component);Da(qa,"displayName","ReferenceDot");Da(qa,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1});Da(qa,"renderDot",function(e,t){var r;return T.isValidElement(e)?r=T.cloneElement(e,t):Z(e)?r=e(t):r=T.createElement(Tx,Vl({},t,{cx:t.cx,cy:t.cy,className:"recharts-reference-dot-dot"})),r});function Yl(){return Yl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Yl.apply(this,arguments)}function Cr(e){"@babel/helpers - typeof";return Cr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Cr(e)}function zg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ug(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?zg(Object(r),!0).forEach(function(n){Ba(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):zg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function yN(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function mN(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Zx(n.key),n)}}function gN(e,t,r){return t&&mN(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function bN(e,t,r){return t=ia(t),xN(e,Yx()?Reflect.construct(t,r||[],ia(e).constructor):t.apply(e,r))}function xN(e,t){if(t&&(Cr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return wN(e)}function wN(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Yx(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Yx=function(){return!!e})()}function ia(e){return ia=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ia(e)}function ON(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Zl(e,t)}function Zl(e,t){return Zl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Zl(e,t)}function Ba(e,t,r){return t=Zx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Zx(e){var t=_N(e,"string");return Cr(t)=="symbol"?t:t+""}function _N(e,t){if(Cr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Cr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var SN=function(t,r,n,i,a){var o=a.x1,u=a.x2,s=a.y1,c=a.y2,f=a.xAxis,l=a.yAxis;if(!f||!l)return null;var h=ah({x:f.scale,y:l.scale}),p={x:t?h.x.apply(o,{position:"start"}):h.x.rangeMin,y:n?h.y.apply(s,{position:"start"}):h.y.rangeMin},y={x:r?h.x.apply(u,{position:"end"}):h.x.rangeMax,y:i?h.y.apply(c,{position:"end"}):h.y.rangeMax};return rt(a,"discard")&&(!h.isInRange(p)||!h.isInRange(y))?null:Dx(p,y)},La=function(e){function t(){return yN(this,t),bN(this,t,arguments)}return ON(t,e),gN(t,[{key:"render",value:function(){var n=this.props,i=n.x1,a=n.x2,o=n.y1,u=n.y2,s=n.className,c=n.alwaysShow,f=n.clipPathId;ft(c===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var l=xe(i),h=xe(a),p=xe(o),y=xe(u),v=this.props.shape;if(!l&&!h&&!p&&!y&&!v)return null;var d=SN(l,h,p,y,this.props);if(!d&&!v)return null;var x=rt(this.props,"hidden")?"url(#".concat(f,")"):void 0;return T.createElement(_e,{className:ie("recharts-reference-area",s)},t.renderRect(v,Ug(Ug({clipPath:x},ee(this.props,!0)),d)),Ee.renderCallByParent(this.props,d))}}])}(T.Component);Ba(La,"displayName","ReferenceArea");Ba(La,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1});Ba(La,"renderRect",function(e,t){var r;return T.isValidElement(e)?r=T.cloneElement(e,t):Z(e)?r=e(t):r=T.createElement(ih,Yl({},t,{className:"recharts-reference-area-rect"})),r});function Jx(e,t,r){if(t<1)return[];if(t===1&&r===void 0)return e;for(var n=[],i=0;i<e.length;i+=t)n.push(e[i]);return n}function AN(e,t,r){var n={width:e.width+t.width,height:e.height+t.height};return Mk(n,r)}function PN(e,t,r){var n=r==="width",i=e.x,a=e.y,o=e.width,u=e.height;return t===1?{start:n?i:a,end:n?i+o:a+u}:{start:n?i+o:a+u,end:n?i:a}}function aa(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function TN(e,t){return Jx(e,t+1)}function EN(e,t,r,n,i){for(var a=(n||[]).slice(),o=t.start,u=t.end,s=0,c=1,f=o,l=function(){var y=n?.[s];if(y===void 0)return{v:Jx(n,c)};var v=s,d,x=function(){return d===void 0&&(d=r(y,v)),d},w=y.coordinate,b=s===0||aa(e,w,x,f,u);b||(s=0,f=o,c+=1),b&&(f=w+e*(x()/2+i),s+=c)},h;c<=a.length;)if(h=l(),h)return h.v;return[]}function Fn(e){"@babel/helpers - typeof";return Fn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Fn(e)}function Wg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Te(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Wg(Object(r),!0).forEach(function(n){jN(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Wg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function jN(e,t,r){return t=MN(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function MN(e){var t=CN(e,"string");return Fn(t)=="symbol"?t:t+""}function CN(e,t){if(Fn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Fn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function $N(e,t,r,n,i){for(var a=(n||[]).slice(),o=a.length,u=t.start,s=t.end,c=function(h){var p=a[h],y,v=function(){return y===void 0&&(y=r(p,h)),y};if(h===o-1){var d=e*(p.coordinate+e*v()/2-s);a[h]=p=Te(Te({},p),{},{tickCoord:d>0?p.coordinate-d*e:p.coordinate})}else a[h]=p=Te(Te({},p),{},{tickCoord:p.coordinate});var x=aa(e,p.tickCoord,v,u,s);x&&(s=p.tickCoord-e*(v()/2+i),a[h]=Te(Te({},p),{},{isShow:!0}))},f=o-1;f>=0;f--)c(f);return a}function IN(e,t,r,n,i,a){var o=(n||[]).slice(),u=o.length,s=t.start,c=t.end;if(a){var f=n[u-1],l=r(f,u-1),h=e*(f.coordinate+e*l/2-c);o[u-1]=f=Te(Te({},f),{},{tickCoord:h>0?f.coordinate-h*e:f.coordinate});var p=aa(e,f.tickCoord,function(){return l},s,c);p&&(c=f.tickCoord-e*(l/2+i),o[u-1]=Te(Te({},f),{},{isShow:!0}))}for(var y=a?u-1:u,v=function(w){var b=o[w],O,m=function(){return O===void 0&&(O=r(b,w)),O};if(w===0){var g=e*(b.coordinate-e*m()/2-s);o[w]=b=Te(Te({},b),{},{tickCoord:g<0?b.coordinate-g*e:b.coordinate})}else o[w]=b=Te(Te({},b),{},{tickCoord:b.coordinate});var _=aa(e,b.tickCoord,m,s,c);_&&(s=b.tickCoord+e*(m()/2+i),o[w]=Te(Te({},b),{},{isShow:!0}))},d=0;d<y;d++)v(d);return o}function hh(e,t,r){var n=e.tick,i=e.ticks,a=e.viewBox,o=e.minTickGap,u=e.orientation,s=e.interval,c=e.tickFormatter,f=e.unit,l=e.angle;if(!i||!i.length||!n)return[];if(B(s)||Kn.isSsr)return TN(i,typeof s=="number"&&B(s)?s:0);var h=[],p=u==="top"||u==="bottom"?"width":"height",y=f&&p==="width"?an(f,{fontSize:t,letterSpacing:r}):{width:0,height:0},v=function(b,O){var m=Z(c)?c(b.value,O):b.value;return p==="width"?AN(an(m,{fontSize:t,letterSpacing:r}),y,l):an(m,{fontSize:t,letterSpacing:r})[p]},d=i.length>=2?Ye(i[1].coordinate-i[0].coordinate):1,x=PN(a,d,p);return s==="equidistantPreserveStart"?EN(d,x,v,i,o):(s==="preserveStart"||s==="preserveStartEnd"?h=IN(d,x,v,i,o,s==="preserveStartEnd"):h=$N(d,x,v,i,o),h.filter(function(w){return w.isShow}))}var kN=["viewBox"],NN=["viewBox"],RN=["ticks"];function $r(e){"@babel/helpers - typeof";return $r=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},$r(e)}function lr(){return lr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},lr.apply(this,arguments)}function Hg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Me(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Hg(Object(r),!0).forEach(function(n){dh(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Hg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function zc(e,t){if(e==null)return{};var r=DN(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function DN(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function qN(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Gg(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ew(n.key),n)}}function BN(e,t,r){return t&&Gg(e.prototype,t),r&&Gg(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function LN(e,t,r){return t=oa(t),FN(e,Qx()?Reflect.construct(t,r||[],oa(e).constructor):t.apply(e,r))}function FN(e,t){if(t&&($r(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return zN(e)}function zN(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Qx(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Qx=function(){return!!e})()}function oa(e){return oa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},oa(e)}function UN(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Jl(e,t)}function Jl(e,t){return Jl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Jl(e,t)}function dh(e,t,r){return t=ew(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ew(e){var t=WN(e,"string");return $r(t)=="symbol"?t:t+""}function WN(e,t){if($r(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if($r(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Ur=function(e){function t(r){var n;return qN(this,t),n=LN(this,t,[r]),n.state={fontSize:"",letterSpacing:""},n}return UN(t,e),BN(t,[{key:"shouldComponentUpdate",value:function(n,i){var a=n.viewBox,o=zc(n,kN),u=this.props,s=u.viewBox,c=zc(u,NN);return!hr(a,s)||!hr(o,c)||!hr(i,this.state)}},{key:"componentDidMount",value:function(){var n=this.layerReference;if(n){var i=n.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];i&&this.setState({fontSize:window.getComputedStyle(i).fontSize,letterSpacing:window.getComputedStyle(i).letterSpacing})}}},{key:"getTickLineCoord",value:function(n){var i=this.props,a=i.x,o=i.y,u=i.width,s=i.height,c=i.orientation,f=i.tickSize,l=i.mirror,h=i.tickMargin,p,y,v,d,x,w,b=l?-1:1,O=n.tickSize||f,m=B(n.tickCoord)?n.tickCoord:n.coordinate;switch(c){case"top":p=y=n.coordinate,d=o+ +!l*s,v=d-b*O,w=v-b*h,x=m;break;case"left":v=d=n.coordinate,y=a+ +!l*u,p=y-b*O,x=p-b*h,w=m;break;case"right":v=d=n.coordinate,y=a+ +l*u,p=y+b*O,x=p+b*h,w=m;break;default:p=y=n.coordinate,d=o+ +l*s,v=d+b*O,w=v+b*h,x=m;break}return{line:{x1:p,y1:v,x2:y,y2:d},tick:{x,y:w}}}},{key:"getTickTextAnchor",value:function(){var n=this.props,i=n.orientation,a=n.mirror,o;switch(i){case"left":o=a?"start":"end";break;case"right":o=a?"end":"start";break;default:o="middle";break}return o}},{key:"getTickVerticalAnchor",value:function(){var n=this.props,i=n.orientation,a=n.mirror,o="end";switch(i){case"left":case"right":o="middle";break;case"top":o=a?"start":"end";break;default:o=a?"end":"start";break}return o}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,s=n.orientation,c=n.mirror,f=n.axisLine,l=Me(Me(Me({},ee(this.props,!1)),ee(f,!1)),{},{fill:"none"});if(s==="top"||s==="bottom"){var h=+(s==="top"&&!c||s==="bottom"&&c);l=Me(Me({},l),{},{x1:i,y1:a+h*u,x2:i+o,y2:a+h*u})}else{var p=+(s==="left"&&!c||s==="right"&&c);l=Me(Me({},l),{},{x1:i+p*o,y1:a,x2:i+p*o,y2:a+u})}return T.createElement("line",lr({},l,{className:ie("recharts-cartesian-axis-line",Ue(f,"className"))}))}},{key:"renderTicks",value:function(n,i,a){var o=this,u=this.props,s=u.tickLine,c=u.stroke,f=u.tick,l=u.tickFormatter,h=u.unit,p=hh(Me(Me({},this.props),{},{ticks:n}),i,a),y=this.getTickTextAnchor(),v=this.getTickVerticalAnchor(),d=ee(this.props,!1),x=ee(f,!1),w=Me(Me({},d),{},{fill:"none"},ee(s,!1)),b=p.map(function(O,m){var g=o.getTickLineCoord(O),_=g.line,S=g.tick,P=Me(Me(Me(Me({textAnchor:y,verticalAnchor:v},d),{},{stroke:"none",fill:c},x),S),{},{index:m,payload:O,visibleTicksCount:p.length,tickFormatter:l});return T.createElement(_e,lr({className:"recharts-cartesian-axis-tick",key:"tick-".concat(O.value,"-").concat(O.coordinate,"-").concat(O.tickCoord)},mi(o.props,O,m)),s&&T.createElement("line",lr({},w,_,{className:ie("recharts-cartesian-axis-tick-line",Ue(s,"className"))})),f&&t.renderTickItem(f,P,"".concat(Z(l)?l(O.value,m):O.value).concat(h||"")))});return T.createElement("g",{className:"recharts-cartesian-axis-ticks"},b)}},{key:"render",value:function(){var n=this,i=this.props,a=i.axisLine,o=i.width,u=i.height,s=i.ticksGenerator,c=i.className,f=i.hide;if(f)return null;var l=this.props,h=l.ticks,p=zc(l,RN),y=h;return Z(s)&&(y=h&&h.length>0?s(this.props):s(p)),o<=0||u<=0||!y||!y.length?null:T.createElement(_e,{className:ie("recharts-cartesian-axis",c),ref:function(d){n.layerReference=d}},a&&this.renderAxisLine(),this.renderTicks(y,this.state.fontSize,this.state.letterSpacing),Ee.renderCallByParent(this.props))}}],[{key:"renderTickItem",value:function(n,i,a){var o;return T.isValidElement(n)?o=T.cloneElement(n,i):Z(n)?o=n(i):o=T.createElement(Ei,lr({},i,{className:"recharts-cartesian-axis-tick-value"}),a),o}}])}(q.Component);dh(Ur,"displayName","CartesianAxis");dh(Ur,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var HN=["x1","y1","x2","y2","key"],GN=["offset"];function Vt(e){"@babel/helpers - typeof";return Vt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Vt(e)}function Kg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function je(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Kg(Object(r),!0).forEach(function(n){KN(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Kg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function KN(e,t,r){return t=VN(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function VN(e){var t=XN(e,"string");return Vt(t)=="symbol"?t:t+""}function XN(e,t){if(Vt(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Vt(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Ft(){return Ft=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ft.apply(this,arguments)}function Vg(e,t){if(e==null)return{};var r=YN(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function YN(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var ZN=function(t){var r=t.fill;if(!r||r==="none")return null;var n=t.fillOpacity,i=t.x,a=t.y,o=t.width,u=t.height,s=t.ry;return T.createElement("rect",{x:i,y:a,ry:s,width:o,height:u,stroke:"none",fill:r,fillOpacity:n,className:"recharts-cartesian-grid-bg"})};function tw(e,t){var r;if(T.isValidElement(e))r=T.cloneElement(e,t);else if(Z(e))r=e(t);else{var n=t.x1,i=t.y1,a=t.x2,o=t.y2,u=t.key,s=Vg(t,HN),c=ee(s,!1);c.offset;var f=Vg(c,GN);r=T.createElement("line",Ft({},f,{x1:n,y1:i,x2:a,y2:o,fill:"none",key:u}))}return r}function JN(e){var t=e.x,r=e.width,n=e.horizontal,i=n===void 0?!0:n,a=e.horizontalPoints;if(!i||!a||!a.length)return null;var o=a.map(function(u,s){var c=je(je({},e),{},{x1:t,y1:u,x2:t+r,y2:u,key:"line-".concat(s),index:s});return tw(i,c)});return T.createElement("g",{className:"recharts-cartesian-grid-horizontal"},o)}function QN(e){var t=e.y,r=e.height,n=e.vertical,i=n===void 0?!0:n,a=e.verticalPoints;if(!i||!a||!a.length)return null;var o=a.map(function(u,s){var c=je(je({},e),{},{x1:u,y1:t,x2:u,y2:t+r,key:"line-".concat(s),index:s});return tw(i,c)});return T.createElement("g",{className:"recharts-cartesian-grid-vertical"},o)}function eR(e){var t=e.horizontalFill,r=e.fillOpacity,n=e.x,i=e.y,a=e.width,o=e.height,u=e.horizontalPoints,s=e.horizontal,c=s===void 0?!0:s;if(!c||!t||!t.length)return null;var f=u.map(function(h){return Math.round(h+i-i)}).sort(function(h,p){return h-p});i!==f[0]&&f.unshift(0);var l=f.map(function(h,p){var y=!f[p+1],v=y?i+o-h:f[p+1]-h;if(v<=0)return null;var d=p%t.length;return T.createElement("rect",{key:"react-".concat(p),y:h,x:n,height:v,width:a,stroke:"none",fill:t[d],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return T.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},l)}function tR(e){var t=e.vertical,r=t===void 0?!0:t,n=e.verticalFill,i=e.fillOpacity,a=e.x,o=e.y,u=e.width,s=e.height,c=e.verticalPoints;if(!r||!n||!n.length)return null;var f=c.map(function(h){return Math.round(h+a-a)}).sort(function(h,p){return h-p});a!==f[0]&&f.unshift(0);var l=f.map(function(h,p){var y=!f[p+1],v=y?a+u-h:f[p+1]-h;if(v<=0)return null;var d=p%n.length;return T.createElement("rect",{key:"react-".concat(p),x:h,y:o,width:v,height:s,stroke:"none",fill:n[d],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return T.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},l)}var rR=function(t,r){var n=t.xAxis,i=t.width,a=t.height,o=t.offset;return px(hh(je(je(je({},Ur.defaultProps),n),{},{ticks:ct(n,!0),viewBox:{x:0,y:0,width:i,height:a}})),o.left,o.left+o.width,r)},nR=function(t,r){var n=t.yAxis,i=t.width,a=t.height,o=t.offset;return px(hh(je(je(je({},Ur.defaultProps),n),{},{ticks:ct(n,!0),viewBox:{x:0,y:0,width:i,height:a}})),o.top,o.top+o.height,r)},ar={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function rw(e){var t,r,n,i,a,o,u=sh(),s=ch(),c=Wk(),f=je(je({},e),{},{stroke:(t=e.stroke)!==null&&t!==void 0?t:ar.stroke,fill:(r=e.fill)!==null&&r!==void 0?r:ar.fill,horizontal:(n=e.horizontal)!==null&&n!==void 0?n:ar.horizontal,horizontalFill:(i=e.horizontalFill)!==null&&i!==void 0?i:ar.horizontalFill,vertical:(a=e.vertical)!==null&&a!==void 0?a:ar.vertical,verticalFill:(o=e.verticalFill)!==null&&o!==void 0?o:ar.verticalFill,x:B(e.x)?e.x:c.left,y:B(e.y)?e.y:c.top,width:B(e.width)?e.width:c.width,height:B(e.height)?e.height:c.height}),l=f.x,h=f.y,p=f.width,y=f.height,v=f.syncWithTicks,d=f.horizontalValues,x=f.verticalValues,w=Fk(),b=zk();if(!B(p)||p<=0||!B(y)||y<=0||!B(l)||l!==+l||!B(h)||h!==+h)return null;var O=f.verticalCoordinatesGenerator||rR,m=f.horizontalCoordinatesGenerator||nR,g=f.horizontalPoints,_=f.verticalPoints;if((!g||!g.length)&&Z(m)){var S=d&&d.length,P=m({yAxis:b?je(je({},b),{},{ticks:S?d:b.ticks}):void 0,width:u,height:s,offset:c},S?!0:v);ft(Array.isArray(P),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(Vt(P),"]")),Array.isArray(P)&&(g=P)}if((!_||!_.length)&&Z(O)){var C=x&&x.length,A=O({xAxis:w?je(je({},w),{},{ticks:C?x:w.ticks}):void 0,width:u,height:s,offset:c},C?!0:v);ft(Array.isArray(A),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(Vt(A),"]")),Array.isArray(A)&&(_=A)}return T.createElement("g",{className:"recharts-cartesian-grid"},T.createElement(ZN,{fill:f.fill,fillOpacity:f.fillOpacity,x:f.x,y:f.y,width:f.width,height:f.height,ry:f.ry}),T.createElement(JN,Ft({},f,{offset:c,horizontalPoints:g,xAxis:w,yAxis:b})),T.createElement(QN,Ft({},f,{offset:c,verticalPoints:_,xAxis:w,yAxis:b})),T.createElement(eR,Ft({},f,{horizontalPoints:g})),T.createElement(tR,Ft({},f,{verticalPoints:_})))}rw.displayName="CartesianGrid";function Ir(e){"@babel/helpers - typeof";return Ir=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ir(e)}function iR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function aR(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,aw(n.key),n)}}function oR(e,t,r){return t&&aR(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function uR(e,t,r){return t=ua(t),sR(e,nw()?Reflect.construct(t,r||[],ua(e).constructor):t.apply(e,r))}function sR(e,t){if(t&&(Ir(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return cR(e)}function cR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function nw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(nw=function(){return!!e})()}function ua(e){return ua=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ua(e)}function lR(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ql(e,t)}function Ql(e,t){return Ql=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Ql(e,t)}function iw(e,t,r){return t=aw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function aw(e){var t=fR(e,"string");return Ir(t)=="symbol"?t:t+""}function fR(e,t){if(Ir(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Ir(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function ef(){return ef=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ef.apply(this,arguments)}function hR(e){var t=e.xAxisId,r=sh(),n=ch(),i=Wx(t);return i==null?null:T.createElement(Ur,ef({},i,{className:ie("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(o){return ct(o,!0)}}))}var Fa=function(e){function t(){return iR(this,t),uR(this,t,arguments)}return lR(t,e),oR(t,[{key:"render",value:function(){return T.createElement(hR,this.props)}}])}(T.Component);iw(Fa,"displayName","XAxis");iw(Fa,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});function kr(e){"@babel/helpers - typeof";return kr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},kr(e)}function dR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function pR(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,sw(n.key),n)}}function vR(e,t,r){return t&&pR(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function yR(e,t,r){return t=sa(t),mR(e,ow()?Reflect.construct(t,r||[],sa(e).constructor):t.apply(e,r))}function mR(e,t){if(t&&(kr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return gR(e)}function gR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ow(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(ow=function(){return!!e})()}function sa(e){return sa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},sa(e)}function bR(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&tf(e,t)}function tf(e,t){return tf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},tf(e,t)}function uw(e,t,r){return t=sw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function sw(e){var t=xR(e,"string");return kr(t)=="symbol"?t:t+""}function xR(e,t){if(kr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(kr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function rf(){return rf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},rf.apply(this,arguments)}var wR=function(t){var r=t.yAxisId,n=sh(),i=ch(),a=Hx(r);return a==null?null:T.createElement(Ur,rf({},a,{className:ie("recharts-".concat(a.axisType," ").concat(a.axisType),a.className),viewBox:{x:0,y:0,width:n,height:i},ticksGenerator:function(u){return ct(u,!0)}}))},za=function(e){function t(){return dR(this,t),yR(this,t,arguments)}return bR(t,e),vR(t,[{key:"render",value:function(){return T.createElement(wR,this.props)}}])}(T.Component);uw(za,"displayName","YAxis");uw(za,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});function Xg(e){return AR(e)||SR(e)||_R(e)||OR()}function OR(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function _R(e,t){if(e){if(typeof e=="string")return nf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nf(e,t)}}function SR(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function AR(e){if(Array.isArray(e))return nf(e)}function nf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var af=function(t,r,n,i,a){var o=Ze(t,fh),u=Ze(t,qa),s=[].concat(Xg(o),Xg(u)),c=Ze(t,La),f="".concat(i,"Id"),l=i[0],h=r;if(s.length&&(h=s.reduce(function(v,d){if(d.props[f]===n&&rt(d.props,"extendDomain")&&B(d.props[l])){var x=d.props[l];return[Math.min(v[0],x),Math.max(v[1],x)]}return v},h)),c.length){var p="".concat(l,"1"),y="".concat(l,"2");h=c.reduce(function(v,d){if(d.props[f]===n&&rt(d.props,"extendDomain")&&B(d.props[p])&&B(d.props[y])){var x=d.props[p],w=d.props[y];return[Math.min(v[0],x,w),Math.max(v[1],x,w)]}return v},h)}return a&&a.length&&(h=a.reduce(function(v,d){return B(d)?[Math.min(v[0],d),Math.max(v[1],d)]:v},h)),h},Uc={exports:{}},Yg;function PR(){return Yg||(Yg=1,function(e){var t=Object.prototype.hasOwnProperty,r="~";function n(){}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1));function i(s,c,f){this.fn=s,this.context=c,this.once=f||!1}function a(s,c,f,l,h){if(typeof f!="function")throw new TypeError("The listener must be a function");var p=new i(f,l||s,h),y=r?r+c:c;return s._events[y]?s._events[y].fn?s._events[y]=[s._events[y],p]:s._events[y].push(p):(s._events[y]=p,s._eventsCount++),s}function o(s,c){--s._eventsCount===0?s._events=new n:delete s._events[c]}function u(){this._events=new n,this._eventsCount=0}u.prototype.eventNames=function(){var c=[],f,l;if(this._eventsCount===0)return c;for(l in f=this._events)t.call(f,l)&&c.push(r?l.slice(1):l);return Object.getOwnPropertySymbols?c.concat(Object.getOwnPropertySymbols(f)):c},u.prototype.listeners=function(c){var f=r?r+c:c,l=this._events[f];if(!l)return[];if(l.fn)return[l.fn];for(var h=0,p=l.length,y=new Array(p);h<p;h++)y[h]=l[h].fn;return y},u.prototype.listenerCount=function(c){var f=r?r+c:c,l=this._events[f];return l?l.fn?1:l.length:0},u.prototype.emit=function(c,f,l,h,p,y){var v=r?r+c:c;if(!this._events[v])return!1;var d=this._events[v],x=arguments.length,w,b;if(d.fn){switch(d.once&&this.removeListener(c,d.fn,void 0,!0),x){case 1:return d.fn.call(d.context),!0;case 2:return d.fn.call(d.context,f),!0;case 3:return d.fn.call(d.context,f,l),!0;case 4:return d.fn.call(d.context,f,l,h),!0;case 5:return d.fn.call(d.context,f,l,h,p),!0;case 6:return d.fn.call(d.context,f,l,h,p,y),!0}for(b=1,w=new Array(x-1);b<x;b++)w[b-1]=arguments[b];d.fn.apply(d.context,w)}else{var O=d.length,m;for(b=0;b<O;b++)switch(d[b].once&&this.removeListener(c,d[b].fn,void 0,!0),x){case 1:d[b].fn.call(d[b].context);break;case 2:d[b].fn.call(d[b].context,f);break;case 3:d[b].fn.call(d[b].context,f,l);break;case 4:d[b].fn.call(d[b].context,f,l,h);break;default:if(!w)for(m=1,w=new Array(x-1);m<x;m++)w[m-1]=arguments[m];d[b].fn.apply(d[b].context,w)}}return!0},u.prototype.on=function(c,f,l){return a(this,c,f,l,!1)},u.prototype.once=function(c,f,l){return a(this,c,f,l,!0)},u.prototype.removeListener=function(c,f,l,h){var p=r?r+c:c;if(!this._events[p])return this;if(!f)return o(this,p),this;var y=this._events[p];if(y.fn)y.fn===f&&(!h||y.once)&&(!l||y.context===l)&&o(this,p);else{for(var v=0,d=[],x=y.length;v<x;v++)(y[v].fn!==f||h&&!y[v].once||l&&y[v].context!==l)&&d.push(y[v]);d.length?this._events[p]=d.length===1?d[0]:d:o(this,p)}return this},u.prototype.removeAllListeners=function(c){var f;return c?(f=r?r+c:c,this._events[f]&&o(this,f)):(this._events=new n,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=r,u.EventEmitter=u,e.exports=u}(Uc)),Uc.exports}var TR=PR();const ER=se(TR);var Wc=new ER,Hc="recharts.syncMouseEvents";function zn(e){"@babel/helpers - typeof";return zn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},zn(e)}function jR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function MR(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,cw(n.key),n)}}function CR(e,t,r){return t&&MR(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function Gc(e,t,r){return t=cw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function cw(e){var t=$R(e,"string");return zn(t)=="symbol"?t:t+""}function $R(e,t){if(zn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(zn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var IR=function(){function e(){jR(this,e),Gc(this,"activeIndex",0),Gc(this,"coordinateList",[]),Gc(this,"layout","horizontal")}return CR(e,[{key:"setDetails",value:function(r){var n,i=r.coordinateList,a=i===void 0?null:i,o=r.container,u=o===void 0?null:o,s=r.layout,c=s===void 0?null:s,f=r.offset,l=f===void 0?null:f,h=r.mouseHandlerCallback,p=h===void 0?null:h;this.coordinateList=(n=a??this.coordinateList)!==null&&n!==void 0?n:[],this.container=u??this.container,this.layout=c??this.layout,this.offset=l??this.offset,this.mouseHandlerCallback=p??this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(r){if(this.coordinateList.length!==0)switch(r.key){case"ArrowRight":{if(this.layout!=="horizontal")return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break}case"ArrowLeft":{if(this.layout!=="horizontal")return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse();break}}}},{key:"setIndex",value:function(r){this.activeIndex=r}},{key:"spoofMouse",value:function(){var r,n;if(this.layout==="horizontal"&&this.coordinateList.length!==0){var i=this.container.getBoundingClientRect(),a=i.x,o=i.y,u=i.height,s=this.coordinateList[this.activeIndex].coordinate,c=((r=window)===null||r===void 0?void 0:r.scrollX)||0,f=((n=window)===null||n===void 0?void 0:n.scrollY)||0,l=a+s+c,h=o+this.offset.top+u/2+f;this.mouseHandlerCallback({pageX:l,pageY:h})}}}])}();function kR(e,t,r){if(r==="number"&&t===!0&&Array.isArray(e)){var n=e?.[0],i=e?.[1];if(n&&i&&B(n)&&B(i))return!0}return!1}function NR(e,t,r,n){var i=n/2;return{stroke:"none",fill:"#ccc",x:e==="horizontal"?t.x-i:r.left+.5,y:e==="horizontal"?r.top+.5:t.y-i,width:e==="horizontal"?n:r.width-1,height:e==="horizontal"?r.height-1:n}}function lw(e){var t=e.cx,r=e.cy,n=e.radius,i=e.startAngle,a=e.endAngle,o=Pe(t,r,n,i),u=Pe(t,r,n,a);return{points:[o,u],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}function RR(e,t,r){var n,i,a,o;if(e==="horizontal")n=t.x,a=n,i=r.top,o=r.top+r.height;else if(e==="vertical")i=t.y,o=i,n=r.left,a=r.left+r.width;else if(t.cx!=null&&t.cy!=null)if(e==="centric"){var u=t.cx,s=t.cy,c=t.innerRadius,f=t.outerRadius,l=t.angle,h=Pe(u,s,c,l),p=Pe(u,s,f,l);n=h.x,i=h.y,a=p.x,o=p.y}else return lw(t);return[{x:n,y:i},{x:a,y:o}]}function Un(e){"@babel/helpers - typeof";return Un=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Un(e)}function Zg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function pi(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Zg(Object(r),!0).forEach(function(n){DR(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Zg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function DR(e,t,r){return t=qR(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function qR(e){var t=BR(e,"string");return Un(t)=="symbol"?t:t+""}function BR(e,t){if(Un(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Un(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function LR(e){var t,r,n=e.element,i=e.tooltipEventType,a=e.isActive,o=e.activeCoordinate,u=e.activePayload,s=e.offset,c=e.activeTooltipIndex,f=e.tooltipAxisBandSize,l=e.layout,h=e.chartName,p=(t=n.props.cursor)!==null&&t!==void 0?t:(r=n.type.defaultProps)===null||r===void 0?void 0:r.cursor;if(!n||!p||!a||!o||h!=="ScatterChart"&&i!=="axis")return null;var y,v=Em;if(h==="ScatterChart")y=o,v=Z$;else if(h==="BarChart")y=NR(l,o,s,f),v=ih;else if(l==="radial"){var d=lw(o),x=d.cx,w=d.cy,b=d.radius,O=d.startAngle,m=d.endAngle;y={cx:x,cy:w,startAngle:O,endAngle:m,innerRadius:b,outerRadius:b},v=bx}else y={points:RR(l,o,s)},v=Em;var g=pi(pi(pi(pi({stroke:"#ccc",pointerEvents:"none"},s),y),ee(p,!1)),{},{payload:u,payloadIndex:c,className:ie("recharts-tooltip-cursor",p.className)});return q.isValidElement(p)?q.cloneElement(p,g):q.createElement(v,g)}var FR=["item"],zR=["children","className","width","height","style","compact","title","desc"];function Nr(e){"@babel/helpers - typeof";return Nr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Nr(e)}function fr(){return fr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},fr.apply(this,arguments)}function Jg(e,t){return HR(e)||WR(e,t)||hw(e,t)||UR()}function UR(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function WR(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],s=!0,c=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);s=!0);}catch(f){c=!0,i=f}finally{try{if(!s&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return u}}function HR(e){if(Array.isArray(e))return e}function Qg(e,t){if(e==null)return{};var r=GR(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function GR(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function KR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function VR(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,dw(n.key),n)}}function XR(e,t,r){return t&&VR(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function YR(e,t,r){return t=ca(t),ZR(e,fw()?Reflect.construct(t,r||[],ca(e).constructor):t.apply(e,r))}function ZR(e,t){if(t&&(Nr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return JR(e)}function JR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function fw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(fw=function(){return!!e})()}function ca(e){return ca=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ca(e)}function QR(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&of(e,t)}function of(e,t){return of=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},of(e,t)}function Rr(e){return rD(e)||tD(e)||hw(e)||eD()}function eD(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function hw(e,t){if(e){if(typeof e=="string")return uf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uf(e,t)}}function tD(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function rD(e){if(Array.isArray(e))return uf(e)}function uf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function eb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function $(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?eb(Object(r),!0).forEach(function(n){G(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eb(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function G(e,t,r){return t=dw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dw(e){var t=nD(e,"string");return Nr(t)=="symbol"?t:t+""}function nD(e,t){if(Nr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Nr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var iD={xAxis:["bottom","top"],yAxis:["left","right"]},aD={width:"100%",height:"100%"},pw={x:0,y:0};function vi(e){return e}var oD=function(t,r){return r==="horizontal"?t.x:r==="vertical"?t.y:r==="centric"?t.angle:t.radius},uD=function(t,r,n,i){var a=r.find(function(f){return f&&f.index===n});if(a){if(t==="horizontal")return{x:a.coordinate,y:i.y};if(t==="vertical")return{x:i.x,y:a.coordinate};if(t==="centric"){var o=a.coordinate,u=i.radius;return $($($({},i),Pe(i.cx,i.cy,u,o)),{},{angle:o,radius:u})}var s=a.coordinate,c=i.angle;return $($($({},i),Pe(i.cx,i.cy,s,c)),{},{angle:c,radius:s})}return pw},Ua=function(t,r){var n=r.graphicalItems,i=r.dataStartIndex,a=r.dataEndIndex,o=(n??[]).reduce(function(u,s){var c=s.props.data;return c&&c.length?[].concat(Rr(u),Rr(c)):u},[]);return o.length>0?o:t&&t.length&&B(i)&&B(a)?t.slice(i,a+1):[]};function vw(e){return e==="number"?[0,"auto"]:void 0}var sf=function(t,r,n,i){var a=t.graphicalItems,o=t.tooltipAxis,u=Ua(r,t);return n<0||!a||!a.length||n>=u.length?null:a.reduce(function(s,c){var f,l=(f=c.props.data)!==null&&f!==void 0?f:r;l&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=n&&(l=l.slice(t.dataStartIndex,t.dataEndIndex+1));var h;if(o.dataKey&&!o.allowDuplicatedCategory){var p=l===void 0?u:l;h=Kc(p,o.dataKey,i)}else h=l&&l[n]||u[n];return h?[].concat(Rr(s),[yx(c,h)]):s},[])},tb=function(t,r,n,i){var a=i||{x:t.chartX,y:t.chartY},o=oD(a,n),u=t.orderedTooltipTicks,s=t.tooltipAxis,c=t.tooltipTicks,f=kj(o,u,c,s);if(f>=0&&c){var l=c[f]&&c[f].value,h=sf(t,r,f,l),p=uD(n,u,f,a);return{activeTooltipIndex:f,activeLabel:l,activePayload:h,activeCoordinate:p}}return null},sD=function(t,r){var n=r.axes,i=r.graphicalItems,a=r.axisType,o=r.axisIdKey,u=r.stackGroups,s=r.dataStartIndex,c=r.dataEndIndex,f=t.layout,l=t.children,h=t.stackOffset,p=dx(f,a);return n.reduce(function(y,v){var d,x=v.type.defaultProps!==void 0?$($({},v.type.defaultProps),v.props):v.props,w=x.type,b=x.dataKey,O=x.allowDataOverflow,m=x.allowDuplicatedCategory,g=x.scale,_=x.ticks,S=x.includeHidden,P=x[o];if(y[P])return y;var C=Ua(t.data,{graphicalItems:i.filter(function(U){var V,ce=o in U.props?U.props[o]:(V=U.type.defaultProps)===null||V===void 0?void 0:V[o];return ce===P}),dataStartIndex:s,dataEndIndex:c}),A=C.length,E,j,k;kR(x.domain,O,w)&&(E=Tl(x.domain,null,O),p&&(w==="number"||g!=="auto")&&(k=un(C,b,"category")));var I=vw(w);if(!E||E.length===0){var N,D=(N=x.domain)!==null&&N!==void 0?N:I;if(b){if(E=un(C,b,w),w==="category"&&p){var L=P1(E);m&&L?(j=E,E=Zi(0,A)):m||(E=pm(D,E,v).reduce(function(U,V){return U.indexOf(V)>=0?U:[].concat(Rr(U),[V])},[]))}else if(w==="category")m?E=E.filter(function(U){return U!==""&&!ne(U)}):E=pm(D,E,v).reduce(function(U,V){return U.indexOf(V)>=0||V===""||ne(V)?U:[].concat(Rr(U),[V])},[]);else if(w==="number"){var F=Bj(C,i.filter(function(U){var V,ce,pe=o in U.props?U.props[o]:(V=U.type.defaultProps)===null||V===void 0?void 0:V[o],Re="hide"in U.props?U.props.hide:(ce=U.type.defaultProps)===null||ce===void 0?void 0:ce.hide;return pe===P&&(S||!Re)}),b,a,f);F&&(E=F)}p&&(w==="number"||g!=="auto")&&(k=un(C,b,"category"))}else p?E=Zi(0,A):u&&u[P]&&u[P].hasStack&&w==="number"?E=h==="expand"?[0,1]:vx(u[P].stackGroups,s,c):E=hx(C,i.filter(function(U){var V=o in U.props?U.props[o]:U.type.defaultProps[o],ce="hide"in U.props?U.props.hide:U.type.defaultProps.hide;return V===P&&(S||!ce)}),w,f,!0);if(w==="number")E=af(l,E,P,a,_),D&&(E=Tl(D,E,O));else if(w==="category"&&D){var H=D,K=E.every(function(U){return H.indexOf(U)>=0});K&&(E=H)}}return $($({},y),{},G({},P,$($({},x),{},{axisType:a,domain:E,categoricalDomain:k,duplicateDomain:j,originalDomain:(d=x.domain)!==null&&d!==void 0?d:I,isCategorical:p,layout:f})))},{})},cD=function(t,r){var n=r.graphicalItems,i=r.Axis,a=r.axisType,o=r.axisIdKey,u=r.stackGroups,s=r.dataStartIndex,c=r.dataEndIndex,f=t.layout,l=t.children,h=Ua(t.data,{graphicalItems:n,dataStartIndex:s,dataEndIndex:c}),p=h.length,y=dx(f,a),v=-1;return n.reduce(function(d,x){var w=x.type.defaultProps!==void 0?$($({},x.type.defaultProps),x.props):x.props,b=w[o],O=vw("number");if(!d[b]){v++;var m;return y?m=Zi(0,p):u&&u[b]&&u[b].hasStack?(m=vx(u[b].stackGroups,s,c),m=af(l,m,b,a)):(m=Tl(O,hx(h,n.filter(function(g){var _,S,P=o in g.props?g.props[o]:(_=g.type.defaultProps)===null||_===void 0?void 0:_[o],C="hide"in g.props?g.props.hide:(S=g.type.defaultProps)===null||S===void 0?void 0:S.hide;return P===b&&!C}),"number",f),i.defaultProps.allowDataOverflow),m=af(l,m,b,a)),$($({},d),{},G({},b,$($({axisType:a},i.defaultProps),{},{hide:!0,orientation:Ue(iD,"".concat(a,".").concat(v%2),null),domain:m,originalDomain:O,isCategorical:y,layout:f})))}return d},{})},lD=function(t,r){var n=r.axisType,i=n===void 0?"xAxis":n,a=r.AxisComp,o=r.graphicalItems,u=r.stackGroups,s=r.dataStartIndex,c=r.dataEndIndex,f=t.children,l="".concat(i,"Id"),h=Ze(f,a),p={};return h&&h.length?p=sD(t,{axes:h,graphicalItems:o,axisType:i,axisIdKey:l,stackGroups:u,dataStartIndex:s,dataEndIndex:c}):o&&o.length&&(p=cD(t,{Axis:a,graphicalItems:o,axisType:i,axisIdKey:l,stackGroups:u,dataStartIndex:s,dataEndIndex:c})),p},fD=function(t){var r=Ot(t),n=ct(r,!1,!0);return{tooltipTicks:n,orderedTooltipTicks:Mf(n,function(i){return i.coordinate}),tooltipAxis:r,tooltipAxisBandSize:zi(r,n)}},rb=function(t){var r=t.children,n=t.defaultShowTooltip,i=qe(r,Tr),a=0,o=0;return t.data&&t.data.length!==0&&(o=t.data.length-1),i&&i.props&&(i.props.startIndex>=0&&(a=i.props.startIndex),i.props.endIndex>=0&&(o=i.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:a,dataEndIndex:o,activeTooltipIndex:-1,isTooltipActive:!!n}},hD=function(t){return!t||!t.length?!1:t.some(function(r){var n=lt(r&&r.type);return n&&n.indexOf("Bar")>=0})},nb=function(t){return t==="horizontal"?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:t==="vertical"?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:t==="centric"?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},dD=function(t,r){var n=t.props,i=t.graphicalItems,a=t.xAxisMap,o=a===void 0?{}:a,u=t.yAxisMap,s=u===void 0?{}:u,c=n.width,f=n.height,l=n.children,h=n.margin||{},p=qe(l,Tr),y=qe(l,dr),v=Object.keys(s).reduce(function(m,g){var _=s[g],S=_.orientation;return!_.mirror&&!_.hide?$($({},m),{},G({},S,m[S]+_.width)):m},{left:h.left||0,right:h.right||0}),d=Object.keys(o).reduce(function(m,g){var _=o[g],S=_.orientation;return!_.mirror&&!_.hide?$($({},m),{},G({},S,Ue(m,"".concat(S))+_.height)):m},{top:h.top||0,bottom:h.bottom||0}),x=$($({},d),v),w=x.bottom;p&&(x.bottom+=p.props.height||Tr.defaultProps.height),y&&r&&(x=Dj(x,i,n,r));var b=c-x.left-x.right,O=f-x.top-x.bottom;return $($({brushBottom:w},x),{},{width:Math.max(b,0),height:Math.max(O,0)})},pD=function(t,r){if(r==="xAxis")return t[r].width;if(r==="yAxis")return t[r].height},vD=function(t){var r=t.chartName,n=t.GraphicalChild,i=t.defaultTooltipEventType,a=i===void 0?"axis":i,o=t.validateTooltipEventTypes,u=o===void 0?["axis"]:o,s=t.axisComponents,c=t.legendContent,f=t.formatAxisMap,l=t.defaultProps,h=function(x,w){var b=w.graphicalItems,O=w.stackGroups,m=w.offset,g=w.updateId,_=w.dataStartIndex,S=w.dataEndIndex,P=x.barSize,C=x.layout,A=x.barGap,E=x.barCategoryGap,j=x.maxBarSize,k=nb(C),I=k.numericAxisName,N=k.cateAxisName,D=hD(b),L=[];return b.forEach(function(F,H){var K=Ua(x.data,{graphicalItems:[F],dataStartIndex:_,dataEndIndex:S}),U=F.type.defaultProps!==void 0?$($({},F.type.defaultProps),F.props):F.props,V=U.dataKey,ce=U.maxBarSize,pe=U["".concat(I,"Id")],Re=U["".concat(N,"Id")],Ct={},$e=s.reduce(function($t,It){var Wa=w["".concat(It.axisType,"Map")],ph=U["".concat(It.axisType,"Id")];Wa&&Wa[ph]||It.axisType==="zAxis"||Wt(!1);var vh=Wa[ph];return $($({},$t),{},G(G({},It.axisType,vh),"".concat(It.axisType,"Ticks"),ct(vh)))},Ct),z=$e[N],X=$e["".concat(N,"Ticks")],Y=O&&O[pe]&&O[pe].hasStack&&Zj(F,O[pe].stackGroups),R=lt(F.type).indexOf("Bar")>=0,he=zi(z,X),J=[],me=D&&Nj({barSize:P,stackGroups:O,totalSize:pD($e,N)});if(R){var ge,Ie,xt=ne(ce)?j:ce,er=(ge=(Ie=zi(z,X,!0))!==null&&Ie!==void 0?Ie:xt)!==null&&ge!==void 0?ge:0;J=Rj({barGap:A,barCategoryGap:E,bandSize:er!==he?er:he,sizeList:me[Re],maxBarSize:xt}),er!==he&&(J=J.map(function($t){return $($({},$t),{},{position:$($({},$t.position),{},{offset:$t.position.offset-er/2})})}))}var Jn=F&&F.type&&F.type.getComposedData;Jn&&L.push({props:$($({},Jn($($({},$e),{},{displayedData:K,props:x,dataKey:V,item:F,bandSize:he,barPosition:J,offset:m,stackedData:Y,layout:C,dataStartIndex:_,dataEndIndex:S}))),{},G(G(G({key:F.key||"item-".concat(H)},I,$e[I]),N,$e[N]),"animationId",g)),childIndex:q1(F,x.children),item:F})}),L},p=function(x,w){var b=x.props,O=x.dataStartIndex,m=x.dataEndIndex,g=x.updateId;if(!jd({props:b}))return null;var _=b.children,S=b.layout,P=b.stackOffset,C=b.data,A=b.reverseStackOrder,E=nb(S),j=E.numericAxisName,k=E.cateAxisName,I=Ze(_,n),N=Vj(C,I,"".concat(j,"Id"),"".concat(k,"Id"),P,A),D=s.reduce(function(U,V){var ce="".concat(V.axisType,"Map");return $($({},U),{},G({},ce,lD(b,$($({},V),{},{graphicalItems:I,stackGroups:V.axisType===j&&N,dataStartIndex:O,dataEndIndex:m}))))},{}),L=dD($($({},D),{},{props:b,graphicalItems:I}),w?.legendBBox);Object.keys(D).forEach(function(U){D[U]=f(b,D[U],L,U.replace("Map",""),r)});var F=D["".concat(k,"Map")],H=fD(F),K=h(b,$($({},D),{},{dataStartIndex:O,dataEndIndex:m,updateId:g,graphicalItems:I,stackGroups:N,offset:L}));return $($({formattedGraphicalItems:K,graphicalItems:I,offset:L,stackGroups:N},H),D)},y=function(d){function x(w){var b,O,m;return KR(this,x),m=YR(this,x,[w]),G(m,"eventEmitterSymbol",Symbol("rechartsEventEmitter")),G(m,"accessibilityManager",new IR),G(m,"handleLegendBBoxUpdate",function(g){if(g){var _=m.state,S=_.dataStartIndex,P=_.dataEndIndex,C=_.updateId;m.setState($({legendBBox:g},p({props:m.props,dataStartIndex:S,dataEndIndex:P,updateId:C},$($({},m.state),{},{legendBBox:g}))))}}),G(m,"handleReceiveSyncEvent",function(g,_,S){if(m.props.syncId===g){if(S===m.eventEmitterSymbol&&typeof m.props.syncMethod!="function")return;m.applySyncEvent(_)}}),G(m,"handleBrushChange",function(g){var _=g.startIndex,S=g.endIndex;if(_!==m.state.dataStartIndex||S!==m.state.dataEndIndex){var P=m.state.updateId;m.setState(function(){return $({dataStartIndex:_,dataEndIndex:S},p({props:m.props,dataStartIndex:_,dataEndIndex:S,updateId:P},m.state))}),m.triggerSyncEvent({dataStartIndex:_,dataEndIndex:S})}}),G(m,"handleMouseEnter",function(g){var _=m.getMouseInfo(g);if(_){var S=$($({},_),{},{isTooltipActive:!0});m.setState(S),m.triggerSyncEvent(S);var P=m.props.onMouseEnter;Z(P)&&P(S,g)}}),G(m,"triggeredAfterMouseMove",function(g){var _=m.getMouseInfo(g),S=_?$($({},_),{},{isTooltipActive:!0}):{isTooltipActive:!1};m.setState(S),m.triggerSyncEvent(S);var P=m.props.onMouseMove;Z(P)&&P(S,g)}),G(m,"handleItemMouseEnter",function(g){m.setState(function(){return{isTooltipActive:!0,activeItem:g,activePayload:g.tooltipPayload,activeCoordinate:g.tooltipPosition||{x:g.cx,y:g.cy}}})}),G(m,"handleItemMouseLeave",function(){m.setState(function(){return{isTooltipActive:!1}})}),G(m,"handleMouseMove",function(g){g.persist(),m.throttleTriggeredAfterMouseMove(g)}),G(m,"handleMouseLeave",function(g){m.throttleTriggeredAfterMouseMove.cancel();var _={isTooltipActive:!1};m.setState(_),m.triggerSyncEvent(_);var S=m.props.onMouseLeave;Z(S)&&S(_,g)}),G(m,"handleOuterEvent",function(g){var _=D1(g),S=Ue(m.props,"".concat(_));if(_&&Z(S)){var P,C;/.*touch.*/i.test(_)?C=m.getMouseInfo(g.changedTouches[0]):C=m.getMouseInfo(g),S((P=C)!==null&&P!==void 0?P:{},g)}}),G(m,"handleClick",function(g){var _=m.getMouseInfo(g);if(_){var S=$($({},_),{},{isTooltipActive:!0});m.setState(S),m.triggerSyncEvent(S);var P=m.props.onClick;Z(P)&&P(S,g)}}),G(m,"handleMouseDown",function(g){var _=m.props.onMouseDown;if(Z(_)){var S=m.getMouseInfo(g);_(S,g)}}),G(m,"handleMouseUp",function(g){var _=m.props.onMouseUp;if(Z(_)){var S=m.getMouseInfo(g);_(S,g)}}),G(m,"handleTouchMove",function(g){g.changedTouches!=null&&g.changedTouches.length>0&&m.throttleTriggeredAfterMouseMove(g.changedTouches[0])}),G(m,"handleTouchStart",function(g){g.changedTouches!=null&&g.changedTouches.length>0&&m.handleMouseDown(g.changedTouches[0])}),G(m,"handleTouchEnd",function(g){g.changedTouches!=null&&g.changedTouches.length>0&&m.handleMouseUp(g.changedTouches[0])}),G(m,"handleDoubleClick",function(g){var _=m.props.onDoubleClick;if(Z(_)){var S=m.getMouseInfo(g);_(S,g)}}),G(m,"handleContextMenu",function(g){var _=m.props.onContextMenu;if(Z(_)){var S=m.getMouseInfo(g);_(S,g)}}),G(m,"triggerSyncEvent",function(g){m.props.syncId!==void 0&&Wc.emit(Hc,m.props.syncId,g,m.eventEmitterSymbol)}),G(m,"applySyncEvent",function(g){var _=m.props,S=_.layout,P=_.syncMethod,C=m.state.updateId,A=g.dataStartIndex,E=g.dataEndIndex;if(g.dataStartIndex!==void 0||g.dataEndIndex!==void 0)m.setState($({dataStartIndex:A,dataEndIndex:E},p({props:m.props,dataStartIndex:A,dataEndIndex:E,updateId:C},m.state)));else if(g.activeTooltipIndex!==void 0){var j=g.chartX,k=g.chartY,I=g.activeTooltipIndex,N=m.state,D=N.offset,L=N.tooltipTicks;if(!D)return;if(typeof P=="function")I=P(L,g);else if(P==="value"){I=-1;for(var F=0;F<L.length;F++)if(L[F].value===g.activeLabel){I=F;break}}var H=$($({},D),{},{x:D.left,y:D.top}),K=Math.min(j,H.x+H.width),U=Math.min(k,H.y+H.height),V=L[I]&&L[I].value,ce=sf(m.state,m.props.data,I),pe=L[I]?{x:S==="horizontal"?L[I].coordinate:K,y:S==="horizontal"?U:L[I].coordinate}:pw;m.setState($($({},g),{},{activeLabel:V,activeCoordinate:pe,activePayload:ce,activeTooltipIndex:I}))}else m.setState(g)}),G(m,"renderCursor",function(g){var _,S=m.state,P=S.isTooltipActive,C=S.activeCoordinate,A=S.activePayload,E=S.offset,j=S.activeTooltipIndex,k=S.tooltipAxisBandSize,I=m.getTooltipEventType(),N=(_=g.props.active)!==null&&_!==void 0?_:P,D=m.props.layout,L=g.key||"_recharts-cursor";return T.createElement(LR,{key:L,activeCoordinate:C,activePayload:A,activeTooltipIndex:j,chartName:r,element:g,isActive:N,layout:D,offset:E,tooltipAxisBandSize:k,tooltipEventType:I})}),G(m,"renderPolarAxis",function(g,_,S){var P=Ue(g,"type.axisType"),C=Ue(m.state,"".concat(P,"Map")),A=g.type.defaultProps,E=A!==void 0?$($({},A),g.props):g.props,j=C&&C[E["".concat(P,"Id")]];return q.cloneElement(g,$($({},j),{},{className:ie(P,j.className),key:g.key||"".concat(_,"-").concat(S),ticks:ct(j,!0)}))}),G(m,"renderPolarGrid",function(g){var _=g.props,S=_.radialLines,P=_.polarAngles,C=_.polarRadius,A=m.state,E=A.radiusAxisMap,j=A.angleAxisMap,k=Ot(E),I=Ot(j),N=I.cx,D=I.cy,L=I.innerRadius,F=I.outerRadius;return q.cloneElement(g,{polarAngles:Array.isArray(P)?P:ct(I,!0).map(function(H){return H.coordinate}),polarRadius:Array.isArray(C)?C:ct(k,!0).map(function(H){return H.coordinate}),cx:N,cy:D,innerRadius:L,outerRadius:F,key:g.key||"polar-grid",radialLines:S})}),G(m,"renderLegend",function(){var g=m.state.formattedGraphicalItems,_=m.props,S=_.children,P=_.width,C=_.height,A=m.props.margin||{},E=P-(A.left||0)-(A.right||0),j=lx({children:S,formattedGraphicalItems:g,legendWidth:E,legendContent:c});if(!j)return null;var k=j.item,I=Qg(j,FR);return q.cloneElement(k,$($({},I),{},{chartWidth:P,chartHeight:C,margin:A,onBBoxUpdate:m.handleLegendBBoxUpdate}))}),G(m,"renderTooltip",function(){var g,_=m.props,S=_.children,P=_.accessibilityLayer,C=qe(S,Qe);if(!C)return null;var A=m.state,E=A.isTooltipActive,j=A.activeCoordinate,k=A.activePayload,I=A.activeLabel,N=A.offset,D=(g=C.props.active)!==null&&g!==void 0?g:E;return q.cloneElement(C,{viewBox:$($({},N),{},{x:N.left,y:N.top}),active:D,label:I,payload:D?k:[],coordinate:j,accessibilityLayer:P})}),G(m,"renderBrush",function(g){var _=m.props,S=_.margin,P=_.data,C=m.state,A=C.offset,E=C.dataStartIndex,j=C.dataEndIndex,k=C.updateId;return q.cloneElement(g,{key:g.key||"_recharts-brush",onChange:li(m.handleBrushChange,g.props.onChange),data:P,x:B(g.props.x)?g.props.x:A.left,y:B(g.props.y)?g.props.y:A.top+A.height+A.brushBottom-(S.bottom||0),width:B(g.props.width)?g.props.width:A.width,startIndex:E,endIndex:j,updateId:"brush-".concat(k)})}),G(m,"renderReferenceElement",function(g,_,S){if(!g)return null;var P=m,C=P.clipPathId,A=m.state,E=A.xAxisMap,j=A.yAxisMap,k=A.offset,I=g.type.defaultProps||{},N=g.props,D=N.xAxisId,L=D===void 0?I.xAxisId:D,F=N.yAxisId,H=F===void 0?I.yAxisId:F;return q.cloneElement(g,{key:g.key||"".concat(_,"-").concat(S),xAxis:E[L],yAxis:j[H],viewBox:{x:k.left,y:k.top,width:k.width,height:k.height},clipPathId:C})}),G(m,"renderActivePoints",function(g){var _=g.item,S=g.activePoint,P=g.basePoint,C=g.childIndex,A=g.isRange,E=[],j=_.props.key,k=_.item.type.defaultProps!==void 0?$($({},_.item.type.defaultProps),_.item.props):_.item.props,I=k.activeDot,N=k.dataKey,D=$($({index:C,dataKey:N,cx:S.x,cy:S.y,r:4,fill:nh(_.item),strokeWidth:2,stroke:"#fff",payload:S.payload,value:S.value},ee(I,!1)),yi(I));return E.push(x.renderActiveDot(I,D,"".concat(j,"-activePoint-").concat(C))),P?E.push(x.renderActiveDot(I,$($({},D),{},{cx:P.x,cy:P.y}),"".concat(j,"-basePoint-").concat(C))):A&&E.push(null),E}),G(m,"renderGraphicChild",function(g,_,S){var P=m.filterFormatItem(g,_,S);if(!P)return null;var C=m.getTooltipEventType(),A=m.state,E=A.isTooltipActive,j=A.tooltipAxis,k=A.activeTooltipIndex,I=A.activeLabel,N=m.props.children,D=qe(N,Qe),L=P.props,F=L.points,H=L.isRange,K=L.baseLine,U=P.item.type.defaultProps!==void 0?$($({},P.item.type.defaultProps),P.item.props):P.item.props,V=U.activeDot,ce=U.hide,pe=U.activeBar,Re=U.activeShape,Ct=!!(!ce&&E&&D&&(V||pe||Re)),$e={};C!=="axis"&&D&&D.props.trigger==="click"?$e={onClick:li(m.handleItemMouseEnter,g.props.onClick)}:C!=="axis"&&($e={onMouseLeave:li(m.handleItemMouseLeave,g.props.onMouseLeave),onMouseEnter:li(m.handleItemMouseEnter,g.props.onMouseEnter)});var z=q.cloneElement(g,$($({},P.props),$e));function X(It){return typeof j.dataKey=="function"?j.dataKey(It.payload):null}if(Ct)if(k>=0){var Y,R;if(j.dataKey&&!j.allowDuplicatedCategory){var he=typeof j.dataKey=="function"?X:"payload.".concat(j.dataKey.toString());Y=Kc(F,he,I),R=H&&K&&Kc(K,he,I)}else Y=F?.[k],R=H&&K&&K[k];if(Re||pe){var J=g.props.activeIndex!==void 0?g.props.activeIndex:k;return[q.cloneElement(g,$($($({},P.props),$e),{},{activeIndex:J})),null,null]}if(!ne(Y))return[z].concat(Rr(m.renderActivePoints({item:P,activePoint:Y,basePoint:R,childIndex:k,isRange:H})))}else{var me,ge=(me=m.getItemByXY(m.state.activeCoordinate))!==null&&me!==void 0?me:{graphicalItem:z},Ie=ge.graphicalItem,xt=Ie.item,er=xt===void 0?g:xt,Jn=Ie.childIndex,$t=$($($({},P.props),$e),{},{activeIndex:Jn});return[q.cloneElement(er,$t),null,null]}return H?[z,null,null]:[z,null]}),G(m,"renderCustomized",function(g,_,S){return q.cloneElement(g,$($({key:"recharts-customized-".concat(S)},m.props),m.state))}),G(m,"renderMap",{CartesianGrid:{handler:vi,once:!0},ReferenceArea:{handler:m.renderReferenceElement},ReferenceLine:{handler:vi},ReferenceDot:{handler:m.renderReferenceElement},XAxis:{handler:vi},YAxis:{handler:vi},Brush:{handler:m.renderBrush,once:!0},Bar:{handler:m.renderGraphicChild},Line:{handler:m.renderGraphicChild},Area:{handler:m.renderGraphicChild},Radar:{handler:m.renderGraphicChild},RadialBar:{handler:m.renderGraphicChild},Scatter:{handler:m.renderGraphicChild},Pie:{handler:m.renderGraphicChild},Funnel:{handler:m.renderGraphicChild},Tooltip:{handler:m.renderCursor,once:!0},PolarGrid:{handler:m.renderPolarGrid,once:!0},PolarAngleAxis:{handler:m.renderPolarAxis},PolarRadiusAxis:{handler:m.renderPolarAxis},Customized:{handler:m.renderCustomized}}),m.clipPathId="".concat((b=w.id)!==null&&b!==void 0?b:va("recharts"),"-clip"),m.throttleTriggeredAfterMouseMove=c0(m.triggeredAfterMouseMove,(O=w.throttleDelay)!==null&&O!==void 0?O:1e3/60),m.state={},m}return QR(x,d),XR(x,[{key:"componentDidMount",value:function(){var b,O;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:(b=this.props.margin.left)!==null&&b!==void 0?b:0,top:(O=this.props.margin.top)!==null&&O!==void 0?O:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var b=this.props,O=b.children,m=b.data,g=b.height,_=b.layout,S=qe(O,Qe);if(S){var P=S.props.defaultIndex;if(!(typeof P!="number"||P<0||P>this.state.tooltipTicks.length-1)){var C=this.state.tooltipTicks[P]&&this.state.tooltipTicks[P].value,A=sf(this.state,m,P,C),E=this.state.tooltipTicks[P].coordinate,j=(this.state.offset.top+g)/2,k=_==="horizontal",I=k?{x:E,y:j}:{y:E,x:j},N=this.state.formattedGraphicalItems.find(function(L){var F=L.item;return F.type.name==="Scatter"});N&&(I=$($({},I),N.props.points[P].tooltipPosition),A=N.props.points[P].tooltipPayload);var D={activeTooltipIndex:P,isTooltipActive:!0,activeLabel:C,activePayload:A,activeCoordinate:I};this.setState(D),this.renderCursor(S),this.accessibilityManager.setIndex(P)}}}},{key:"getSnapshotBeforeUpdate",value:function(b,O){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==O.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==b.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==b.margin){var m,g;this.accessibilityManager.setDetails({offset:{left:(m=this.props.margin.left)!==null&&m!==void 0?m:0,top:(g=this.props.margin.top)!==null&&g!==void 0?g:0}})}return null}},{key:"componentDidUpdate",value:function(b){Xc([qe(b.children,Qe)],[qe(this.props.children,Qe)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var b=qe(this.props.children,Qe);if(b&&typeof b.props.shared=="boolean"){var O=b.props.shared?"axis":"item";return u.indexOf(O)>=0?O:a}return a}},{key:"getMouseInfo",value:function(b){if(!this.container)return null;var O=this.container,m=O.getBoundingClientRect(),g=AA(m),_={chartX:Math.round(b.pageX-g.left),chartY:Math.round(b.pageY-g.top)},S=m.width/O.offsetWidth||1,P=this.inRange(_.chartX,_.chartY,S);if(!P)return null;var C=this.state,A=C.xAxisMap,E=C.yAxisMap,j=this.getTooltipEventType(),k=tb(this.state,this.props.data,this.props.layout,P);if(j!=="axis"&&A&&E){var I=Ot(A).scale,N=Ot(E).scale,D=I&&I.invert?I.invert(_.chartX):null,L=N&&N.invert?N.invert(_.chartY):null;return $($({},_),{},{xValue:D,yValue:L},k)}return k?$($({},_),k):null}},{key:"inRange",value:function(b,O){var m=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,g=this.props.layout,_=b/m,S=O/m;if(g==="horizontal"||g==="vertical"){var P=this.state.offset,C=_>=P.left&&_<=P.left+P.width&&S>=P.top&&S<=P.top+P.height;return C?{x:_,y:S}:null}var A=this.state,E=A.angleAxisMap,j=A.radiusAxisMap;if(E&&j){var k=Ot(E);return mm({x:_,y:S},k)}return null}},{key:"parseEventsOfWrapper",value:function(){var b=this.props.children,O=this.getTooltipEventType(),m=qe(b,Qe),g={};m&&O==="axis"&&(m.props.trigger==="click"?g={onClick:this.handleClick}:g={onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu});var _=yi(this.props,this.handleOuterEvent);return $($({},_),g)}},{key:"addListener",value:function(){Wc.on(Hc,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){Wc.removeListener(Hc,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(b,O,m){for(var g=this.state.formattedGraphicalItems,_=0,S=g.length;_<S;_++){var P=g[_];if(P.item===b||P.props.key===b.key||O===lt(P.item.type)&&m===P.childIndex)return P}return null}},{key:"renderClipPath",value:function(){var b=this.clipPathId,O=this.state.offset,m=O.left,g=O.top,_=O.height,S=O.width;return T.createElement("defs",null,T.createElement("clipPath",{id:b},T.createElement("rect",{x:m,y:g,height:_,width:S})))}},{key:"getXScales",value:function(){var b=this.state.xAxisMap;return b?Object.entries(b).reduce(function(O,m){var g=Jg(m,2),_=g[0],S=g[1];return $($({},O),{},G({},_,S.scale))},{}):null}},{key:"getYScales",value:function(){var b=this.state.yAxisMap;return b?Object.entries(b).reduce(function(O,m){var g=Jg(m,2),_=g[0],S=g[1];return $($({},O),{},G({},_,S.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(b){var O;return(O=this.state.xAxisMap)===null||O===void 0||(O=O[b])===null||O===void 0?void 0:O.scale}},{key:"getYScaleByAxisId",value:function(b){var O;return(O=this.state.yAxisMap)===null||O===void 0||(O=O[b])===null||O===void 0?void 0:O.scale}},{key:"getItemByXY",value:function(b){var O=this.state,m=O.formattedGraphicalItems,g=O.activeItem;if(m&&m.length)for(var _=0,S=m.length;_<S;_++){var P=m[_],C=P.props,A=P.item,E=A.type.defaultProps!==void 0?$($({},A.type.defaultProps),A.props):A.props,j=lt(A.type);if(j==="Bar"){var k=(C.data||[]).find(function(L){return F$(b,L)});if(k)return{graphicalItem:P,payload:k}}else if(j==="RadialBar"){var I=(C.data||[]).find(function(L){return mm(b,L)});if(I)return{graphicalItem:P,payload:I}}else if(ka(P,g)||Na(P,g)||Dn(P,g)){var N=CI({graphicalItem:P,activeTooltipItem:g,itemData:E.data}),D=E.activeIndex===void 0?N:E.activeIndex;return{graphicalItem:$($({},P),{},{childIndex:D}),payload:Dn(P,g)?E.data[N]:P.props.data[N]}}}return null}},{key:"render",value:function(){var b=this;if(!jd(this))return null;var O=this.props,m=O.children,g=O.className,_=O.width,S=O.height,P=O.style,C=O.compact,A=O.title,E=O.desc,j=Qg(O,zR),k=ee(j,!1);if(C)return T.createElement(Rg,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},T.createElement(Zc,fr({},k,{width:_,height:S,title:A,desc:E}),this.renderClipPath(),Cd(m,this.renderMap)));if(this.props.accessibilityLayer){var I,N;k.tabIndex=(I=this.props.tabIndex)!==null&&I!==void 0?I:0,k.role=(N=this.props.role)!==null&&N!==void 0?N:"application",k.onKeyDown=function(L){b.accessibilityManager.keyboardEvent(L)},k.onFocus=function(){b.accessibilityManager.focus()}}var D=this.parseEventsOfWrapper();return T.createElement(Rg,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},T.createElement("div",fr({className:ie("recharts-wrapper",g),style:$({position:"relative",cursor:"default",width:_,height:S},P)},D,{ref:function(F){b.container=F}}),T.createElement(Zc,fr({},k,{width:_,height:S,title:A,desc:E,style:aD}),this.renderClipPath(),Cd(m,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}])}(q.Component);G(y,"displayName",r),G(y,"defaultProps",$({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},l)),G(y,"getDerivedStateFromProps",function(d,x){var w=d.dataKey,b=d.data,O=d.children,m=d.width,g=d.height,_=d.layout,S=d.stackOffset,P=d.margin,C=x.dataStartIndex,A=x.dataEndIndex;if(x.updateId===void 0){var E=rb(d);return $($($({},E),{},{updateId:0},p($($({props:d},E),{},{updateId:0}),x)),{},{prevDataKey:w,prevData:b,prevWidth:m,prevHeight:g,prevLayout:_,prevStackOffset:S,prevMargin:P,prevChildren:O})}if(w!==x.prevDataKey||b!==x.prevData||m!==x.prevWidth||g!==x.prevHeight||_!==x.prevLayout||S!==x.prevStackOffset||!hr(P,x.prevMargin)){var j=rb(d),k={chartX:x.chartX,chartY:x.chartY,isTooltipActive:x.isTooltipActive},I=$($({},tb(x,b,_)),{},{updateId:x.updateId+1}),N=$($($({},j),k),I);return $($($({},N),p($({props:d},N),x)),{},{prevDataKey:w,prevData:b,prevWidth:m,prevHeight:g,prevLayout:_,prevStackOffset:S,prevMargin:P,prevChildren:O})}if(!Xc(O,x.prevChildren)){var D,L,F,H,K=qe(O,Tr),U=K&&(D=(L=K.props)===null||L===void 0?void 0:L.startIndex)!==null&&D!==void 0?D:C,V=K&&(F=(H=K.props)===null||H===void 0?void 0:H.endIndex)!==null&&F!==void 0?F:A,ce=U!==C||V!==A,pe=!ne(b),Re=pe&&!ce?x.updateId:x.updateId+1;return $($({updateId:Re},p($($({props:d},x),{},{updateId:Re,dataStartIndex:U,dataEndIndex:V}),x)),{},{prevChildren:O,dataStartIndex:U,dataEndIndex:V})}return null}),G(y,"renderActiveDot",function(d,x,w){var b;return q.isValidElement(d)?b=q.cloneElement(d,x):Z(d)?b=d(x):b=T.createElement(Tx,x),T.createElement(_e,{className:"recharts-active-dot",key:w},b)});var v=q.forwardRef(function(x,w){return T.createElement(y,fr({},x,{ref:w}))});return v.displayName=y.displayName,v},yD=vD({chartName:"BarChart",GraphicalChild:Qt,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:Fa},{axisType:"yAxis",AxisComp:za}],formatAxisMap:Tk});const mD={light:"",dark:".dark"},yw=q.createContext(null);function gD(){const e=q.useContext(yw);if(!e)throw new Error("useChart must be used within a <ChartContainer />");return e}function bD({id:e,className:t,children:r,config:n,...i}){const a=q.useId(),o=`chart-${e||a.replace(/:/g,"")}`;return M.jsx(yw.Provider,{value:{config:n},children:M.jsxs("div",{"data-slot":"chart","data-chart":o,className:Nt("[&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border flex aspect-video justify-center text-xs [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-hidden [&_.recharts-sector]:outline-hidden [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-surface]:outline-hidden",t),...i,children:[M.jsx(xD,{id:o,config:n}),M.jsx(gA,{children:r})]})})}const xD=({id:e,config:t})=>{const r=Object.entries(t).filter(([,n])=>n.theme||n.color);return r.length?M.jsx("style",{dangerouslySetInnerHTML:{__html:Object.entries(mD).map(([n,i])=>`
${i} [data-chart=${e}] {
${r.map(([a,o])=>{const u=o.theme?.[n]||o.color;return u?`  --color-${a}: ${u};`:null}).join(`
`)}
}
`).join(`
`)}}):null},wD=Qe;function OD({active:e,payload:t,className:r,indicator:n="dot",hideLabel:i=!1,hideIndicator:a=!1,label:o,labelFormatter:u,labelClassName:s,formatter:c,color:f,nameKey:l,labelKey:h}){const{config:p}=gD(),y=q.useMemo(()=>{if(i||!t?.length)return null;const[d]=t,x=`${h||d?.dataKey||d?.name||"value"}`,w=ib(p,d,x),b=!h&&typeof o=="string"?p[o]?.label||o:w?.label;return u?M.jsx("div",{className:Nt("font-medium",s),children:u(b,t)}):b?M.jsx("div",{className:Nt("font-medium",s),children:b}):null},[o,u,t,i,s,p,h]);if(!e||!t?.length)return null;const v=t.length===1&&n!=="dot";return M.jsxs("div",{className:Nt("border-border/50 bg-background grid min-w-[8rem] items-start gap-1.5 rounded-lg border px-2.5 py-1.5 text-xs shadow-xl",r),children:[v?null:y,M.jsx("div",{className:"grid gap-1.5",children:t.map((d,x)=>{const w=`${l||d.name||d.dataKey||"value"}`,b=ib(p,d,w),O=f||d.payload.fill||d.color;return M.jsx("div",{className:Nt("[&>svg]:text-muted-foreground flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5",n==="dot"&&"items-center"),children:c&&d?.value!==void 0&&d.name?c(d.value,d.name,d,x,d.payload):M.jsxs(M.Fragment,{children:[b?.icon?M.jsx(b.icon,{}):!a&&M.jsx("div",{className:Nt("shrink-0 rounded-[2px] border-(--color-border) bg-(--color-bg)",{"h-2.5 w-2.5":n==="dot","w-1":n==="line","w-0 border-[1.5px] border-dashed bg-transparent":n==="dashed","my-0.5":v&&n==="dashed"}),style:{"--color-bg":O,"--color-border":O}}),M.jsxs("div",{className:Nt("flex flex-1 justify-between leading-none",v?"items-end":"items-center"),children:[M.jsxs("div",{className:"grid gap-1.5",children:[v?y:null,M.jsx("span",{className:"text-muted-foreground",children:b?.label||d.name})]}),d.value&&M.jsx("span",{className:"text-foreground font-mono font-medium tabular-nums",children:d.value.toLocaleString()})]})]})},d.dataKey)})})]})}function ib(e,t,r){if(typeof t!="object"||t===null)return;const n="payload"in t&&typeof t.payload=="object"&&t.payload!==null?t.payload:void 0;let i=r;return r in t&&typeof t[r]=="string"?i=t[r]:n&&r in n&&typeof n[r]=="string"&&(i=n[r]),i in e?e[i]:e[r]}const _D=[{month:"F1",desktop:186,mobile:80},{month:"F2",desktop:305,mobile:200},{month:"F3",desktop:237,mobile:120},{month:"F4",desktop:73,mobile:190},{month:"F5",desktop:209,mobile:130},{month:"F6",desktop:214,mobile:140}],SD={desktop:{label:"Desktop",color:"var(--chart-2)"},mobile:{label:"Mobile",color:"var(--chart-2)"},label:{color:"var(--background)"}};function AD(){return M.jsxs(or,{children:[M.jsx(ur,{children:M.jsx(sr,{children:"Jetty Status"})}),M.jsx(ab,{children:M.jsx(bD,{config:SD,children:M.jsxs(yD,{accessibilityLayer:!0,data:_D,layout:"vertical",margin:{right:16},children:[M.jsx(rw,{horizontal:!1}),M.jsx(za,{dataKey:"month",type:"category",tickLine:!1,tickMargin:10,axisLine:!1,tickFormatter:e=>e.slice(0,3),hide:!0}),M.jsx(Fa,{dataKey:"desktop",type:"number",hide:!0}),M.jsx(wD,{cursor:!1,content:M.jsx(OD,{indicator:"line"})}),M.jsxs(Qt,{dataKey:"desktop",layout:"vertical",fill:"var(--color-desktop)",radius:4,children:[M.jsx(dt,{dataKey:"month",position:"insideLeft",offset:8,className:"fill-(--color-label)",fontSize:12}),M.jsx(dt,{dataKey:"desktop",position:"right",offset:8,className:"fill-foreground",fontSize:12})]})]})})}),M.jsxs(Qr,{className:"flex-col items-start gap-2 text-sm",children:[M.jsxs("div",{className:"flex gap-2 leading-none font-medium",children:["Trending up by 5.2% this month ",M.jsx(Rw,{className:"h-4 w-4"})]}),M.jsx("div",{className:"text-muted-foreground leading-none",children:"Showing total visitors for the last 6 months"})]})]})}function kD(){return M.jsxs("div",{className:"flex flex-1 flex-col space-y-2",children:[M.jsx("div",{className:"flex items-center justify-between space-y-2",children:M.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:"Hi, Welcome back 👋"})}),M.jsxs("div",{className:"*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs md:grid-cols-2 lg:grid-cols-4",children:[M.jsxs(or,{className:"@container/card",children:[M.jsxs(ur,{children:[M.jsx(ei,{children:"Approval Requests"}),M.jsx(sr,{className:"text-2xl font-semibold tabular-nums @[250px]/card:text-3xl",children:"100"}),M.jsx(ti,{children:M.jsxs(ri,{variant:"outline",children:[M.jsx(tr,{}),"+1.5%"]})})]}),M.jsxs(Qr,{className:"flex-col items-start gap-1.5 text-sm",children:[M.jsxs("div",{className:"line-clamp-1 flex gap-2 font-medium",children:["Increased approval requests ",M.jsx(tr,{className:"size-4"})]}),M.jsx("div",{className:"text-muted-foreground",children:"Compared to previous month"})]})]}),M.jsxs(or,{className:"@container/card",children:[M.jsxs(ur,{children:[M.jsx(ei,{children:"Pending Approval"}),M.jsx(sr,{className:"text-2xl font-semibold tabular-nums @[250px]/card:text-3xl",children:"10"}),M.jsx(ti,{children:M.jsxs(ri,{variant:"outline",children:[M.jsx(bh,{}),"-20%"]})})]}),M.jsxs(Qr,{className:"flex-col items-start gap-1.5 text-sm",children:[M.jsxs("div",{className:"line-clamp-1 flex gap-2 font-medium",children:["Reduced pending approvals ",M.jsx(bh,{className:"size-4"})]}),M.jsx("div",{className:"text-muted-foreground",children:"Faster processing time"})]})]}),M.jsxs(or,{className:"@container/card",children:[M.jsxs(ur,{children:[M.jsx(ei,{children:"Rejected Requests"}),M.jsx(sr,{className:"text-2xl font-semibold tabular-nums @[250px]/card:text-3xl",children:"1"}),M.jsx(ti,{children:M.jsxs(ri,{variant:"outline",children:[M.jsx(tr,{}),"+12.5%"]})})]}),M.jsxs(Qr,{className:"flex-col items-start gap-1.5 text-sm",children:[M.jsxs("div",{className:"line-clamp-1 flex gap-2 font-medium",children:["Higher quality submissions ",M.jsx(tr,{className:"size-4"})]}),M.jsx("div",{className:"text-muted-foreground",children:"Better compliance rate"})]})]}),M.jsxs(or,{className:"@container/card",children:[M.jsxs(ur,{children:[M.jsx(ei,{children:"Scheduled Vessels Today"}),M.jsx(sr,{className:"text-2xl font-semibold tabular-nums @[250px]/card:text-3xl",children:"10"}),M.jsx(ti,{children:M.jsxs(ri,{variant:"outline",children:[M.jsx(tr,{}),"+4.5%"]})})]}),M.jsxs(Qr,{className:"flex-col items-start gap-1.5 text-sm",children:[M.jsxs("div",{className:"line-clamp-1 flex gap-2 font-medium",children:["More vessels scheduled ",M.jsx(tr,{className:"size-4"})]}),M.jsx("div",{className:"text-muted-foreground",children:"Improved jetty utilization"})]})]})]}),M.jsxs("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-7",children:[M.jsx("div",{className:"col-span-4",children:M.jsx(qw,{})}),M.jsx("div",{className:"col-span-4 md:col-span-3",children:M.jsx(AD,{})})]})]})}export{kD as O};
//# sourceMappingURL=overview-DuQ0rNeC.js.map
