using Imip.JettyApproval.Web.Services.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text.Json;
using System.Threading.Tasks;

namespace Imip.JettyApproval.Web.Services;

public class AppToAppService
{
    private readonly ITokenService _tokenService;
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;
    private readonly ILogger<AppToAppService> _logger;

    public AppToAppService(
        ITokenService tokenService,
        HttpClient httpClient,
        IConfiguration configuration,
        ILogger<AppToAppService> logger)
    {
        _tokenService = tokenService;
        _httpClient = httpClient;
        _configuration = configuration;
        _logger = logger;
    }

    /// <summary>
    /// Calls another application using the SSO token
    /// </summary>
    public async Task<T> CallOtherAppAsync<T>(string appUrl, string endpoint, object data = null)
    {
        try
        {
            // Get a valid token (automatically refreshes if needed)
            var accessToken = await _tokenService.GetValidAccessTokenAsync();

            if (string.IsNullOrEmpty(accessToken))
            {
                _logger.LogError("No valid access token available for app-to-app call");
                throw new UnauthorizedAccessException("No valid access token available");
            }

            // Set the authorization header with the SSO token
            _httpClient.DefaultRequestHeaders.Authorization =
                new AuthenticationHeaderValue("Bearer", accessToken);

            // Add any additional headers if needed
            _httpClient.DefaultRequestHeaders.Add("X-App-Name", "JettyApproval");
            _httpClient.DefaultRequestHeaders.Add("X-App-Version", "1.0");

            var fullUrl = $"{appUrl.TrimEnd('/')}/{endpoint.TrimStart('/')}";
            _logger.LogInformation("Calling other app: {Url}", fullUrl);

            HttpResponseMessage response;

            if (data != null)
            {
                // POST request with data
                var json = JsonSerializer.Serialize(data);
                var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");
                response = await _httpClient.PostAsync(fullUrl, content);
            }
            else
            {
                // GET request
                response = await _httpClient.GetAsync(fullUrl);
            }

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                _logger.LogInformation("App-to-app call successful: {StatusCode}", response.StatusCode);

                // Deserialize the response
                return JsonSerializer.Deserialize<T>(responseContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
            }
            else if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
            {
                _logger.LogWarning("App-to-app call returned 401, token might be invalid");

                // Try to refresh the token and retry once
                var newToken = await _tokenService.RefreshAccessTokenAsync();
                if (!string.IsNullOrEmpty(newToken))
                {
                    _httpClient.DefaultRequestHeaders.Authorization =
                        new AuthenticationHeaderValue("Bearer", newToken);

                    if (data != null)
                    {
                        var json = JsonSerializer.Serialize(data);
                        var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");
                        response = await _httpClient.PostAsync(fullUrl, content);
                    }
                    else
                    {
                        response = await _httpClient.GetAsync(fullUrl);
                    }

                    if (response.IsSuccessStatusCode)
                    {
                        var responseContent = await response.Content.ReadAsStringAsync();
                        _logger.LogInformation("App-to-app call successful after token refresh: {StatusCode}", response.StatusCode);
                        return JsonSerializer.Deserialize<T>(responseContent, new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true
                        });
                    }
                }

                throw new UnauthorizedAccessException("App-to-app call failed after token refresh");
            }
            else
            {
                _logger.LogError("App-to-app call failed with status: {StatusCode}", response.StatusCode);
                throw new HttpRequestException($"App-to-app call failed with status: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in app-to-app call");
            throw;
        }
    }

    /// <summary>
    /// Example: Call another app's API
    /// </summary>
    public async Task<object> GetDataFromOtherAppAsync()
    {
        var otherAppUrl = _configuration["ExternalApps:OtherApp:BaseUrl"];
        return await CallOtherAppAsync<object>(otherAppUrl, "/api/data");
    }

    /// <summary>
    /// Example: Send data to another app
    /// </summary>
    public async Task<object> SendDataToOtherAppAsync(object data)
    {
        var otherAppUrl = _configuration["ExternalApps:OtherApp:BaseUrl"];
        return await CallOtherAppAsync<object>(otherAppUrl, "/api/data", data);
    }
}