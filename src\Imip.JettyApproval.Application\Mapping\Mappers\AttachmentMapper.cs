using System;
using System.Collections.Generic;
using Imip.JettyApproval.Attachments;
using Riok.Mapperly.Abstractions;

namespace Imip.JettyApproval.Mapping.Mappers;

/// <summary>
/// Mapper for Attachment entity
/// </summary>
[Mapper]
public partial class AttachmentMapper : IMapperlyMapper
{
    // Entity to DTO mapping
    [MapProperty(nameof(Attachment.Id), nameof(AttachmentDto.Id))]
    [MapperIgnoreSource(nameof(Attachment.IsDeleted))]
    [MapperIgnoreSource(nameof(Attachment.DeleterId))]
    [MapperIgnoreSource(nameof(Attachment.DeletionTime))]
    [MapperIgnoreSource(nameof(Attachment.LastModificationTime))]
    [MapperIgnoreSource(nameof(Attachment.LastModifierId))]
    [MapperIgnoreSource(nameof(Attachment.CreationTime))]
    [MapperIgnoreSource(nameof(Attachment.CreatorId))]
    [MapperIgnoreSource(nameof(Attachment.ExtraProperties))]
    [MapperIgnoreSource(nameof(Attachment.ConcurrencyStamp))]
    public partial AttachmentDto MapToDto(Attachment entity);

    // DTO to Entity mapping for updates (maps to existing entity)
    [MapperIgnoreTarget(nameof(Attachment.Id))] // Don't change existing Id
    public partial void MapToEntity(CreateUpdateAttachmentDto dto, Attachment entity);

    // Custom mapping methods for complex scenarios
    public Attachment CreateEntityWithId(CreateUpdateAttachmentDto dto, Guid id)
    {
        // Create entity using reflection since constructor is protected
        var entity = (Attachment)Activator.CreateInstance(typeof(Attachment), true)!;

        // Map properties manually
        MapToEntity(dto, entity);

        return entity;
    }

    // Map list of entities to DTOs
    public partial List<AttachmentDto> MapToDtoList(List<Attachment> entities);

    // Map IEnumerable for LINQ scenarios
    public partial IEnumerable<AttachmentDto> MapToDtoEnumerable(IEnumerable<Attachment> entities);
}