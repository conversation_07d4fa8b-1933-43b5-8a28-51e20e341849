'use client'
import { type CreateUpdateOpenIddictResourceDto, postApiOpeniddictResources } from '@/client'
import { Button } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { useToast } from '@/lib/useToast'
import { handleApiError } from '@/lib/handleApiError'
import { QueryNames } from '@/lib/hooks/QueryConstants'
import { useGrantedPolicies } from '@/lib/hooks/useGrantedPolicies'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { Toaster } from '@/components/ui/toaster'
import { RiAddLine } from '@remixicon/react'
import { FormField, FormSection } from '@/components/ui/FormField'
import { Textarea } from '@/components/ui/textarea'

export type AddClientProps = {
  children?: React.ReactNode
}

export const Add = ({ children }: AddClientProps) => {
  const { can } = useGrantedPolicies()
  const [open, setOpen] = useState(false)
  const { toast } = useToast()
  const queryClient = useQueryClient()
  const { handleSubmit, register, reset } = useForm<CreateUpdateOpenIddictResourceDto>()

  const resetForm = () => {
    reset({
      name: '',
      displayName: '',
      description: ''
    })
  }

  const createDataMutation = useMutation({
    mutationFn: async (dataMutation: CreateUpdateOpenIddictResourceDto) =>
      postApiOpeniddictResources({
        body: dataMutation
      }),
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Resource Created Successfully',
        variant: 'success',
      })
      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetOpeniddictResources] })
      resetForm()
      setOpen(false)
    },
    onError: (err: unknown) => {
      // Use the global helper to handle API errors
      const error = handleApiError(err);
      toast({
        title: error.title,
        description: error.description,
        variant: 'error',
      })
    }
  })

  const onSubmit = (formData: CreateUpdateOpenIddictResourceDto) => {
    // Merge form data with consent type and permissions
    const userData: CreateUpdateOpenIddictResourceDto = {
      ...formData,
    }

    // Explicitly mark the promise as handled
    void createDataMutation.mutate(userData)
  }

  const handleOpenChange = (newOpen: boolean) => {
    if (newOpen) {
      resetForm()
    }
    setOpen(newOpen)
  }

  return (
    <section>
      <Toaster />
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogTrigger asChild>{children}</DialogTrigger>
        <section className="flex items-center justify-between pb-5">
          {can('AbpIdentity.Users.Create') && (
            <Button size='sm' className="w-full sm:py-1 sm:mt-0 sm:w-fit" onClick={() => handleOpenChange(true)}>
              <RiAddLine className="-ml-1 size-4 shrink-0" aria-hidden="true" />
              <span className="hidden truncate sm:inline">Create New Resources</span>
            </Button>
          )}
        </section>
        <DialogContent size="xl">
          <DialogHeader>
            <DialogTitle>Create a New Resource</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSubmit(onSubmit)} className='mt-2'>
            <section className="flex w-full flex-col space-y-2">
              <FormSection>
                <FormField
                  label="Name"
                  description="The unique identifier for this resource. Used in requests"
                >
                  <Input required {...register('name')} placeholder="Name" />
                </FormField>
                <FormField
                  label="Display Name"
                  description="The display name for this resource"
                >
                  <Input required {...register('displayName')} placeholder="Display Name" />
                </FormField>
                <FormField
                  label="Description"
                  description="The description for this resource"
                >
                  <Textarea required {...register('description')} placeholder="Description" />
                </FormField>
              </FormSection>
            </section>
            <DialogFooter className="mt-5">
              <Button
                variant="ghost"
                onClick={(e) => {
                  e.preventDefault()
                  setOpen(false)
                }}
                disabled={createDataMutation.isPending}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={createDataMutation.isPending}>
                {createDataMutation.isPending ? 'Saving...' : 'Save'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </section>
  )
}
