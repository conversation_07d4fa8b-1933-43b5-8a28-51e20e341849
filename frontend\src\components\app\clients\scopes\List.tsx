'use client'
import { QueryNames } from '@/lib/hooks/QueryConstants'
import { useState } from 'react'

import { type OpenIddictScopeDto } from '@/client'
import { TableSkeleton } from '@/components/ui/TableSkeleton'
import { type PaginationState } from '@tanstack/react-table'

import { useToast } from '@/lib/useToast'
import { Delete } from './Delete'

import { useQueryClient } from '@tanstack/react-query'
import { DataTable } from '@/components/data-table/DataTable'
import { getColumns } from './Columns'
import { NotionFilter } from '../../../data-table/NotionFilter'
import { useOpeniddictScopesWithFilters } from '@/lib/hooks/useOpeniddictScopesWithFilters'
import { Add } from './Add'
import { Edit } from './Edit'
import { type FilterCondition } from '@/lib/interfaces/IFilterCondition'

export const ClientScopeList = () => {
  const { toast } = useToast()
  const queryClient = useQueryClient()

  const [searchStr, setSearchStr] = useState<string>('')
  const [filterConditions, setFilterConditions] = useState<FilterCondition[]>([])
  const [actionDialog, setActionDialog] = useState<{
    dataId: string
    dataEdit: OpenIddictScopeDto
    dialogType?: 'edit' | 'permission' | 'delete'
  } | null>()

  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  })

  const { isLoading, data } = useOpeniddictScopesWithFilters(
    pagination.pageIndex,
    pagination.pageSize,
    filterConditions
  )

  // Handler for user actions (edit, permission, delete)
  const handleUserAction = (dataId: string, dataEdit: OpenIddictScopeDto, dialogType: 'edit' | 'permission' | 'delete') => {
    setActionDialog({
      dataId,
      dataEdit,
      dialogType,
    })
  }

  // Get columns with the action handler
  const columns = getColumns(handleUserAction)

  const handleSearch = (value: string) => {
    // Always update the search string for UI consistency
    setSearchStr(value)

    // Create a search filter condition if there's a search value
    // First, remove any existing name filter
    const existingFilters = filterConditions.filter(fc => fc.fieldName !== 'name')
    const newFilterConditions = [...existingFilters]

    // Only add the search filter if there's a value
    if (value) {
      newFilterConditions.push({
        fieldName: 'name',
        operator: 'Contains',
        value: value
      })
    }

    // Only update state if filters have changed
    const currentFiltersStr = JSON.stringify(filterConditions)
    const newFiltersStr = JSON.stringify(newFilterConditions)

    if (currentFiltersStr !== newFiltersStr) {
      setFilterConditions(newFilterConditions)
      setPagination(prev => ({ ...prev, pageIndex: 0 })) // Reset to first page on search
    }
  }

  const handlePaginationChange = (newPagination: PaginationState) => {
    setPagination(newPagination)
  }

  // Handler for refreshing the data
  const handleRefresh = () => {
    // Invalidate the query to fetch fresh data
    void queryClient.invalidateQueries({ queryKey: [QueryNames.GetOpeniddictScopes] })

    // Show toast notification after a short delay to match the animation
    setTimeout(() => {
      toast({
        title: "Data refreshed",
        description: "The client list has been refreshed.",
        variant: "success",
      })
    }, 800)
  }

  if (isLoading) return (
    <TableSkeleton
      rowCount={pagination.pageSize}
      columnCount={4}
      hasTitle={true}
      hasSearch={true}
      hasFilters={true}
      hasPagination={true}
      hasActions={true}
    />
  )

  // Ensure we have valid data to render
  const items = data?.items ?? [];
  const totalCount = data?.totalCount ?? 0;

  return (
    <>
      <div className="space-y-4">
        <DataTable
          title="Scopes Management"
          columns={columns}
          data={items}
          totalCount={totalCount}
          isLoading={isLoading}
          manualPagination={true}
          pageSize={pagination.pageSize}
          onPaginationChange={handlePaginationChange}
          onSearch={handleSearch}
          searchValue={searchStr}
          customFilterbar={(props) => (
            <NotionFilter
              {...props}
              activeFilters={filterConditions}
              onServerFilter={(conditions) => {
                // Only update if the conditions have actually changed
                const currentStr = JSON.stringify(filterConditions);
                const newStr = JSON.stringify(conditions);

                if (currentStr !== newStr) {
                  setFilterConditions(conditions)
                  setPagination(prev => ({ ...prev, pageIndex: 0 })) // Reset to first page on filter change
                }
              }}
            />
          )}
          hideDefaultFilterbar={true}
          onRefresh={handleRefresh}
          enableRowSelection={false}
          actionButton={{
            // label: "Create New User",
            onClick: () => { /* Required but not used */ },
            content: <Add />
          }}
        />
      </div>

      {actionDialog && actionDialog.dialogType === 'edit' && (
        <Edit
          dataId={actionDialog.dataId}
          dataEdit={actionDialog.dataEdit}
          onDismiss={() => {
            void queryClient.invalidateQueries({ queryKey: [QueryNames.GetOpeniddictScopes] })
            setActionDialog(null)
          }}
        />
      )}
      {actionDialog && actionDialog.dialogType === 'delete' && (
        <Delete
          data={{
            dataId: actionDialog.dataId,
            dataEdit: actionDialog.dataEdit,
          }}
          onDismiss={() => {
            void queryClient.invalidateQueries({ queryKey: [QueryNames.GetOpeniddictScopes] })
            setActionDialog(null)
          }}
        />
      )}
    </>
  )
}
