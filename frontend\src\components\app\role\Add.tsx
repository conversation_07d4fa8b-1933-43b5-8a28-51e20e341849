'use client'
import { type IdentityRoleCreateDto, postApiIdentityRoles } from '@/client'
import { Button } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { useToast } from '@/lib/useToast'
import { QueryNames } from '@/lib/hooks/QueryConstants'
import { useGrantedPolicies } from '@/lib/hooks/useGrantedPolicies'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { Toaster } from '@/components/ui/toaster'
import { RiAddLine } from '@remixicon/react'
import { FormField, FormSection } from '@/components/ui/FormField'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { handleApiError } from '@/lib/handleApiError'

export type AddClientProps = {
  children?: React.ReactNode
}

export const Add = ({ children }: AddClientProps) => {
  const { can } = useGrantedPolicies()
  const [open, setOpen] = useState(false)
  const { toast } = useToast()
  const queryClient = useQueryClient()
  const { handleSubmit, register, reset } = useForm<IdentityRoleCreateDto>()
  const [, setSelectedValueType] = useState<string[]>([])

  // Reset form when dialog is closed
  useEffect(() => {
    if (!open) {
      reset({
        name: '',
        isDefault: false,
        isPublic: false,
      })
      setSelectedValueType([])
    }
  }, [open, reset])

  const createDataMutation = useMutation({
    mutationFn: async (formData: IdentityRoleCreateDto) =>
      postApiIdentityRoles({
        body: {
          name: formData.name ?? '',
          isDefault: formData.isDefault ?? false,
          isPublic: formData.isPublic ?? false,
        }
      }),
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Role Created Successfully',
        variant: 'success',
      })
      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetRoles] })
      setOpen(false)
    },
    onError: (err: unknown) => {
      console.log('Error creating role:', err);
      const error = handleApiError(err);
      toast({
        title: error.title,
        description: error.description,
        variant: 'error',
      })
    }
  })

  const onSubmit = (formData: IdentityRoleCreateDto) => {
    // Merge form data with consent type and permissions
    const userData: IdentityRoleCreateDto = {
      ...formData,
    }

    createDataMutation.mutate(userData)
  }

  const handleOpenChange = (newOpenState: boolean) => {
    setOpen(newOpenState)
  }

  return (
    <section>
      <Toaster />
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogTrigger asChild>{children}</DialogTrigger>
        <section className="flex items-center justify-between pb-5">
          {can('IdentityServer.ClaimTypes.Create') && (
            <Button size='sm' className="w-full sm:py-1 sm:mt-0 sm:w-fit" onClick={() => setOpen(true)}>
              <RiAddLine className="-ml-1 size-4 shrink-0" aria-hidden="true" />
              <span className="hidden truncate sm:inline">New Role</span>
            </Button>
          )}
        </section>
        <DialogContent className='max-w-2xl'>
          <DialogHeader>
            <DialogTitle>Create a New Role</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSubmit(onSubmit)} className='mt-2' onKeyDown={(e) => {
            if (e.key === 'Enter' && e.target instanceof HTMLInputElement) {
              e.preventDefault();
              void handleSubmit(onSubmit)();
            }
          }}>
            <section className="flex w-full flex-col space-y-2">
              <FormSection>
                <FormField
                  label="Name"
                  description="The name of the claim"
                >
                  <Input required {...register('name')} placeholder="Claim Name" />
                </FormField>

                <FormField
                  label="Default"
                  description="Whether the role is default"
                >
                  <Checkbox {...register('isDefault', {
                    setValueAs: (value) => value === true
                  })} />
                </FormField>

                <FormField
                  label="Public"
                  description="Whether the role is public"
                >
                  <Checkbox {...register('isPublic', {
                    setValueAs: (value) => value === true
                  })} />
                </FormField>
              </FormSection>
            </section>
            <DialogFooter className="mt-5">
              <Button
                variant="ghost"
                onClick={(e) => {
                  e.preventDefault()
                  setOpen(false)
                }}
                disabled={createDataMutation.isPending}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={createDataMutation.isPending}>
                {createDataMutation.isPending ? 'Saving...' : 'Save'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </section>
  )
}
