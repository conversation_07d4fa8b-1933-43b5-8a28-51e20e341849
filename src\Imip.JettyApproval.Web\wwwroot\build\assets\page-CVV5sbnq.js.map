{"version": 3, "file": "page-CVV5sbnq.js", "sources": ["../../../../../frontend/src/pages/application/list/page.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport AppLayout from \"@/layouts/app-layout\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport { DataTable } from \"@/components/data-table/DataTable\";\r\nimport { type ColumnDef, type PaginationState } from \"@tanstack/react-table\";\r\n\r\ninterface JettyApplication {\r\n  id: string;\r\n  docnum: string;\r\n  vesselName: string;\r\n  applicationDate: string;\r\n  status: \"Pending\" | \"Approved\" | \"Rejected\" | \"Draft\";\r\n}\r\n\r\n// Define the exact type expected by the DataTable component from data-table.tsx's schema\r\ninterface DataTableItem {\r\n  id: number;\r\n  header: string; // For display in DataTable's 'Header' column\r\n  owner: string; // For DataTable's internal filter (expects 'owner')\r\n  type: string;\r\n  status: string;\r\n  target: string;\r\n  limit: string;\r\n  reviewer: string;\r\n}\r\n\r\nconst mockApplications: JettyApplication[] = [\r\n  {\r\n    id: \"1\",\r\n    docnum: \"APP-001\",\r\n    vesselName: \"MV. Sea Princess\",\r\n    applicationDate: \"2024-03-01\",\r\n    status: \"Pending\",\r\n  },\r\n  {\r\n    id: \"2\",\r\n    docnum: \"APP-002\",\r\n    vesselName: \"MV. Ocean Queen\",\r\n    applicationDate: \"2024-03-05\",\r\n    status: \"Approved\",\r\n  },\r\n  {\r\n    id: \"3\",\r\n    docnum: \"APP-003\",\r\n    vesselName: \"MV. River King\",\r\n    applicationDate: \"2024-03-10\",\r\n    status: \"Rejected\",\r\n  },\r\n  {\r\n    id: \"4\",\r\n    docnum: \"APP-004\",\r\n    vesselName: \"MV. Star Light\",\r\n    applicationDate: \"2024-03-15\",\r\n    status: \"Draft\",\r\n  },\r\n  {\r\n    id: \"5\",\r\n    docnum: \"APP-005\",\r\n    vesselName: \"MV. Blue Sky\",\r\n    applicationDate: \"2024-03-20\",\r\n    status: \"Pending\",\r\n  },\r\n];\r\n\r\n// Transform JettyApplication to DataTableItem type for DataTable\r\nconst transformToDataTableItem = (apps: JettyApplication[]): DataTableItem[] => {\r\n  return apps.map(app => ({\r\n    id: parseInt(app.id),\r\n    header: app.docnum, // Map docnum to header for display\r\n    owner: app.docnum, // Map docnum to owner for internal filter\r\n    type: app.vesselName,\r\n    status: app.status,\r\n    target: app.applicationDate,\r\n    limit: \"N/A\",\r\n    reviewer: \"\",\r\n  }));\r\n};\r\n\r\nconst columns: ColumnDef<DataTableItem>[] = [\r\n  {\r\n    accessorKey: \"header\",\r\n    header: \"Document Number\",\r\n  },\r\n  {\r\n    accessorKey: \"type\",\r\n    header: \"Vessel Name\",\r\n  },\r\n  {\r\n    accessorKey: \"status\",\r\n    header: \"Status\",\r\n  },\r\n  {\r\n    accessorKey: \"target\",\r\n    header: \"Application Date\",\r\n  },\r\n  {\r\n    accessorKey: \"limit\",\r\n    header: \"Limit\",\r\n  },\r\n  {\r\n    accessorKey: \"reviewer\",\r\n    header: \"Reviewer\",\r\n  },\r\n];\r\n\r\nconst ApplicationListPage: React.FC = () => {\r\n  const [data, setData] = useState<DataTableItem[]>([]);\r\n  const [searchStr, setSearchStr] = useState<string>('');\r\n  const [pagination, setPagination] = useState<PaginationState>({\r\n    pageIndex: 0,\r\n    pageSize: 10,\r\n  });\r\n\r\n  useEffect(() => {\r\n    // Filter mock data based on search string\r\n    const filteredData = mockApplications.filter(app =>\r\n      app.docnum.toLowerCase().includes(searchStr.toLowerCase()) ||\r\n      app.vesselName.toLowerCase().includes(searchStr.toLowerCase())\r\n    );\r\n    setData(transformToDataTableItem(filteredData));\r\n  }, [searchStr]); // Re-run effect when searchStr changes\r\n\r\n  const handleSearch = (value: string) => {\r\n    setSearchStr(value);\r\n    setPagination(prev => ({ ...prev, pageIndex: 0 })); // Reset to first page on search\r\n  };\r\n\r\n  const handlePaginationChange = (newPagination: PaginationState) => {\r\n    setPagination(newPagination);\r\n  };\r\n\r\n  return (\r\n    <AppLayout>\r\n      <div className=\"flex flex-col space-y-4 p-4\">\r\n        <Card>\r\n          <CardContent>\r\n            <DataTable\r\n              title=\"Jetty Application List\"\r\n              columns={columns}\r\n              data={data}\r\n              totalCount={mockApplications.length} // Using total length of mock data\r\n              isLoading={false}\r\n              manualPagination={false} // Since we're using mock data, let DataTable handle pagination\r\n              pageSize={pagination.pageSize}\r\n              onPaginationChange={handlePaginationChange}\r\n              onSearch={handleSearch}\r\n              searchValue={searchStr}\r\n              hideDefaultFilterbar={true} // Hide default filter bar, use a simple search for now\r\n              enableRowSelection={false}\r\n            />\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n    </AppLayout>\r\n  );\r\n};\r\n\r\nexport default ApplicationListPage;"], "names": ["mockApplications", "transformToDataTableItem", "apps", "app", "columns", "ApplicationListPage", "data", "setData", "useState", "searchStr", "setSearchStr", "pagination", "setPagination", "useEffect", "filteredData", "handleSearch", "value", "prev", "handlePaginationChange", "newPagination", "jsx", "AppLayout", "Card", "<PERSON><PERSON><PERSON><PERSON>", "DataTable"], "mappings": "oaA0BA,MAAMA,EAAuC,CAC3C,CACE,GAAI,IACJ,OAAQ,UACR,WAAY,mBACZ,gBAAiB,aACjB,OAAQ,SACV,EACA,CACE,GAAI,IACJ,OAAQ,UACR,WAAY,kBACZ,gBAAiB,aACjB,OAAQ,UACV,EACA,CACE,GAAI,IACJ,OAAQ,UACR,WAAY,iBACZ,gBAAiB,aACjB,OAAQ,UACV,EACA,CACE,GAAI,IACJ,OAAQ,UACR,WAAY,iBACZ,gBAAiB,aACjB,OAAQ,OACV,EACA,CACE,GAAI,IACJ,OAAQ,UACR,WAAY,eACZ,gBAAiB,aACjB,OAAQ,SAAA,CAEZ,EAGMC,EAA4BC,GACzBA,EAAK,IAAYC,IAAA,CACtB,GAAI,SAASA,EAAI,EAAE,EACnB,OAAQA,EAAI,OACZ,MAAOA,EAAI,OACX,KAAMA,EAAI,WACV,OAAQA,EAAI,OACZ,OAAQA,EAAI,gBACZ,MAAO,MACP,SAAU,EAAA,EACV,EAGEC,EAAsC,CAC1C,CACE,YAAa,SACb,OAAQ,iBACV,EACA,CACE,YAAa,OACb,OAAQ,aACV,EACA,CACE,YAAa,SACb,OAAQ,QACV,EACA,CACE,YAAa,SACb,OAAQ,kBACV,EACA,CACE,YAAa,QACb,OAAQ,OACV,EACA,CACE,YAAa,WACb,OAAQ,UAAA,CAEZ,EAEMC,EAAgC,IAAM,CAC1C,KAAM,CAACC,EAAMC,CAAO,EAAIC,EAAAA,SAA0B,CAAA,CAAE,EAC9C,CAACC,EAAWC,CAAY,EAAIF,EAAAA,SAAiB,EAAE,EAC/C,CAACG,EAAYC,CAAa,EAAIJ,WAA0B,CAC5D,UAAW,EACX,SAAU,EAAA,CACX,EAEDK,EAAAA,UAAU,IAAM,CAEd,MAAMC,EAAed,EAAiB,UACpCG,EAAI,OAAO,YAAY,EAAE,SAASM,EAAU,YAAa,CAAA,GACzDN,EAAI,WAAW,cAAc,SAASM,EAAU,YAAa,CAAA,CAC/D,EACQF,EAAAN,EAAyBa,CAAY,CAAC,CAAA,EAC7C,CAACL,CAAS,CAAC,EAER,MAAAM,EAAgBC,GAAkB,CACtCN,EAAaM,CAAK,EAClBJ,MAAuB,CAAE,GAAGK,EAAM,UAAW,GAAI,CACnD,EAEMC,EAA0BC,GAAmC,CACjEP,EAAcO,CAAa,CAC7B,EAGE,OAAAC,EAAAA,IAACC,GACC,SAACD,EAAAA,IAAA,MAAA,CAAI,UAAU,8BACb,SAAAA,EAAAA,IAACE,EACC,CAAA,SAAAF,EAAAA,IAACG,EACC,CAAA,SAAAH,EAAA,IAACI,EAAA,CACC,MAAM,yBACN,QAAApB,EACA,KAAAE,EACA,WAAYN,EAAiB,OAC7B,UAAW,GACX,iBAAkB,GAClB,SAAUW,EAAW,SACrB,mBAAoBO,EACpB,SAAUH,EACV,YAAaN,EACb,qBAAsB,GACtB,mBAAoB,EAAA,CAAA,EAExB,CACF,CAAA,CACF,CAAA,EACF,CAEJ"}