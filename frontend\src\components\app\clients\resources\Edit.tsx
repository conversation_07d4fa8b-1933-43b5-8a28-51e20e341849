'use client'
import { type CreateUpdateOpenIddictResourceDto, type OpenIddictResourceDto, putApiOpeniddictResourcesById } from '@/client'
import { Button } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>alogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { useToast } from '@/lib/useToast'
import { QueryNames } from '@/lib/hooks/QueryConstants'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { Toaster } from '@/components/ui/toaster'
import { FormField, FormSection } from '@/components/ui/FormField'
import { Textarea } from '@/components/ui/textarea'
import { handleApiError } from '@/lib/handleApiError'

type EditDataProps = {
  dataEdit: OpenIddictResourceDto
  dataId: string
  onDismiss: () => void
}

export const Edit = ({ dataEdit, dataId, onDismiss }: EditDataProps) => {
  const [open, setOpen] = useState(true)
  const { toast } = useToast()
  const queryClient = useQueryClient()
  const { handleSubmit, register, reset } = useForm<CreateUpdateOpenIddictResourceDto>()

  // useEffect(() => {
  //   if (dataEdit) {
  //     console.log("dataEdit2", dataEdit)
  //     reset({
  //       name: dataEdit.name || '',
  //       displayName: dataEdit.displayName || '',
  //       description: dataEdit.description || '',
  //     })
  //   }
  // }, [dataEdit, reset])

  const resetForm = () => {
    reset({
      name: '',
      displayName: '',
      description: ''
    })
  }

  const createDataMutation = useMutation({
    mutationFn: async (dataMutation: CreateUpdateOpenIddictResourceDto) =>
      putApiOpeniddictResourcesById({
        path: { id: dataId || '' },
        body: dataMutation
      }),
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Resource Updated Successfully',
        variant: 'success',
      })
      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetOpeniddictResources] })
      resetForm()
      setOpen(false)
      onDismiss()
    },
    onError: (err: unknown) => {
      // Use the global helper to handle API errors
      const error = handleApiError(err);
      toast({
        title: error.title,
        description: error.description,
        variant: 'error',
      })
    }
  })

  const onSubmit = (formData: CreateUpdateOpenIddictResourceDto) => {
    // Merge form data with consent type and permissions
    const userData: CreateUpdateOpenIddictResourceDto = {
      ...formData,

    }

    createDataMutation.mutate(userData)
  }

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      onDismiss()
    }
    setOpen(newOpen)
  }

  return (
    <section>
      <Toaster />
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogContent size="xl">
          <DialogHeader>
            <DialogTitle>Edit Resource</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSubmit(onSubmit)} className='mt-2'>
            <section className="flex w-full flex-col space-y-2">
              <FormSection>
                <FormField
                  label="Name"
                  description="The unique identifier for this resource. Used in requests"
                >
                  <Input required {...register('name')} defaultValue={dataEdit.name ?? ''} placeholder="Name" />
                </FormField>
                <FormField
                  label="Display Name"
                  description="The display name for this resource"
                >
                  <Input required {...register('displayName')} defaultValue={dataEdit.displayName ?? ''} placeholder="Display Name" />
                </FormField>
                <FormField
                  label="Description"
                  description="The description for this resource"
                >
                  <Textarea required {...register('description')} defaultValue={dataEdit.description ?? ''} placeholder="Description" />
                </FormField>
              </FormSection>
            </section>
            <DialogFooter className="mt-5">
              <Button
                variant="ghost"
                onClick={(e) => {
                  e.preventDefault()
                  setOpen(false)
                  onDismiss()
                }}
                disabled={createDataMutation.isPending}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={createDataMutation.isPending}>
                {createDataMutation.isPending ? 'Saving...' : 'Save'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </section>
  )
}
