FROM mcr.microsoft.com/dotnet/aspnet:9.0

# Install fonts and fontconfig for proper document rendering
RUN apt-get update -y && apt-get install -y \
    fontconfig \
    fonts-dejavu-core \
    fonts-liberation \
    fonts-noto-cjk \
    fonts-noto-color-emoji \
    fonts-wqy-zenhei \
    fonts-wqy-microhei \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

ARG PUBLISH_DIR=web
COPY ${PUBLISH_DIR}/ app/
WORKDIR /app
ENV ASPNETCORE_URLS=http://+:80
ENV ASPNETCORE_ENVIRONMENT=Production

# Create directory for certificates and data protection keys
RUN mkdir -p /app/certs /app/data-protection-keys && \
    chmod -R 777 /app/data-protection-keys

# Create a new entrypoint script with proper line endings
RUN echo '#!/bin/bash\n\
set -e\n\
\n\
# Rebuild font cache for proper Chinese font rendering\n\
echo "Rebuilding font cache..."\n\
fc-cache -f -v\n\
\n\
# Set environment\n\
echo "Setting ASPNETCORE_ENVIRONMENT to ${ASPNETCORE_ENVIRONMENT:-Production}"\n\
export ASPNETCORE_ENVIRONMENT=${ASPNETCORE_ENVIRONMENT:-Production}\n\
\n\
# Check for certificates\n\
if [ -d "/app/certs" ]; then\n\
    echo "Certificate directory contents:"\n\
    ls -la /app/certs\n\
\n\
    if [ -f "/app/certs/identity-server.pfx" ]; then\n\
        echo "Found certificate at /app/certs/identity-server.pfx"\n\
    else\n\
        echo "WARNING: Certificate not found at /app/certs/identity-server.pfx"\n\
        echo "This will cause the application to fail"\n\
    fi\n\
else\n\
    echo "WARNING: Certificate directory /app/certs does not exist"\n\
    echo "This will cause the application to fail"\n\
fi\n\
\n\
# Ensure data protection keys directory exists and has proper permissions\n\
echo "Setting up data protection keys directory..."\n\
mkdir -p /app/data-protection-keys\n\
chmod -R 777 /app/data-protection-keys\n\
echo "Data protection keys directory contents:"\n\
ls -la /app/data-protection-keys\n\
\n\
# Print environment for debugging\n\
echo "Current environment: $ASPNETCORE_ENVIRONMENT"\n\
echo "Current directory: $(pwd)"\n\
echo "Directory listing: $(ls -la)"\n\
\n\
# Print Redis configuration\n\
echo "Redis configuration:"\n\
echo "Redis:IsEnabled=${Redis__IsEnabled:-true}"\n\
echo "Redis:Configuration=${Redis__Configuration:-not set}"\n\
\n\
# Print hostname and node information\n\
echo "Pod hostname: $(hostname)"\n\
echo "Node name: ${NODE_NAME:-unknown}"\n\
\n\
# Start the application\n\
echo "Starting application..."\n\
exec dotnet  Imip.JettyApproval.Web.dll' > /app/entrypoint.sh && \
chmod +x /app/entrypoint.sh

# Use exec form of ENTRYPOINT with shell to ensure proper execution
ENTRYPOINT ["/bin/bash", "/app/entrypoint.sh"]
