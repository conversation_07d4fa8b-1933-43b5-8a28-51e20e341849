using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Volo.Abp.Security.Claims;

namespace Imip.JettyApproval.Web.Middleware;

public class JwtBearerMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<JwtBearerMiddleware> _logger;

    public JwtBearerMiddleware(RequestDelegate next, ILogger<JwtBearerMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // Only process API requests
        if (context.Request.Path.StartsWithSegments("/api"))
        {
            var authorizationHeader = context.Request.Headers["Authorization"].FirstOrDefault();

            if (!string.IsNullOrEmpty(authorizationHeader) &&
                authorizationHeader.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase))
            {
                var token = authorizationHeader.Substring("Bearer ".Length).Trim();

                _logger.LogDebug("Processing JWT Bearer token for API request: {Path}", context.Request.Path);

                try
                {
                    // Try to authenticate using JWT Bearer scheme
                    var result = await context.AuthenticateAsync("Bearer");

                    if (result?.Succeeded == true)
                    {
                        // Set the user principal
                        context.User = result.Principal;

                        _logger.LogDebug("JWT Bearer authentication successful for user: {Email}",
                            result.Principal.FindFirst(AbpClaimTypes.Email)?.Value);
                    }
                    else
                    {
                        _logger.LogWarning("JWT Bearer authentication failed for request: {Path}", context.Request.Path);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing JWT Bearer token");
                }
            }
        }

        await _next(context);
    }
}

// Extension method for easy registration
public static class JwtBearerMiddlewareExtensions
{
    public static IApplicationBuilder UseJwtBearerMiddleware(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<JwtBearerMiddleware>();
    }
}