import{j as e,r as o,h as M,u as T}from"./vendor-CrSBzUoz.js";import{n as C,ai as R,B as h,P as L,Q as v,aj as $,u as G,D as Q,e as z,f as O,j as V,o as w,ak as B,al as U,a0 as S,am as Y,an as W,I as k,W as J,ao as X,A as Z}from"./app-layout-CNB1Wtrx.js";import{f as E,u as ee,g as te}from"./index-CZZWNLgr.js";import{C as se}from"./chevron-left-BtkzUODq.js";import{v as j,L as ne,E as ae}from"./Loader-DXXM89G3.js";import{S as ie}from"./search-YAT3sv3T.js";import{C as D}from"./cog-DTsC64pW.js";import{A as re,a as le,b as ce,c as oe,d as de,e as ue,f as me,g as he}from"./TableSkeleton-Bzdk6y-O.js";import{D as A,b as F,c as P,d as I,e as q}from"./dialog-DAr_Mtxm.js";import{u as _}from"./index.esm-CT1elm-0.js";import{C as H}from"./checkbox-CayrCcBd.js";import{$ as xe}from"./App-CGHLK9xH.js";import"./radix-DaY-mnHi.js";import"./card-BAJCNJxm.js";/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pe=[["path",{d:"m17 18-6-6 6-6",key:"1yerx2"}],["path",{d:"M7 6v12",key:"1p53r6"}]],ge=C("chevron-first",pe);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fe=[["path",{d:"m7 18 6-6-6-6",key:"lwmzdw"}],["path",{d:"M17 6v12",key:"1o0aio"}]],ye=C("chevron-last",fe);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const je=[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]],ve=C("pencil",je);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const be=[["path",{d:"M20 7h-9",key:"3s1dr2"}],["path",{d:"M14 17H5",key:"gfn3mx"}],["circle",{cx:"17",cy:"17",r:"3",key:"18b49y"}],["circle",{cx:"7",cy:"7",r:"3",key:"dfmy0x"}]],Ne=C("settings-2",be);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ce=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]],Se=C("trash",Ce),we=({pageCount:n,table:t})=>{const r=R(n,t.getState().pagination.pageIndex),a=t.getState().pagination.pageIndex>=0&&t.getState().pagination.pageIndex<n-1,l=i=>i==="SPACER"?e.jsx("span",{className:"text-primary",children:"..."},j()):e.jsx(h,{size:"sm",disabled:t.getState().pagination.pageIndex===Number(i)-1,onClick:()=>{t.setPageIndex(Number(i)-1)},children:i},j());return e.jsxs("section",{className:"pagination flex items-center space-x-1",children:[e.jsx(h,{size:"sm",disabled:!t.getCanPreviousPage(),onClick:()=>{t.getCanPreviousPage()&&t.setPageIndex(0)},children:e.jsx(ge,{width:24,height:24})}),e.jsx(h,{size:"sm",disabled:!t.getCanPreviousPage(),onClick:()=>{t.getCanPreviousPage()&&t.previousPage()},children:e.jsx(se,{width:24,height:24})}),e.jsxs("div",{className:"block pl-2 pr-2 lg:hidden",children:[t.getState().pagination.pageIndex," / ",n]}),e.jsx("div",{className:"hidden sm:ml-1 sm:mr-1 sm:space-x-2 lg:inline-block",children:r.map(l)}),e.jsx(h,{size:"sm",disabled:!a,onClick:()=>{a&&t.nextPage()},children:e.jsx(L,{width:24,height:24})}),e.jsx(h,{size:"sm",disabled:!a,onClick:()=>{a&&t.setPageIndex(n-1)},children:e.jsx(ye,{width:24,height:24})})]})},Te=({table:n,totalCount:t,pageSize:r})=>{const a=o.useCallback(()=>n.getHeaderGroups().map(c=>{const x=c.headers;return e.jsx("tr",{className:"first:hidden",children:x.map(d=>d.isPlaceholder?!1:e.jsx("th",{className:"last:1/2 truncate px-3 lg:last:w-1/4",children:E(d.column.columnDef.header,d.getContext())},d.id))},c.id)}),[]),l=o.useCallback(()=>n.getRowModel().rows.map(c=>{const x=c.getVisibleCells();return e.jsx("tr",{className:"hover:text-primary-content border-b border-b-primary transition delay-75 ease-in hover:bg-primary/90",children:x.map(d=>e.jsx("td",{className:"truncate py-3 pl-3 text-left text-xs",children:E(d.column.columnDef.cell,d.getContext())},d.id))},c.id)}),[]);if(t===0)return e.jsx("section",{className:"flex justify-center p-3",children:e.jsx("h3",{className:"text-base-content leading-3",children:"No Records Found"})});const i=Math.ceil(t/r);return e.jsxs("section",{children:[e.jsx("section",{className:"overflow-auto",children:e.jsxs("table",{className:"divide-base-200 text-base-content w-full table-auto divide-y text-left sm:overflow-x-auto lg:table-fixed",children:[e.jsx("thead",{children:a()}),e.jsx("tbody",{children:l()})]})}),e.jsxs("div",{className:"flex flex-col border-t p-5 lg:flex-row lg:items-center",children:[e.jsxs("div",{className:"text-base-content grow pb-2",children:[t," total"]}),t>10&&e.jsx(we,{pageCount:i,table:n})]})]})},ke=Te,Ee=(n,t,r,a)=>M({queryKey:[v.GetTenants,n,t,r,a],queryFn:async()=>{let l=0;n>0&&(l=n*t);const{data:i}=await $({query:{MaxResultCount:t,SkipCount:l,Filter:r,Sorting:a}});return i}}),De=({actions:n})=>{const{can:t}=G(),r=a=>!t(a.policy)||a.visible?!1:e.jsx(V,{children:e.jsxs(h,{onClick:a.callback,children:[a.icon==="permission"&&e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(Ne,{width:18,height:18,className:"text-primary-content flex-1"}),e.jsx("span",{className:"text-primary-content hidden sm:inline",children:"Permission"})]}),a.icon==="trash"&&e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(Se,{width:18,height:18,className:"text-primary-content flex-1"}),e.jsx("span",{className:"text-primary-content hidden sm:inline",children:"Delete"})]}),a.icon==="pencil"&&e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(ve,{width:18,height:18,className:"text-primary-content flex-1"}),e.jsx("span",{className:"text-primary-content hidden sm:inline",children:"Edit"})]}),a.icon==="features"&&e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(D,{width:18,height:18,className:"text-primary-content flex-1"}),e.jsx("span",{className:"text-primary-content hidden sm:inline",children:"Settings"})]})]})},j());return e.jsx("section",{className:"flex items-center space-x-2",children:e.jsxs(Q,{children:[e.jsx(z,{asChild:!0,children:e.jsxs(h,{size:"sm",className:"flex items-center space-x-1",children:[e.jsx(D,{width:16,height:16}),e.jsx("span",{className:"hidden sm:inline",children:"Actions"})]})},j()),e.jsx(O,{children:n.map(r)})]})})},Me=({tenant:{tenantId:n,tenantName:t},onDismiss:r})=>{const{toast:a}=w(),[l,i]=o.useState(!1),m=async()=>{try{await B({path:{id:n}}),a({title:"Success",description:`Tenant "${t}" has been deleted successfully.`}),r()}catch(c){c instanceof Error&&a({title:"Failed",description:`There was a problem when deleting the role ${t}. Kindly try again.`,variant:"destructive"})}};return o.useEffect(()=>{i(!0)},[]),e.jsx(re,{open:l,children:e.jsxs(le,{children:[e.jsxs(ce,{children:[e.jsx(oe,{children:"Are you absolutely sure?"}),e.jsxs(de,{children:['This action cannot be undone. This will permanently delete your tenant name "',t,'"']})]}),e.jsxs(ue,{children:[e.jsx(me,{onClick:r,children:"Cancel"}),e.jsx(he,{onClick:m,children:"Yes"})]})]})})},Ae=(n,t)=>M({queryKey:[v.GetFeatures,n,t],queryFn:async()=>{const{data:r}=await U({query:{providerName:n,providerKey:t}});return r}}),Fe=({onDismiss:n,tenantId:t})=>{const{data:r}=Ae(S.T,t),a=T(),[l,i]=o.useState(!1),[m,c]=o.useState(!1),[x,d]=o.useState(!1),{handleSubmit:f}=_(),{toast:y}=w(),p=()=>{d(!1),n()};o.useEffect(()=>(d(!0),r?.groups?.forEach(s=>{s.features?.forEach(u=>{u.name==="SettingManagement.Enable"&&u.value==="true"?i(!0):u.name==="SettingManagement.AllowChangingEmailSettings"&&u.value==="true"&&c(!0)})}),()=>{a.invalidateQueries({queryKey:[v.GetFeatures]}).then(),a.invalidateQueries({queryKey:[v.GetTenants]}).then(),a.invalidateQueries({queryKey:[S.T]}).then()}),[n,r]);const g=(s,u)=>{u==="SettingManagement.Enable"?i(s):u==="SettingManagement.AllowChangingEmailSettings"&&c(s)},b=async()=>{try{const s={};s.features=[{name:"SettingManagement.Enable",value:l.toString()},{name:"SettingManagement.AllowChangingEmailSettings",value:m.toString()}],await W({body:s,query:{providerKey:S.T,providerName:t}}),y({title:"Success",description:"Features Update Successfully",variant:"default"}),p()}catch(s){s instanceof Error&&y({title:"Failed",description:"Feature update failed.",variant:"destructive"})}},N=async()=>{try{await Y({query:{providerKey:S.T,providerName:t}}),y({title:"Success",description:"Features has been set to default.",variant:"default"}),p()}catch(s){s instanceof Error&&y({title:"Failed",description:"Features wasn't able to reset tp default.",variant:"destructive"})}};return e.jsx("section",{className:"p-3",children:e.jsx(A,{open:x,onOpenChange:p,children:e.jsxs(F,{children:[e.jsx(P,{children:e.jsx(I,{children:"Features"})}),e.jsxs("form",{onSubmit:f(b),children:[e.jsxs("div",{className:"grid grid-cols-1 items-baseline gap-2 sm:grid-cols-[12rem_minmax(10rem,1fr)_auto]",children:[e.jsx("div",{className:"p-3",children:r?.groups?.map(s=>e.jsx("span",{children:s.displayName},j()))}),e.jsx("div",{className:"mt-5 p-3",children:r?.groups?.map(s=>e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-medium",children:s.displayName}),e.jsx("hr",{className:"mt-2 w-full pb-2"}),s.features?.map(u=>e.jsxs("div",{className:"mt-2 text-base",children:[e.jsx(H,{id:`${u.name}_enable`,name:u.name,checked:u.name==="SettingManagement.Enable"?l:m,onCheckedChange:K=>g(!!K.valueOf(),u.name)}),e.jsx("label",{htmlFor:`${u.name}_enable`,className:"text-sm font-medium leading-none",children:e.jsx("span",{className:"pl-2",children:u.displayName})}),e.jsx("p",{className:"pl-6 pt-1 text-xs",children:u.description})]},j()))]},j()))})]}),e.jsxs(q,{className:"mt-5",children:[e.jsx(h,{onClick:async s=>{s.preventDefault(),await N()},children:"Reset to default"}),e.jsx(h,{onClick:s=>{s.preventDefault(),p()},children:"Cancel"}),e.jsx(h,{type:"submit",children:"Save"})]})]})]})})})},Pe=({tenantDto:n,tenantId:t,onDismiss:r})=>{const a=T(),[l,i]=o.useState(!1),[m,c]=o.useState(!1),{toast:x}=w(),{handleSubmit:d,register:f}=_();o.useEffect(()=>{n?.extraProperties?.Host&&c(!0)},[n,n?.extraProperties?.Host]);const y=async g=>{const b=g;try{await X({body:b,path:{id:t}}),x({title:"Success",description:"Tenant information updated successfully",variant:"default"}),i(!1)}catch(N){N instanceof Error&&x({title:"Failed",description:"Tenant update wasn't successfull.",variant:"destructive"})}},p=()=>{i(!1),r()};return o.useEffect(()=>(i(!0),()=>{a.invalidateQueries({queryKey:[v.GetTenants]})}),[]),e.jsx(A,{open:l,onOpenChange:p,children:e.jsxs(F,{children:[e.jsx(P,{children:e.jsx(I,{children:"Update a Tenant Name"})}),e.jsxs("form",{onSubmit:d(y),children:[e.jsx("section",{className:"flex flex-col space-y-5",children:e.jsx(k,{required:!0,placeholder:"Tenant Name",defaultValue:n.name??"",...f("name")})}),e.jsxs("div",{className:J("flex items-center space-x-2 pb-2 pt-5"),children:[e.jsx(H,{id:"enableHost",name:"enableHost",defaultChecked:!0,checked:m,onCheckedChange:g=>c(!!g.valueOf())}),e.jsx("label",{htmlFor:"activeHost",className:"text-sm font-medium leading-none",children:"Enable host"})]}),m&&e.jsx("div",{className:"flex w-auto flex-col pb-5 pt-5",children:e.jsx("section",{className:"flex w-full flex-col space-y-5",children:e.jsx(k,{required:!0,...f("host"),placeholder:"Hose Name"})})}),e.jsxs(q,{className:"mt-5",children:[e.jsx(h,{onClick:g=>{g.preventDefault(),p()},children:"Cancel"}),e.jsx(h,{type:"submit",children:"Save"})]})]})]})})},Ie=()=>{const{toast:n}=w(),t=T(),[r,a]=o.useState(),[l,i]=o.useState(),[{pageIndex:m,pageSize:c},x]=o.useState({pageIndex:0,pageSize:10}),{isLoading:d,data:f,isError:y}=Ee(m,c,r),p=o.useMemo(()=>({pageIndex:m,pageSize:c}),[m,c,n]),g=o.useMemo(()=>[{header:"Tenant Management",columns:[{accessorKey:"actions",header:"Actions",cell:s=>e.jsx(De,{actions:[{icon:"features",policy:"AbpTenantManagement.Tenants.ManageFeatures",callback:()=>{i({dialgoType:"manage_features",tenantId:s.row.original.id,tenantDto:s.row.original})}},{icon:"pencil",policy:"AbpTenantManagement.Tenants.Update",callback:()=>{i({dialgoType:"edit",tenantId:s.row.original.id,tenantDto:s.row.original})}},{icon:"trash",policy:"AbpTenantManagement.Tenants.Delete",callback:()=>{i({tenantId:s.row.original.id,tenantDto:s.row.original,dialgoType:"delete"})}}]})},{accessorKey:"name",header:"Tenant Name",cell:s=>s.getValue()}]}],[l]),b=s=>{a(s)},N=ee({data:f?.items??[],pageCount:f?.totalCount??-1,state:{pagination:p},columns:g,getCoreRowModel:te(),onPaginationChange:x,manualPagination:!0});return d?e.jsx(ne,{}):y?e.jsx(ae,{}):e.jsxs(e.Fragment,{children:[l?.dialgoType==="edit"&&e.jsx(Pe,{tenantDto:l.tenantDto,tenantId:l.tenantId,onDismiss:()=>{t.invalidateQueries({queryKey:[v.GetTenants]}),i(null)}}),l?.dialgoType==="delete"&&e.jsx(Me,{tenant:{tenantId:l.tenantId,tenantName:l.tenantDto.name},onDismiss:()=>{t.invalidateQueries({queryKey:[v.GetTenants]}),i(null)}}),l?.dialgoType==="manage_features"&&e.jsx(Fe,{onDismiss:()=>i(null),tenantId:l.tenantId}),e.jsx(ie,{onUpdate:b,value:r??""}),e.jsx(ke,{table:N,totalCount:f?.totalCount??0,pageSize:c})]})};function Ye(){return e.jsxs(Z,{children:[e.jsx(xe,{title:"Dashboard"}),e.jsx(Ie,{})]})}export{Ye as default};
//# sourceMappingURL=tenant-g0dJleBN.js.map
