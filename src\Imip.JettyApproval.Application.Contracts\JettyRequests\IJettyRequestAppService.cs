using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.JettyApproval.JettyRequests;

/// <summary>
/// Application service interface for JettyRequest entity
/// </summary>
public interface IJettyRequestAppService :
    ICrudAppService<
        JettyRequestDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateJettyRequestDto,
        CreateUpdateJettyRequestDto>
{
    /// <summary>
    /// Get jetty requests by vessel name
    /// </summary>
    Task<List<JettyRequestDto>> GetByVesselNameAsync(string vesselName);

    /// <summary>
    /// Get jetty requests by jetty location
    /// </summary>
    Task<List<JettyRequestDto>> GetByJettyAsync(string jetty);

    /// <summary>
    /// Get jetty requests by date range
    /// </summary>
    Task<List<JettyRequestDto>> GetByDateRangeAsync(DateTime startDate, DateTime endDate);
}