import{r as n,j as e}from"./vendor-CrSBzUoz.js";import{A as H,I as p,D as f,e as v,B as c,f as S,g as U,h as Y,i as E,j as y,L as N,S as _,a as P,b as q,c as G,d as D}from"./app-layout-CNB1Wtrx.js";import{C as Q,a as W,b as X,c as K}from"./card-BAJCNJxm.js";import{T as ee,a as se,b as J,c as C,d as ae,e as b}from"./table-CAbNlII1.js";import{C as T}from"./checkbox-CayrCcBd.js";import{D as te,b as ne,c as le,d as ce,e as ie}from"./dialog-DAr_Mtxm.js";import{I as re}from"./IconChevronDown-D4jBeGMo.js";import"./radix-DaY-mnHi.js";import"./App-CGHLK9xH.js";const h=Array(10).fill(null).map((x,l)=>({id:`jetty_${l+1}`,location:`Fatufia ${l%2===0?"A":"B"}`,jettyName:`F${l+1}`,boundedZone:l%3===0?"Yes":"No"}));function Ne(){const[x,l]=n.useState(""),[r,m]=n.useState(new Set),[o,k]=n.useState(new Set(Object.keys(h[0]))),[M,d]=n.useState(!1),[A,w]=n.useState(null),[I,j]=n.useState(""),[L,u]=n.useState(""),[O,g]=n.useState("No"),i=h.filter(s=>Object.values(s).some(a=>a.toString().toLowerCase().includes(x.toLowerCase()))),Z=s=>{m(s?new Set(i.map(a=>a.id)):new Set)},F=(s,a)=>{const t=new Set(r);a?t.add(s):t.delete(s),m(t)},B=(s,a)=>{const t=new Set(o);a?t.add(s):t.delete(s),k(t)},z=()=>{w(null),j(""),u(""),g("No"),d(!0)},R=s=>{w(s),j(s.location),u(s.jettyName),g(s.boundedZone),d(!0)},V=s=>{},$=()=>{d(!1)};return e.jsxs(H,{children:[e.jsx("div",{className:"container mx-auto p-4",children:e.jsxs(Q,{children:[e.jsx(W,{children:e.jsx(X,{className:"text-2xl font-bold",children:"Manage Jetty"})}),e.jsxs(K,{children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx(p,{placeholder:"Filter lines...",value:x,onChange:s=>l(s.target.value),className:"max-w-sm"}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsxs(f,{children:[e.jsx(v,{asChild:!0,children:e.jsxs(c,{variant:"outline",children:["Columns ",e.jsx(re,{className:"ml-2 h-4 w-4"})]})}),e.jsx(S,{align:"end",children:Object.keys(h[0]).map(s=>e.jsx(U,{className:"capitalize",checked:o.has(s),onCheckedChange:a=>B(s,a===!0),children:s.replace(/([A-Z])/g," $1").trim()},s))})]}),e.jsx(c,{onClick:z,children:"Create New Jetty"})]})]}),e.jsx("div",{className:"rounded-md border",children:e.jsxs(ee,{children:[e.jsx(se,{children:e.jsxs(J,{children:[e.jsx(C,{className:"w-[30px]",children:e.jsx(T,{checked:r.size===i.length&&i.length>0,onCheckedChange:s=>Z(s===!0)})}),Object.keys(h[0]).map(s=>o.has(s)&&s!=="id"&&e.jsx(C,{className:"capitalize",children:s.replace(/([A-Z])/g," $1").trim()},s)),e.jsx(C,{className:"text-right",children:"Actions"})]})}),e.jsx(ae,{children:i.map(s=>e.jsxs(J,{children:[e.jsx(b,{children:e.jsx(T,{checked:r.has(s.id),onCheckedChange:a=>F(s.id,a===!0)})}),Object.entries(s).map(([a,t])=>o.has(a)&&a!=="id"&&e.jsx(b,{children:t},a)),e.jsx(b,{className:"text-right",children:e.jsxs(f,{children:[e.jsx(v,{asChild:!0,children:e.jsxs(c,{variant:"ghost",className:"h-8 w-8 p-0",children:[e.jsx("span",{className:"sr-only",children:"Open menu"}),e.jsx(Y,{className:"h-4 w-4"})]})}),e.jsxs(S,{align:"end",children:[e.jsx(E,{children:"Actions"}),e.jsx(y,{onClick:()=>R(s),children:"Update"}),e.jsx(y,{onClick:()=>V(s.id),children:"Delete"})]})]})})]},s.id))})]})}),e.jsxs("div",{className:"flex items-center justify-between mt-4",children:[e.jsxs("div",{className:"text-sm text-gray-500",children:[r.size," of ",i.length," row(s) selected."]}),e.jsxs("div",{className:"space-x-2",children:[e.jsx(c,{variant:"outline",size:"sm",children:"Previous"}),e.jsx(c,{variant:"outline",size:"sm",children:"Next"})]})]})]})]})}),e.jsx(te,{open:M,onOpenChange:d,children:e.jsxs(ne,{className:"sm:max-w-[425px]",children:[e.jsx(le,{children:e.jsx(ce,{children:A?"Update Jetty":"Create New Jetty"})}),e.jsxs("div",{className:"grid gap-4 py-4",children:[e.jsxs("div",{className:"grid grid-cols-4 items-center gap-4",children:[e.jsx(N,{htmlFor:"location",className:"text-right",children:"Location"}),e.jsx(p,{id:"location",value:I,onChange:s=>j(s.target.value),className:"col-span-3"})]}),e.jsxs("div",{className:"grid grid-cols-4 items-center gap-4",children:[e.jsx(N,{htmlFor:"jettyName",className:"text-right",children:"Jetty"}),e.jsx(p,{id:"jettyName",value:L,onChange:s=>u(s.target.value),className:"col-span-3"})]}),e.jsxs("div",{className:"grid grid-cols-4 items-center gap-4",children:[e.jsx(N,{htmlFor:"boundedZone",className:"text-right",children:"Bounded Z"}),e.jsxs(_,{onValueChange:s=>g(s),value:O,children:[e.jsx(P,{className:"col-span-3",children:e.jsx(q,{placeholder:"Select Bounded Zone"})}),e.jsxs(G,{children:[e.jsx(D,{value:"Yes",children:"Yes"}),e.jsx(D,{value:"No",children:"No"})]})]})]})]}),e.jsx(ie,{children:e.jsx(c,{onClick:$,className:"px-6 py-2 bg-black text-white rounded-md hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50",children:"Save"})})]})})]})}export{Ne as default};
//# sourceMappingURL=jetty-CpCMtfA1.js.map
