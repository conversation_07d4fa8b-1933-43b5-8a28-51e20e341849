using Imip.JettyApproval.Web.Services.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Linq;
using System.Threading.Tasks;

namespace Imip.JettyApproval.Web.Middleware;

public class TokenRefreshMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<TokenRefreshMiddleware> _logger;

    public TokenRefreshMiddleware(RequestDelegate next, ILogger<TokenRefreshMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, ITokenService tokenService)
    {
        // Skip token refresh for certain paths
        if (ShouldSkipTokenRefresh(context.Request.Path))
        {
            await _next(context);
            return;
        }

        try
        {
            // Check if user is authenticated
            if (context.User?.Identity?.IsAuthenticated == true)
            {
                // Try to get a valid token (this will automatically refresh if needed)
                var validToken = await tokenService.GetValidAccessTokenAsync();

                if (string.IsNullOrEmpty(validToken))
                {
                    _logger.LogWarning("No valid token available for user: {UserName}", context.User.Identity.Name);

                    // Token refresh failed, redirect to login
                    context.Response.Redirect("/Account/Login");
                    return;
                }

                _logger.LogDebug("Token validation completed for user: {UserName}", context.User.Identity.Name);
            }
        }
        catch (System.Exception ex)
        {
            _logger.LogError(ex, "Error in token refresh middleware");
            // Continue with the request even if token refresh fails
        }

        await _next(context);
    }

    private bool ShouldSkipTokenRefresh(PathString path)
    {
        // Skip token refresh for these paths
        var skipPaths = new[]
        {
            "/Account/Login",
            "/Account/Logout",
            "/signin-oidc",
            "/signout-oidc",
            "/signout-callback-oidc",
            "/health-status",
            "/swagger",
            "/api/token", // Skip for token endpoints to avoid infinite loops
            "/css",
            "/js",
            "/images",
            "/favicon.ico"
        };

        return skipPaths.Any(skipPath => path.StartsWithSegments(skipPath));
    }
}