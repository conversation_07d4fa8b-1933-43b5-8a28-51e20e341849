using System;
using Volo.Abp.Application.Dtos;

namespace Imip.JettyApproval.Approvals.ApprovalDelegations;

/// <summary>
/// DTO for ApprovalDelegation entity
/// </summary>
public class ApprovalDelegationDto : FullAuditedEntityDto<Guid>
{
    /// <summary>
    /// ID of the original approver
    /// </summary>
    public Guid ApproverId { get; set; }

    /// <summary>
    /// ID of the substitute approver
    /// </summary>
    public Guid SubstituteId { get; set; }

    /// <summary>
    /// Start date of the delegation
    /// </summary>
    public DateTime StartDate { get; set; }

    /// <summary>
    /// End date of the delegation
    /// </summary>
    public DateTime EndDate { get; set; }

    /// <summary>
    /// Whether the delegation is currently active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Additional notes for the delegation
    /// </summary>
    public string? Notes { get; set; }
}