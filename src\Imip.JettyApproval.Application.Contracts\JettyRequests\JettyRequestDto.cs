using System;
using System.Collections.Generic;
using Volo.Abp.Application.Dtos;

namespace Imip.JettyApproval.JettyRequests;

/// <summary>
/// DTO for JettyRequest entity
/// </summary>
public class JettyRequestDto : FullAuditedEntityDto<Guid>
{
    /// <summary>
    /// Document number for the jetty request
    /// </summary>
    public int DocNum { get; set; }

    /// <summary>
    /// Type of vessel
    /// </summary>
    public string? VesselType { get; set; }

    /// <summary>
    /// Reference ID for the request
    /// </summary>
    public Guid? ReferenceId { get; set; }

    /// <summary>
    /// Name of the vessel
    /// </summary>
    public string? VesselName { get; set; }

    /// <summary>
    /// Voyage information
    /// </summary>
    public string? Voyage { get; set; }

    /// <summary>
    /// Jetty name/location
    /// </summary>
    public string? Jetty { get; set; }

    /// <summary>
    /// Arrival date and time
    /// </summary>
    public DateTime? ArrivalDate { get; set; }

    /// <summary>
    /// Departure date and time
    /// </summary>
    public DateTime? DepartureDate { get; set; }

    /// <summary>
    /// Aside date and time
    /// </summary>
    public DateTime? AsideDate { get; set; }

    /// <summary>
    /// Cast off date and time
    /// </summary>
    public DateTime? CastOfDate { get; set; }

    /// <summary>
    /// Post date and time
    /// </summary>
    public DateTime? PostDate { get; set; }

    /// <summary>
    /// Collection of jetty request items
    /// </summary>
    public List<JettyRequestItemDto> Items { get; set; } = new List<JettyRequestItemDto>();
}