import{r as a,j as e}from"./vendor-CrSBzUoz.js";import{A as te,I as g,S as N,a as C,b as S,c as D,d as x,B as c,D as ae,e as le,f as ne,g as ie}from"./app-layout-CNB1Wtrx.js";import{C as re,a as ce,b as oe,c as de}from"./card-BAJCNJxm.js";import{C as T}from"./checkbox-CayrCcBd.js";import{D as ue,a as me,b as xe,c as he,d as pe}from"./dialog-DAr_Mtxm.js";import{T as je,a as ge,b as V,c as A,d as fe,e as k}from"./table-CAbNlII1.js";import{D as d,H as ve,r as be}from"./ht-theme-main.min-D0JFAomv.js";import{D as we}from"./DocumentPreviewDialog-DkiCQKZH.js";import{F as O,a as n}from"./FormField-BFBouSal.js";import{I as ye}from"./IconChevronDown-D4jBeGMo.js";import"./radix-DaY-mnHi.js";import"./App-CGHLK9xH.js";import"./chevron-left-BtkzUODq.js";be();const Ne=Array(10).fill(null).map((h,i)=>({tenant:i%2===0?"ITSS":"IRNC",itemName:"STEEL PRODUCT",quantity:"36.399",uom:"MT",remark:"Remark 1",status:i%3===0?"Data":"Draft",action:""})),R=[{data:"tenant",type:"text",title:"Tenant"},{data:"itemName",type:"text",title:"Item Name"},{data:"quantity",type:"numeric",title:"Quantity"},{data:"uom",type:"text",title:"UoM"},{data:"remark",type:"text",title:"Remark"},{data:"status",type:"text",title:"Status",readOnly:!0},{data:"action",title:"Action",readOnly:!0}],o=Array(10).fill(null).map((h,i)=>({docNum:`2505000${i+1}`,vesselName:"MV. ORIENTAL LUNA",voyage:`00${i+1}`,arrival:"2025-05-01",departure:"2025-05-02"}));function Ee(){const[h,i]=a.useState("25060001"),[B,H]=a.useState(""),[f,v]=a.useState(""),[L,M]=a.useState("001"),[P,I]=a.useState(""),[z]=a.useState(Ne),[F,b]=a.useState(!1),[r,p]=a.useState(new Set),[j,E]=a.useState(""),[u,_]=a.useState(new Set(Object.keys(o[0]))),[J,w]=a.useState(!1),[$,q]=a.useState(""),U=a.useRef(null),Z=s=>{p(s?new Set(o.map(t=>t.docNum)):new Set)},Q=(s,t)=>{const l=new Set(r);t?l.add(s):l.delete(s),p(l)},W=()=>{if(r.size>0){const s=Array.from(r)[0],t=o.find(l=>l.docNum===s);t&&v(t.vesselName+" "+t.voyage)}else v("");b(!1)},G=(s,t)=>{const l=new Set(u);t?l.add(s):l.delete(s),_(l)},m=o.filter(s=>s.docNum.toLowerCase().includes(j.toLowerCase())||s.vesselName.toLowerCase().includes(j.toLowerCase())),K=()=>{},X=a.useCallback((s,t,l,Ce,Se,De,Te)=>{const ee=`<button class="px-2 py-1 bg-blue-500 text-white rounded-md text-sm hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 mr-2" data-row="${l}" data-action="preview">Preview</button>`,se='<button class="px-2 py-1 bg-blue-500 text-white rounded-md text-sm hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">Submit</button>';t.innerHTML=ee+se;const y=t.querySelector('[data-action="preview"]');y&&y.addEventListener("click",()=>{q("/pdf/surat1.pdf"),w(!0)})},[]),Y=R.map(s=>s.data==="action"?{...s,renderer:X}:s);return e.jsxs(te,{children:[e.jsx("div",{className:"container mx-auto",children:e.jsxs(re,{children:[e.jsx(ce,{children:e.jsx(oe,{className:"text-2xl font-bold",children:"New Application"})}),e.jsxs(de,{children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-2",children:[e.jsxs(O,{children:[e.jsx(n,{label:"DocNum",children:e.jsx(g,{id:"docNum",value:h,onChange:s=>i(s.target.value)})}),e.jsx(n,{label:"Vessel Type",children:e.jsxs(N,{value:B,onValueChange:H,children:[e.jsx(C,{className:"w-full",children:e.jsx(S,{placeholder:"Select Vessel Type"})}),e.jsxs(D,{children:[e.jsx(x,{value:"typeA",children:"Type A"}),e.jsx(x,{value:"typeB",children:"Type B"})]})]})}),e.jsx(n,{label:"Vessel",children:e.jsxs(ue,{open:F,onOpenChange:b,children:[e.jsx(me,{asChild:!0,children:e.jsx(c,{variant:"outline",className:"w-full justify-start font-normal",children:f||"Select Vessel"})}),e.jsxs(xe,{className:"min-w-[900px] w-auto max-h-[70vh] flex flex-col",children:[e.jsx(he,{children:e.jsx(pe,{children:"Select Vessel"})}),e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx(g,{placeholder:"Filter lines...",value:j,onChange:s=>E(s.target.value),className:"max-w-sm"}),e.jsxs(ae,{children:[e.jsx(le,{asChild:!0,children:e.jsxs(c,{variant:"outline",className:"ml-auto",children:["Columns ",e.jsx(ye,{className:"ml-2 h-4 w-4"})]})}),e.jsx(ne,{align:"end",children:Object.keys(o[0]).map(s=>e.jsx(ie,{className:"capitalize",checked:u.has(s),onCheckedChange:t=>G(s,t===!0),children:s.replace(/([A-Z])/g," $1").trim()},s))})]})]}),e.jsx("div",{className:"flex-grow overflow-auto border rounded-md",children:e.jsxs(je,{children:[e.jsx(ge,{className:"sticky top-0 bg-white",children:e.jsxs(V,{children:[e.jsx(A,{className:"w-[30px]",children:e.jsx(T,{checked:r.size===m.length&&m.length>0,onCheckedChange:s=>Z(s===!0)})}),Object.keys(o[0]).map(s=>u.has(s)&&e.jsx(A,{className:"capitalize",children:s.replace(/([A-Z])/g," $1").trim()},s))]})}),e.jsx(fe,{children:m.map(s=>e.jsxs(V,{className:"cursor-pointer hover:bg-gray-100",children:[e.jsx(k,{children:e.jsx(T,{checked:r.has(s.docNum),onCheckedChange:t=>Q(s.docNum,t===!0)})}),Object.entries(s).map(([t,l])=>u.has(t)&&e.jsx(k,{children:l},t))]},s.docNum))})]})}),e.jsxs("div",{className:"flex items-center justify-between mt-4",children:[e.jsxs("div",{className:"text-sm text-gray-500",children:[r.size," of ",m.length," row(s) selected."]}),e.jsxs("div",{className:"space-x-2",children:[e.jsx(c,{variant:"outline",size:"sm",children:"Previous"}),e.jsx(c,{variant:"outline",size:"sm",children:"Next"})]}),e.jsx(c,{onClick:W,children:"Select Vessel"})]})]})]})}),e.jsx(n,{label:"Voyage",children:e.jsx(g,{id:"voyage",value:L,onChange:s=>M(s.target.value)})}),e.jsx(n,{label:"Jetty",children:e.jsxs(N,{value:P,onValueChange:I,children:[e.jsx(C,{className:"w-full",children:e.jsx(S,{placeholder:"Select Jetty"})}),e.jsxs(D,{children:[e.jsx(x,{value:"jettyA",children:"Jetty A"}),e.jsx(x,{value:"jettyB",children:"Jetty B"})]})]})})]}),e.jsxs(O,{children:[e.jsx(n,{label:"Arrival Date & Time",children:e.jsx("div",{className:"flex gap-2",children:e.jsx(d,{})})}),e.jsx(n,{label:"Departure Date & Time",children:e.jsx("div",{className:"flex gap-2",children:e.jsx(d,{})})}),e.jsx(n,{label:"A/Side Date & Time",children:e.jsx("div",{className:"flex gap-2",children:e.jsx(d,{})})}),e.jsx(n,{label:"Cast Of Date & Time",children:e.jsx("div",{className:"flex gap-2",children:e.jsx(d,{})})}),e.jsx(n,{label:"Posting Date & Time",children:e.jsx("div",{className:"flex gap-2",children:e.jsx(d,{})})})]})]}),e.jsx("div",{className:"mb-8",children:e.jsx(ve,{ref:U,themeName:"ht-theme-main",data:z,columns:Y,colHeaders:R.map(s=>s.title),rowHeaders:!0,height:"auto",autoWrapRow:!0,licenseKey:"non-commercial-and-evaluation",stretchH:"all",contextMenu:!0})}),e.jsx("div",{className:"flex justify-end",children:e.jsx(c,{onClick:K,className:"px-6 py-2 bg-black text-white rounded-md hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50",children:"Save"})})]})]})}),e.jsx(we,{isOpen:J,onOpenChange:w,documentSrc:$})]})}export{Ee as default};
//# sourceMappingURL=page-fkU3I9lc.js.map
