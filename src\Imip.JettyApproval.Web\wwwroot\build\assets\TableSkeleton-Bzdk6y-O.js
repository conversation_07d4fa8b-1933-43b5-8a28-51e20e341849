import{j as a}from"./vendor-CrSBzUoz.js";import{v as j,w as g,x as p,D as N,y as w,A as h,z as b,B as v}from"./radix-DaY-mnHi.js";import{M as l,R as d}from"./app-layout-CNB1Wtrx.js";import{C as A}from"./card-BAJCNJxm.js";function s({className:e,...t}){return a.jsx("div",{"data-slot":"skeleton",className:l("bg-accent animate-pulse rounded-md",e),...t})}function T({...e}){return a.jsx(j,{"data-slot":"alert-dialog",...e})}function y({...e}){return a.jsx(b,{"data-slot":"alert-dialog-portal",...e})}function D({className:e,...t}){return a.jsx(v,{"data-slot":"alert-dialog-overlay",className:l("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function R({className:e,...t}){return a.jsxs(y,{children:[a.jsx(D,{}),a.jsx(g,{"data-slot":"alert-dialog-content",className:l("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...t})]})}function O({className:e,...t}){return a.jsx("div",{"data-slot":"alert-dialog-header",className:l("flex flex-col gap-2 text-center sm:text-left",e),...t})}function P({className:e,...t}){return a.jsx("div",{"data-slot":"alert-dialog-footer",className:l("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function _({className:e,...t}){return a.jsx(p,{"data-slot":"alert-dialog-title",className:l("text-lg font-semibold",e),...t})}function B({className:e,...t}){return a.jsx(N,{"data-slot":"alert-dialog-description",className:l("text-muted-foreground text-sm",e),...t})}function E({className:e,...t}){return a.jsx(h,{className:l(d(),e),...t})}function H({className:e,...t}){return a.jsx(w,{className:l(d({variant:"outline"}),e),...t})}function M({rowCount:e=10,columnCount:t=4,hasTitle:m=!0,hasSearch:i=!0,hasFilters:c=!0,hasPagination:x=!0,hasActions:u=!0}){const f=Array.from({length:e},(r,o)=>o),n=Array.from({length:t},(r,o)=>o);return a.jsxs(A,{className:"space-y-4 py-4",children:[m&&a.jsxs("div",{className:"flex items-center justify-between mb-6 px-4",children:[a.jsx(s,{className:"h-8 w-48"})," ",u&&a.jsxs("div",{className:"flex space-x-2",children:[a.jsx(s,{className:"h-9 w-24"})," ",a.jsx(s,{className:"h-9 w-24"})," "]})]}),(i||c)&&a.jsxs("div",{className:"flex items-center justify-between mb-4 px-4",children:[i&&a.jsx(s,{className:"h-10 w-64"})," ",c&&a.jsx(s,{className:"h-10 w-32"})," "]}),a.jsxs("div",{className:"flex w-full border-b pb-2 px-4",children:[a.jsx(s,{className:"h-6 w-8 mr-4"})," ",n.map(r=>a.jsx(s,{className:`h-6 ${r===n.length-1?"w-1/6":"w-1/4 mr-4"}`},`header-${r}`))]}),f.map(r=>a.jsxs("div",{className:"flex w-full py-3 border-b px-4",children:[a.jsx(s,{className:"h-5 w-5 mr-4"})," ",n.map(o=>a.jsx(s,{className:`h-5 ${o===n.length-1?"w-1/6":"w-1/4 mr-4"}`},`cell-${r}-${o}`))]},`row-${r}`)),x&&a.jsxs("div",{className:"flex items-center justify-between pt-4 px-4",children:[a.jsx(s,{className:"h-5 w-32"})," ",a.jsxs("div",{className:"flex space-x-1",children:[a.jsx(s,{className:"h-8 w-8"})," ",a.jsx(s,{className:"h-8 w-8"})," ",a.jsx(s,{className:"h-8 w-8"})," ",a.jsx(s,{className:"h-8 w-8"})," "]})]})]})}export{T as A,M as T,R as a,O as b,_ as c,B as d,P as e,H as f,E as g};
//# sourceMappingURL=TableSkeleton-Bzdk6y-O.js.map
