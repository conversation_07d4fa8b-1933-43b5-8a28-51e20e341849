import{r as n,j as e,u as k,f as V,h as W}from"./vendor-CrSBzUoz.js";import{o as H,Y,u as O,D as _,e as Z,B as b,f as J,j as T,M as X,T as ee,I as $,Z as se,Q as P,_ as te,$ as ae,a0 as R,a1 as ie,a2 as ne,A as le}from"./app-layout-CNB1Wtrx.js";import{D as I,u as F,h as Q}from"./DataTableColumnHeader-DJ80pmDz.js";import{A as re,a as oe,b as ce,c as de,d as ue,e as me,f as pe,g as he,T as fe}from"./TableSkeleton-Bzdk6y-O.js";import{V as xe,D as ge}from"./DataTable-BL4SJdnU.js";import{l as ye,p as je,_ as be,a as Se}from"./popover-7NwOVASC.js";import{C as A}from"./checkbox-CayrCcBd.js";import{S as ve}from"./search-YAT3sv3T.js";import{D as E,a as Ne,b as G,c as q,d as M,e as K}from"./dialog-DAr_Mtxm.js";import{u as z}from"./index.esm-CT1elm-0.js";import{F as L,a as w}from"./FormField-BFBouSal.js";import{u as Ce,P as De,T as Pe}from"./TogglePermission-LBMxXwtr.js";import{$ as we}from"./App-CGHLK9xH.js";import"./radix-DaY-mnHi.js";import"./card-BAJCNJxm.js";import"./table-CAbNlII1.js";import"./tiny-invariant-CopsF_GD.js";import"./index-CZZWNLgr.js";const Ae=({dataId:t,onDismiss:s})=>{const{toast:r}=H(),[c,i]=n.useState(!1),u=async()=>{try{await Y({path:{id:t}}),r({title:"Success",description:"Role has been deleted successfully."}),s()}catch(p){p instanceof Error&&r({title:"Failed",description:"There was a problem when deleting the role. Kindly try again.",variant:"destructive"})}};return n.useEffect(()=>{i(!0)},[]),e.jsx(re,{open:c,children:e.jsxs(oe,{children:[e.jsxs(ce,{children:[e.jsx(de,{children:"Are you absolutely sure?"}),e.jsx(ue,{children:"This action cannot be undone. This will permanently delete your this role."})]}),e.jsxs(me,{children:[e.jsx(pe,{onClick:s,children:"Cancel"}),e.jsx(he,{onClick:u,children:"Yes"})]})]})})},Re=({userId:t,userDto:s,onAction:r,variant:c="dropdown"})=>{const{can:i}=O();return c==="dropdown"?e.jsx("div",{className:"flex justify-end",children:e.jsxs(_,{children:[e.jsx(Z,{asChild:!0,children:e.jsxs(b,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:[e.jsx(ye,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"Open menu"})]})}),e.jsxs(J,{align:"end",className:"w-[160px]",children:[i("AbpIdentity.Roles.Update")&&e.jsx(T,{className:"cursor-pointer text-sm",onClick:()=>r(t,s,"edit"),children:"Edit"}),i("AbpIdentity.Roles.ManagePermissions")&&e.jsx(T,{className:"cursor-pointer text-sm",onClick:()=>r(t,s,"permission"),children:"Permission"}),i("AbpIdentity.Roles.Delete")&&e.jsx(T,{className:"cursor-pointer text-sm text-red-500",onClick:()=>r(t,s,"delete"),children:"Delete"})]})]})}):e.jsxs("div",{className:"flex items-center justify-end gap-1",children:[i("AbpIdentity.Users.ManagePermissions")&&e.jsxs(b,{variant:"primary",size:"sm",className:"flex items-center gap-1 px-2 py-1",onClick:()=>r(t,s,"permission"),children:[e.jsx(je,{className:"h-4 w-4"}),e.jsx("span",{children:"Permission"})]}),i("AbpIdentity.Users.Update")&&e.jsxs(b,{variant:"primary",size:"sm",className:"flex items-center gap-1 px-2 py-1",onClick:()=>r(t,s,"edit"),children:[e.jsx(be,{className:"h-4 w-4"}),e.jsx("span",{children:"Edit"})]})]})},U=({value:t,className:s})=>e.jsx("span",{className:X("inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",t?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300":"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",s),children:t?"Yes":"No"}),ke=t=>[{id:"select",header:({table:s})=>e.jsx(A,{checked:s.getIsAllPageRowsSelected()?!0:s.getIsSomeRowsSelected()?"indeterminate":!1,onCheckedChange:()=>s.toggleAllPageRowsSelected(),className:"translate-y-0.5","aria-label":"Select all"}),cell:({row:s})=>e.jsx(A,{checked:s.getIsSelected(),onCheckedChange:()=>s.toggleSelected(),className:"translate-y-0.5","aria-label":"Select row"}),enableSorting:!1,enableHiding:!0,meta:{displayName:"Select"}},{accessorKey:"name",header:({column:s})=>e.jsx(I,{column:s,title:"Name"}),enableSorting:!0,enableHiding:!0,cell:s=>s.getValue(),meta:{className:"text-left",displayName:"Name"}},{accessorKey:"isDefault",header:({column:s})=>e.jsx(I,{column:s,title:"Is Default"}),enableSorting:!0,enableHiding:!0,cell:s=>e.jsx(U,{value:s.getValue()}),meta:{className:"text-left",displayName:"Is Default"}},{accessorKey:"isPublic",header:({column:s})=>e.jsx(I,{column:s,title:"Is Public"}),enableSorting:!0,enableHiding:!0,cell:s=>e.jsx(U,{value:s.getValue()}),meta:{className:"text-left",displayName:"Is Public"}},{id:"actions",header:"Actions",cell:s=>e.jsx(Re,{userId:s.row.original.id,userDto:s.row.original,onAction:t,variant:"dropdown"}),enableSorting:!1,enableHiding:!0,meta:{className:"text-right",displayName:"Action"}}];function Te({table:t,onSearch:s,searchValue:r=""}){const c=t.getState().columnFilters.length>0;return e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-2",children:[s&&e.jsx("div",{className:"w-full sm:w-auto sm:max-w-[250px]",children:e.jsx(ve,{onUpdate:s,value:r})}),e.jsxs("div",{className:"flex items-center gap-2 ml-auto",children:[c&&e.jsx(b,{variant:"ghost",onClick:()=>t.resetColumnFilters(),className:"border border-gray-200 px-2 font-semibold text-indigo-600 sm:border-none sm:py-1 dark:border-gray-800 dark:text-indigo-500",children:"Clear filters"}),e.jsx(xe,{table:t})]})]})}const Ie=({children:t})=>{const{can:s}=O(),[r,c]=n.useState(!1),{toast:i}=F(),u=k(),{handleSubmit:p,register:m,reset:y}=z(),[,d]=n.useState([]);n.useEffect(()=>{r||(y({name:"",isDefault:!1,isPublic:!1}),d([]))},[r,y]);const j=V({mutationFn:async l=>se({body:{name:l.name??"",isDefault:l.isDefault??!1,isPublic:l.isPublic??!1}}),onSuccess:()=>{i({title:"Success",description:"Role Created Successfully",variant:"success"}),u.invalidateQueries({queryKey:[P.GetRoles]}),c(!1)},onError:l=>{const h=Q(l);i({title:h.title,description:h.description,variant:"error"})}}),x=l=>{const h={...l};j.mutate(h)},S=l=>{c(l)};return e.jsxs("section",{children:[e.jsx(ee,{}),e.jsxs(E,{open:r,onOpenChange:S,children:[e.jsx(Ne,{asChild:!0,children:t}),e.jsx("section",{className:"flex items-center justify-between pb-5",children:s("IdentityServer.ClaimTypes.Create")&&e.jsxs(b,{size:"sm",className:"w-full sm:py-1 sm:mt-0 sm:w-fit",onClick:()=>c(!0),children:[e.jsx(Se,{className:"-ml-1 size-4 shrink-0","aria-hidden":"true"}),e.jsx("span",{className:"hidden truncate sm:inline",children:"New Role"})]})}),e.jsxs(G,{className:"max-w-2xl",children:[e.jsx(q,{children:e.jsx(M,{children:"Create a New Role"})}),e.jsxs("form",{onSubmit:p(x),className:"mt-2",onKeyDown:l=>{l.key==="Enter"&&l.target instanceof HTMLInputElement&&(l.preventDefault(),p(x)())},children:[e.jsx("section",{className:"flex w-full flex-col space-y-2",children:e.jsxs(L,{children:[e.jsx(w,{label:"Name",description:"The name of the claim",children:e.jsx($,{required:!0,...m("name"),placeholder:"Claim Name"})}),e.jsx(w,{label:"Default",description:"Whether the role is default",children:e.jsx(A,{...m("isDefault",{setValueAs:l=>l===!0})})}),e.jsx(w,{label:"Public",description:"Whether the role is public",children:e.jsx(A,{...m("isPublic",{setValueAs:l=>l===!0})})})]})}),e.jsxs(K,{className:"mt-5",children:[e.jsx(b,{variant:"ghost",onClick:l=>{l.preventDefault(),c(!1)},disabled:j.isPending,children:"Cancel"}),e.jsx(b,{type:"submit",disabled:j.isPending,children:j.isPending?"Saving...":"Save"})]})]})]})]})]})},Fe=({dataEdit:t,dataId:s,onDismiss:r})=>{const[c,i]=n.useState(!1),{toast:u}=F(),p=k(),{handleSubmit:m,register:y,setValue:d}=z(),[j,x]=n.useState(t.isDefault??!1),[S,l]=n.useState(t.isPublic??!1),h=V({mutationFn:async a=>te({path:{id:s},body:{name:a.name??t.name??"",isDefault:a.isDefault??t.isDefault??!1,isPublic:a.isPublic??t.isPublic??!1}}),onSuccess:()=>{u({title:"Success",description:"Role Updated Successfully",variant:"success"}),p.invalidateQueries({queryKey:[P.GetRoles]}),D()},onError:a=>{const o=Q(a);u({title:o.title,description:o.description,variant:"error"})}}),C=a=>{const o={...a};h.mutate(o)},D=()=>{i(!1),r()};return n.useEffect(()=>{c&&(d("name",t.name||""),d("isDefault",t.isDefault??!1),d("isPublic",t.isPublic??!1))},[c,t,d]),n.useEffect(()=>{i(!0)},[]),e.jsx(E,{open:c,onOpenChange:D,children:e.jsxs(G,{children:[e.jsx(q,{children:e.jsxs(M,{children:["Update a Role: ",t.name]})}),e.jsxs("form",{onSubmit:m(C),className:"mt-2",onKeyDown:a=>{a.key==="Enter"&&a.target instanceof HTMLInputElement&&(a.preventDefault(),m(C)())},children:[e.jsx("section",{className:"flex w-full flex-col space-y-2",children:e.jsxs(L,{children:[e.jsx(w,{label:"Name",description:"The name of the role",children:e.jsx($,{required:!0,...y("name"),defaultValue:t.name??"",placeholder:"Role Name"})}),e.jsx(w,{label:"Default",description:"Whether the role is default",children:e.jsx(A,{checked:j,onCheckedChange:a=>{x(!!a),d("isDefault",!!a)}})}),e.jsx(w,{label:"Public",description:"Whether the role is public",children:e.jsx(A,{checked:S,onCheckedChange:a=>{l(!!a),d("isPublic",!!a)}})})]})}),e.jsxs(K,{className:"mt-5",children:[e.jsx(b,{variant:"ghost",onClick:a=>{a.preventDefault(),i(!1)},disabled:h.isPending,type:"button",children:"Cancel"}),e.jsx(b,{type:"submit",disabled:h.isPending,children:h.isPending?"Saving...":"Save"})]})]})]})})},Ee=(t,s,r,c)=>W({queryKey:[P.GetRoles,t,s,r,c],queryFn:async()=>{let i=0;t>0&&(i=t*s);const{data:u}=await ae({query:{MaxResultCount:s,SkipCount:i,Filter:r,Sorting:c}});return u}}),Ge=({roleDto:t,onDismiss:s})=>{const[r,c]=n.useState(!1),{toast:i}=H(),[u,p]=n.useState(!1),{data:m}=Ce(R.R,t.name??void 0),y=k(),[d,j]=n.useState([]);n.useEffect(()=>(c(!0),()=>{y.invalidateQueries({queryKey:[R.R]})}),[]),n.useEffect(()=>{m?.groups&&j([...m?.groups])},[m]),n.useEffect(()=>{if(m?.groups&&m.groups.length>0){const a=m.groups.map(o=>o.permissions?.every(g=>g.isGranted)).every(o=>o);p(a)}},[m]),n.useEffect(()=>{if(d.length>0){const a=d.map(o=>({...o,permissions:o.permissions?.map(g=>({...g,isGranted:u}))??null}));j(a)}},[u]);const x=n.useCallback(()=>{c(!1),s()},[s]),S=n.useCallback(async a=>{if(a.preventDefault(),!d||d.length===0)return;const g={permissions:d.map(v=>(v.permissions??[]).map(f=>({name:f.name??null,isGranted:f.isGranted??!1}))).flat()};try{await ie({query:{providerKey:t.name??"",providerName:R.R},body:g}),i({title:"Success",description:"Permission Updated Successfully",variant:"default"}),y.invalidateQueries({queryKey:[R.R]}),x()}catch(v){v instanceof Error&&i({title:"Failed",description:"Permission update wasn't successful.",variant:"destructive"})}},[d,t.name,i,y,x]),l=n.useMemo(()=>t.name?.includes(ne.ADMIN)??!1,[t]),h=n.useRef(`dialog-${Math.random().toString(36).substring(2,9)}`).current,C=a=>((a||"").split(" ")[0]??"").toLowerCase(),D=n.useMemo(()=>{const a=[];for(let o=0;o<d.length;o+=2)a.push(d.slice(o,o+2));return a},[d]);return e.jsx(E,{open:r,onOpenChange:x,children:e.jsxs(G,{className:"max-h-[90vh] overflow-hidden flex flex-col",style:{maxWidth:"900px",width:"90vw"},children:[e.jsx(q,{children:e.jsxs(M,{children:["Permissions - ",t.name]})}),e.jsx("form",{onSubmit:S,className:"flex-1 overflow-y-auto",children:e.jsxs("div",{className:"p-1",children:[e.jsx(De,{name:"Grant All Permissions",isGranted:u,id:"all_granted",disabled:!l,onUpdate:()=>{p(a=>!a)},className:"ml-2 mb-4"}),e.jsx("div",{className:"space-y-6",children:D.map((a,o)=>e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:a.map((g,v)=>e.jsxs("div",{className:"border rounded-lg p-4",children:[e.jsx("h3",{className:"text-lg font-medium mb-2",children:g.displayName}),e.jsx("div",{className:"border-t pt-3",children:e.jsx(Pe,{permissions:g.permissions??[],type:C(g.displayName??""),disabled:u&&l,hideSelectAll:u,hideSave:!0},`group-${g.displayName}-${o}-${v}`)})]},`${o}-${v}`))},o))})]})}),e.jsxs(K,{className:"mt-4 border-t pt-4 bg-white dark:bg-gray-950",children:[e.jsx(b,{onClick:a=>{a.preventDefault(),x()},variant:"ghost",children:"Cancel"}),e.jsx(b,{onClick:S,children:"Save"})]})]})},h)},qe=()=>{const{toast:t}=F(),s=k(),[r,c]=n.useState(""),[i,u]=n.useState(),[p,m]=n.useState({pageIndex:0,pageSize:10}),[y,d]=n.useState([{id:"name",desc:!1}]),j=f=>{if(!f.length)return"name asc";const N=f[0];return`${N?.id} ${N?.desc?"desc":"asc"}`},{isLoading:x,data:S}=Ee(p.pageIndex,p.pageSize,r,j(y)),h=ke((f,N,B)=>{u({dataId:f,dataEdit:N,dialogType:B})}),C=f=>{c(f),m(N=>({...N,pageIndex:0}))},D=f=>{m(f)},a=f=>{d(f),m(N=>({...N,pageIndex:0}))},o=()=>{s.invalidateQueries({queryKey:[P.GetRoles]}),setTimeout(()=>{t({title:"Data refreshed",description:"The claims list has been refreshed.",variant:"success"})},100)};if(x)return e.jsx(fe,{rowCount:p.pageSize,columnCount:4,hasTitle:!0,hasSearch:!0,hasFilters:!0,hasPagination:!0,hasActions:!0});const g=S?.items??[],v=S?.totalCount??0;return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"space-y-4",children:e.jsx(ge,{title:"Roles",columns:h,data:g,totalCount:v,isLoading:x,manualPagination:!0,manualSorting:!0,pageSize:p.pageSize,onPaginationChange:D,onSortingChange:a,sortingState:y,onSearch:C,searchValue:r,customFilterbar:Te,hideDefaultFilterbar:!0,onRefresh:o,enableRowSelection:!1,actionButton:{onClick:()=>{},content:e.jsx(Ie,{})}})}),i&&i.dialogType==="edit"&&e.jsx(Fe,{dataId:i.dataId,dataEdit:i.dataEdit,onDismiss:()=>{s.invalidateQueries({queryKey:[P.GetRoles]}),u(null)}}),i&&i.dialogType==="delete"&&e.jsx(Ae,{dataId:i.dataId,onDismiss:()=>{s.invalidateQueries({queryKey:[P.GetRoles]}),u(null)}}),i&&i.dialogType==="permission"&&e.jsx(Ge,{roleDto:i.dataEdit,onDismiss:()=>u(null)})]})};function ss(){return e.jsxs(le,{children:[e.jsx(we,{title:"Dashboard"}),e.jsx(qe,{})]})}export{ss as default};
//# sourceMappingURL=role-VZNP3dwx.js.map
