using System.Threading.Tasks;
using Imip.JettyApproval.Web.Services;
using Imip.JettyApproval.Web.Services.Dtos.ExternalVessel;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Volo.Abp.AspNetCore.Mvc;

namespace Imip.JettyApproval.Web.Controllers;

[Route("api/[controller]")]
public class EkbController : AbpController
{
    private readonly AppToAppService _appToAppService;
    private readonly IConfiguration _configuration;

    public EkbController(AppToAppService appToAppService, IConfiguration configuration)
    {
        _appToAppService = appToAppService;
        _configuration = configuration;
    }

    [HttpPost("agent")]
    public async Task<IActionResult> SearchAgent([FromBody] FilterRequestDto request)
    {
        var apiUrl = _configuration["ExternalApps:EkbdApi:BaseUrl"];
        var endpoint = "api/ekb/agent/filter-list ";
        var result = await _appToAppService.CallOtherAppAsync<object>(apiUrl, endpoint, request);
        return Ok(result);
    }

    [HttpPost("tenant")]
    public async Task<IActionResult> SearchTenant([FromBody] FilterRequestDto request)
    {
        var apiUrl = _configuration["ExternalApps:EkbdApi:BaseUrl"];
        var endpoint = "api/ekb/tenant/filter-list ";
        var result = await _appToAppService.CallOtherAppAsync<object>(apiUrl, endpoint, request);
        return Ok(result);
    }

    [HttpPost("jetty")]
    public async Task<IActionResult> SearchJetty([FromBody] FilterRequestDto request)
    {
        var apiUrl = _configuration["ExternalApps:EkbdApi:BaseUrl"];
        var endpoint = "api/ekb/jetty/filter-list ";
        var result = await _appToAppService.CallOtherAppAsync<object>(apiUrl, endpoint, request);
        return Ok(result);
    }

    [HttpPost("cargo")]
    public async Task<IActionResult> SearchCargo([FromBody] FilterRequestDto request)
    {
        var apiUrl = _configuration["ExternalApps:EkbdApi:BaseUrl"];
        var endpoint = "api/ekb/cargo/filter-list ";
        var result = await _appToAppService.CallOtherAppAsync<object>(apiUrl, endpoint, request);
        return Ok(result);
    }

    [HttpPost("business-partner")]
    public async Task<IActionResult> SearchBusinessPartner([FromBody] FilterRequestDto request)
    {
        var apiUrl = _configuration["ExternalApps:EkbdApi:BaseUrl"];
        var endpoint = "api/ekb/business-partner/filter-list ";
        var result = await _appToAppService.CallOtherAppAsync<object>(apiUrl, endpoint, request);
        return Ok(result);
    }

    [HttpPost("destination-port")]
    public async Task<IActionResult> SearchDestinationPort([FromBody] FilterRequestDto request)
    {
        var apiUrl = _configuration["ExternalApps:EkbdApi:BaseUrl"];
        var endpoint = "api/ekb/destination-port/filter-list ";
        var result = await _appToAppService.CallOtherAppAsync<object>(apiUrl, endpoint, request);
        return Ok(result);
    }

    [HttpPost("surveyor")]
    public async Task<IActionResult> SearchSurveyor([FromBody] FilterRequestDto request)
    {
        var apiUrl = _configuration["ExternalApps:EkbdApi:BaseUrl"];
        var endpoint = "api/ekb/surveyor/filter-list ";
        var result = await _appToAppService.CallOtherAppAsync<object>(apiUrl, endpoint, request);
        return Ok(result);
    }
}
