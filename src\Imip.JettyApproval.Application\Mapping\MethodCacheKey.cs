using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Imip.JettyApproval.Contracts;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.DependencyInjection;

namespace Imip.JettyApproval.Mapping;

// Helper struct for method cache key
public readonly struct MethodCacheKey : IEquatable<MethodCacheKey>
{
    public Type SourceType { get; }
    public Type DestinationType { get; }
    public bool WithDestination { get; }

    public MethodCacheKey(Type sourceType, Type destinationType, bool withDestination)
    {
        SourceType = sourceType;
        DestinationType = destinationType;
        WithDestination = withDestination;
    }

    public bool Equals(MethodCacheKey other)
    {
        return SourceType == other.SourceType &&
               DestinationType == other.DestinationType &&
               WithDestination == other.WithDestination;
    }

    public override bool Equals(object obj)
    {
        return obj is MethodCacheKey other && Equals(other);
    }

    public override int GetHashCode()
    {
        return HashCode.Combine(SourceType, DestinationType, WithDestination);
    }
}

public class MapperlyObjectMapper : IObjectMapper, ITransientDependency
{
    private readonly IServiceProvider _serviceProvider;
    private readonly Dictionary<(Type, Type), object> _mapperCache = new();
    private readonly Dictionary<MethodCacheKey, MethodInfo> _methodCache = new();

    public MapperlyObjectMapper(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public TDestination Map<TDestination>(object source)
    {
        if (source == null) return default(TDestination);

        var sourceType = source.GetType();
        var mapper = GetMapper(sourceType, typeof(TDestination));
        var method = GetMapMethod(mapper.GetType(), sourceType, typeof(TDestination));

        return (TDestination)method?.Invoke(mapper, new[] { source });
    }

    public TDestination Map<TSource, TDestination>(TSource source)
    {
        if (source == null) return default(TDestination);

        var mapper = GetMapper<TSource, TDestination>();
        var method = GetMapMethod(mapper.GetType(), typeof(TSource), typeof(TDestination));

        return (TDestination)method?.Invoke(mapper, new object[] { source });
    }

    public TDestination Map<TSource, TDestination>(TSource source, TDestination destination)
    {
        if (source == null) return destination;

        var mapper = GetMapper<TSource, TDestination>();
        var method = GetMapMethod(mapper.GetType(), typeof(TSource), typeof(TDestination), true);

        return method != null
            ? (TDestination)method.Invoke(mapper, new object[] { source, destination })
            : Map<TSource, TDestination>(source);
    }

    public List<TDestination> Map<TSource, TDestination>(IEnumerable<TSource> source)
    {
        if (source == null) return new List<TDestination>();

        return source.Select(item => Map<TSource, TDestination>(item)).ToList();
    }

    public IQueryable<TDestination> ProjectTo<TDestination>(IQueryable source)
    {
        // For true projection, we need to use Mapperly's ProjectTo method
        // This is a simplified implementation - in practice, you'd need more complex logic
        var sourceType = source.ElementType;
        var mapper = GetMapper(sourceType, typeof(TDestination));
        var projectToMethod = GetProjectToMethod(mapper.GetType(), sourceType, typeof(TDestination));

        if (projectToMethod != null)
        {
            return (IQueryable<TDestination>)projectToMethod.Invoke(mapper, new object[] { source });
        }

        // Fallback to in-memory mapping
        return source.Cast<object>().Select(item => Map<TDestination>(item)).AsQueryable();
    }

    private object GetMapper<TSource, TDestination>()
    {
        return GetMapper(typeof(TSource), typeof(TDestination));
    }

    private object GetMapper(Type sourceType, Type destinationType)
    {
        var key = (sourceType, destinationType);

        if (!_mapperCache.TryGetValue(key, out var mapper))
        {
            var mapperType = FindMapperType(sourceType, destinationType);
            mapper = _serviceProvider.GetRequiredService(mapperType);
            _mapperCache[key] = mapper;
        }

        return mapper;
    }

    private MethodInfo GetMapMethod(Type mapperType, Type sourceType, Type destinationType, bool withDestination = false)
    {
        var methodKey = new MethodCacheKey(sourceType, destinationType, withDestination);

        if (!_methodCache.TryGetValue(methodKey, out var method))
        {
            method = FindMapMethod(mapperType, sourceType, destinationType, withDestination);
            _methodCache[methodKey] = method;
        }

        return method;
    }

    private MethodInfo GetProjectToMethod(Type mapperType, Type sourceType, Type destinationType)
    {
        // Look for ProjectTo method in Mapperly-generated mappers
        return mapperType.GetMethods()
            .FirstOrDefault(m => m.Name == "ProjectTo" &&
                                m.GetParameters().Length == 1 &&
                                m.GetParameters()[0].ParameterType == typeof(IQueryable<>).MakeGenericType(sourceType) &&
                                m.ReturnType == typeof(IQueryable<>).MakeGenericType(destinationType));
    }

    private MethodInfo FindMapMethod(Type mapperType, Type sourceType, Type destinationType, bool withDestination)
    {
        var methods = mapperType.GetMethods();

        if (withDestination)
        {
            return methods.FirstOrDefault(m =>
                m.GetParameters().Length == 2 &&
                m.GetParameters()[0].ParameterType == sourceType &&
                m.GetParameters()[1].ParameterType == destinationType &&
                m.ReturnType == destinationType);
        }
        else
        {
            return methods.FirstOrDefault(m =>
                m.GetParameters().Length == 1 &&
                m.GetParameters()[0].ParameterType == sourceType &&
                m.ReturnType == destinationType);
        }
    }

    private Type FindMapperType(Type sourceType, Type destinationType)
    {
        // First, try to find a specific mapper for this type combination
        var specificMapperName = $"{sourceType.Name}To{destinationType.Name}Mapper";
        var mapperTypes = AppDomain.CurrentDomain.GetAssemblies()
            .SelectMany(a => a.GetTypes())
            .Where(t => t.Name.EndsWith("Mapper") && !t.IsAbstract)
            .ToList();

        // Look for specific mapper first
        var specificMapper = mapperTypes.FirstOrDefault(t => t.Name == specificMapperName);
        if (specificMapper != null)
        {
            return specificMapper;
        }

        // Look for generic mapper that can handle these types
        foreach (var mapperType in mapperTypes)
        {
            var methods = mapperType.GetMethods();
            if (methods.Any(m =>
                m.GetParameters().Any(p => p.ParameterType == sourceType) &&
                m.ReturnType == destinationType))
            {
                return mapperType;
            }
        }

        throw new InvalidOperationException($"No mapper found for {sourceType.Name} -> {destinationType.Name}");
    }
}

