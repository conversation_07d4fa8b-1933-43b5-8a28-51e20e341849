"use client"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { cx } from "@/lib/utils"
import * as React from "react"

// import { DataTableBulkEditor } from "./DataTableBulkEditor"
import { But<PERSON> } from "@/components/ui/button"
import { Search } from "@/components/ui/search"
import { RiAddLine, RiArrowDownLine, RiArrowUpLine, RiRefreshLine } from "@remixicon/react"
import { Filterbar } from "./DataTableFilterbar"
import { DataTablePagination } from "./DataTablePagination"

import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  type ColumnDef,
  type OnChangeFn,
  type PaginationState,
  type Table as ReactTable,
  type SortingState,
} from "@tanstack/react-table"

interface DataTableProps<TData> {
  columns: ColumnDef<TData>[]
  data: TData[]
  totalCount?: number
  isLoading?: boolean
  manualPagination?: boolean
  manualSorting?: boolean
  pageSize?: number
  onPaginationChange?: (pagination: PaginationState) => void
  onSortingChange?: (sorting: SortingState) => void
  sortingState?: SortingState
  onSearch?: (value: string) => void
  searchValue?: string
  customFilterbar?: React.ComponentType<{ table: ReactTable<TData>, onSearch?: (value: string) => void, searchValue?: string }>
  hideDefaultFilterbar?: boolean
  onRefresh?: () => void
  title?: string
  actionButton?: {
    label?: string
    onClick: () => void
    content?: React.ReactNode
  }
  enableRowSelection?: boolean
}

export function DataTable<TData>({
  columns,
  data,
  totalCount,
  isLoading,
  manualPagination = false,
  manualSorting = false,
  pageSize = 20,
  onPaginationChange,
  onSortingChange,
  sortingState,
  onSearch,
  searchValue = "",
  customFilterbar: CustomFilterbar,
  hideDefaultFilterbar = false,
  onRefresh,
  title,
  actionButton,
  enableRowSelection = true,
}: DataTableProps<TData>) {
  const [rowSelection, setRowSelection] = React.useState({})
  const [pagination, setPagination] = React.useState<PaginationState>({
    pageIndex: 0,
    pageSize: pageSize,
  })
  const [sorting, setSorting] = React.useState<SortingState>(sortingState ?? [])
  const [isRefreshing, setIsRefreshing] = React.useState(false)

  // Update local sorting state when prop changes
  React.useEffect(() => {
    if (sortingState) {
      setSorting(sortingState);
    }
  }, [sortingState]);

  // Handle pagination changes internally or pass to parent
  const handlePaginationChange: OnChangeFn<PaginationState> = React.useCallback(
    (updaterOrValue) => {
      const newPagination = typeof updaterOrValue === 'function'
        ? updaterOrValue(pagination)
        : updaterOrValue;

      setPagination(newPagination);
      if (onPaginationChange) {
        onPaginationChange(newPagination);
      }
    },
    [onPaginationChange, pagination]
  );

  // Handle sorting changes internally or pass to parent
  const handleSortingChange: OnChangeFn<SortingState> = React.useCallback(
    (updaterOrValue) => {
      const newSorting = typeof updaterOrValue === 'function'
        ? updaterOrValue(sorting)
        : updaterOrValue;

      setSorting(newSorting);
      if (onSortingChange) {
        onSortingChange(newSorting);
      }
    },
    [onSortingChange, sorting]
  );

  // Handle page size changes
  const handlePageSizeChange = React.useCallback(
    (newPageSize: number) => {
      const newPagination = {
        pageIndex: 0, // Reset to first page when changing page size
        pageSize: newPageSize,
      };

      setPagination(newPagination);
      if (onPaginationChange) {
        onPaginationChange(newPagination);
      }
    },
    [onPaginationChange]
  );

  // Handle refresh with animation
  const handleRefresh = React.useCallback(() => {
    if (onRefresh && !isLoading && !isRefreshing) {
      setIsRefreshing(true);

      // Call the provided refresh function
      onRefresh();

      // Reset the refreshing state after a delay
      setTimeout(() => {
        setIsRefreshing(false);
      }, 1000);
    }
  }, [onRefresh, isLoading, isRefreshing]);

  // Filter out selection column if row selection is disabled
  const tableColumns = React.useMemo(() => {
    if (!enableRowSelection) {
      return columns.filter(col => col.id !== 'select');
    }
    return columns;
  }, [columns, enableRowSelection]);

  // Enhanced column headers with sort indicators
  const enhancedColumns = React.useMemo(() => {
    return tableColumns.map(column => {
      // Skip if column is not sortable or explicitly disabled sorting
      if (column.enableSorting === false) {
        return column;
      }

      // Create a new column definition with custom header
      return {
        ...column,
        header: (context) => {
          const headerColumn = context.column;
          const isSorted = headerColumn.getIsSorted();
          const content = typeof column.header === 'string'
            ? column.header
            : column.header
              ? flexRender(column.header, context)
              : null;

          const label = (
            <>
              {content}
              {isSorted && (
                <span className="inline-flex items-center">
                  {isSorted === "asc" ? (
                    <RiArrowUpLine className="w-3.5 h-3.5" />
                  ) : (
                    <RiArrowDownLine className="w-3.5 h-3.5" />
                  )}
                </span>
              )}
            </>
          );

          return column.enableSorting !== false ? (
            <button
              type="button"
              onClick={() => headerColumn.toggleSorting()}
              className={cx(
                "inline-flex items-center gap-1 hover:text-primary",
                isSorted ? "text-primary" : ""
              )}
            >
              {label}
            </button>
          ) : (
            label
          );
        }
      };
    }) as ColumnDef<TData>[];
  }, [tableColumns]);

  const table = useReactTable({
    data,
    columns: enhancedColumns,
    state: {
      rowSelection,
      pagination,
      sorting,
    },
    pageCount: totalCount ? Math.ceil(totalCount / pagination.pageSize) : -1,
    enableRowSelection,
    enableSorting: true,
    manualSorting,
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onRowSelectionChange: setRowSelection,
    onSortingChange: handleSortingChange,
    getCoreRowModel: getCoreRowModel(),
    onPaginationChange: handlePaginationChange,
    manualPagination,
  })

  return (
    <>
      <div className="space-y-3">
        {title && (
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">{title}</h2>
            {actionButton?.content}
          </div>
        )}

        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2">
          <div className="flex-1 w-full">
            {CustomFilterbar ? (
              <CustomFilterbar table={table} onSearch={onSearch} searchValue={searchValue} />
            ) : (
              <>
                {onSearch && (
                  <Search onUpdate={onSearch} value={searchValue} />
                )}
                {!hideDefaultFilterbar && <Filterbar table={table} />}
              </>
            )}
          </div>

          <div className="flex items-center gap-2">
            {!title && actionButton && (
              <Button
                variant="primary"
                size="sm"
                className="flex items-center gap-1 px-3 py-2"
                onClick={actionButton.onClick}
              >
                {actionButton.content ?? <RiAddLine className="h-4 w-4" />}
                <span>{actionButton.label}</span>
              </Button>
            )}

            {onRefresh && (
              <Button
                variant="secondary"
                size="sm"
                className="flex items-center gap-1"
                onClick={handleRefresh}
                disabled={isLoading ?? isRefreshing}
              >
                <RiRefreshLine
                  className={cx(
                    "h-4 w-4",
                    isRefreshing && "animate-spin"
                  )}
                />
                <span className="hidden sm:inline">Refresh</span>
              </Button>
            )}
          </div>
        </div>

        <div className="relative overflow-hidden overflow-x-auto">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow
                  key={headerGroup.id}
                  className="border-y border-gray-200 dark:border-gray-800"
                >
                  {headerGroup.headers.map((header) => (
                    <TableHead
                      key={header.id}
                      className={cx(
                        "whitespace-nowrap py-1 text-sm sm:text-xs",
                        header.column.columnDef.meta?.className,
                      )}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell
                    colSpan={tableColumns.length}
                    className="h-24 text-center"
                  >
                    Loading...
                  </TableCell>
                </TableRow>
              ) : table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    onClick={() => enableRowSelection && row.toggleSelected(!row.getIsSelected())}
                    className={cx(
                      "group",
                      enableRowSelection ? "select-none hover:bg-gray-50 dark:hover:bg-gray-900" : ""
                    )}
                  >
                    {row.getVisibleCells().map((cell, index) => (
                      <TableCell
                        key={cell.id}
                        className={cx(
                          row.getIsSelected()
                            ? "bg-gray-50 dark:bg-gray-900"
                            : "",
                          "relative whitespace-nowrap py-1 text-gray-600 first:w-10 dark:text-gray-400",
                          cell.column.columnDef.meta?.className,
                        )}
                      >
                        {index === 0 && row.getIsSelected() && (
                          <div className="absolute inset-y-0 left-0 w-0.5 bg-indigo-600 dark:bg-indigo-500" />
                        )}
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext(),
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={tableColumns.length}
                    className="h-24 text-center"
                  >
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
          {/* <DataTableBulkEditor table={table} rowSelection={rowSelection} /> */}
        </div>
        <DataTablePagination
          table={table}
          pageSize={pagination.pageSize}
          totalCount={totalCount}
          onPageSizeChange={handlePageSizeChange}
        />
      </div>
    </>
  )
}
