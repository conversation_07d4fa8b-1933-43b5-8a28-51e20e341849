{"version": 3, "file": "search-YAT3sv3T.js", "sources": ["../../../../../frontend/src/components/ui/useDebounce.ts", "../../../../../frontend/src/components/ui/search.tsx"], "sourcesContent": ["import { useEffect, useState } from 'react'\r\n\r\nexport const useDebounce = <V>(value: V, delay = 800): V => {\r\n  // State and setters for debounced value\r\n  const [debouncedValue, setDebouncedValue] = useState<V>(value)\r\n  useEffect(\r\n    () => {\r\n      // Update debounced value after delay\r\n      const handler = setTimeout(() => {\r\n        setDebouncedValue(value)\r\n      }, delay)\r\n      // Cancel the timeout if value changes (also on delay change or unmount)\r\n      // This is how we prevent debounced value from updating if value is changed ...\r\n      // .. within the delay period. Timeout gets cleared and restarted.\r\n      return () => {\r\n        clearTimeout(handler)\r\n      }\r\n    },\r\n    [value, delay] // Only re-call effect if value or delay changes\r\n  )\r\n  return debouncedValue\r\n}\r\n", "import { memo, type SyntheticEvent, useCallback, useEffect, useRef, useState } from 'react'\r\nimport { Input } from '@/components/ui/input'\r\nimport { useDebounce } from './useDebounce'\r\n\r\ntype SearchProps = {\r\n  onUpdate: (searchStr: string) => void\r\n  value: string\r\n}\r\nconst SearchInput = ({ onUpdate, value }: SearchProps) => {\r\n  // Initialize with the provided value\r\n  const [searchTerm, setSearchTerm] = useState<string>(value)\r\n  const ref = useRef<HTMLInputElement>(null)\r\n  const searchStr = useDebounce<string>(searchTerm)\r\n\r\n  // Track if we're handling a user input vs. prop change\r\n  const isUserInput = useRef(false)\r\n\r\n  // Handle user typing in the search box\r\n  const onSearchEvent = useCallback((e: SyntheticEvent) => {\r\n    const target = e.target as HTMLInputElement\r\n    const { value } = target\r\n    isUserInput.current = true\r\n    setSearchTerm(value)\r\n  }, [])\r\n\r\n  // Only update parent when debounced search term changes (from user input)\r\n  useEffect(() => {\r\n    if (isUserInput.current) {\r\n      onUpdate(searchStr || '')\r\n      isUserInput.current = false\r\n    }\r\n  }, [searchStr, onUpdate])\r\n\r\n  // Update internal state when prop value changes (from parent)\r\n  useEffect(() => {\r\n    // Only update if the value has actually changed and is different from current searchTerm\r\n    // And only if it's not from user input\r\n    if (value !== searchTerm && !isUserInput.current) {\r\n      setSearchTerm(value)\r\n    }\r\n\r\n    // Focus the input if there's a value\r\n    if (value) {\r\n      ref.current?.focus()\r\n    }\r\n  }, [value, searchTerm])\r\n\r\n  return (\r\n    <section className=\"search\">\r\n      <Input\r\n        ref={ref}\r\n        type=\"text\"\r\n        value={searchTerm}\r\n        placeholder=\"Search...\"\r\n        onChange={onSearchEvent}\r\n      />\r\n    </section>\r\n  )\r\n}\r\n\r\nexport const Search = memo(SearchInput)\r\n"], "names": ["useDebounce", "value", "delay", "debounced<PERSON><PERSON><PERSON>", "setDebouncedValue", "useState", "useEffect", "handler", "SearchInput", "onUpdate", "searchTerm", "setSearchTerm", "ref", "useRef", "searchStr", "isUserInput", "onSearchEvent", "useCallback", "e", "target", "jsx", "Input", "Search", "memo"], "mappings": "6FAEO,MAAMA,EAAc,CAAIC,EAAUC,EAAQ,MAAW,CAE1D,KAAM,CAACC,EAAgBC,CAAiB,EAAIC,EAAAA,SAAYJ,CAAK,EAC7DK,OAAAA,EAAA,UACE,IAAM,CAEE,MAAAC,EAAU,WAAW,IAAM,CAC/BH,EAAkBH,CAAK,GACtBC,CAAK,EAIR,MAAO,IAAM,CACX,aAAaK,CAAO,CACtB,CACF,EACA,CAACN,EAAOC,CAAK,CACf,EACOC,CACT,ECbMK,EAAc,CAAC,CAAE,SAAAC,EAAU,MAAAR,KAAyB,CAExD,KAAM,CAACS,EAAYC,CAAa,EAAIN,EAAAA,SAAiBJ,CAAK,EACpDW,EAAMC,SAAyB,IAAI,EACnCC,EAAYd,EAAoBU,CAAU,EAG1CK,EAAcF,SAAO,EAAK,EAG1BG,EAAgBC,cAAaC,GAAsB,CACvD,MAAMC,EAASD,EAAE,OACX,CAAE,MAAAjB,CAAAA,EAAUkB,EAClBJ,EAAY,QAAU,GACtBJ,EAAcV,CAAK,CACrB,EAAG,EAAE,EAGLK,OAAAA,EAAAA,UAAU,IAAM,CACVS,EAAY,UACdN,EAASK,GAAa,EAAE,EACxBC,EAAY,QAAU,GACxB,EACC,CAACD,EAAWL,CAAQ,CAAC,EAGxBH,EAAAA,UAAU,IAAM,CAGVL,IAAUS,GAAc,CAACK,EAAY,SACvCJ,EAAcV,CAAK,EAIjBA,GACFW,EAAI,SAAS,MAAM,CACrB,EACC,CAACX,EAAOS,CAAU,CAAC,EAGpBU,EAAAA,IAAC,UAAQ,CAAA,UAAU,SACjB,SAAAA,EAAA,IAACC,EAAA,CACC,IAAAT,EACA,KAAK,OACL,MAAOF,EACP,YAAY,YACZ,SAAUM,CAAA,CAAA,EAEd,CAEJ,EAEaM,EAASC,OAAKf,CAAW"}