{"version": 3, "file": "client-Dzf-sMcJ.js", "sources": ["../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/computer.js", "../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/globe.js", "../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/lock-open.js", "../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/shield-check.js", "../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/shield-question.js", "../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/smartphone.js", "../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/tv.js", "../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/user-check.js", "../../../../../frontend/src/components/app/clients/DeleteClient.tsx", "../../../../../frontend/src/lib/hooks/useOpeniddictApplicationsPermissions.ts", "../../../../../frontend/src/data/applicationType.ts", "../../../../../frontend/src/data/clientType.ts", "../../../../../frontend/src/data/consentType.ts", "../../../../../frontend/src/lib/utils/client.ts", "../../../../../frontend/src/components/app/clients/EditClient.tsx", "../../../../../frontend/src/components/app/clients/AddClient.tsx", "../../../../../frontend/src/components/app/clients/ClientActions.tsx", "../../../../../frontend/src/components/app/clients/Columns.tsx", "../../../../../frontend/src/lib/hooks/useOpeniddictApplications.ts", "../../../../../frontend/src/components/app/clients/ClientList.tsx", "../../../../../frontend/src/pages/client.tsx"], "sourcesContent": ["/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"rect\", { width: \"14\", height: \"8\", x: \"5\", y: \"2\", rx: \"2\", key: \"wc9tft\" }],\n  [\"rect\", { width: \"20\", height: \"8\", x: \"2\", y: \"14\", rx: \"2\", key: \"w68u3i\" }],\n  [\"path\", { d: \"M6 18h2\", key: \"rwmk9e\" }],\n  [\"path\", { d: \"M12 18h6\", key: \"aqd8w3\" }]\n];\nconst Computer = createLucideIcon(\"computer\", __iconNode);\n\nexport { __iconNode, Computer as default };\n//# sourceMappingURL=computer.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"circle\", { cx: \"12\", cy: \"12\", r: \"10\", key: \"1mglay\" }],\n  [\"path\", { d: \"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20\", key: \"13o1zl\" }],\n  [\"path\", { d: \"M2 12h20\", key: \"9i4pu4\" }]\n];\nconst Globe = createLucideIcon(\"globe\", __iconNode);\n\nexport { __iconNode, Globe as default };\n//# sourceMappingURL=globe.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"rect\", { width: \"18\", height: \"11\", x: \"3\", y: \"11\", rx: \"2\", ry: \"2\", key: \"1w4ew1\" }],\n  [\"path\", { d: \"M7 11V7a5 5 0 0 1 9.9-1\", key: \"1mm8w8\" }]\n];\nconst LockOpen = createLucideIcon(\"lock-open\", __iconNode);\n\nexport { __iconNode, LockOpen as default };\n//# sourceMappingURL=lock-open.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\n    \"path\",\n    {\n      d: \"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z\",\n      key: \"oel41y\"\n    }\n  ],\n  [\"path\", { d: \"m9 12 2 2 4-4\", key: \"dzmm74\" }]\n];\nconst ShieldCheck = createLucideIcon(\"shield-check\", __iconNode);\n\nexport { __iconNode, ShieldCheck as default };\n//# sourceMappingURL=shield-check.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\n    \"path\",\n    {\n      d: \"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z\",\n      key: \"oel41y\"\n    }\n  ],\n  [\"path\", { d: \"M9.1 9a3 3 0 0 1 5.82 1c0 2-3 3-3 3\", key: \"mhlwft\" }],\n  [\"path\", { d: \"M12 17h.01\", key: \"p32p05\" }]\n];\nconst ShieldQuestion = createLucideIcon(\"shield-question\", __iconNode);\n\nexport { __iconNode, ShieldQuestion as default };\n//# sourceMappingURL=shield-question.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"rect\", { width: \"14\", height: \"20\", x: \"5\", y: \"2\", rx: \"2\", ry: \"2\", key: \"1yt0o3\" }],\n  [\"path\", { d: \"M12 18h.01\", key: \"mhygvu\" }]\n];\nconst Smartphone = createLucideIcon(\"smartphone\", __iconNode);\n\nexport { __iconNode, Smartphone as default };\n//# sourceMappingURL=smartphone.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"m17 2-5 5-5-5\", key: \"16satq\" }],\n  [\"rect\", { width: \"20\", height: \"15\", x: \"2\", y: \"7\", rx: \"2\", key: \"1e6viu\" }]\n];\nconst Tv = createLucideIcon(\"tv\", __iconNode);\n\nexport { __iconNode, Tv as default };\n//# sourceMappingURL=tv.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"m16 11 2 2 4-4\", key: \"9rsbq5\" }],\n  [\"path\", { d: \"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\", key: \"1yyitq\" }],\n  [\"circle\", { cx: \"9\", cy: \"7\", r: \"4\", key: \"nufk8\" }]\n];\nconst UserCheck = createLucideIcon(\"user-check\", __iconNode);\n\nexport { __iconNode, UserCheck as default };\n//# sourceMappingURL=user-check.js.map\n", "import { deleteApiOpeniddictApplicationsById, type OpenIddictApplicationDto } from '@/client'\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@/components/ui/alert-dialog'\r\nimport { useToast } from \"@/hooks/use-toast\"\r\nimport { useEffect, useState } from 'react'\r\n\r\ntype DeleteUserProps = {\r\n  dataId: string\r\n  dataEdit: OpenIddictApplicationDto\r\n  onDismiss: () => void\r\n}\r\nexport const DeleteClient = ({ dataId, dataEdit, onDismiss }: DeleteUserProps) => {\r\n  const { toast } = useToast()\r\n  const [open, setOpen] = useState<boolean>(false)\r\n  const onYesEvent = async () => {\r\n    try {\r\n      await deleteApiOpeniddictApplicationsById({\r\n        path: { id: dataId },\r\n      })\r\n      toast({\r\n        title: 'Success',\r\n        description: `Client \"${dataEdit.displayName}\" has been deleted successfully.`,\r\n      })\r\n      onDismiss()\r\n    } catch (err: unknown) {\r\n      if (err instanceof Error) {\r\n        toast({\r\n          title: 'Failed',\r\n          description: `There was a problem when deleting the client \"${dataEdit.displayName}\". Kindly try again.`,\r\n          variant: 'destructive',\r\n        })\r\n      }\r\n    }\r\n  }\r\n\r\n  useEffect(() => {\r\n    setOpen(true)\r\n  }, [])\r\n\r\n  return (\r\n    <AlertDialog open={open}>\r\n      <AlertDialogContent>\r\n        <AlertDialogHeader>\r\n          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>\r\n          <AlertDialogDescription>\r\n            This action cannot be undone. This will permanently delete your this client &quot;\r\n            {dataEdit.displayName}&quot;\r\n          </AlertDialogDescription>\r\n        </AlertDialogHeader>\r\n        <AlertDialogFooter>\r\n          <AlertDialogCancel onClick={onDismiss}>Cancel</AlertDialogCancel>\r\n          <AlertDialogAction onClick={onYesEvent}>Yes</AlertDialogAction>\r\n        </AlertDialogFooter>\r\n      </AlertDialogContent>\r\n    </AlertDialog>\r\n  )\r\n}\r\n", "import { getApiOpeniddictApplicationsPermissions, getApiOpeniddictRequirements } from '@/client'\r\nimport { useQuery } from '@tanstack/react-query'\r\nimport { QueryNames } from './QueryConstants'\r\n\r\nexport const useOpeniddictApplicationsPermissions = () => {\r\n  return useQuery({\r\n    queryKey: [QueryNames.GetOpeniddictApplicationsPermissions],\r\n    queryFn: async () => {\r\n      const response = await getApiOpeniddictApplicationsPermissions()\r\n      return response.data?.data\r\n    },\r\n  })\r\n}\r\n\r\nexport const useOpeniddictRequirements = () => {\r\n  return useQuery({\r\n    queryKey: [QueryNames.GetOpeniddictRequirements],\r\n    queryFn: async () => {\r\n      const response = await getApiOpeniddictRequirements()\r\n      return response.data?.data\r\n    },\r\n  })\r\n}\r\n", "import { Cog, Computer, Globe, Smartphone, Tv } from \"lucide-react\";\r\n\r\nexport const APPLICATION_TYPES = [\r\n  {\r\n    value: \"web\",\r\n    option: \"Web Application\",\r\n    description: \"Server-side web applications running on a web server (e.g., ASP.NET, Node.js, PHP)\",\r\n    icon: Globe\r\n  },\r\n  {\r\n    value: \"spa\",\r\n    option: \"Single Page Application\",\r\n    description: \"JavaScript applications running in the browser (e.g., React, Angular, Vue.js)\",\r\n    icon: Computer\r\n  },\r\n  {\r\n    value: \"native\",\r\n    option: \"Native Application\",\r\n    description: \"Desktop or mobile applications running natively on devices\",\r\n    icon: Smartphone\r\n  },\r\n  {\r\n    value: \"device\",\r\n    option: \"Device Application\",\r\n    description: \"IoT devices, smart TVs, or other limited-input devices\",\r\n    icon: Tv\r\n  },\r\n  {\r\n    value: \"machine\",\r\n    option: \"Machine-to-Machine\",\r\n    description: \"Service accounts, daemons, or backend services that run without user interaction\",\r\n    icon: Cog\r\n  }\r\n] as const;", "import { Lock, Unlock } from \"lucide-react\";\r\n\r\nexport const CLIENT_TYPES = [\r\n  {\r\n    value: \"public\",\r\n    option: \"Public\",\r\n    description: \"Clients that cannot maintain the confidentiality of their credentials\",\r\n    icon: Unlock\r\n  },\r\n  {\r\n    value: \"confidential\",\r\n    option: \"Confidential\",\r\n    description: \"Clients capable of maintaining the confidentiality of their credentials\",\r\n    icon: Lock\r\n  }\r\n] as const;\r\n", "import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Shield<PERSON><PERSON><PERSON> } from \"lucide-react\";\r\n\r\nexport const CONSENT_TYPES = [\r\n  {\r\n    value: \"implicit\",\r\n    option: \"Implicit\",\r\n    description: \"Consent is assumed without requiring explicit user approval\",\r\n    icon: Shield<PERSON>he<PERSON>\r\n  },\r\n  {\r\n    value: \"explicit\",\r\n    option: \"Explicit\",\r\n    description: \"Requires explicit user approval before granting access\",\r\n    icon: UserCheck\r\n  },\r\n  {\r\n    value: \"hybrid\",\r\n    option: \"Hybrid\",\r\n    description: \"Requires explicit user approval before granting access\",\r\n    icon: ShieldQuestion\r\n  }\r\n] as const;\r\n", "// Function to format permission labels\r\nexport function formatPermissionLabel(permission: string): string {\r\n  if (!permission) return '';\r\n\r\n  // Split by colon to get the type and name\r\n  const parts = permission.split(':');\r\n  if (parts.length !== 2) return permission;\r\n\r\n  const [type, name] = parts;\r\n  if (!name || !type) return permission;\r\n\r\n  // Format based on type prefix\r\n  switch (type) {\r\n    case 'ept':\r\n      return `${name.charAt(0).toUpperCase()}${name.slice(1)} Endpoint`;\r\n    case 'gt':\r\n      return `${name.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')} Grant`;\r\n    case 'rst':\r\n      return `${name.charAt(0).toUpperCase()}${name.slice(1)} Response Type`;\r\n    case 'scp':\r\n      return `${name.charAt(0).toUpperCase()}${name.slice(1)} Scope`;\r\n    default:\r\n      return permission;\r\n  }\r\n}\r\n\r\n// Function to generate a random client ID\r\nexport function generateClientId(): string {\r\n  // List of adjectives and nouns to create app-like names\r\n  const adjectives = ['swift', 'bright', 'clever', 'dynamic', 'efficient', 'flexible', 'global', 'innovative',\r\n    'logical', 'modern', 'nimble', 'optimal', 'precise', 'quick', 'reliable', 'secure', 'stable',\r\n    'tech', 'unified', 'virtual', 'wise', 'active', 'agile', 'bold', 'central', 'digital'];\r\n\r\n  const nouns = ['app', 'api', 'system', 'platform', 'service', 'portal', 'hub', 'core', 'base',\r\n    'cloud', 'data', 'engine', 'framework', 'grid', 'interface', 'logic', 'matrix', 'network',\r\n    'object', 'project', 'resource', 'solution', 'tool', 'utility', 'vision', 'workspace'];\r\n\r\n  // Pick random words\r\n  const adjective = adjectives[Math.floor(Math.random() * adjectives.length)];\r\n  const noun = nouns[Math.floor(Math.random() * nouns.length)];\r\n\r\n  // Add a random number for uniqueness\r\n  const uniqueNumber = Math.floor(Math.random() * 1000);\r\n\r\n  // Combine to form app name\r\n  return `${adjective}${noun}${uniqueNumber}`;\r\n}\r\n\r\n// Function to generate a random client secret\r\nexport function generateClientSecret(): string {\r\n  // Generate a secret with only letters and numbers\r\n  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\r\n  let secret = '';\r\n  for (let i = 0; i < 32; i++) {\r\n    secret += chars.charAt(Math.floor(Math.random() * chars.length));\r\n  }\r\n  return secret;\r\n}\r\n\r\n/**\r\n * Converts a comma-separated string into an array of trimmed strings\r\n * @param input - Comma-separated string, array of strings, or any input that can be converted to string\r\n * @returns Array of trimmed non-empty strings\r\n */\r\nexport function convertCommaSeparatedToArray(input: string | string[] | null | undefined): string[] {\r\n  // If input is already an array, return it\r\n  if (Array.isArray(input)) {\r\n    return input.filter(item => item && item.length > 0);\r\n  }\r\n\r\n  // Otherwise, convert to string and split by comma\r\n  return input\r\n    ? String(input)\r\n      .split(',')\r\n      .map(item => item.trim())\r\n      .filter(item => item.length > 0)\r\n    : [];\r\n}\r\n", "'use client'\r\nimport { type CreateUpdateOpenIddictApplicationDto, type OpenIddictApplicationDto, putApiOpeniddictApplicationsById } from '@/client'\r\nimport { Button } from '@/components/ui/button'\r\nimport {\r\n  <PERSON><PERSON>,\r\n  DialogContent,\r\n  <PERSON><PERSON><PERSON>ooter,\r\n  <PERSON><PERSON>Header,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog'\r\nimport { Input } from '@/components/ui/input'\r\nimport { useToast } from '@/lib/useToast'\r\nimport { QueryNames } from '@/lib/hooks/QueryConstants'\r\nimport { useMutation, useQueryClient } from '@tanstack/react-query'\r\nimport { useState } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { Toaster } from '@/components/ui/toaster'\r\nimport { RiRefreshLine } from '@remixicon/react'\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItemExtended,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select'\r\nimport { MultiSelect, type MultiSelectOption } from '@/components/ui/multi-select'\r\nimport { useOpeniddictApplicationsPermissions, useOpeniddictRequirements } from '@/lib/hooks/useOpeniddictApplicationsPermissions'\r\nimport { FormField, FormSection } from '@/components/ui/FormField'\r\nimport { APPLICATION_TYPES } from '@/data/applicationType'\r\nimport { CLIENT_TYPES } from '@/data/clientType'\r\nimport { CONSENT_TYPES } from '@/data/consentType'\r\nimport { convertCommaSeparatedToArray, formatPermissionLabel, generateClientId } from '@/lib/utils/client'\r\nimport { handleApiError } from '@/lib/handleApiError'\r\n\r\nexport type EditClientProps = {\r\n  dataEdit: OpenIddictApplicationDto\r\n  dataId: string\r\n  onDismiss: () => void\r\n}\r\n\r\nexport const EditClient = ({ dataEdit, dataId, onDismiss }: EditClientProps) => {\r\n  const [open, setOpen] = useState(true)\r\n  const { toast } = useToast()\r\n  const queryClient = useQueryClient()\r\n  const { handleSubmit, register, setValue, reset } = useForm<CreateUpdateOpenIddictApplicationDto>()\r\n  const [selectedPermissions, setSelectedPermissions] = useState<string[]>(dataEdit.permissions ?? [])\r\n  const [selectedRequirements, setSelectedRequirements] = useState<string[]>(dataEdit.requirements ?? [])\r\n  // Fetch available permissions from the API\r\n  const { data: permissionsData, isLoading: permissionsLoading } = useOpeniddictApplicationsPermissions()\r\n  const { data: requirementsData, isLoading: requirementsLoading } = useOpeniddictRequirements()\r\n\r\n  const resetForm = () => {\r\n    reset({\r\n      applicationType: '',\r\n      clientType: '',\r\n      clientId: '',\r\n      clientSecret: '',\r\n      displayName: '',\r\n      consentType: '',\r\n      redirectUris: [],\r\n      postLogoutRedirectUris: [],\r\n      permissions: [],\r\n      requirements: []\r\n    })\r\n  }\r\n\r\n  // Convert API permissions (string array) to MultiSelectOption format\r\n  const permissionOptions: MultiSelectOption[] = permissionsData\r\n    ? (Array.isArray(permissionsData)\r\n      ? permissionsData.map(permission => ({\r\n        value: permission,\r\n        label: formatPermissionLabel(permission)\r\n      }))\r\n      : [])\r\n    : []\r\n\r\n  const requirementOptions: MultiSelectOption[] = requirementsData\r\n    ? (Array.isArray(requirementsData)\r\n      ? (requirementsData as Array<MultiSelectOption>).map(requirement => ({\r\n        value: requirement.value,\r\n        label: requirement.label\r\n      }))\r\n      : [])\r\n    : []\r\n\r\n  const createUserMutation = useMutation({\r\n    mutationFn: async (dataMutation: CreateUpdateOpenIddictApplicationDto) =>\r\n      putApiOpeniddictApplicationsById({\r\n        path: { id: dataId },\r\n        body: dataMutation\r\n      }),\r\n    onSuccess: () => {\r\n      toast({\r\n        title: 'Success',\r\n        description: 'Client Created Successfully',\r\n        variant: 'success',\r\n      })\r\n      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetOpeniddictApplications] })\r\n      resetForm()\r\n      setOpen(false)\r\n      onDismiss()\r\n    },\r\n    onError: (err: unknown) => {\r\n      const error = handleApiError(err);\r\n      toast({\r\n        title: error.title,\r\n        description: error.description,\r\n        variant: 'error',\r\n      })\r\n    }\r\n  })\r\n\r\n  const onSubmit = (formData: CreateUpdateOpenIddictApplicationDto) => {\r\n    // Merge form data with consent type and permissions\r\n    const dataMutation: CreateUpdateOpenIddictApplicationDto = {\r\n      ...formData,\r\n      applicationType: formData.applicationType ?? dataEdit.applicationType ?? '', // Include applicationType\r\n      clientType: formData.clientType ?? dataEdit.clientType ?? '', // Include clientType\r\n      permissions: selectedPermissions,\r\n      redirectUris: convertCommaSeparatedToArray(formData.redirectUris),\r\n      postLogoutRedirectUris: convertCommaSeparatedToArray(formData.postLogoutRedirectUris),\r\n      // clientSecret: null\r\n    }\r\n    createUserMutation.mutate(dataMutation)\r\n  }\r\n\r\n  const handleOpenChange = (newOpen: boolean) => {\r\n    if (newOpen) {\r\n      resetForm()\r\n      onDismiss()\r\n    }\r\n    setOpen(newOpen)\r\n  }\r\n\r\n  return (\r\n    <section>\r\n      <Toaster />\r\n      <Dialog open={open} onOpenChange={handleOpenChange}>\r\n        <DialogContent style={{ maxWidth: \"800px\" }}>\r\n          <DialogHeader>\r\n            <DialogTitle>Edit Client</DialogTitle>\r\n          </DialogHeader>\r\n          <form onSubmit={handleSubmit(onSubmit)} className='mt-2'>\r\n            <section className=\"flex w-full flex-col space-y-2\">\r\n              <FormSection>\r\n                <FormField\r\n                  label='Application Type'\r\n                  description='The type of the application'\r\n                >\r\n                  <Select\r\n                    defaultValue={dataEdit.applicationType ?? ''}\r\n                    onValueChange={(value) => setValue('applicationType', value)}\r\n                  >\r\n                    <SelectTrigger\r\n                      className=\"w-full\"\r\n                      clearable={true}\r\n                      onClear={() => setValue('applicationType', '')}\r\n                    >\r\n                      <SelectValue placeholder=\"Select application type\" />\r\n                    </SelectTrigger>\r\n                    <SelectContent>\r\n                      {APPLICATION_TYPES.map((type) => (\r\n                        <SelectItemExtended\r\n                          key={type.value}\r\n                          value={type.value}\r\n                          option={type.option}\r\n                          description={type.description}\r\n                          icon={type.icon}\r\n                          clearable={true}\r\n                        />\r\n                      ))}\r\n                    </SelectContent>\r\n                  </Select>\r\n                </FormField>\r\n                <FormField\r\n                  label='Client Type'\r\n                  description='The type of the client'\r\n                >\r\n                  <Select\r\n                    defaultValue={dataEdit.clientType ?? ''}\r\n                    onValueChange={(value) => setValue('clientType', value)}\r\n                  >\r\n                    <SelectTrigger className=\"w-full\">\r\n                      <SelectValue placeholder=\"Select client type\" />\r\n                    </SelectTrigger>\r\n                    <SelectContent>\r\n                      {CLIENT_TYPES.map((type) => (\r\n                        <SelectItemExtended\r\n                          key={type.value}\r\n                          value={type.value}\r\n                          option={type.option}\r\n                          description={type.description}\r\n                          icon={type.icon}\r\n                        />\r\n                      ))}\r\n                    </SelectContent>\r\n                  </Select>\r\n                </FormField>\r\n                <FormField\r\n                  label=\"Client Id\"\r\n                  description=\"The client id of the client\"\r\n                >\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Input required {...register('clientId')} defaultValue={dataEdit.clientId ?? ''} placeholder=\"Client Id\" />\r\n                    <Button\r\n                      type=\"button\"\r\n                      variant=\"secondary\"\r\n                      onClick={generateClientId}\r\n                      title=\"Generate Client ID\"\r\n                    >\r\n                      <RiRefreshLine className=\"size-4\" />\r\n                    </Button>\r\n                  </div>\r\n                </FormField>\r\n\r\n                {/* <FormField\r\n                  label=\"Client Secret\"\r\n                  description=\"The client secret of the client\"\r\n                >\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Input required {...register('clientSecret')} defaultValue={dataEdit.clientSecret ?? ''} placeholder=\"Client Secret\" />\r\n                    <Button\r\n                      type=\"button\"\r\n                      variant=\"secondary\"\r\n                      onClick={generateClientSecret}\r\n                      title=\"Generate Client Secret\"\r\n                    >\r\n                      <RiRefreshLine className=\"size-4\" />\r\n                    </Button>\r\n                  </div>\r\n                </FormField> */}\r\n\r\n                <FormField\r\n                  label=\"Display name\"\r\n                  description=\"The display name of the application\"\r\n                >\r\n                  <Input placeholder=\"Display name\" {...register('displayName')} defaultValue={dataEdit.displayName ?? ''} />\r\n                </FormField>\r\n                <FormField\r\n                  label=\"Consent type\"\r\n                  description=\"The consent type of the client\"\r\n                >\r\n                  <Select {...register('consentType')} defaultValue={dataEdit.consentType ?? ''}>\r\n                    <SelectTrigger className=\"w-full\">\r\n                      <SelectValue placeholder=\"Select consent type\" />\r\n                    </SelectTrigger>\r\n                    <SelectContent>\r\n                      {CONSENT_TYPES.map((type) => (\r\n                        <SelectItemExtended\r\n                          key={type.value}\r\n                          value={type.value}\r\n                          option={type.option}\r\n                          description={type.description}\r\n                          icon={type.icon}\r\n                        />\r\n                      ))}\r\n                    </SelectContent>\r\n                  </Select>\r\n                </FormField>\r\n\r\n                <FormField\r\n                  label=\"Client Uri\"\r\n                  description=\"The client uri of the client\"\r\n                >\r\n                  <Input placeholder=\"Client Uri\" {...register('clientUri')} defaultValue={dataEdit.clientUri ?? ''} />\r\n                </FormField>\r\n\r\n                <FormField\r\n                  label=\"Redirect Uris\"\r\n                  description=\"The redirect uris of the client\"\r\n                >\r\n                  <Input placeholder=\"Redirect Uris\" {...register('redirectUris')} defaultValue={dataEdit.redirectUris ?? ''} />\r\n                </FormField>\r\n                <FormField\r\n                  label=\"Post logout redirect Uris\"\r\n                  description=\"The post logout redirect uris of the client\"\r\n                >\r\n                  <Input placeholder=\"Post logout redirect Uris\" {...register('postLogoutRedirectUris')} defaultValue={dataEdit.postLogoutRedirectUris ?? ''} />\r\n                </FormField>\r\n\r\n                <FormField\r\n                  label=\"Permissions\"\r\n                  description=\"The permissions of the client\"\r\n                >\r\n                  <MultiSelect\r\n                    options={permissionOptions}\r\n                    value={selectedPermissions}\r\n                    onChange={(values) => {\r\n                      setSelectedPermissions(values);\r\n                      setValue('permissions', values);\r\n                    }}\r\n                    placeholder={permissionsLoading ? \"Loading permissions...\" : \"Select permissions\"}\r\n                    disabled={permissionsLoading}\r\n                    maxHeight={300}\r\n                  />\r\n                </FormField>\r\n\r\n                <FormField\r\n                  label=\"Requirements\"\r\n                  description=\"The requirements of the client\"\r\n                >\r\n                  <MultiSelect\r\n                    mode='single'\r\n                    options={requirementOptions}\r\n                    value={selectedRequirements}\r\n                    onChange={(values) => {\r\n                      setSelectedRequirements(values);\r\n                      setValue('requirements', values);\r\n                    }}\r\n                    placeholder={requirementsLoading ? \"Loading requirements...\" : \"Select requirements\"}\r\n                    disabled={requirementsLoading}\r\n                    maxHeight={300}\r\n                  />\r\n                </FormField>\r\n              </FormSection>\r\n            </section>\r\n            <DialogFooter className=\"mt-5\">\r\n              <Button\r\n                variant=\"ghost\"\r\n                onClick={(e) => {\r\n                  e.preventDefault()\r\n                  setOpen(false)\r\n                }}\r\n                disabled={createUserMutation.isPending}\r\n              >\r\n                Cancel\r\n              </Button>\r\n              <Button type=\"submit\" disabled={createUserMutation.isPending}>\r\n                {createUserMutation.isPending ? 'Saving...' : 'Save'}\r\n              </Button>\r\n            </DialogFooter>\r\n          </form>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </section>\r\n  )\r\n}\r\n\r\n\r\n", "'use client'\r\nimport { type CreateUpdateOpenIddictApplicationDto, postApiOpeniddictApplications } from '@/client'\r\nimport { Button } from '@/components/ui/button'\r\nimport {\r\n  Di<PERSON>,\r\n  DialogContent,\r\n  DialogFooter,\r\n  Di<PERSON>Header,\r\n  <PERSON><PERSON>Title,\r\n  DialogTrigger,\r\n} from '@/components/ui/dialog'\r\nimport { Input } from '@/components/ui/input'\r\nimport { useToast } from '@/lib/useToast'\r\nimport { QueryNames } from '@/lib/hooks/QueryConstants'\r\nimport { useGrantedPolicies } from '@/lib/hooks/useGrantedPolicies'\r\nimport { useMutation, useQueryClient } from '@tanstack/react-query'\r\nimport { useState } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { Toaster } from '@/components/ui/toaster'\r\nimport { RiAddLine, RiRefreshLine } from '@remixicon/react'\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItemExtended,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select'\r\nimport { MultiSelect, type MultiSelectOption } from '@/components/ui/multi-select'\r\nimport { useOpeniddictApplicationsPermissions, useOpeniddictRequirements } from '@/lib/hooks/useOpeniddictApplicationsPermissions'\r\nimport { FormField, FormSection } from '@/components/ui/FormField'\r\nimport { APPLICATION_TYPES } from '@/data/applicationType'\r\nimport { CLIENT_TYPES } from '@/data/clientType'\r\nimport { CONSENT_TYPES } from '@/data/consentType'\r\nimport { formatPermissionLabel, generateClientId, generateClientSecret, convertCommaSeparatedToArray } from '@/lib/utils/client'\r\nimport { handleApiError } from '@/lib/handleApiError'\r\nexport type AddClientProps = {\r\n  children?: React.ReactNode\r\n}\r\n\r\nexport const AddClient = ({ children }: AddClientProps) => {\r\n  const { can } = useGrantedPolicies()\r\n  const [open, setOpen] = useState(false)\r\n  const { toast } = useToast()\r\n  const queryClient = useQueryClient()\r\n  const { handleSubmit, register, setValue, reset } = useForm<CreateUpdateOpenIddictApplicationDto>()\r\n  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([])\r\n  const [selectedRequirements, setSelectedRequirements] = useState<string[]>([])\r\n  // Fetch available permissions from the API\r\n  const { data: permissionsData, isLoading: permissionsLoading } = useOpeniddictApplicationsPermissions()\r\n  const { data: requirementsData, isLoading: requirementsLoading } = useOpeniddictRequirements()\r\n\r\n  const resetForm = () => {\r\n    reset({\r\n      applicationType: '',\r\n      clientType: '',\r\n      clientId: '',\r\n      clientSecret: '',\r\n      displayName: '',\r\n      consentType: '',\r\n      redirectUris: [],\r\n      postLogoutRedirectUris: [],\r\n      permissions: [],\r\n      requirements: []\r\n    })\r\n  }\r\n\r\n  // Convert API permissions (string array) to MultiSelectOption format\r\n  const permissionOptions: MultiSelectOption[] = permissionsData\r\n    ? (Array.isArray(permissionsData)\r\n      ? permissionsData.map(permission => ({\r\n        value: permission,\r\n        label: formatPermissionLabel(permission)\r\n      }))\r\n      : [])\r\n    : []\r\n\r\n  const requirementOptions: MultiSelectOption[] = requirementsData\r\n    ? (Array.isArray(requirementsData)\r\n      ? (requirementsData as Array<MultiSelectOption>).map(requirement => ({\r\n        value: requirement.value,\r\n        label: requirement.label\r\n      }))\r\n      : [])\r\n    : []\r\n\r\n  const createUserMutation = useMutation({\r\n    mutationFn: async (dataMutation: CreateUpdateOpenIddictApplicationDto) =>\r\n      postApiOpeniddictApplications({\r\n        body: dataMutation\r\n      }),\r\n    onSuccess: () => {\r\n      toast({\r\n        title: 'Success',\r\n        description: 'Client Created Successfully',\r\n        variant: 'success',\r\n      })\r\n      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetOpeniddictApplications] })\r\n      setOpen(false)\r\n      resetForm()\r\n    },\r\n    onError: (err: unknown) => {\r\n      const error = handleApiError(err);\r\n      toast({\r\n        title: error.title,\r\n        description: error.description,\r\n        variant: 'error',\r\n      })\r\n    }\r\n  })\r\n\r\n  const onSubmit = (formData: CreateUpdateOpenIddictApplicationDto) => {\r\n    // Merge form data with consent type and permissions\r\n    console.log(\"formData\", formData)\r\n    const dataMutation: CreateUpdateOpenIddictApplicationDto = {\r\n      ...formData,\r\n      redirectUris: convertCommaSeparatedToArray(formData.redirectUris),\r\n      postLogoutRedirectUris: convertCommaSeparatedToArray(formData.postLogoutRedirectUris),\r\n      permissions: selectedPermissions,\r\n      // applicationType: selectedPermissions,\r\n    }\r\n\r\n    createUserMutation.mutate(dataMutation)\r\n  }\r\n\r\n  const handleOpenChange = (newOpen: boolean) => {\r\n    if (newOpen) {\r\n      resetForm()\r\n    }\r\n    setOpen(newOpen)\r\n  }\r\n\r\n  return (\r\n    <section>\r\n      <Toaster />\r\n      <Dialog open={open} onOpenChange={handleOpenChange}>\r\n        <DialogTrigger asChild>{children}</DialogTrigger>\r\n        <section className=\"flex items-center justify-between pb-5\">\r\n          {can('AbpIdentity.Users.Create') && (\r\n            <Button size='sm' className=\"w-full sm:py-1 sm:mt-0 sm:w-fit\" onClick={() => handleOpenChange(true)}>\r\n              <RiAddLine className=\"-ml-1 size-4 shrink-0\" aria-hidden=\"true\" />\r\n              <span className=\"hidden truncate sm:inline\">Create New Client</span>\r\n            </Button>\r\n          )}\r\n        </section>\r\n        <DialogContent style={{ maxWidth: \"800px\" }}>\r\n          <DialogHeader>\r\n            <DialogTitle>Create a New Client</DialogTitle>\r\n          </DialogHeader>\r\n          <form onSubmit={handleSubmit(onSubmit)} className='mt-2'>\r\n            <section className=\"flex w-full flex-col space-y-2\">\r\n              <FormSection>\r\n                <FormField\r\n                  label='Application Type'\r\n                  description='The type of the application'\r\n                >\r\n                  <Select {...register('applicationType')} onValueChange={(item) => setValue('applicationType', item)}>\r\n                    <SelectTrigger\r\n                      className=\"w-full\"\r\n                      clearable={true}\r\n                      onClear={() => setValue('applicationType', '')}\r\n                    >\r\n                      <SelectValue placeholder=\"Select application type\" />\r\n                    </SelectTrigger>\r\n                    <SelectContent >\r\n                      {APPLICATION_TYPES.map((type) => (\r\n                        <SelectItemExtended\r\n                          key={type.value}\r\n                          value={type.value}\r\n                          option={type.option}\r\n                          description={type.description}\r\n                          icon={type.icon}\r\n                          clearable={true}\r\n                        />\r\n                      ))}\r\n                    </SelectContent>\r\n                  </Select>\r\n                </FormField>\r\n                <FormField\r\n                  label='Client Type'\r\n                  description='The type of the client'\r\n                >\r\n                  <Select {...register('clientType')} onValueChange={(item) => setValue('clientType', item)}>\r\n                    <SelectTrigger className=\"w-full\">\r\n                      <SelectValue placeholder=\"Select client type\" />\r\n                    </SelectTrigger>\r\n                    <SelectContent>\r\n                      {CLIENT_TYPES.map((type) => (\r\n                        <SelectItemExtended\r\n                          key={type.value}\r\n                          value={type.value}\r\n                          option={type.option}\r\n                          description={type.description}\r\n                          icon={type.icon}\r\n                        />\r\n                      ))}\r\n                    </SelectContent>\r\n                  </Select>\r\n                </FormField>\r\n                <FormField\r\n                  label=\"Client Id\"\r\n                  description=\"The client id of the client\"\r\n                >\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Input required {...register('clientId')} placeholder=\"Client Id\" />\r\n                    <Button\r\n                      type=\"button\"\r\n                      size={'sm'}\r\n                      variant=\"secondary\"\r\n                      onClick={() => setValue(\"clientId\", generateClientId())}\r\n                      title=\"Generate Client ID\"\r\n                    >\r\n                      <RiRefreshLine className=\"size-4\" />\r\n                    </Button>\r\n                  </div>\r\n                </FormField>\r\n\r\n                <FormField\r\n                  label=\"Client Secret\"\r\n                  description=\"The client secret of the client\"\r\n                >\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Input required {...register('clientSecret')} placeholder=\"Client Secret\" />\r\n                    <Button\r\n                      type=\"button\"\r\n                      variant=\"secondary\"\r\n                      size={'sm'}\r\n                      onClick={() => setValue(\"clientSecret\", generateClientSecret())}\r\n                      title=\"Generate Client Secret\"\r\n                    >\r\n                      <RiRefreshLine className=\"size-4\" />\r\n                    </Button>\r\n                  </div>\r\n                </FormField>\r\n\r\n                <FormField\r\n                  label=\"Display name\"\r\n                  description=\"The display name of the application\"\r\n                >\r\n                  <Input placeholder=\"Display name\" {...register('displayName')} />\r\n                </FormField>\r\n                <FormField\r\n                  label=\"Consent type\"\r\n                  description=\"The consent type of the client\"\r\n                >\r\n                  <Select {...register('consentType')} onValueChange={(item) => setValue('consentType', item)}>\r\n                    <SelectTrigger className=\"w-full\">\r\n                      <SelectValue placeholder=\"Select consent type\" />\r\n                    </SelectTrigger>\r\n                    <SelectContent>\r\n                      {CONSENT_TYPES.map((type) => (\r\n                        <SelectItemExtended\r\n                          key={type.value}\r\n                          value={type.value}\r\n                          option={type.option}\r\n                          description={type.description}\r\n                          icon={type.icon}\r\n                        />\r\n                      ))}\r\n                    </SelectContent>\r\n                  </Select>\r\n                </FormField>\r\n\r\n                <FormField\r\n                  label=\"Client Uri\"\r\n                  description=\"The client uri of the client\"\r\n                >\r\n                  <Input placeholder=\"Client Uri\" {...register('clientUri')} />\r\n                </FormField>\r\n\r\n                <FormField\r\n                  label=\"Redirect Uris\"\r\n                  description=\"The redirect uris of the client\"\r\n                >\r\n                  <Input placeholder=\"Redirect Uris\" {...register('redirectUris')} />\r\n                </FormField>\r\n                <FormField\r\n                  label=\"Post logout redirect Uris\"\r\n                  description=\"The post logout redirect uris of the client\"\r\n                >\r\n                  <Input placeholder=\"Post logout redirect Uris\" {...register('postLogoutRedirectUris')} />\r\n                </FormField>\r\n\r\n                <FormField\r\n                  label=\"Permissions\"\r\n                  description=\"The permissions of the client\"\r\n                >\r\n                  <MultiSelect\r\n                    options={permissionOptions}\r\n                    value={selectedPermissions}\r\n                    onChange={(values) => {\r\n                      setSelectedPermissions(values);\r\n                      setValue('permissions', values);\r\n                    }}\r\n                    placeholder={permissionsLoading ? \"Loading permissions...\" : \"Select permissions\"}\r\n                    disabled={permissionsLoading}\r\n                    maxHeight={300}\r\n                  />\r\n                </FormField>\r\n\r\n                <FormField\r\n                  label=\"Requirements\"\r\n                  description=\"The requirements of the client\"\r\n                >\r\n                  <MultiSelect\r\n                    mode='single'\r\n                    options={requirementOptions}\r\n                    value={selectedRequirements}\r\n                    onChange={(values) => {\r\n                      setSelectedRequirements(values);\r\n                      setValue('requirements', values);\r\n                    }}\r\n                    placeholder={requirementsLoading ? \"Loading requirements...\" : \"Select requirements\"}\r\n                    disabled={requirementsLoading}\r\n                    maxHeight={300}\r\n                  />\r\n                </FormField>\r\n              </FormSection>\r\n            </section>\r\n            <DialogFooter className=\"mt-5\">\r\n              <Button\r\n                variant=\"ghost\"\r\n                onClick={(e) => {\r\n                  e.preventDefault()\r\n                  setOpen(false)\r\n                }}\r\n                disabled={createUserMutation.isPending}\r\n              >\r\n                Cancel\r\n              </Button>\r\n              <Button type=\"submit\" disabled={createUserMutation.isPending}>\r\n                {createUserMutation.isPending ? 'Saving...' : 'Save'}\r\n              </Button>\r\n            </DialogFooter>\r\n          </form>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </section>\r\n  )\r\n}\r\n\r\n", "'use client'\r\n\r\nimport { type OpenIddictApplicationDto } from '@/client'\r\nimport { useGrantedPolicies } from '@/lib/hooks/useGrantedPolicies'\r\nimport { RiMoreLine, RiPencilLine, RiShieldKeyholeLine } from '@remixicon/react'\r\nimport { Button } from '@/components/ui/button'\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu'\r\n\r\ntype UserActionProps = {\r\n  dataId: string\r\n  dataEdit: OpenIddictApplicationDto\r\n  onAction: (dataId: string, dataEdit: OpenIddictApplicationDto, dialogType: 'edit' | 'permission' | 'delete') => void\r\n  variant?: 'dropdown' | 'buttons'\r\n}\r\n\r\nexport const ClientActions = ({ dataId, dataEdit, onAction, variant = 'dropdown' }: UserActionProps) => {\r\n  const { can } = useGrantedPolicies()\r\n\r\n  // For dropdown menu style (first image)\r\n  if (variant === 'dropdown') {\r\n    return (\r\n      <div className=\"flex justify-end\">\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"h-8 w-8 p-0\"\r\n            >\r\n              <RiMoreLine className=\"h-4 w-4\" />\r\n              <span className=\"sr-only\">Open menu</span>\r\n            </Button>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent align=\"end\" className=\"w-[160px]\">\r\n            {can('IdentityServer.OpenIddictApplications.Edit') && (\r\n              <DropdownMenuItem\r\n                className=\"cursor-pointer text-sm\"\r\n                onClick={() => onAction(dataId, dataEdit, 'edit')}\r\n              >\r\n                Edit\r\n              </DropdownMenuItem>\r\n            )}\r\n            {can('IdentityServer.OpenIddictApplications.Delete') && (\r\n              <DropdownMenuItem\r\n                className=\"cursor-pointer text-sm text-red-500\"\r\n                onClick={() => onAction(dataId, dataEdit, 'delete')}\r\n              >\r\n                Delete\r\n              </DropdownMenuItem>\r\n            )}\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // For button group style (second image)\r\n  return (\r\n    <div className=\"flex items-center justify-end gap-1\">\r\n      {can('AbpIdentity.Users.ManagePermissions') && (\r\n        <Button\r\n          variant=\"primary\"\r\n          size=\"sm\"\r\n          className=\"flex items-center gap-1 px-2 py-1\"\r\n          onClick={() => onAction(dataId, dataEdit, 'permission')}\r\n        >\r\n          <RiShieldKeyholeLine className=\"h-4 w-4\" />\r\n          <span>Permission</span>\r\n        </Button>\r\n      )}\r\n      {can('AbpIdentity.Users.Update') && (\r\n        <Button\r\n          variant=\"primary\"\r\n          size=\"sm\"\r\n          className=\"flex items-center gap-1 px-2 py-1\"\r\n          onClick={() => onAction(dataId, dataEdit, 'edit')}\r\n        >\r\n          <RiPencilLine className=\"h-4 w-4\" />\r\n          <span>Edit</span>\r\n        </Button>\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n", "'use client'\r\n\r\nimport { type OpenIddictApplicationDto } from '@/client'\r\nimport { type ColumnDef } from '@tanstack/react-table'\r\nimport { ClientActions } from './ClientActions'\r\nimport { DataTableColumnHeader } from '@/components/data-table/DataTableColumnHeader'\r\nimport { Badge } from '@/components/ui/badge'\r\nimport { customFilterFunction } from '@/components/data-table/filterFunctions'\r\n\r\n// Type for the callback function to handle user actions\r\ntype UserActionCallback = (dataId: string, dataEdit: OpenIddictApplicationDto, dialogType: 'edit' | 'permission' | 'delete') => void\r\n\r\n// Function to create user columns with the action callback\r\nexport const getUserColumns = (\r\n  handleUserAction: UserActionCallback\r\n): ColumnDef<OpenIddictApplicationDto>[] => [\r\n    {\r\n      accessorKey: 'clientId',\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title=\"Client ID\" />\r\n      ),\r\n      enableSorting: true,\r\n      filterFn: customFilterFunction,\r\n      enableHiding: true,\r\n      cell: (info) => info.getValue(),\r\n      meta: {\r\n        className: \"text-left\",\r\n        displayName: \"Client ID\",\r\n      },\r\n    },\r\n    {\r\n      accessorKey: 'displayName',\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title=\"Display Name\" />\r\n      ),\r\n      enableSorting: true,\r\n      filterFn: customFilterFunction,\r\n      enableHiding: true,\r\n      cell: (info) => info.getValue(),\r\n      meta: {\r\n        className: \"text-left\",\r\n        displayName: \"Display Name\",\r\n      },\r\n    },\r\n    {\r\n      accessorKey: 'applicationType',\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title=\"Application Type\" />\r\n      ),\r\n      enableSorting: true,\r\n      filterFn: customFilterFunction,\r\n      enableHiding: true,\r\n      cell: (info) => info.getValue(),\r\n      meta: {\r\n        className: \"text-left\",\r\n        displayName: \"Application Type\",\r\n      },\r\n    },\r\n    // {\r\n    //   accessorKey: 'clientType',\r\n    //   header: ({ column }) => (\r\n    //     <DataTableColumnHeader column={column} title=\"Client Type\" />\r\n    //   ),\r\n    //   enableSorting: true,\r\n    // filterFn: customFilterFunction,\r\n    //   enableHiding: true,\r\n    //   cell: (info) => info.getValue(),\r\n    //   meta: {\r\n    //     className: \"text-left\",\r\n    //     displayName: \"Client Type\",\r\n    //   },\r\n    // },\r\n    {\r\n      accessorKey: 'consentType',\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title=\"Consent Type\" />\r\n      ),\r\n      enableSorting: true,\r\n      filterFn: customFilterFunction,\r\n      enableHiding: true,\r\n      cell: (info) => info.getValue(),\r\n      meta: {\r\n        className: \"text-left\",\r\n        displayName: \"Consent Type\",\r\n      },\r\n    },\r\n    {\r\n      accessorKey: 'redirectUris',\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title=\"Redirect URIs\" />\r\n      ),\r\n      enableSorting: true,\r\n      filterFn: customFilterFunction,\r\n      enableHiding: true,\r\n      cell: (info) => {\r\n        const resources = info.getValue() as string[];\r\n        return (\r\n          <div className=\"flex flex-wrap gap-1\">\r\n            {resources?.map((resource) => (\r\n              <Badge key={resource} variant=\"secondary\">\r\n                {resource}\r\n              </Badge>\r\n            ))}\r\n          </div>\r\n        );\r\n      },\r\n      meta: {\r\n        className: \"text-left\",\r\n        displayName: \"Redirect URIs\",\r\n      },\r\n    },\r\n    {\r\n      id: 'actions',\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title=\"Actions\" />\r\n      ),\r\n      enableSorting: false,\r\n      enableHiding: true,\r\n      cell: (info) => (\r\n        <ClientActions\r\n          dataId={info.row.original.id!}\r\n          dataEdit={info.row.original}\r\n          onAction={handleUserAction}\r\n          variant=\"dropdown\" // Use \"dropdown\" for the first image style or \"buttons\" for the second image style\r\n        />\r\n      ),\r\n      meta: {\r\n        className: \"text-right\",\r\n        displayName: \"Actions\",\r\n      },\r\n    },\r\n  ]\r\n", "import {\r\n  postApiOpeniddictApplicationsList,\r\n} from '@/client'\r\nimport { extractApiError } from '@/lib/query-utils'\r\nimport { generateExtendedQueryParameters } from '@/lib/query-utils-extended'\r\nimport { toast } from '@/lib/useToast'\r\nimport { useQuery } from '@tanstack/react-query'\r\nimport { QueryNames } from './QueryConstants'\r\nimport type { FilterCondition } from '@/lib/interfaces/IFilterCondition'\r\n\r\n\r\nexport const useOpeniddictApplications = (\r\n  pageIndex: number,\r\n  pageSize: number,\r\n  filterConditions: FilterCondition[] = [],\r\n  sorting?: string\r\n) => {\r\n  return useQuery({\r\n    queryKey: [QueryNames.GetOpeniddictApplications, pageIndex, pageSize, JSON.stringify(filterConditions), sorting],\r\n    queryFn: async () => {\r\n      try {\r\n        // Generate query parameters using the extended utility function\r\n        const body = generateExtendedQueryParameters({\r\n          pageIndex,\r\n          pageSize,\r\n          sorting,\r\n          filterConditions,\r\n        })\r\n\r\n        const response = await postApiOpeniddictApplicationsList({\r\n          body,\r\n        })\r\n\r\n        // Ensure we return a valid data structure even if the API response is unexpected\r\n        return response.data?.data\r\n      } catch (error) {\r\n        // Use the error extraction utility\r\n        const { title, description } = extractApiError(error, 'Error loading clients')\r\n\r\n        // Show toast notification\r\n        toast({\r\n          title,\r\n          description,\r\n          variant: 'destructive',\r\n        })\r\n\r\n        // Return empty data to prevent UI crashes\r\n        return { items: [], totalCount: 0 }\r\n      }\r\n    },\r\n    retry: false, // Don't retry on error\r\n  })\r\n}\r\n", "'use client'\r\nimport { QueryNames } from '@/lib/hooks/QueryConstants'\r\nimport { useState } from 'react'\r\n\r\nimport { type OpenIddictApplicationDto } from '@/client'\r\nimport { type PaginationState } from '@tanstack/react-table'\r\n\r\nimport { useToast } from '@/lib/useToast'\r\nimport { DeleteClient } from './DeleteClient'\r\nimport { EditClient } from './EditClient'\r\nimport { AddClient } from './AddClient'\r\n\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { DataTable } from '@/components/data-table/DataTable'\r\nimport { getUserColumns } from './Columns'\r\nimport { useOpeniddictApplications } from '@/lib/hooks/useOpeniddictApplications'\r\nimport { TableSkeleton } from '@/components/ui/TableSkeleton'\r\nimport { NotionFilter } from '@/components/data-table/NotionFilter'\r\nimport { type FilterCondition } from '@/lib/interfaces/IFilterCondition'\r\n\r\nexport const ClientList = () => {\r\n  const { toast } = useToast()\r\n  const queryClient = useQueryClient()\r\n\r\n  const [searchStr, setSearchStr] = useState<string>('')\r\n  const [filterConditions, setFilterConditions] = useState<FilterCondition[]>([])\r\n  const [userActionDialog, setUserActionDialog] = useState<{\r\n    dataId: string\r\n    dataEdit: OpenIddictApplicationDto\r\n    dialogType?: 'edit' | 'permission' | 'delete'\r\n  } | null>()\r\n\r\n  const [pagination, setPagination] = useState<PaginationState>({\r\n    pageIndex: 0,\r\n    pageSize: 10,\r\n  })\r\n\r\n  const { isLoading, data } = useOpeniddictApplications(\r\n    pagination.pageIndex,\r\n    pagination.pageSize,\r\n    filterConditions\r\n  )\r\n\r\n  // Handler for user actions (edit, permission, delete)\r\n  const handleUserAction = (dataId: string, dataEdit: OpenIddictApplicationDto, dialogType: 'edit' | 'permission' | 'delete') => {\r\n    setUserActionDialog({\r\n      dataId,\r\n      dataEdit,\r\n      dialogType,\r\n    })\r\n  }\r\n\r\n  // Get columns with the action handler\r\n  const columns = getUserColumns(handleUserAction)\r\n\r\n  const handleSearch = (value: string) => {\r\n    // Always update the search string for UI consistency\r\n    setSearchStr(value)\r\n\r\n    // Create a search filter condition if there's a search value\r\n    // First, remove any existing name filter\r\n    const existingFilters = filterConditions.filter(fc => fc.fieldName !== 'displayName')\r\n    const newFilterConditions = [...existingFilters]\r\n\r\n    // Only add the search filter if there's a value\r\n    if (value) {\r\n      newFilterConditions.push({\r\n        fieldName: 'displayName',\r\n        operator: 'Contains',\r\n        value: value\r\n      })\r\n    }\r\n\r\n    // Only update state if filters have changed\r\n    const currentFiltersStr = JSON.stringify(filterConditions)\r\n    const newFiltersStr = JSON.stringify(newFilterConditions)\r\n\r\n    if (currentFiltersStr !== newFiltersStr) {\r\n      setFilterConditions(newFilterConditions)\r\n      setPagination(prev => ({ ...prev, pageIndex: 0 })) // Reset to first page on search\r\n    }\r\n  }\r\n\r\n  const handlePaginationChange = (newPagination: PaginationState) => {\r\n    setPagination(newPagination)\r\n  }\r\n\r\n  // Handler for refreshing the data\r\n  const handleRefresh = () => {\r\n    // Invalidate the query to fetch fresh data\r\n    void queryClient.invalidateQueries({ queryKey: [QueryNames.GetOpeniddictApplications] })\r\n\r\n    // Show toast notification after a short delay to match the animation\r\n    toast({\r\n      title: \"Data refreshed\",\r\n      description: \"The client list has been refreshed.\",\r\n      variant: \"success\",\r\n    })\r\n  }\r\n\r\n  if (isLoading) return (\r\n    <TableSkeleton\r\n      rowCount={pagination.pageSize}\r\n      columnCount={4}\r\n      hasTitle={true}\r\n      hasSearch={true}\r\n      hasFilters={true}\r\n      hasPagination={true}\r\n      hasActions={true}\r\n    />\r\n  )\r\n\r\n  const items = data?.items ?? [];\r\n  const totalCount = data?.totalCount ?? 0;\r\n\r\n  return (\r\n    <>\r\n      <div className=\"space-y-4\">\r\n        <DataTable\r\n          title=\"Clients Management\"\r\n          columns={columns}\r\n          data={items}\r\n          totalCount={totalCount}\r\n          isLoading={isLoading}\r\n          manualPagination={true}\r\n          pageSize={pagination.pageSize}\r\n          onPaginationChange={handlePaginationChange}\r\n          onSearch={handleSearch}\r\n          searchValue={searchStr}\r\n          customFilterbar={(props) => (\r\n            <NotionFilter\r\n              {...props}\r\n              activeFilters={filterConditions}\r\n              onServerFilter={(conditions) => {\r\n                // Only update if the conditions have actually changed\r\n                const currentStr = JSON.stringify(filterConditions);\r\n                const newStr = JSON.stringify(conditions);\r\n\r\n                if (currentStr !== newStr) {\r\n                  setFilterConditions(conditions)\r\n                  setPagination(prev => ({ ...prev, pageIndex: 0 })) // Reset to first page on filter change\r\n                }\r\n              }}\r\n            />\r\n          )}\r\n          hideDefaultFilterbar={true}\r\n          onRefresh={handleRefresh}\r\n          enableRowSelection={false}\r\n          actionButton={{\r\n            // label: \"Create New User\",\r\n            onClick: () => { /* Required but not used */ },\r\n            content: <AddClient />\r\n          }}\r\n        />\r\n      </div>\r\n\r\n      {userActionDialog && userActionDialog.dialogType === 'edit' && (\r\n        <EditClient\r\n          dataEdit={userActionDialog.dataEdit}\r\n          dataId={userActionDialog.dataId}\r\n          onDismiss={() => {\r\n            void queryClient.invalidateQueries({ queryKey: [QueryNames.GetOpeniddictApplications] })\r\n            setUserActionDialog(null)\r\n          }}\r\n        />\r\n      )}\r\n      {userActionDialog && userActionDialog.dialogType === 'permission' && (\r\n        <EditClient\r\n          dataEdit={userActionDialog.dataEdit}\r\n          dataId={userActionDialog.dataId}\r\n          onDismiss={() => setUserActionDialog(null)}\r\n        />\r\n      )}\r\n      {userActionDialog && userActionDialog.dialogType === 'delete' && (\r\n        <DeleteClient\r\n          dataEdit={userActionDialog.dataEdit}\r\n          dataId={userActionDialog.dataId}\r\n          onDismiss={() => {\r\n            void queryClient.invalidateQueries({ queryKey: [QueryNames.GetOpeniddictApplications] })\r\n            setUserActionDialog(null)\r\n          }}\r\n        />\r\n      )}\r\n    </>\r\n  )\r\n}\r\n", "'use client';\r\n\r\nimport { ClientList } from '@/components/app/clients/ClientList';\r\nimport AppLayout from '@/layouts/app-layout';\r\nimport { Head } from '@inertiajs/react';\r\nexport default function OverViewLayout() {\r\n  return (\r\n    <AppLayout>\r\n      <Head title=\"Dashboard\" />\r\n\r\n      <ClientList />\r\n    </AppLayout>\r\n  );\r\n}\r\n"], "names": ["__iconNode", "Computer", "createLucideIcon", "Globe", "LockOpen", "ShieldCheck", "ShieldQuestion", "Smartphone", "Tv", "UserCheck", "DeleteClient", "dataId", "dataEdit", "on<PERSON><PERSON><PERSON>", "toast", "useToast", "open", "<PERSON><PERSON><PERSON>", "useState", "onYesEvent", "deleteApiOpeniddictApplicationsById", "err", "useEffect", "jsx", "AlertDialog", "jsxs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Alert<PERSON><PERSON><PERSON>H<PERSON>er", "AlertDialogTitle", "AlertDialogDescription", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AlertDialogCancel", "AlertDialogAction", "useOpeniddictApplicationsPermissions", "useQuery", "QueryNames", "getApiOpeniddictApplicationsPermissions", "useOpeniddictRequirements", "getApiOpeniddictRequirements", "APPLICATION_TYPES", "Cog", "CLIENT_TYPES", "Unlock", "Lock", "CONSENT_TYPES", "formatPermissionLabel", "permission", "parts", "type", "name", "word", "generateClientId", "adjectives", "nouns", "adjective", "noun", "uniqueNumber", "generateClientSecret", "chars", "secret", "i", "convertCommaSeparatedToArray", "input", "item", "EditClient", "queryClient", "useQueryClient", "handleSubmit", "register", "setValue", "reset", "useForm", "selectedPermissions", "setSelectedPermissions", "selectedRequirements", "setSelectedRequirements", "permissionsData", "permissionsLoading", "requirementsData", "requirementsLoading", "resetForm", "permissionOptions", "requirementOptions", "requirement", "createUserMutation", "useMutation", "dataMutation", "putApiOpeniddictApplicationsById", "error", "handleApiError", "onSubmit", "formData", "handleOpenChange", "newOpen", "Toaster", "Dialog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "DialogTitle", "FormSection", "FormField", "Select", "value", "SelectTrigger", "SelectValue", "SelectContent", "SelectItemExtended", "Input", "<PERSON><PERSON>", "RiRefreshLine", "MultiSelect", "values", "<PERSON><PERSON><PERSON><PERSON>er", "e", "AddClient", "children", "can", "useGrantedPolicies", "postApiOpeniddictApplications", "DialogTrigger", "RiAddLine", "ClientActions", "onAction", "variant", "DropdownMenu", "DropdownMenuTrigger", "RiMoreLine", "DropdownMenuContent", "DropdownMenuItem", "RiShieldKeyholeLine", "RiPencilLine", "getUserColumns", "handleUserAction", "column", "DataTableColumnHeader", "customFilterFunction", "info", "resources", "resource", "Badge", "useOpeniddictApplications", "pageIndex", "pageSize", "filterConditions", "sorting", "body", "generateExtendedQueryParameters", "postApiOpeniddictApplicationsList", "title", "description", "extractApiError", "ClientList", "searchStr", "setSearchStr", "setFilterConditions", "userActionDialog", "setUserActionDialog", "pagination", "setPagination", "isLoading", "data", "columns", "dialogType", "handleSearch", "newFilterConditions", "fc", "currentFiltersStr", "newFiltersStr", "prev", "handlePaginationChange", "newPagination", "handleRefresh", "TableSkeleton", "items", "totalCount", "Fragment", "DataTable", "props", "NotionFilter", "conditions", "currentStr", "newStr", "OverViewLayout", "AppLayout", "Head"], "mappings": "iwCAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,MAAO,KAAM,OAAQ,IAAK,EAAG,IAAK,EAAG,IAAK,GAAI,IAAK,IAAK,QAAQ,CAAE,EAC7E,CAAC,OAAQ,CAAE,MAAO,KAAM,OAAQ,IAAK,EAAG,IAAK,EAAG,KAAM,GAAI,IAAK,IAAK,QAAQ,CAAE,EAC9E,CAAC,OAAQ,CAAE,EAAG,UAAW,IAAK,QAAQ,CAAE,EACxC,CAAC,OAAQ,CAAE,EAAG,WAAY,IAAK,QAAU,CAAA,CAC3C,EACMC,GAAWC,EAAiB,WAAYF,EAAU,ECfxD;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,SAAU,CAAE,GAAI,KAAM,GAAI,KAAM,EAAG,KAAM,IAAK,SAAU,EACzD,CAAC,OAAQ,CAAE,EAAG,kDAAmD,IAAK,QAAQ,CAAE,EAChF,CAAC,OAAQ,CAAE,EAAG,WAAY,IAAK,QAAU,CAAA,CAC3C,EACMG,GAAQD,EAAiB,QAASF,EAAU,ECdlD;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,MAAO,KAAM,OAAQ,KAAM,EAAG,IAAK,EAAG,KAAM,GAAI,IAAK,GAAI,IAAK,IAAK,SAAU,EACxF,CAAC,OAAQ,CAAE,EAAG,0BAA2B,IAAK,QAAU,CAAA,CAC1D,EACMI,GAAWF,EAAiB,YAAaF,EAAU,ECbzD;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CACE,OACA,CACE,EAAG,qKACH,IAAK,QACX,CACG,EACD,CAAC,OAAQ,CAAE,EAAG,gBAAiB,IAAK,QAAU,CAAA,CAChD,EACMK,GAAcH,EAAiB,eAAgBF,EAAU,ECnB/D;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CACE,OACA,CACE,EAAG,qKACH,IAAK,QACX,CACG,EACD,CAAC,OAAQ,CAAE,EAAG,sCAAuC,IAAK,QAAQ,CAAE,EACpE,CAAC,OAAQ,CAAE,EAAG,aAAc,IAAK,QAAU,CAAA,CAC7C,EACMM,GAAiBJ,EAAiB,kBAAmBF,EAAU,ECpBrE;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,MAAO,KAAM,OAAQ,KAAM,EAAG,IAAK,EAAG,IAAK,GAAI,IAAK,GAAI,IAAK,IAAK,SAAU,EACvF,CAAC,OAAQ,CAAE,EAAG,aAAc,IAAK,QAAU,CAAA,CAC7C,EACMO,GAAaL,EAAiB,aAAcF,EAAU,ECb5D;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,gBAAiB,IAAK,QAAQ,CAAE,EAC9C,CAAC,OAAQ,CAAE,MAAO,KAAM,OAAQ,KAAM,EAAG,IAAK,EAAG,IAAK,GAAI,IAAK,IAAK,QAAU,CAAA,CAChF,EACMQ,GAAKN,EAAiB,KAAMF,EAAU,ECb5C;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,iBAAkB,IAAK,QAAQ,CAAE,EAC/C,CAAC,OAAQ,CAAE,EAAG,4CAA6C,IAAK,QAAQ,CAAE,EAC1E,CAAC,SAAU,CAAE,GAAI,IAAK,GAAI,IAAK,EAAG,IAAK,IAAK,OAAS,CAAA,CACvD,EACMS,GAAYP,EAAiB,aAAcF,EAAU,ECK9CU,GAAe,CAAC,CAAE,OAAAC,EAAQ,SAAAC,EAAU,UAAAC,KAAiC,CAC1E,KAAA,CAAE,MAAAC,CAAM,EAAIC,GAAS,EACrB,CAACC,EAAMC,CAAO,EAAIC,EAAAA,SAAkB,EAAK,EACzCC,EAAa,SAAY,CACzB,GAAA,CACF,MAAMC,GAAoC,CACxC,KAAM,CAAE,GAAIT,CAAO,CAAA,CACpB,EACKG,EAAA,CACJ,MAAO,UACP,YAAa,WAAWF,EAAS,WAAW,kCAAA,CAC7C,EACSC,EAAA,QACHQ,EAAc,CACjBA,aAAe,OACXP,EAAA,CACJ,MAAO,SACP,YAAa,iDAAiDF,EAAS,WAAW,uBAClF,QAAS,aAAA,CACV,CACH,CAEJ,EAEAU,OAAAA,EAAAA,UAAU,IAAM,CACdL,EAAQ,EAAI,CACd,EAAG,EAAE,EAGFM,EAAA,IAAAC,GAAA,CAAY,KAAAR,EACX,SAAAS,OAACC,GACC,CAAA,SAAA,CAAAD,OAACE,GACC,CAAA,SAAA,CAAAJ,EAAAA,IAACK,IAAiB,SAAwB,0BAAA,CAAA,SACzCC,GAAuB,CAAA,SAAA,CAAA,gFAErBjB,EAAS,YAAY,GAAA,CACxB,CAAA,CAAA,EACF,SACCkB,GACC,CAAA,SAAA,CAACP,EAAA,IAAAQ,GAAA,CAAkB,QAASlB,EAAW,SAAM,SAAA,EAC5CU,EAAA,IAAAS,GAAA,CAAkB,QAASb,EAAY,SAAG,KAAA,CAAA,CAAA,CAC7C,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,EC5Dac,GAAuC,IAC3CC,EAAS,CACd,SAAU,CAACC,EAAW,oCAAoC,EAC1D,QAAS,UACU,MAAMC,GAAwC,GAC/C,MAAM,IACxB,CACD,EAGUC,GAA4B,IAChCH,EAAS,CACd,SAAU,CAACC,EAAW,yBAAyB,EAC/C,QAAS,UACU,MAAMG,GAA6B,GACpC,MAAM,IACxB,CACD,ECnBUC,GAAoB,CAC/B,CACE,MAAO,MACP,OAAQ,kBACR,YAAa,qFACb,KAAMpC,EACR,EACA,CACE,MAAO,MACP,OAAQ,0BACR,YAAa,gFACb,KAAMF,EACR,EACA,CACE,MAAO,SACP,OAAQ,qBACR,YAAa,6DACb,KAAMM,EACR,EACA,CACE,MAAO,SACP,OAAQ,qBACR,YAAa,yDACb,KAAMC,EACR,EACA,CACE,MAAO,UACP,OAAQ,qBACR,YAAa,mFACb,KAAMgC,EAAA,CAEV,EC/BaC,GAAe,CAC1B,CACE,MAAO,SACP,OAAQ,SACR,YAAa,wEACb,KAAMC,EACR,EACA,CACE,MAAO,eACP,OAAQ,eACR,YAAa,0EACb,KAAMC,EAAA,CAEV,ECbaC,GAAgB,CAC3B,CACE,MAAO,WACP,OAAQ,WACR,YAAa,8DACb,KAAMvC,EACR,EACA,CACE,MAAO,WACP,OAAQ,WACR,YAAa,yDACb,KAAMI,EACR,EACA,CACE,MAAO,SACP,OAAQ,SACR,YAAa,yDACb,KAAMH,EAAA,CAEV,ECpBO,SAASuC,GAAsBC,EAA4B,CAC5D,GAAA,CAACA,EAAmB,MAAA,GAGlB,MAAAC,EAAQD,EAAW,MAAM,GAAG,EAC9B,GAAAC,EAAM,SAAW,EAAU,OAAAD,EAEzB,KAAA,CAACE,EAAMC,CAAI,EAAIF,EACrB,GAAI,CAACE,GAAQ,CAACD,EAAa,OAAAF,EAG3B,OAAQE,EAAM,CACZ,IAAK,MACI,MAAA,GAAGC,EAAK,OAAO,CAAC,EAAE,aAAa,GAAGA,EAAK,MAAM,CAAC,CAAC,YACxD,IAAK,KACI,MAAA,GAAGA,EAAK,MAAM,GAAG,EAAE,IAAIC,GAAQA,EAAK,OAAO,CAAC,EAAE,YAAY,EAAIA,EAAK,MAAM,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,SAC/F,IAAK,MACI,MAAA,GAAGD,EAAK,OAAO,CAAC,EAAE,aAAa,GAAGA,EAAK,MAAM,CAAC,CAAC,iBACxD,IAAK,MACI,MAAA,GAAGA,EAAK,OAAO,CAAC,EAAE,aAAa,GAAGA,EAAK,MAAM,CAAC,CAAC,SACxD,QACS,OAAAH,CAAA,CAEb,CAGO,SAASK,IAA2B,CAEzC,MAAMC,EAAa,CAAC,QAAS,SAAU,SAAU,UAAW,YAAa,WAAY,SAAU,aAC7F,UAAW,SAAU,SAAU,UAAW,UAAW,QAAS,WAAY,SAAU,SACpF,OAAQ,UAAW,UAAW,OAAQ,SAAU,QAAS,OAAQ,UAAW,SAAS,EAEjFC,EAAQ,CAAC,MAAO,MAAO,SAAU,WAAY,UAAW,SAAU,MAAO,OAAQ,OACrF,QAAS,OAAQ,SAAU,YAAa,OAAQ,YAAa,QAAS,SAAU,UAChF,SAAU,UAAW,WAAY,WAAY,OAAQ,UAAW,SAAU,WAAW,EAGjFC,EAAYF,EAAW,KAAK,MAAM,KAAK,OAAO,EAAIA,EAAW,MAAM,CAAC,EACpEG,EAAOF,EAAM,KAAK,MAAM,KAAK,OAAO,EAAIA,EAAM,MAAM,CAAC,EAGrDG,EAAe,KAAK,MAAM,KAAK,SAAW,GAAI,EAGpD,MAAO,GAAGF,CAAS,GAAGC,CAAI,GAAGC,CAAY,EAC3C,CAGO,SAASC,IAA+B,CAE7C,MAAMC,EAAQ,iEACd,IAAIC,EAAS,GACb,QAASC,EAAI,EAAGA,EAAI,GAAIA,IACZD,GAAAD,EAAM,OAAO,KAAK,MAAM,KAAK,OAAO,EAAIA,EAAM,MAAM,CAAC,EAE1D,OAAAC,CACT,CAOO,SAASE,EAA6BC,EAAuD,CAE9F,OAAA,MAAM,QAAQA,CAAK,EACdA,EAAM,OAAOC,GAAQA,GAAQA,EAAK,OAAS,CAAC,EAI9CD,EACH,OAAOA,CAAK,EACX,MAAM,GAAG,EACT,IAAIC,GAAQA,EAAK,KAAM,CAAA,EACvB,OAAOA,GAAQA,EAAK,OAAS,CAAC,EAC/B,CAAC,CACP,CCrCO,MAAMC,EAAa,CAAC,CAAE,SAAApD,EAAU,OAAAD,EAAQ,UAAAE,KAAiC,CAC9E,KAAM,CAACG,EAAMC,CAAO,EAAIC,EAAAA,SAAS,EAAI,EAC/B,CAAE,MAAAJ,CAAM,EAAIC,EAAS,EACrBkD,EAAcC,EAAe,EAC7B,CAAE,aAAAC,EAAc,SAAAC,EAAU,SAAAC,EAAU,MAAAC,CAAA,EAAUC,GAA8C,EAC5F,CAACC,EAAqBC,CAAsB,EAAIvD,WAAmBN,EAAS,aAAe,EAAE,EAC7F,CAAC8D,EAAsBC,CAAuB,EAAIzD,WAAmBN,EAAS,cAAgB,EAAE,EAEhG,CAAE,KAAMgE,EAAiB,UAAWC,CAAA,EAAuB5C,GAAqC,EAChG,CAAE,KAAM6C,EAAkB,UAAWC,CAAA,EAAwB1C,GAA0B,EAEvF2C,EAAY,IAAM,CAChBV,EAAA,CACJ,gBAAiB,GACjB,WAAY,GACZ,SAAU,GACV,aAAc,GACd,YAAa,GACb,YAAa,GACb,aAAc,CAAC,EACf,uBAAwB,CAAC,EACzB,YAAa,CAAC,EACd,aAAc,CAAA,CAAC,CAChB,CACH,EAGMW,EAAyCL,EAC1C,MAAM,QAAQA,CAAe,EAC5BA,EAAgB,IAAmB9B,IAAA,CACnC,MAAOA,EACP,MAAOD,GAAsBC,CAAU,CAAA,EACvC,EACA,CAAA,EACF,CAAC,EAECoC,EAA0CJ,EAC3C,MAAM,QAAQA,CAAgB,EAC5BA,EAA8C,IAAoBK,IAAA,CACnE,MAAOA,EAAY,MACnB,MAAOA,EAAY,KAAA,EACnB,EACA,CAAA,EACF,CAAC,EAECC,EAAqBC,EAAY,CACrC,WAAY,MAAOC,GACjBC,GAAiC,CAC/B,KAAM,CAAE,GAAI5E,CAAO,EACnB,KAAM2E,CAAA,CACP,EACH,UAAW,IAAM,CACTxE,EAAA,CACJ,MAAO,UACP,YAAa,8BACb,QAAS,SAAA,CACV,EACImD,EAAY,kBAAkB,CAAE,SAAU,CAAC9B,EAAW,yBAAyB,EAAG,EAC7E6C,EAAA,EACV/D,EAAQ,EAAK,EACHJ,EAAA,CACZ,EACA,QAAUQ,GAAiB,CACnB,MAAAmE,EAAQC,EAAepE,CAAG,EAC1BP,EAAA,CACJ,MAAO0E,EAAM,MACb,YAAaA,EAAM,YACnB,QAAS,OAAA,CACV,CAAA,CACH,CACD,EAEKE,EAAYC,GAAmD,CAEnE,MAAML,EAAqD,CACzD,GAAGK,EACH,gBAAiBA,EAAS,iBAAmB/E,EAAS,iBAAmB,GACzE,WAAY+E,EAAS,YAAc/E,EAAS,YAAc,GAC1D,YAAa4D,EACb,aAAcX,EAA6B8B,EAAS,YAAY,EAChE,uBAAwB9B,EAA6B8B,EAAS,sBAAsB,CAEtF,EACAP,EAAmB,OAAOE,CAAY,CACxC,EAEMM,EAAoBC,GAAqB,CACzCA,IACQb,EAAA,EACAnE,EAAA,GAEZI,EAAQ4E,CAAO,CACjB,EAEA,cACG,UACC,CAAA,SAAA,CAAAtE,EAAA,IAACuE,EAAQ,EAAA,EACTvE,EAAAA,IAACwE,EAAO,CAAA,KAAA/E,EAAY,aAAc4E,EAChC,SAACnE,EAAA,KAAAuE,GAAA,CAAc,MAAO,CAAE,SAAU,OAAA,EAChC,SAAA,CAAAzE,MAAC0E,GACC,CAAA,SAAA1E,EAAA,IAAC2E,GAAY,CAAA,SAAA,aAAW,CAAA,EAC1B,SACC,OAAK,CAAA,SAAU/B,EAAauB,CAAQ,EAAG,UAAU,OAChD,SAAA,CAAAnE,MAAC,UAAQ,CAAA,UAAU,iCACjB,SAAAE,EAAA,KAAC0E,GACC,CAAA,SAAA,CAAA5E,EAAA,IAAC6E,EAAA,CACC,MAAM,mBACN,YAAY,8BAEZ,SAAA3E,EAAA,KAAC4E,EAAA,CACC,aAAczF,EAAS,iBAAmB,GAC1C,cAAgB0F,GAAUjC,EAAS,kBAAmBiC,CAAK,EAE3D,SAAA,CAAA/E,EAAA,IAACgF,EAAA,CACC,UAAU,SACV,UAAW,GACX,QAAS,IAAMlC,EAAS,kBAAmB,EAAE,EAE7C,SAAA9C,EAAAA,IAACiF,EAAY,CAAA,YAAY,yBAA0B,CAAA,CAAA,CACrD,EACCjF,EAAA,IAAAkF,EAAA,CACE,SAAkBlE,GAAA,IAAKS,GACtBzB,EAAA,IAACmF,EAAA,CAEC,MAAO1D,EAAK,MACZ,OAAQA,EAAK,OACb,YAAaA,EAAK,YAClB,KAAMA,EAAK,KACX,UAAW,EAAA,EALNA,EAAK,KAAA,CAOb,CACH,CAAA,CAAA,CAAA,CAAA,CACF,CACF,EACAzB,EAAA,IAAC6E,EAAA,CACC,MAAM,cACN,YAAY,yBAEZ,SAAA3E,EAAA,KAAC4E,EAAA,CACC,aAAczF,EAAS,YAAc,GACrC,cAAgB0F,GAAUjC,EAAS,aAAciC,CAAK,EAEtD,SAAA,CAAA/E,EAAAA,IAACgF,GAAc,UAAU,SACvB,eAACC,EAAY,CAAA,YAAY,qBAAqB,CAChD,CAAA,EACCjF,EAAA,IAAAkF,EAAA,CACE,SAAahE,GAAA,IAAKO,GACjBzB,EAAA,IAACmF,EAAA,CAEC,MAAO1D,EAAK,MACZ,OAAQA,EAAK,OACb,YAAaA,EAAK,YAClB,KAAMA,EAAK,IAAA,EAJNA,EAAK,KAAA,CAMb,CACH,CAAA,CAAA,CAAA,CAAA,CACF,CACF,EACAzB,EAAA,IAAC6E,EAAA,CACC,MAAM,YACN,YAAY,8BAEZ,SAAA3E,EAAA,KAAC,MAAI,CAAA,UAAU,0BACb,SAAA,CAAAF,EAAAA,IAACoF,EAAM,CAAA,SAAQ,GAAE,GAAGvC,EAAS,UAAU,EAAG,aAAcxD,EAAS,UAAY,GAAI,YAAY,WAAY,CAAA,EACzGW,EAAA,IAACqF,EAAA,CACC,KAAK,SACL,QAAQ,YACR,QAASzD,GACT,MAAM,qBAEN,SAAA5B,EAAAA,IAACsF,EAAc,CAAA,UAAU,QAAS,CAAA,CAAA,CAAA,CACpC,CACF,CAAA,CAAA,CACF,EAmBAtF,EAAA,IAAC6E,EAAA,CACC,MAAM,eACN,YAAY,sCAEZ,SAAA7E,EAAAA,IAACoF,EAAM,CAAA,YAAY,eAAgB,GAAGvC,EAAS,aAAa,EAAG,aAAcxD,EAAS,aAAe,EAAI,CAAA,CAAA,CAC3G,EACAW,EAAA,IAAC6E,EAAA,CACC,MAAM,eACN,YAAY,iCAEZ,SAAA3E,EAAA,KAAC4E,GAAQ,GAAGjC,EAAS,aAAa,EAAG,aAAcxD,EAAS,aAAe,GACzE,SAAA,CAAAW,EAAAA,IAACgF,GAAc,UAAU,SACvB,eAACC,EAAY,CAAA,YAAY,sBAAsB,CACjD,CAAA,EACCjF,EAAA,IAAAkF,EAAA,CACE,SAAc7D,GAAA,IAAKI,GAClBzB,EAAA,IAACmF,EAAA,CAEC,MAAO1D,EAAK,MACZ,OAAQA,EAAK,OACb,YAAaA,EAAK,YAClB,KAAMA,EAAK,IAAA,EAJNA,EAAK,KAAA,CAMb,CACH,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EAEAzB,EAAA,IAAC6E,EAAA,CACC,MAAM,aACN,YAAY,+BAEZ,SAAA7E,EAAAA,IAACoF,EAAM,CAAA,YAAY,aAAc,GAAGvC,EAAS,WAAW,EAAG,aAAcxD,EAAS,WAAa,EAAI,CAAA,CAAA,CACrG,EAEAW,EAAA,IAAC6E,EAAA,CACC,MAAM,gBACN,YAAY,kCAEZ,SAAA7E,EAAAA,IAACoF,EAAM,CAAA,YAAY,gBAAiB,GAAGvC,EAAS,cAAc,EAAG,aAAcxD,EAAS,cAAgB,EAAI,CAAA,CAAA,CAC9G,EACAW,EAAA,IAAC6E,EAAA,CACC,MAAM,4BACN,YAAY,8CAEZ,SAAA7E,EAAAA,IAACoF,EAAM,CAAA,YAAY,4BAA6B,GAAGvC,EAAS,wBAAwB,EAAG,aAAcxD,EAAS,wBAA0B,EAAI,CAAA,CAAA,CAC9I,EAEAW,EAAA,IAAC6E,EAAA,CACC,MAAM,cACN,YAAY,gCAEZ,SAAA7E,EAAA,IAACuF,EAAA,CACC,QAAS7B,EACT,MAAOT,EACP,SAAWuC,GAAW,CACpBtC,EAAuBsC,CAAM,EAC7B1C,EAAS,cAAe0C,CAAM,CAChC,EACA,YAAalC,EAAqB,yBAA2B,qBAC7D,SAAUA,EACV,UAAW,GAAA,CAAA,CACb,CACF,EAEAtD,EAAA,IAAC6E,EAAA,CACC,MAAM,eACN,YAAY,iCAEZ,SAAA7E,EAAA,IAACuF,EAAA,CACC,KAAK,SACL,QAAS5B,EACT,MAAOR,EACP,SAAWqC,GAAW,CACpBpC,EAAwBoC,CAAM,EAC9B1C,EAAS,eAAgB0C,CAAM,CACjC,EACA,YAAahC,EAAsB,0BAA4B,sBAC/D,SAAUA,EACV,UAAW,GAAA,CAAA,CACb,CAAA,CACF,CAAA,CACF,CACF,CAAA,EACAtD,EAAAA,KAACuF,GAAa,CAAA,UAAU,OACtB,SAAA,CAAAzF,EAAA,IAACqF,EAAA,CACC,QAAQ,QACR,QAAUK,GAAM,CACdA,EAAE,eAAe,EACjBhG,EAAQ,EAAK,CACf,EACA,SAAUmE,EAAmB,UAC9B,SAAA,QAAA,CAED,EACA7D,EAAAA,IAACqF,EAAO,CAAA,KAAK,SAAS,SAAUxB,EAAmB,UAChD,SAAAA,EAAmB,UAAY,YAAc,MAChD,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,EACF,CAEJ,ECzSa8B,GAAY,CAAC,CAAE,SAAAC,KAA+B,CACnD,KAAA,CAAE,IAAAC,CAAI,EAAIC,EAAmB,EAC7B,CAACrG,EAAMC,CAAO,EAAIC,EAAAA,SAAS,EAAK,EAChC,CAAE,MAAAJ,CAAM,EAAIC,EAAS,EACrBkD,EAAcC,EAAe,EAC7B,CAAE,aAAAC,EAAc,SAAAC,EAAU,SAAAC,EAAU,MAAAC,CAAA,EAAUC,GAA8C,EAC5F,CAACC,EAAqBC,CAAsB,EAAIvD,EAAAA,SAAmB,CAAA,CAAE,EACrE,CAACwD,EAAsBC,CAAuB,EAAIzD,EAAAA,SAAmB,CAAA,CAAE,EAEvE,CAAE,KAAM0D,EAAiB,UAAWC,CAAA,EAAuB5C,GAAqC,EAChG,CAAE,KAAM6C,EAAkB,UAAWC,CAAA,EAAwB1C,GAA0B,EAEvF2C,EAAY,IAAM,CAChBV,EAAA,CACJ,gBAAiB,GACjB,WAAY,GACZ,SAAU,GACV,aAAc,GACd,YAAa,GACb,YAAa,GACb,aAAc,CAAC,EACf,uBAAwB,CAAC,EACzB,YAAa,CAAC,EACd,aAAc,CAAA,CAAC,CAChB,CACH,EAGMW,EAAyCL,EAC1C,MAAM,QAAQA,CAAe,EAC5BA,EAAgB,IAAmB9B,IAAA,CACnC,MAAOA,EACP,MAAOD,GAAsBC,CAAU,CAAA,EACvC,EACA,CAAA,EACF,CAAC,EAECoC,EAA0CJ,EAC3C,MAAM,QAAQA,CAAgB,EAC5BA,EAA8C,IAAoBK,IAAA,CACnE,MAAOA,EAAY,MACnB,MAAOA,EAAY,KAAA,EACnB,EACA,CAAA,EACF,CAAC,EAECC,EAAqBC,EAAY,CACrC,WAAY,MAAOC,GACjBgC,GAA8B,CAC5B,KAAMhC,CAAA,CACP,EACH,UAAW,IAAM,CACTxE,EAAA,CACJ,MAAO,UACP,YAAa,8BACb,QAAS,SAAA,CACV,EACImD,EAAY,kBAAkB,CAAE,SAAU,CAAC9B,EAAW,yBAAyB,EAAG,EACvFlB,EAAQ,EAAK,EACH+D,EAAA,CACZ,EACA,QAAU3D,GAAiB,CACnB,MAAAmE,EAAQC,EAAepE,CAAG,EAC1BP,EAAA,CACJ,MAAO0E,EAAM,MACb,YAAaA,EAAM,YACnB,QAAS,OAAA,CACV,CAAA,CACH,CACD,EAEKE,EAAYC,GAAmD,CAGnE,MAAML,EAAqD,CACzD,GAAGK,EACH,aAAc9B,EAA6B8B,EAAS,YAAY,EAChE,uBAAwB9B,EAA6B8B,EAAS,sBAAsB,EACpF,YAAanB,CAEf,EAEAY,EAAmB,OAAOE,CAAY,CACxC,EAEMM,EAAoBC,GAAqB,CACzCA,GACQb,EAAA,EAEZ/D,EAAQ4E,CAAO,CACjB,EAEA,cACG,UACC,CAAA,SAAA,CAAAtE,EAAA,IAACuE,EAAQ,EAAA,EACRrE,EAAA,KAAAsE,EAAA,CAAO,KAAA/E,EAAY,aAAc4E,EAChC,SAAA,CAACrE,EAAAA,IAAAgG,GAAA,CAAc,QAAO,GAAE,SAAAJ,CAAS,CAAA,QAChC,UAAQ,CAAA,UAAU,yCAChB,SAAAC,EAAI,0BAA0B,GAC7B3F,OAACmF,EAAO,CAAA,KAAK,KAAK,UAAU,kCAAkC,QAAS,IAAMhB,EAAiB,EAAI,EAChG,SAAA,CAAArE,EAAA,IAACiG,GAAU,CAAA,UAAU,wBAAwB,cAAY,OAAO,EAC/DjG,EAAA,IAAA,OAAA,CAAK,UAAU,4BAA4B,SAAiB,mBAAA,CAAA,CAAA,CAAA,CAC/D,CAEJ,CAAA,SACCyE,GAAc,CAAA,MAAO,CAAE,SAAU,OAChC,EAAA,SAAA,CAAAzE,MAAC0E,GACC,CAAA,SAAA1E,EAAA,IAAC2E,GAAY,CAAA,SAAA,qBAAmB,CAAA,EAClC,SACC,OAAK,CAAA,SAAU/B,EAAauB,CAAQ,EAAG,UAAU,OAChD,SAAA,CAAAnE,MAAC,UAAQ,CAAA,UAAU,iCACjB,SAAAE,EAAA,KAAC0E,GACC,CAAA,SAAA,CAAA5E,EAAA,IAAC6E,EAAA,CACC,MAAM,mBACN,YAAY,8BAEZ,SAAC3E,EAAAA,KAAA4E,EAAA,CAAQ,GAAGjC,EAAS,iBAAiB,EAAG,cAAgBL,GAASM,EAAS,kBAAmBN,CAAI,EAChG,SAAA,CAAAxC,EAAA,IAACgF,EAAA,CACC,UAAU,SACV,UAAW,GACX,QAAS,IAAMlC,EAAS,kBAAmB,EAAE,EAE7C,SAAA9C,EAAAA,IAACiF,EAAY,CAAA,YAAY,yBAA0B,CAAA,CAAA,CACrD,EACCjF,EAAA,IAAAkF,EAAA,CACE,SAAkBlE,GAAA,IAAKS,GACtBzB,EAAA,IAACmF,EAAA,CAEC,MAAO1D,EAAK,MACZ,OAAQA,EAAK,OACb,YAAaA,EAAK,YAClB,KAAMA,EAAK,KACX,UAAW,EAAA,EALNA,EAAK,KAAA,CAOb,CACH,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EACAzB,EAAA,IAAC6E,EAAA,CACC,MAAM,cACN,YAAY,yBAEZ,SAAC3E,EAAAA,KAAA4E,EAAA,CAAQ,GAAGjC,EAAS,YAAY,EAAG,cAAgBL,GAASM,EAAS,aAAcN,CAAI,EACtF,SAAA,CAAAxC,EAAAA,IAACgF,GAAc,UAAU,SACvB,eAACC,EAAY,CAAA,YAAY,qBAAqB,CAChD,CAAA,EACCjF,EAAA,IAAAkF,EAAA,CACE,SAAahE,GAAA,IAAKO,GACjBzB,EAAA,IAACmF,EAAA,CAEC,MAAO1D,EAAK,MACZ,OAAQA,EAAK,OACb,YAAaA,EAAK,YAClB,KAAMA,EAAK,IAAA,EAJNA,EAAK,KAAA,CAMb,CACH,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EACAzB,EAAA,IAAC6E,EAAA,CACC,MAAM,YACN,YAAY,8BAEZ,SAAA3E,EAAA,KAAC,MAAI,CAAA,UAAU,0BACb,SAAA,CAACF,MAAAoF,EAAA,CAAM,SAAQ,GAAE,GAAGvC,EAAS,UAAU,EAAG,YAAY,YAAY,EAClE7C,EAAA,IAACqF,EAAA,CACC,KAAK,SACL,KAAM,KACN,QAAQ,YACR,QAAS,IAAMvC,EAAS,WAAYlB,IAAkB,EACtD,MAAM,qBAEN,SAAA5B,EAAAA,IAACsF,EAAc,CAAA,UAAU,QAAS,CAAA,CAAA,CAAA,CACpC,CACF,CAAA,CAAA,CACF,EAEAtF,EAAA,IAAC6E,EAAA,CACC,MAAM,gBACN,YAAY,kCAEZ,SAAA3E,EAAA,KAAC,MAAI,CAAA,UAAU,0BACb,SAAA,CAACF,MAAAoF,EAAA,CAAM,SAAQ,GAAE,GAAGvC,EAAS,cAAc,EAAG,YAAY,gBAAgB,EAC1E7C,EAAA,IAACqF,EAAA,CACC,KAAK,SACL,QAAQ,YACR,KAAM,KACN,QAAS,IAAMvC,EAAS,eAAgBZ,IAAsB,EAC9D,MAAM,yBAEN,SAAAlC,EAAAA,IAACsF,EAAc,CAAA,UAAU,QAAS,CAAA,CAAA,CAAA,CACpC,CACF,CAAA,CAAA,CACF,EAEAtF,EAAA,IAAC6E,EAAA,CACC,MAAM,eACN,YAAY,sCAEZ,eAACO,EAAM,CAAA,YAAY,eAAgB,GAAGvC,EAAS,aAAa,CAAG,CAAA,CAAA,CACjE,EACA7C,EAAA,IAAC6E,EAAA,CACC,MAAM,eACN,YAAY,iCAEZ,SAAC3E,EAAAA,KAAA4E,EAAA,CAAQ,GAAGjC,EAAS,aAAa,EAAG,cAAgBL,GAASM,EAAS,cAAeN,CAAI,EACxF,SAAA,CAAAxC,EAAAA,IAACgF,GAAc,UAAU,SACvB,eAACC,EAAY,CAAA,YAAY,sBAAsB,CACjD,CAAA,EACCjF,EAAA,IAAAkF,EAAA,CACE,SAAc7D,GAAA,IAAKI,GAClBzB,EAAA,IAACmF,EAAA,CAEC,MAAO1D,EAAK,MACZ,OAAQA,EAAK,OACb,YAAaA,EAAK,YAClB,KAAMA,EAAK,IAAA,EAJNA,EAAK,KAAA,CAMb,CACH,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EAEAzB,EAAA,IAAC6E,EAAA,CACC,MAAM,aACN,YAAY,+BAEZ,eAACO,EAAM,CAAA,YAAY,aAAc,GAAGvC,EAAS,WAAW,CAAG,CAAA,CAAA,CAC7D,EAEA7C,EAAA,IAAC6E,EAAA,CACC,MAAM,gBACN,YAAY,kCAEZ,eAACO,EAAM,CAAA,YAAY,gBAAiB,GAAGvC,EAAS,cAAc,CAAG,CAAA,CAAA,CACnE,EACA7C,EAAA,IAAC6E,EAAA,CACC,MAAM,4BACN,YAAY,8CAEZ,eAACO,EAAM,CAAA,YAAY,4BAA6B,GAAGvC,EAAS,wBAAwB,CAAG,CAAA,CAAA,CACzF,EAEA7C,EAAA,IAAC6E,EAAA,CACC,MAAM,cACN,YAAY,gCAEZ,SAAA7E,EAAA,IAACuF,EAAA,CACC,QAAS7B,EACT,MAAOT,EACP,SAAWuC,GAAW,CACpBtC,EAAuBsC,CAAM,EAC7B1C,EAAS,cAAe0C,CAAM,CAChC,EACA,YAAalC,EAAqB,yBAA2B,qBAC7D,SAAUA,EACV,UAAW,GAAA,CAAA,CACb,CACF,EAEAtD,EAAA,IAAC6E,EAAA,CACC,MAAM,eACN,YAAY,iCAEZ,SAAA7E,EAAA,IAACuF,EAAA,CACC,KAAK,SACL,QAAS5B,EACT,MAAOR,EACP,SAAWqC,GAAW,CACpBpC,EAAwBoC,CAAM,EAC9B1C,EAAS,eAAgB0C,CAAM,CACjC,EACA,YAAahC,EAAsB,0BAA4B,sBAC/D,SAAUA,EACV,UAAW,GAAA,CAAA,CACb,CAAA,CACF,CAAA,CACF,CACF,CAAA,EACAtD,EAAAA,KAACuF,GAAa,CAAA,UAAU,OACtB,SAAA,CAAAzF,EAAA,IAACqF,EAAA,CACC,QAAQ,QACR,QAAUK,GAAM,CACdA,EAAE,eAAe,EACjBhG,EAAQ,EAAK,CACf,EACA,SAAUmE,EAAmB,UAC9B,SAAA,QAAA,CAED,EACA7D,EAAAA,IAACqF,EAAO,CAAA,KAAK,SAAS,SAAUxB,EAAmB,UAChD,SAAAA,EAAmB,UAAY,YAAc,MAChD,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,CAEJ,EC9TaqC,GAAgB,CAAC,CAAE,OAAA9G,EAAQ,SAAAC,EAAU,SAAA8G,EAAU,QAAAC,EAAU,cAAkC,CAChG,KAAA,CAAE,IAAAP,CAAI,EAAIC,EAAmB,EAGnC,OAAIM,IAAY,WAEXpG,EAAA,IAAA,MAAA,CAAI,UAAU,mBACb,gBAACqG,GACC,CAAA,SAAA,CAACrG,EAAAA,IAAAsG,GAAA,CAAoB,QAAO,GAC1B,SAAApG,EAAA,KAACmF,EAAA,CACC,QAAQ,QACR,KAAK,KACL,UAAU,cAEV,SAAA,CAACrF,EAAAA,IAAAuG,GAAA,CAAW,UAAU,SAAU,CAAA,EAC/BvG,EAAA,IAAA,OAAA,CAAK,UAAU,UAAU,SAAS,WAAA,CAAA,CAAA,CAAA,CAAA,EAEvC,EACCE,EAAA,KAAAsG,GAAA,CAAoB,MAAM,MAAM,UAAU,YACxC,SAAA,CAAAX,EAAI,4CAA4C,GAC/C7F,EAAA,IAACyG,EAAA,CACC,UAAU,yBACV,QAAS,IAAMN,EAAS/G,EAAQC,EAAU,MAAM,EACjD,SAAA,MAAA,CAED,EAEDwG,EAAI,8CAA8C,GACjD7F,EAAA,IAACyG,EAAA,CACC,UAAU,sCACV,QAAS,IAAMN,EAAS/G,EAAQC,EAAU,QAAQ,EACnD,SAAA,QAAA,CAAA,CAED,CAEJ,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAMFa,EAAA,KAAC,MAAI,CAAA,UAAU,sCACZ,SAAA,CAAA2F,EAAI,qCAAqC,GACxC3F,EAAA,KAACmF,EAAA,CACC,QAAQ,UACR,KAAK,KACL,UAAU,oCACV,QAAS,IAAMc,EAAS/G,EAAQC,EAAU,YAAY,EAEtD,SAAA,CAACW,EAAAA,IAAA0G,GAAA,CAAoB,UAAU,SAAU,CAAA,EACzC1G,EAAAA,IAAC,QAAK,SAAU,YAAA,CAAA,CAAA,CAAA,CAClB,EAED6F,EAAI,0BAA0B,GAC7B3F,EAAA,KAACmF,EAAA,CACC,QAAQ,UACR,KAAK,KACL,UAAU,oCACV,QAAS,IAAMc,EAAS/G,EAAQC,EAAU,MAAM,EAEhD,SAAA,CAACW,EAAAA,IAAA2G,GAAA,CAAa,UAAU,SAAU,CAAA,EAClC3G,EAAAA,IAAC,QAAK,SAAI,MAAA,CAAA,CAAA,CAAA,CAAA,CACZ,EAEJ,CAEJ,EC3Ea4G,GACXC,GAC0C,CACxC,CACE,YAAa,WACb,OAAQ,CAAC,CAAE,OAAAC,CAAA,IACR9G,EAAA,IAAA+G,EAAA,CAAsB,OAAAD,EAAgB,MAAM,YAAY,EAE3D,cAAe,GACf,SAAUE,EACV,aAAc,GACd,KAAOC,GAASA,EAAK,SAAS,EAC9B,KAAM,CACJ,UAAW,YACX,YAAa,WAAA,CAEjB,EACA,CACE,YAAa,cACb,OAAQ,CAAC,CAAE,OAAAH,CAAA,IACR9G,EAAA,IAAA+G,EAAA,CAAsB,OAAAD,EAAgB,MAAM,eAAe,EAE9D,cAAe,GACf,SAAUE,EACV,aAAc,GACd,KAAOC,GAASA,EAAK,SAAS,EAC9B,KAAM,CACJ,UAAW,YACX,YAAa,cAAA,CAEjB,EACA,CACE,YAAa,kBACb,OAAQ,CAAC,CAAE,OAAAH,CAAA,IACR9G,EAAA,IAAA+G,EAAA,CAAsB,OAAAD,EAAgB,MAAM,mBAAmB,EAElE,cAAe,GACf,SAAUE,EACV,aAAc,GACd,KAAOC,GAASA,EAAK,SAAS,EAC9B,KAAM,CACJ,UAAW,YACX,YAAa,kBAAA,CAEjB,EAeA,CACE,YAAa,cACb,OAAQ,CAAC,CAAE,OAAAH,CAAA,IACR9G,EAAA,IAAA+G,EAAA,CAAsB,OAAAD,EAAgB,MAAM,eAAe,EAE9D,cAAe,GACf,SAAUE,EACV,aAAc,GACd,KAAOC,GAASA,EAAK,SAAS,EAC9B,KAAM,CACJ,UAAW,YACX,YAAa,cAAA,CAEjB,EACA,CACE,YAAa,eACb,OAAQ,CAAC,CAAE,OAAAH,CAAA,IACR9G,EAAA,IAAA+G,EAAA,CAAsB,OAAAD,EAAgB,MAAM,gBAAgB,EAE/D,cAAe,GACf,SAAUE,EACV,aAAc,GACd,KAAOC,GAAS,CACR,MAAAC,EAAYD,EAAK,SAAS,EAChC,OACGjH,EAAA,IAAA,MAAA,CAAI,UAAU,uBACZ,YAAW,IAAKmH,GACfnH,EAAAA,IAACoH,IAAqB,QAAQ,YAC3B,SADSD,CAAA,EAAAA,CAEZ,CACD,EACH,CAEJ,EACA,KAAM,CACJ,UAAW,YACX,YAAa,eAAA,CAEjB,EACA,CACE,GAAI,UACJ,OAAQ,CAAC,CAAE,OAAAL,CAAA,IACR9G,EAAA,IAAA+G,EAAA,CAAsB,OAAAD,EAAgB,MAAM,UAAU,EAEzD,cAAe,GACf,aAAc,GACd,KAAOG,GACLjH,EAAA,IAACkG,GAAA,CACC,OAAQe,EAAK,IAAI,SAAS,GAC1B,SAAUA,EAAK,IAAI,SACnB,SAAUJ,EACV,QAAQ,UAAA,CACV,EAEF,KAAM,CACJ,UAAW,aACX,YAAa,SAAA,CACf,CAEJ,ECxHWQ,GAA4B,CACvCC,EACAC,EACAC,EAAsC,CAAA,EACtCC,IAEO9G,EAAS,CACd,SAAU,CAACC,EAAW,0BAA2B0G,EAAWC,EAAU,KAAK,UAAUC,CAAgB,EAAGC,CAAO,EAC/G,QAAS,SAAY,CACf,GAAA,CAEF,MAAMC,EAAOC,GAAgC,CAC3C,UAAAL,EACA,SAAAC,EACA,QAAAE,EACA,iBAAAD,CAAA,CACD,EAOD,OALiB,MAAMI,GAAkC,CACvD,KAAAF,CAAA,CACD,GAGe,MAAM,WACfzD,EAAO,CAEd,KAAM,CAAE,MAAA4D,EAAO,YAAAC,CAAA,EAAgBC,GAAgB9D,EAAO,uBAAuB,EAGvE,OAAA1E,GAAA,CACJ,MAAAsI,EACA,YAAAC,EACA,QAAS,aAAA,CACV,EAGM,CAAE,MAAO,GAAI,WAAY,CAAE,CAAA,CAEtC,EACA,MAAO,EAAA,CACR,EC/BUE,GAAa,IAAM,CACxB,KAAA,CAAE,MAAAzI,CAAM,EAAIC,EAAS,EACrBkD,EAAcC,EAAe,EAE7B,CAACsF,EAAWC,CAAY,EAAIvI,EAAAA,SAAiB,EAAE,EAC/C,CAAC6H,EAAkBW,CAAmB,EAAIxI,EAAAA,SAA4B,CAAA,CAAE,EACxE,CAACyI,EAAkBC,CAAmB,EAAI1I,WAItC,EAEJ,CAAC2I,EAAYC,CAAa,EAAI5I,WAA0B,CAC5D,UAAW,EACX,SAAU,EAAA,CACX,EAEK,CAAE,UAAA6I,EAAW,KAAAC,CAAA,EAASpB,GAC1BiB,EAAW,UACXA,EAAW,SACXd,CACF,EAYMkB,EAAU9B,GATS,CAACxH,EAAgBC,EAAoCsJ,IAAiD,CACzGN,EAAA,CAClB,OAAAjJ,EACA,SAAAC,EACA,WAAAsJ,CAAA,CACD,CACH,CAG+C,EAEzCC,EAAgB7D,GAAkB,CAEtCmD,EAAanD,CAAK,EAKZ,MAAA8D,EAAsB,CAAC,GADLrB,EAAiB,OAAasB,GAAAA,EAAG,YAAc,aAAa,CACrC,EAG3C/D,GACF8D,EAAoB,KAAK,CACvB,UAAW,cACX,SAAU,WACV,MAAA9D,CAAA,CACD,EAIG,MAAAgE,EAAoB,KAAK,UAAUvB,CAAgB,EACnDwB,EAAgB,KAAK,UAAUH,CAAmB,EAEpDE,IAAsBC,IACxBb,EAAoBU,CAAmB,EACvCN,MAAuB,CAAE,GAAGU,EAAM,UAAW,GAAI,EAErD,EAEMC,EAA0BC,GAAmC,CACjEZ,EAAcY,CAAa,CAC7B,EAGMC,EAAgB,IAAM,CAErB1G,EAAY,kBAAkB,CAAE,SAAU,CAAC9B,EAAW,yBAAyB,EAAG,EAGjFrB,EAAA,CACJ,MAAO,iBACP,YAAa,sCACb,QAAS,SAAA,CACV,CACH,EAEA,GAAIiJ,EACF,OAAAxI,EAAA,IAACqJ,GAAA,CACC,SAAUf,EAAW,SACrB,YAAa,EACb,SAAU,GACV,UAAW,GACX,WAAY,GACZ,cAAe,GACf,WAAY,EAAA,CACd,EAGI,MAAAgB,EAAQb,GAAM,OAAS,CAAC,EACxBc,EAAad,GAAM,YAAc,EAEvC,OAEIvI,EAAA,KAAAsJ,WAAA,CAAA,SAAA,CAACxJ,EAAAA,IAAA,MAAA,CAAI,UAAU,YACb,SAAAA,EAAA,IAACyJ,GAAA,CACC,MAAM,qBACN,QAAAf,EACA,KAAMY,EACN,WAAAC,EACA,UAAAf,EACA,iBAAkB,GAClB,SAAUF,EAAW,SACrB,mBAAoBY,EACpB,SAAUN,EACV,YAAaX,EACb,gBAAkByB,GAChB1J,EAAA,IAAC2J,GAAA,CACE,GAAGD,EACJ,cAAelC,EACf,eAAiBoC,GAAe,CAExB,MAAAC,EAAa,KAAK,UAAUrC,CAAgB,EAC5CsC,EAAS,KAAK,UAAUF,CAAU,EAEpCC,IAAeC,IACjB3B,EAAoByB,CAAU,EAC9BrB,MAAuB,CAAE,GAAGU,EAAM,UAAW,GAAI,EACnD,CACF,CACF,EAEF,qBAAsB,GACtB,UAAWG,EACX,mBAAoB,GACpB,aAAc,CAEZ,QAAS,IAAM,CAA8B,EAC7C,cAAUzD,GAAU,CAAA,CAAA,CAAA,CACtB,CAAA,EAEJ,EAECyC,GAAoBA,EAAiB,aAAe,QACnDpI,EAAA,IAACyC,EAAA,CACC,SAAU2F,EAAiB,SAC3B,OAAQA,EAAiB,OACzB,UAAW,IAAM,CACV1F,EAAY,kBAAkB,CAAE,SAAU,CAAC9B,EAAW,yBAAyB,EAAG,EACvFyH,EAAoB,IAAI,CAAA,CAC1B,CACF,EAEDD,GAAoBA,EAAiB,aAAe,cACnDpI,EAAA,IAACyC,EAAA,CACC,SAAU2F,EAAiB,SAC3B,OAAQA,EAAiB,OACzB,UAAW,IAAMC,EAAoB,IAAI,CAAA,CAC3C,EAEDD,GAAoBA,EAAiB,aAAe,UACnDpI,EAAA,IAACb,GAAA,CACC,SAAUiJ,EAAiB,SAC3B,OAAQA,EAAiB,OACzB,UAAW,IAAM,CACV1F,EAAY,kBAAkB,CAAE,SAAU,CAAC9B,EAAW,yBAAyB,EAAG,EACvFyH,EAAoB,IAAI,CAAA,CAC1B,CAAA,CACF,EAEJ,CAEJ,ECpLA,SAAwB0B,IAAiB,CACvC,cACGC,GACC,CAAA,SAAA,CAAChK,EAAAA,IAAAiK,GAAA,CAAK,MAAM,WAAY,CAAA,QAEvBjC,GAAW,CAAA,CAAA,CAAA,EACd,CAEJ", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7]}