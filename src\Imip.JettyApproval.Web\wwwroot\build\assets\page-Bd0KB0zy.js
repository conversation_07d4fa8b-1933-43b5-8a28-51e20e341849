import{r as m,j as e}from"./vendor-CrSBzUoz.js";import{A as R,I as M,D as p,e as C,B as c,f as w,g as O,h as k,i as E,j as x}from"./app-layout-CNB1Wtrx.js";import{C as I,a as L,b as z,c as V}from"./card-BAJCNJxm.js";import{C as b}from"./checkbox-CayrCcBd.js";import{T as B,a as P,b as v,c as j,d as $,e as u}from"./table-CAbNlII1.js";import{I as H}from"./IconChevronDown-D4jBeGMo.js";import"./radix-DaY-mnHi.js";import"./App-CGHLK9xH.js";const d=Array(10).fill(null).map((o,l)=>({id:`app_${l+1}`,vesselName:`MV. GOLDEN ACE V. 00${l+1}`,arrivalDate:"2025-05-01",departureDate:"2025-05-02",itemName:"STEEL PRODUCT 10MT",requestBy:"USER1"}));function Q(){const[o,l]=m.useState(""),[r,h]=m.useState(new Set),[i,f]=m.useState(new Set(Object.keys(d[0]))),n=d.filter(s=>Object.values(s).some(a=>a.toString().toLowerCase().includes(o.toLowerCase()))),g=s=>{h(s?new Set(n.map(a=>a.id)):new Set)},N=(s,a)=>{const t=new Set(r);a?t.add(s):t.delete(s),h(t)},S=(s,a)=>{const t=new Set(i);a?t.add(s):t.delete(s),f(t)},D=s=>{},A=s=>{},T=s=>{};return e.jsx(R,{children:e.jsx("div",{className:"container mx-auto p-4",children:e.jsxs(I,{children:[e.jsx(L,{children:e.jsx(z,{className:"text-2xl font-bold",children:"Approval Requests"})}),e.jsxs(V,{children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx(M,{placeholder:"Filter lines...",value:o,onChange:s=>l(s.target.value),className:"max-w-sm"}),e.jsxs(p,{children:[e.jsx(C,{asChild:!0,children:e.jsxs(c,{variant:"outline",className:"ml-auto",children:["Columns ",e.jsx(H,{className:"ml-2 h-4 w-4"})]})}),e.jsx(w,{align:"end",children:Object.keys(d[0]).map(s=>e.jsx(O,{className:"capitalize",checked:i.has(s),onCheckedChange:a=>S(s,a===!0),children:s.replace(/([A-Z])/g," $1").trim()},s))})]})]}),e.jsx("div",{className:"rounded-md border",children:e.jsxs(B,{children:[e.jsx(P,{children:e.jsxs(v,{children:[e.jsx(j,{className:"w-[30px]",children:e.jsx(b,{checked:r.size===n.length&&n.length>0,onCheckedChange:s=>g(s===!0)})}),Object.keys(d[0]).map(s=>i.has(s)&&s!=="id"&&e.jsx(j,{className:"capitalize",children:s.replace(/([A-Z])/g," $1").trim()},s)),e.jsx(j,{className:"text-right",children:"Actions"})]})}),e.jsx($,{children:n.map(s=>e.jsxs(v,{children:[e.jsx(u,{children:e.jsx(b,{checked:r.has(s.id),onCheckedChange:a=>N(s.id,a===!0)})}),Object.entries(s).map(([a,t])=>i.has(a)&&a!=="id"&&e.jsx(u,{children:t},a)),e.jsx(u,{className:"text-right",children:e.jsxs(p,{children:[e.jsx(C,{asChild:!0,children:e.jsxs(c,{variant:"ghost",className:"h-8 w-8 p-0",children:[e.jsx("span",{className:"sr-only",children:"Open menu"}),e.jsx(k,{className:"h-4 w-4"})]})}),e.jsxs(w,{align:"end",children:[e.jsx(E,{children:"Actions"}),e.jsx(x,{onClick:()=>D(s.id),children:"Preview"}),e.jsx(x,{onClick:()=>A(s.id),children:"Approve"}),e.jsx(x,{onClick:()=>T(s.id),children:"Reject"})]})]})})]},s.id))})]})}),e.jsxs("div",{className:"flex items-center justify-between mt-4",children:[e.jsxs("div",{className:"text-sm text-gray-500",children:[r.size," of ",n.length," row(s) selected."]}),e.jsxs("div",{className:"space-x-2",children:[e.jsx(c,{variant:"outline",size:"sm",children:"Previous"}),e.jsx(c,{variant:"outline",size:"sm",children:"Next"})]})]})]})]})})})}export{Q as default};
//# sourceMappingURL=page-Bd0KB0zy.js.map
