{"version": 3, "file": "DataTableColumnHeader-DJ80pmDz.js", "sources": ["../../../../../frontend/src/lib/useToast.ts", "../../../../../frontend/src/lib/handleApiError.ts", "../../../../../frontend/src/components/data-table/DataTableColumnHeader.tsx"], "sourcesContent": ["\"use client\"\r\n\r\n// Inspired by react-hot-toast library\r\nimport * as React from \"react\"\r\n\r\nimport type {\r\n  ToastActionElement,\r\n  ToastProps,\r\n} from \"@/components/ui/toast\"\r\n\r\nconst TOAST_LIMIT = 1\r\nconst TOAST_REMOVE_DELAY = 1000000\r\n\r\ntype ToasterToast = ToastProps & {\r\n  id: string\r\n  title?: React.ReactNode\r\n  description?: React.ReactNode\r\n  action?: ToastActionElement\r\n}\r\n\r\nlet count = 0\r\n\r\nfunction genId() {\r\n  count = (count + 1) % Number.MAX_SAFE_INTEGER\r\n  return count.toString()\r\n}\r\n\r\ntype ActionType = {\r\n  ADD_TOAST: \"ADD_TOAST\",\r\n  UPDATE_TOAST: \"UPDATE_TOAST\",\r\n  DISMISS_TOAST: \"DISMISS_TOAST\",\r\n  REMOVE_TOAST: \"REMOVE_TOAST\",\r\n}\r\n\r\ntype Action =\r\n  | {\r\n    type: ActionType[\"ADD_TOAST\"]\r\n    toast: ToasterToast\r\n  }\r\n  | {\r\n    type: ActionType[\"UPDATE_TOAST\"]\r\n    toast: Partial<ToasterToast>\r\n  }\r\n  | {\r\n    type: ActionType[\"DISMISS_TOAST\"]\r\n    toastId?: ToasterToast[\"id\"]\r\n  }\r\n  | {\r\n    type: ActionType[\"REMOVE_TOAST\"]\r\n    toastId?: ToasterToast[\"id\"]\r\n  }\r\n\r\ninterface State {\r\n  toasts: ToasterToast[]\r\n}\r\n\r\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()\r\n\r\nconst addToRemoveQueue = (toastId: string) => {\r\n  if (toastTimeouts.has(toastId)) {\r\n    return\r\n  }\r\n\r\n  const timeout = setTimeout(() => {\r\n    toastTimeouts.delete(toastId)\r\n    dispatch({\r\n      type: \"REMOVE_TOAST\",\r\n      toastId: toastId,\r\n    })\r\n  }, TOAST_REMOVE_DELAY)\r\n\r\n  toastTimeouts.set(toastId, timeout)\r\n}\r\n\r\nexport const reducer = (state: State, action: Action): State => {\r\n  switch (action.type) {\r\n    case \"ADD_TOAST\":\r\n      return {\r\n        ...state,\r\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\r\n      }\r\n\r\n    case \"UPDATE_TOAST\":\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.map((t) =>\r\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\r\n        ),\r\n      }\r\n\r\n    case \"DISMISS_TOAST\": {\r\n      const { toastId } = action\r\n\r\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\r\n      // but I'll keep it here for simplicity\r\n      if (toastId) {\r\n        addToRemoveQueue(toastId)\r\n      } else {\r\n        state.toasts.forEach((toast) => {\r\n          addToRemoveQueue(toast.id)\r\n        })\r\n      }\r\n\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.map((t) =>\r\n          t.id === toastId || toastId === undefined\r\n            ? {\r\n              ...t,\r\n              open: false,\r\n            }\r\n            : t\r\n        ),\r\n      }\r\n    }\r\n    case \"REMOVE_TOAST\":\r\n      if (action.toastId === undefined) {\r\n        return {\r\n          ...state,\r\n          toasts: [],\r\n        }\r\n      }\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\r\n      }\r\n  }\r\n}\r\n\r\nconst listeners: Array<(state: State) => void> = []\r\n\r\nlet memoryState: State = { toasts: [] }\r\n\r\nfunction dispatch(action: Action) {\r\n  memoryState = reducer(memoryState, action)\r\n  listeners.forEach((listener) => {\r\n    listener(memoryState)\r\n  })\r\n}\r\n\r\ntype Toast = Omit<ToasterToast, \"id\">\r\n\r\nfunction toast({ ...props }: Toast) {\r\n  const id = genId()\r\n\r\n  const update = (props: ToasterToast) =>\r\n    dispatch({\r\n      type: \"UPDATE_TOAST\",\r\n      toast: { ...props, id },\r\n    })\r\n  const dismiss = () => dispatch({ type: \"DISMISS_TOAST\", toastId: id })\r\n\r\n  dispatch({\r\n    type: \"ADD_TOAST\",\r\n    toast: {\r\n      ...props,\r\n      id,\r\n      open: true,\r\n      onOpenChange: (open) => {\r\n        if (!open) dismiss()\r\n      },\r\n    },\r\n  })\r\n\r\n  return {\r\n    id: id,\r\n    dismiss,\r\n    update,\r\n  }\r\n}\r\n\r\nfunction useToast() {\r\n  const [state, setState] = React.useState<State>(memoryState)\r\n\r\n  React.useEffect(() => {\r\n    listeners.push(setState)\r\n    return () => {\r\n      const index = listeners.indexOf(setState)\r\n      if (index > -1) {\r\n        listeners.splice(index, 1)\r\n      }\r\n    }\r\n  }, [state])\r\n\r\n  return {\r\n    ...state,\r\n    toast,\r\n    dismiss: (toastId?: string) => dispatch({ type: \"DISMISS_TOAST\", toastId }),\r\n  }\r\n}\r\n\r\nexport { toast, useToast }\r\n", "/**\r\n * Type definition for API errors returned from the backend\r\n */\r\nexport interface ApiError {\r\n  details: {\r\n    error: {\r\n      message: string;\r\n      details: string;\r\n    };\r\n  };\r\n}\r\n\r\n/**\r\n * Type guard to check if an error matches our ApiError structure\r\n * @param error - The error to check\r\n * @returns True if the error matches the ApiError structure\r\n */\r\nexport const isApiError = (error: unknown): error is ApiError => {\r\n  if (typeof error !== 'object' || error === null) return false;\r\n\r\n  // Create a safer type assertion\r\n  const errorObj = error as Record<string, unknown>;\r\n\r\n  if (!('details' in errorObj)) return false;\r\n  if (typeof errorObj.details !== 'object' || errorObj.details === null) return false;\r\n\r\n  // Check if details has an error property\r\n  const details = errorObj.details as Record<string, unknown>;\r\n  return 'error' in details;\r\n};\r\n\r\n/**\r\n * Helper function to handle API errors and display appropriate toast messages\r\n * @param err - The error to handle\r\n * @param toast - The toast function to use for displaying messages\r\n */\r\nexport const handleApiError = (\r\n  err: unknown\r\n) => {\r\n  if (isApiError(err)) {\r\n    return {\r\n      title: err.details.error.message,\r\n      description: err.details.error.details,\r\n      variant: 'error',\r\n    };\r\n  } else {\r\n    // Fallback for unexpected error format\r\n    return {\r\n      title: 'Error',\r\n      description: 'An unexpected error occurred',\r\n      variant: 'error',\r\n    };\r\n  }\r\n};\r\n", "import { RiArrowDownSLine, RiArrowUpSLine } from \"@remixicon/react\"\r\nimport { type Column } from \"@tanstack/react-table\"\r\n\r\nimport { cx } from \"@/lib/utils\"\r\n\r\ninterface DataTableColumnHeaderProps<TData, TValue>\r\n  extends React.HTMLAttributes<HTMLDivElement> {\r\n  column: Column<TData, TValue>\r\n  title: string\r\n}\r\n\r\nexport function DataTableColumnHeader<TData, TValue>({\r\n  column,\r\n  title,\r\n  className,\r\n}: DataTableColumnHeaderProps<TData, TValue>) {\r\n  if (!column.getCanSort()) {\r\n    return <div className={cx(className)}>{title}</div>\r\n  }\r\n\r\n  return (\r\n    <div\r\n      onClick={column.getToggleSortingHandler()}\r\n      className={cx(\r\n        column.columnDef.enableSorting === true\r\n          ? \"-mx-2 inline-flex cursor-pointer select-none items-center gap-2 rounded-md px-2 py-1 hover:bg-gray-50 dark:hover:bg-gray-900\"\r\n          : \"\",\r\n      )}\r\n    >\r\n      <span>{title}</span>\r\n      {column.getCanSort() ? (\r\n        <div className=\"-space-y-2\">\r\n          <RiArrowUpSLine\r\n            className={cx(\r\n              \"size-3.5 text-gray-900 dark:text-gray-50\",\r\n              column.getIsSorted() === \"desc\" ? \"opacity-30\" : \"\",\r\n            )}\r\n            aria-hidden=\"true\"\r\n          />\r\n          <RiArrowDownSLine\r\n            className={cx(\r\n              \"size-3.5 text-gray-900 dark:text-gray-50\",\r\n              column.getIsSorted() === \"asc\" ? \"opacity-30\" : \"\",\r\n            )}\r\n            aria-hidden=\"true\"\r\n          />\r\n        </div>\r\n      ) : null}\r\n    </div>\r\n  )\r\n}\r\n"], "names": ["TOAST_LIMIT", "TOAST_REMOVE_DELAY", "count", "genId", "toastTimeouts", "addToRemoveQueue", "toastId", "timeout", "dispatch", "reducer", "state", "action", "t", "toast", "listeners", "memoryState", "listener", "props", "id", "update", "dismiss", "open", "useToast", "setState", "React.useState", "React.useEffect", "index", "isApiError", "error", "errorObj", "handleApiError", "err", "DataTableColumnHeader", "column", "title", "className", "jsxs", "cx", "jsx", "RiArrowUpSLine", "RiArrowDownSLine"], "mappings": "+IAUA,MAAMA,EAAc,EACdC,EAAqB,IAS3B,IAAIC,EAAQ,EAEZ,SAASC,GAAQ,CACN,OAAAD,GAAAA,EAAQ,GAAK,OAAO,iBACtBA,EAAM,SAAS,CACxB,CA+BA,MAAME,MAAoB,IAEpBC,EAAoBC,GAAoB,CACxC,GAAAF,EAAc,IAAIE,CAAO,EAC3B,OAGI,MAAAC,EAAU,WAAW,IAAM,CAC/BH,EAAc,OAAOE,CAAO,EACnBE,EAAA,CACP,KAAM,eACN,QAAAF,CAAA,CACD,GACAL,CAAkB,EAEPG,EAAA,IAAIE,EAASC,CAAO,CACpC,EAEaE,EAAU,CAACC,EAAcC,IAA0B,CAC9D,OAAQA,EAAO,KAAM,CACnB,IAAK,YACI,MAAA,CACL,GAAGD,EACH,OAAQ,CAACC,EAAO,MAAO,GAAGD,EAAM,MAAM,EAAE,MAAM,EAAGV,CAAW,CAC9D,EAEF,IAAK,eACI,MAAA,CACL,GAAGU,EACH,OAAQA,EAAM,OAAO,IAAKE,GACxBA,EAAE,KAAOD,EAAO,MAAM,GAAK,CAAE,GAAGC,EAAG,GAAGD,EAAO,OAAUC,CAAA,CAE3D,EAEF,IAAK,gBAAiB,CACd,KAAA,CAAE,QAAAN,GAAYK,EAIpB,OAAIL,EACFD,EAAiBC,CAAO,EAElBI,EAAA,OAAO,QAASG,GAAU,CAC9BR,EAAiBQ,EAAM,EAAE,CAAA,CAC1B,EAGI,CACL,GAAGH,EACH,OAAQA,EAAM,OAAO,IAAKE,GACxBA,EAAE,KAAON,GAAWA,IAAY,OAC5B,CACA,GAAGM,EACH,KAAM,EAAA,EAENA,CAAA,CAER,CAAA,CAEF,IAAK,eACC,OAAAD,EAAO,UAAY,OACd,CACL,GAAGD,EACH,OAAQ,CAAA,CACV,EAEK,CACL,GAAGA,EACH,OAAQA,EAAM,OAAO,OAAQE,GAAMA,EAAE,KAAOD,EAAO,OAAO,CAC5D,CAAA,CAEN,EAEMG,EAA2C,CAAC,EAElD,IAAIC,EAAqB,CAAE,OAAQ,EAAG,EAEtC,SAASP,EAASG,EAAgB,CAClBI,EAAAN,EAAQM,EAAaJ,CAAM,EAC/BG,EAAA,QAASE,GAAa,CAC9BA,EAASD,CAAW,CAAA,CACrB,CACH,CAIA,SAASF,EAAM,CAAE,GAAGI,GAAgB,CAClC,MAAMC,EAAKf,EAAM,EAEXgB,EAAUF,GACdT,EAAS,CACP,KAAM,eACN,MAAO,CAAE,GAAGS,EAAO,GAAAC,CAAG,CAAA,CACvB,EACGE,EAAU,IAAMZ,EAAS,CAAE,KAAM,gBAAiB,QAASU,EAAI,EAE5D,OAAAV,EAAA,CACP,KAAM,YACN,MAAO,CACL,GAAGS,EACH,GAAAC,EACA,KAAM,GACN,aAAeG,GAAS,CACjBA,GAAcD,EAAA,CAAA,CACrB,CACF,CACD,EAEM,CACL,GAAAF,EACA,QAAAE,EACA,OAAAD,CACF,CACF,CAEA,SAASG,GAAW,CAClB,KAAM,CAACZ,EAAOa,CAAQ,EAAIC,EAAAA,SAAsBT,CAAW,EAE3DU,OAAAA,EAAAA,UAAgB,KACdX,EAAU,KAAKS,CAAQ,EAChB,IAAM,CACL,MAAAG,EAAQZ,EAAU,QAAQS,CAAQ,EACpCG,EAAQ,IACAZ,EAAA,OAAOY,EAAO,CAAC,CAE7B,GACC,CAAChB,CAAK,CAAC,EAEH,CACL,GAAGA,EACH,MAAAG,EACA,QAAUP,GAAqBE,EAAS,CAAE,KAAM,gBAAiB,QAAAF,CAAS,CAAA,CAC5E,CACF,CC5Ka,MAAAqB,EAAcC,GAAsC,CAC/D,GAAI,OAAOA,GAAU,UAAYA,IAAU,KAAa,MAAA,GAGxD,MAAMC,EAAWD,EAGjB,MADI,EAAE,YAAaC,IACf,OAAOA,EAAS,SAAY,UAAYA,EAAS,UAAY,KAAa,GAIvE,UADSA,EAAS,OAE3B,EAOaC,EACXC,GAEIJ,EAAWI,CAAG,EACT,CACL,MAAOA,EAAI,QAAQ,MAAM,QACzB,YAAaA,EAAI,QAAQ,MAAM,QAC/B,QAAS,OACX,EAGO,CACL,MAAO,QACP,YAAa,+BACb,QAAS,OACX,ECxCG,SAASC,EAAqC,CACnD,OAAAC,EACA,MAAAC,EACA,UAAAC,CACF,EAA8C,CACxC,OAACF,EAAO,aAKVG,EAAA,KAAC,MAAA,CACC,QAASH,EAAO,wBAAwB,EACxC,UAAWI,EACTJ,EAAO,UAAU,gBAAkB,GAC/B,+HACA,EACN,EAEA,SAAA,CAAAK,EAAAA,IAAC,QAAM,SAAMJ,CAAA,CAAA,EACZD,EAAO,WAAW,EAChBG,EAAA,KAAA,MAAA,CAAI,UAAU,aACb,SAAA,CAAAE,EAAA,IAACC,EAAA,CACC,UAAWF,EACT,2CACAJ,EAAO,YAAA,IAAkB,OAAS,aAAe,EACnD,EACA,cAAY,MAAA,CACd,EACAK,EAAA,IAACE,EAAA,CACC,UAAWH,EACT,2CACAJ,EAAO,YAAA,IAAkB,MAAQ,aAAe,EAClD,EACA,cAAY,MAAA,CAAA,CACd,CAAA,CACF,EACE,IAAA,CAAA,CACN,QA/BQ,MAAI,CAAA,UAAWI,EAAGF,CAAS,EAAI,SAAMD,EAAA,CAiCjD"}