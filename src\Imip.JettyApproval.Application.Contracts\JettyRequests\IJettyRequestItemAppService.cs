using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.JettyApproval.JettyRequests;

/// <summary>
/// Application service interface for JettyRequestItem entity
/// </summary>
public interface IJettyRequestItemAppService :
    ICrudAppService<
        JettyRequestItemDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateJettyRequestItemDto,
        CreateUpdateJettyRequestItemDto>
{
    /// <summary>
    /// Get items by jetty request ID
    /// </summary>
    Task<List<JettyRequestItemDto>> GetByJettyRequestIdAsync(Guid jettyRequestId);

    /// <summary>
    /// Get items by tenant name
    /// </summary>
    Task<List<JettyRequestItemDto>> GetByTenantNameAsync(string tenantName);

    /// <summary>
    /// Get items by status
    /// </summary>
    Task<List<JettyRequestItemDto>> GetByStatusAsync(string status);

    /// <summary>
    /// Get items by item name
    /// </summary>
    Task<List<JettyRequestItemDto>> GetByItemNameAsync(string itemName);
}