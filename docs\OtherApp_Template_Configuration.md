# Other App Configuration Template

## 1. appsettings.json for Other App

```json
{
  "App": {
    "SelfUrl": "http://localhost:5001",
    "UseHttps": false
  },
  "AuthServer": {
    "Authority": "https://identity.imip.co.id",
    "RequireHttpsMetadata": false,
    "ClientId": "OtherAppLocal",
    "ClientSecret": "your-client-secret-here",
    "Audience": "OtherApp"
  },
  "JwtBearer": {
    "Authority": "https://identity.imip.co.id",
    "RequireHttpsMetadata": false,
    "Audience": "OtherApp",
    "ValidateIssuer": true,
    "ValidateAudience": true,
    "ValidateLifetime": true,
    "ValidateIssuerSigningKey": true
  }
}
```

## 2. Program.cs or Startup.cs for Other App

```csharp
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;

var builder = WebApplication.CreateBuilder(args);

// Add JWT Bearer authentication
builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.Authority = builder.Configuration["AuthServer:Authority"];
    options.RequireHttpsMetadata = builder.Configuration.GetValue<bool>("AuthServer:RequireHttpsMetadata");
    options.Audience = builder.Configuration["JwtBearer:Audience"];
    
    // Validate the token
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuer = builder.Configuration.GetValue<bool>("JwtBearer:ValidateIssuer"),
        ValidateAudience = builder.Configuration.GetValue<bool>("JwtBearer:ValidateAudience"),
        ValidateLifetime = builder.Configuration.GetValue<bool>("JwtBearer:ValidateLifetime"),
        ValidateIssuerSigningKey = builder.Configuration.GetValue<bool>("JwtBearer:ValidateIssuerSigningKey"),
        
        // Accept tokens from the same authority
        ValidIssuer = builder.Configuration["AuthServer:Authority"],
        ValidAudience = builder.Configuration["JwtBearer:Audience"]
    };
    
    // Handle token validation events
    options.Events = new JwtBearerEvents
    {
        OnTokenValidated = context =>
        {
            // Log successful token validation
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
            logger.LogInformation("Token validated successfully for user: {User}", context.Principal?.Identity?.Name);
            return Task.CompletedTask;
        },
        
        OnAuthenticationFailed = context =>
        {
            // Log authentication failures
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
            logger.LogError(context.Exception, "Token validation failed");
            return Task.CompletedTask;
        }
    };
});

builder.Services.AddAuthorization();

var app = builder.Build();

app.UseAuthentication();
app.UseAuthorization();

// Protected API endpoint
app.MapGet("/api/data", [Authorize] () =>
{
    return new { message = "Data from other app", timestamp = DateTime.UtcNow };
});

app.Run();
```

## 3. Identity Server Configuration

Make sure your identity server (identity.imip.co.id) has the following configuration:

### Client Configuration for Other App
```json
{
  "ClientId": "OtherAppLocal",
  "ClientName": "Other App",
  "ClientSecrets": [
    {
      "Value": "your-client-secret-here"
    }
  ],
  "AllowedGrantTypes": ["client_credentials", "authorization_code"],
  "AllowedScopes": ["openid", "profile", "email", "OtherApp"],
  "RequireClientSecret": true,
  "RequirePkce": false,
  "AllowOfflineAccess": true,
  "AccessTokenLifetime": 3600,
  "RefreshTokenLifetime": 86400
}
```

### API Resource Configuration
```json
{
  "Name": "OtherApp",
  "DisplayName": "Other App API",
  "Description": "API for Other App",
  "Scopes": ["OtherApp"],
  "UserClaims": ["sub", "name", "email", "role"]
}
```

## 4. Testing the App-to-App Communication

### From Your App (JettyApproval)
```bash
# Get data from other app
curl -H "Authorization: Bearer YOUR_SSO_TOKEN" \
     http://localhost:5001/api/data

# Or use your app's endpoint
curl http://localhost:5000/api/app-to-app/data
```

### From Other App
```bash
# Test the protected endpoint
curl -H "Authorization: Bearer VALID_TOKEN" \
     http://localhost:5001/api/data
```

## 5. Key Points

1. **Same Authority**: Both apps use the same identity server
2. **Token Validation**: Other app validates tokens using JWT Bearer authentication
3. **Audience**: Each app has its own audience for token validation
4. **Scopes**: Define appropriate scopes for each app
5. **CORS**: Configure CORS if apps are on different domains

## 6. Security Considerations

- Use HTTPS in production
- Validate token audience and issuer
- Implement proper error handling
- Log authentication events
- Use appropriate token lifetimes
- Consider implementing API rate limiting 