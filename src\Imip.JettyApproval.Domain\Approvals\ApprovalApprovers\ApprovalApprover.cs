using System;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.JettyApproval.Approvals.ApprovalApprovers;

/// <summary>
/// Entity for storing approval approver information
/// </summary>
public class ApprovalApprover : FullAuditedAggregateRoot<Guid>
{
    /// <summary>
    /// Foreign key to the approval template
    /// </summary>
    public Guid ApprovalId { get; set; }

    /// <summary>
    /// ID of the approver
    /// </summary>
    public Guid ApproverId { get; set; }

    /// <summary>
    /// Sequence number for approval order
    /// </summary>
    public int Sequence { get; set; }

    /// <summary>
    /// Status of the approver
    /// </summary>
    public string? Status { get; set; }

    /// <summary>
    /// Navigation property to the approval template
    /// </summary>
    public virtual ApprovalTemplates.ApprovalTemplate ApprovalTemplate { get; set; } = null!;

    /// <summary>
    /// Default constructor for EF Core
    /// </summary>
    protected ApprovalApprover()
    {
    }

    /// <summary>
    /// Creates a new ApprovalApprover
    /// </summary>
    public ApprovalApprover(
        Guid id,
        Guid approvalId,
        Guid approverId,
        int sequence = 0,
        string? status = null)
        : base(id)
    {
        ApprovalId = approvalId;
        ApproverId = approverId;
        Sequence = sequence;
        Status = status;
    }

    /// <summary>
    /// Creates a new ApprovalApprover without ID (for mapping from DTOs)
    /// </summary>
    public ApprovalApprover(
        Guid approvalId,
        Guid approverId,
        int sequence = 0,
        string? status = null)
    {
        ApprovalId = approvalId;
        ApproverId = approverId;
        Sequence = sequence;
        Status = status;
    }
}