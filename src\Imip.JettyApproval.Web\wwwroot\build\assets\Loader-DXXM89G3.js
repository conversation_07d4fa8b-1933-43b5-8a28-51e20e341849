import{j as o}from"./vendor-CrSBzUoz.js";import{m as a}from"./App-CGHLK9xH.js";import{B as l}from"./app-layout-CNB1Wtrx.js";import{T as d}from"./TableSkeleton-Bzdk6y-O.js";const n=[];for(let e=0;e<256;++e)n.push((e+256).toString(16).slice(1));function u(e,t=0){return(n[e[t+0]]+n[e[t+1]]+n[e[t+2]]+n[e[t+3]]+"-"+n[e[t+4]]+n[e[t+5]]+"-"+n[e[t+6]]+n[e[t+7]]+"-"+n[e[t+8]]+n[e[t+9]]+"-"+n[e[t+10]]+n[e[t+11]]+n[e[t+12]]+n[e[t+13]]+n[e[t+14]]+n[e[t+15]]).toLowerCase()}let i;const m=new Uint8Array(16);function c(){if(!i){if(typeof crypto>"u"||!crypto.getRandomValues)throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");i=crypto.getRandomValues.bind(crypto)}return i(m)}const h=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),s={randomUUID:h};function w(e,t,p){if(s.randomUUID&&!e)return s.randomUUID();e=e||{};const r=e.random??e.rng?.()??c();if(r.length<16)throw new Error("Random bytes length must be >= 16");return r[6]=r[6]&15|64,r[8]=r[8]&63|128,u(r)}const b=()=>o.jsxs("div",{className:"bg-base-200 flex flex-col-reverse items-center justify-center gap-1 px-4 py-24 md:gap-28 md:px-44 md:py-20 lg:flex-row lg:px-24 lg:py-24",children:[o.jsx("div",{className:"relative w-full lg:pb-0 xl:w-1/2",children:o.jsx("div",{className:"relative",children:o.jsxs("div",{className:"absolute",children:[o.jsx("h1",{className:"my-2 text-2xl font-bold text-primary",children:"Looks like you have found the doorway to the great nothing"}),o.jsx("p",{className:"text-base-content my-2",children:"Sorry about that! Please visit our homepage to get where you need to go."}),o.jsx(l,{onClick:()=>{a.visit("/")},children:"Take me there!"})]})})}),o.jsx("div",{children:o.jsx("img",{src:"/img/Group.png",width:500,height:500,alt:"Something went wrong"})})]}),j=()=>o.jsx("div",{className:"z-50 w-full min-h-[20rem]",children:o.jsx(d,{rowCount:10,columnCount:4,hasTitle:!0,hasSearch:!0,hasFilters:!0,hasPagination:!0,hasActions:!0})});export{b as E,j as L,w as v};
//# sourceMappingURL=Loader-DXXM89G3.js.map
