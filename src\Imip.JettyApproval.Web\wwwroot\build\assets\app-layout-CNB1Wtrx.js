var so=Object.defineProperty;var io=(e,t,r)=>t in e?so(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var Dr=(e,t,r)=>io(e,typeof t!="symbol"?t+"":t,r);import{j as l,r as R,h as co,c as ne,d as Ln,i as xr,a as zn,b as J,R as $n,Q as lo,k as uo}from"./vendor-CrSBzUoz.js";import{S as it,U as fo,V as po,W as ho,X as mo,Y as vo,Z as go,_ as bo,$ as yo,a0 as xo,a1 as wo,a2 as _o,a3 as So,a4 as jo,a5 as Oo,a6 as Eo,a7 as Co,a8 as Io,a9 as Bn,aa as Fn,ab as Kn,ac as To,ad as Ro,ae as Mo,af as Ao,ag as Po,P as Me,R as ko,C as No,E as Do,F as Lo,G as zo,a as $o,O as Bo,f as qn,ah as Vn,u as Fo,e as Gn,ai as Ko,h as Rt,aj as qo,i as ae,j as wr,ak as Un,al as Vo,am as Go,an as Uo,ao as Wo,ap as Ho,aq as Yo,ar as Xo,as as Jo,at as Qo,au as Zo,av as es,aw as ts,J as Et,ax as rs,r as ns,ay as as,az as Wn,aA as os,K as ss}from"./radix-DaY-mnHi.js";import{q as Hn,Y as Ct}from"./App-CGHLK9xH.js";function Yn(e){var t,r,n="";if(typeof e=="string"||typeof e=="number")n+=e;else if(typeof e=="object")if(Array.isArray(e)){var a=e.length;for(t=0;t<a;t++)e[t]&&(r=Yn(e[t]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}function _r(){for(var e,t,r=0,n="",a=arguments.length;r<a;r++)(e=arguments[r])&&(t=Yn(e))&&(n&&(n+=" "),n+=t);return n}const Lr=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,zr=_r,Sr=(e,t)=>r=>{var n;if(t?.variants==null)return zr(e,r?.class,r?.className);const{variants:a,defaultVariants:s}=t,o=Object.keys(a).map(u=>{const f=r?.[u],d=s?.[u];if(f===null)return null;const m=Lr(f)||Lr(d);return a[u][m]}),c=r&&Object.entries(r).reduce((u,f)=>{let[d,m]=f;return m===void 0||(u[d]=m),u},{}),i=t==null||(n=t.compoundVariants)===null||n===void 0?void 0:n.reduce((u,f)=>{let{class:d,className:m,...g}=f;return Object.entries(g).every(p=>{let[b,h]=p;return Array.isArray(h)?h.includes({...s,...c}[b]):{...s,...c}[b]===h})?[...u,d,m]:u},[]);return zr(e,o,i,r?.class,r?.className)},jr="-",is=e=>{const t=ls(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:o=>{const c=o.split(jr);return c[0]===""&&c.length!==1&&c.shift(),Xn(c,t)||cs(o)},getConflictingClassGroupIds:(o,c)=>{const i=r[o]||[];return c&&n[o]?[...i,...n[o]]:i}}},Xn=(e,t)=>{if(e.length===0)return t.classGroupId;const r=e[0],n=t.nextPart.get(r),a=n?Xn(e.slice(1),n):void 0;if(a)return a;if(t.validators.length===0)return;const s=e.join(jr);return t.validators.find(({validator:o})=>o(s))?.classGroupId},$r=/^\[(.+)\]$/,cs=e=>{if($r.test(e)){const t=$r.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},ls=e=>{const{theme:t,classGroups:r}=e,n={nextPart:new Map,validators:[]};for(const a in r)ar(r[a],n,a,t);return n},ar=(e,t,r,n)=>{e.forEach(a=>{if(typeof a=="string"){const s=a===""?t:Br(t,a);s.classGroupId=r;return}if(typeof a=="function"){if(us(a)){ar(a(n),t,r,n);return}t.validators.push({validator:a,classGroupId:r});return}Object.entries(a).forEach(([s,o])=>{ar(o,Br(t,s),r,n)})})},Br=(e,t)=>{let r=e;return t.split(jr).forEach(n=>{r.nextPart.has(n)||r.nextPart.set(n,{nextPart:new Map,validators:[]}),r=r.nextPart.get(n)}),r},us=e=>e.isThemeGetter,ds=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,r=new Map,n=new Map;const a=(s,o)=>{r.set(s,o),t++,t>e&&(t=0,n=r,r=new Map)};return{get(s){let o=r.get(s);if(o!==void 0)return o;if((o=n.get(s))!==void 0)return a(s,o),o},set(s,o){r.has(s)?r.set(s,o):a(s,o)}}},or="!",sr=":",fs=sr.length,ps=e=>{const{prefix:t,experimentalParseClassName:r}=e;let n=a=>{const s=[];let o=0,c=0,i=0,u;for(let p=0;p<a.length;p++){let b=a[p];if(o===0&&c===0){if(b===sr){s.push(a.slice(i,p)),i=p+fs;continue}if(b==="/"){u=p;continue}}b==="["?o++:b==="]"?o--:b==="("?c++:b===")"&&c--}const f=s.length===0?a:a.substring(i),d=hs(f),m=d!==f,g=u&&u>i?u-i:void 0;return{modifiers:s,hasImportantModifier:m,baseClassName:d,maybePostfixModifierPosition:g}};if(t){const a=t+sr,s=n;n=o=>o.startsWith(a)?s(o.substring(a.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:o,maybePostfixModifierPosition:void 0}}if(r){const a=n;n=s=>r({className:s,parseClassName:a})}return n},hs=e=>e.endsWith(or)?e.substring(0,e.length-1):e.startsWith(or)?e.substring(1):e,ms=e=>{const t=Object.fromEntries(e.orderSensitiveModifiers.map(n=>[n,!0]));return n=>{if(n.length<=1)return n;const a=[];let s=[];return n.forEach(o=>{o[0]==="["||t[o]?(a.push(...s.sort(),o),s=[]):s.push(o)}),a.push(...s.sort()),a}},vs=e=>({cache:ds(e.cacheSize),parseClassName:ps(e),sortModifiers:ms(e),...is(e)}),gs=/\s+/,bs=(e,t)=>{const{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:a,sortModifiers:s}=t,o=[],c=e.trim().split(gs);let i="";for(let u=c.length-1;u>=0;u-=1){const f=c[u],{isExternal:d,modifiers:m,hasImportantModifier:g,baseClassName:p,maybePostfixModifierPosition:b}=r(f);if(d){i=f+(i.length>0?" "+i:i);continue}let h=!!b,w=n(h?p.substring(0,b):p);if(!w){if(!h){i=f+(i.length>0?" "+i:i);continue}if(w=n(p),!w){i=f+(i.length>0?" "+i:i);continue}h=!1}const y=s(m).join(":"),v=g?y+or:y,S=v+w;if(o.includes(S))continue;o.push(S);const C=a(w,h);for(let T=0;T<C.length;++T){const E=C[T];o.push(v+E)}i=f+(i.length>0?" "+i:i)}return i};function ys(){let e=0,t,r,n="";for(;e<arguments.length;)(t=arguments[e++])&&(r=Jn(t))&&(n&&(n+=" "),n+=r);return n}const Jn=e=>{if(typeof e=="string")return e;let t,r="";for(let n=0;n<e.length;n++)e[n]&&(t=Jn(e[n]))&&(r&&(r+=" "),r+=t);return r};function xs(e,...t){let r,n,a,s=o;function o(i){const u=t.reduce((f,d)=>d(f),e());return r=vs(u),n=r.cache.get,a=r.cache.set,s=c,c(i)}function c(i){const u=n(i);if(u)return u;const f=bs(i,r);return a(i,f),f}return function(){return s(ys.apply(null,arguments))}}const Z=e=>{const t=r=>r[e]||[];return t.isThemeGetter=!0,t},Qn=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Zn=/^\((?:(\w[\w-]*):)?(.+)\)$/i,ws=/^\d+\/\d+$/,_s=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Ss=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,js=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Os=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Es=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,He=e=>ws.test(e),K=e=>!!e&&!Number.isNaN(Number(e)),Te=e=>!!e&&Number.isInteger(Number(e)),Bt=e=>e.endsWith("%")&&K(e.slice(0,-1)),Se=e=>_s.test(e),Cs=()=>!0,Is=e=>Ss.test(e)&&!js.test(e),ea=()=>!1,Ts=e=>Os.test(e),Rs=e=>Es.test(e),Ms=e=>!L(e)&&!z(e),As=e=>Xe(e,na,ea),L=e=>Qn.test(e),Le=e=>Xe(e,aa,Is),Ft=e=>Xe(e,Ls,K),Fr=e=>Xe(e,ta,ea),Ps=e=>Xe(e,ra,Rs),pt=e=>Xe(e,oa,Ts),z=e=>Zn.test(e),tt=e=>Je(e,aa),ks=e=>Je(e,zs),Kr=e=>Je(e,ta),Ns=e=>Je(e,na),Ds=e=>Je(e,ra),ht=e=>Je(e,oa,!0),Xe=(e,t,r)=>{const n=Qn.exec(e);return n?n[1]?t(n[1]):r(n[2]):!1},Je=(e,t,r=!1)=>{const n=Zn.exec(e);return n?n[1]?t(n[1]):r:!1},ta=e=>e==="position"||e==="percentage",ra=e=>e==="image"||e==="url",na=e=>e==="length"||e==="size"||e==="bg-size",aa=e=>e==="length",Ls=e=>e==="number",zs=e=>e==="family-name",oa=e=>e==="shadow",$s=()=>{const e=Z("color"),t=Z("font"),r=Z("text"),n=Z("font-weight"),a=Z("tracking"),s=Z("leading"),o=Z("breakpoint"),c=Z("container"),i=Z("spacing"),u=Z("radius"),f=Z("shadow"),d=Z("inset-shadow"),m=Z("text-shadow"),g=Z("drop-shadow"),p=Z("blur"),b=Z("perspective"),h=Z("aspect"),w=Z("ease"),y=Z("animate"),v=()=>["auto","avoid","all","avoid-page","page","left","right","column"],S=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],C=()=>[...S(),z,L],T=()=>["auto","hidden","clip","visible","scroll"],E=()=>["auto","contain","none"],O=()=>[z,L,i],I=()=>[He,"full","auto",...O()],x=()=>[Te,"none","subgrid",z,L],_=()=>["auto",{span:["full",Te,z,L]},Te,z,L],P=()=>[Te,"auto",z,L],M=()=>["auto","min","max","fr",z,L],j=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],N=()=>["start","end","center","stretch","center-safe","end-safe"],k=()=>["auto",...O()],D=()=>[He,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...O()],A=()=>[e,z,L],V=()=>[...S(),Kr,Fr,{position:[z,L]}],W=()=>["no-repeat",{repeat:["","x","y","space","round"]}],Q=()=>["auto","cover","contain",Ns,As,{size:[z,L]}],ee=()=>[Bt,tt,Le],G=()=>["","none","full",u,z,L],U=()=>["",K,tt,Le],oe=()=>["solid","dashed","dotted","double"],et=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],H=()=>[K,Bt,Kr,Fr],Ue=()=>["","none",p,z,L],ke=()=>["none",K,z,L],Ne=()=>["none",K,z,L],We=()=>[K,z,L],Ce=()=>[He,"full",...O()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[Se],breakpoint:[Se],color:[Cs],container:[Se],"drop-shadow":[Se],ease:["in","out","in-out"],font:[Ms],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[Se],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[Se],shadow:[Se],spacing:["px",K],text:[Se],"text-shadow":[Se],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",He,L,z,h]}],container:["container"],columns:[{columns:[K,L,z,c]}],"break-after":[{"break-after":v()}],"break-before":[{"break-before":v()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:C()}],overflow:[{overflow:T()}],"overflow-x":[{"overflow-x":T()}],"overflow-y":[{"overflow-y":T()}],overscroll:[{overscroll:E()}],"overscroll-x":[{"overscroll-x":E()}],"overscroll-y":[{"overscroll-y":E()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:I()}],"inset-x":[{"inset-x":I()}],"inset-y":[{"inset-y":I()}],start:[{start:I()}],end:[{end:I()}],top:[{top:I()}],right:[{right:I()}],bottom:[{bottom:I()}],left:[{left:I()}],visibility:["visible","invisible","collapse"],z:[{z:[Te,"auto",z,L]}],basis:[{basis:[He,"full","auto",c,...O()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[K,He,"auto","initial","none",L]}],grow:[{grow:["",K,z,L]}],shrink:[{shrink:["",K,z,L]}],order:[{order:[Te,"first","last","none",z,L]}],"grid-cols":[{"grid-cols":x()}],"col-start-end":[{col:_()}],"col-start":[{"col-start":P()}],"col-end":[{"col-end":P()}],"grid-rows":[{"grid-rows":x()}],"row-start-end":[{row:_()}],"row-start":[{"row-start":P()}],"row-end":[{"row-end":P()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":M()}],"auto-rows":[{"auto-rows":M()}],gap:[{gap:O()}],"gap-x":[{"gap-x":O()}],"gap-y":[{"gap-y":O()}],"justify-content":[{justify:[...j(),"normal"]}],"justify-items":[{"justify-items":[...N(),"normal"]}],"justify-self":[{"justify-self":["auto",...N()]}],"align-content":[{content:["normal",...j()]}],"align-items":[{items:[...N(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...N(),{baseline:["","last"]}]}],"place-content":[{"place-content":j()}],"place-items":[{"place-items":[...N(),"baseline"]}],"place-self":[{"place-self":["auto",...N()]}],p:[{p:O()}],px:[{px:O()}],py:[{py:O()}],ps:[{ps:O()}],pe:[{pe:O()}],pt:[{pt:O()}],pr:[{pr:O()}],pb:[{pb:O()}],pl:[{pl:O()}],m:[{m:k()}],mx:[{mx:k()}],my:[{my:k()}],ms:[{ms:k()}],me:[{me:k()}],mt:[{mt:k()}],mr:[{mr:k()}],mb:[{mb:k()}],ml:[{ml:k()}],"space-x":[{"space-x":O()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":O()}],"space-y-reverse":["space-y-reverse"],size:[{size:D()}],w:[{w:[c,"screen",...D()]}],"min-w":[{"min-w":[c,"screen","none",...D()]}],"max-w":[{"max-w":[c,"screen","none","prose",{screen:[o]},...D()]}],h:[{h:["screen","lh",...D()]}],"min-h":[{"min-h":["screen","lh","none",...D()]}],"max-h":[{"max-h":["screen","lh",...D()]}],"font-size":[{text:["base",r,tt,Le]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,z,Ft]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Bt,L]}],"font-family":[{font:[ks,L,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[a,z,L]}],"line-clamp":[{"line-clamp":[K,"none",z,Ft]}],leading:[{leading:[s,...O()]}],"list-image":[{"list-image":["none",z,L]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",z,L]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:A()}],"text-color":[{text:A()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...oe(),"wavy"]}],"text-decoration-thickness":[{decoration:[K,"from-font","auto",z,Le]}],"text-decoration-color":[{decoration:A()}],"underline-offset":[{"underline-offset":[K,"auto",z,L]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:O()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",z,L]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",z,L]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:V()}],"bg-repeat":[{bg:W()}],"bg-size":[{bg:Q()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Te,z,L],radial:["",z,L],conic:[Te,z,L]},Ds,Ps]}],"bg-color":[{bg:A()}],"gradient-from-pos":[{from:ee()}],"gradient-via-pos":[{via:ee()}],"gradient-to-pos":[{to:ee()}],"gradient-from":[{from:A()}],"gradient-via":[{via:A()}],"gradient-to":[{to:A()}],rounded:[{rounded:G()}],"rounded-s":[{"rounded-s":G()}],"rounded-e":[{"rounded-e":G()}],"rounded-t":[{"rounded-t":G()}],"rounded-r":[{"rounded-r":G()}],"rounded-b":[{"rounded-b":G()}],"rounded-l":[{"rounded-l":G()}],"rounded-ss":[{"rounded-ss":G()}],"rounded-se":[{"rounded-se":G()}],"rounded-ee":[{"rounded-ee":G()}],"rounded-es":[{"rounded-es":G()}],"rounded-tl":[{"rounded-tl":G()}],"rounded-tr":[{"rounded-tr":G()}],"rounded-br":[{"rounded-br":G()}],"rounded-bl":[{"rounded-bl":G()}],"border-w":[{border:U()}],"border-w-x":[{"border-x":U()}],"border-w-y":[{"border-y":U()}],"border-w-s":[{"border-s":U()}],"border-w-e":[{"border-e":U()}],"border-w-t":[{"border-t":U()}],"border-w-r":[{"border-r":U()}],"border-w-b":[{"border-b":U()}],"border-w-l":[{"border-l":U()}],"divide-x":[{"divide-x":U()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":U()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...oe(),"hidden","none"]}],"divide-style":[{divide:[...oe(),"hidden","none"]}],"border-color":[{border:A()}],"border-color-x":[{"border-x":A()}],"border-color-y":[{"border-y":A()}],"border-color-s":[{"border-s":A()}],"border-color-e":[{"border-e":A()}],"border-color-t":[{"border-t":A()}],"border-color-r":[{"border-r":A()}],"border-color-b":[{"border-b":A()}],"border-color-l":[{"border-l":A()}],"divide-color":[{divide:A()}],"outline-style":[{outline:[...oe(),"none","hidden"]}],"outline-offset":[{"outline-offset":[K,z,L]}],"outline-w":[{outline:["",K,tt,Le]}],"outline-color":[{outline:A()}],shadow:[{shadow:["","none",f,ht,pt]}],"shadow-color":[{shadow:A()}],"inset-shadow":[{"inset-shadow":["none",d,ht,pt]}],"inset-shadow-color":[{"inset-shadow":A()}],"ring-w":[{ring:U()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:A()}],"ring-offset-w":[{"ring-offset":[K,Le]}],"ring-offset-color":[{"ring-offset":A()}],"inset-ring-w":[{"inset-ring":U()}],"inset-ring-color":[{"inset-ring":A()}],"text-shadow":[{"text-shadow":["none",m,ht,pt]}],"text-shadow-color":[{"text-shadow":A()}],opacity:[{opacity:[K,z,L]}],"mix-blend":[{"mix-blend":[...et(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":et()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[K]}],"mask-image-linear-from-pos":[{"mask-linear-from":H()}],"mask-image-linear-to-pos":[{"mask-linear-to":H()}],"mask-image-linear-from-color":[{"mask-linear-from":A()}],"mask-image-linear-to-color":[{"mask-linear-to":A()}],"mask-image-t-from-pos":[{"mask-t-from":H()}],"mask-image-t-to-pos":[{"mask-t-to":H()}],"mask-image-t-from-color":[{"mask-t-from":A()}],"mask-image-t-to-color":[{"mask-t-to":A()}],"mask-image-r-from-pos":[{"mask-r-from":H()}],"mask-image-r-to-pos":[{"mask-r-to":H()}],"mask-image-r-from-color":[{"mask-r-from":A()}],"mask-image-r-to-color":[{"mask-r-to":A()}],"mask-image-b-from-pos":[{"mask-b-from":H()}],"mask-image-b-to-pos":[{"mask-b-to":H()}],"mask-image-b-from-color":[{"mask-b-from":A()}],"mask-image-b-to-color":[{"mask-b-to":A()}],"mask-image-l-from-pos":[{"mask-l-from":H()}],"mask-image-l-to-pos":[{"mask-l-to":H()}],"mask-image-l-from-color":[{"mask-l-from":A()}],"mask-image-l-to-color":[{"mask-l-to":A()}],"mask-image-x-from-pos":[{"mask-x-from":H()}],"mask-image-x-to-pos":[{"mask-x-to":H()}],"mask-image-x-from-color":[{"mask-x-from":A()}],"mask-image-x-to-color":[{"mask-x-to":A()}],"mask-image-y-from-pos":[{"mask-y-from":H()}],"mask-image-y-to-pos":[{"mask-y-to":H()}],"mask-image-y-from-color":[{"mask-y-from":A()}],"mask-image-y-to-color":[{"mask-y-to":A()}],"mask-image-radial":[{"mask-radial":[z,L]}],"mask-image-radial-from-pos":[{"mask-radial-from":H()}],"mask-image-radial-to-pos":[{"mask-radial-to":H()}],"mask-image-radial-from-color":[{"mask-radial-from":A()}],"mask-image-radial-to-color":[{"mask-radial-to":A()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":S()}],"mask-image-conic-pos":[{"mask-conic":[K]}],"mask-image-conic-from-pos":[{"mask-conic-from":H()}],"mask-image-conic-to-pos":[{"mask-conic-to":H()}],"mask-image-conic-from-color":[{"mask-conic-from":A()}],"mask-image-conic-to-color":[{"mask-conic-to":A()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:V()}],"mask-repeat":[{mask:W()}],"mask-size":[{mask:Q()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",z,L]}],filter:[{filter:["","none",z,L]}],blur:[{blur:Ue()}],brightness:[{brightness:[K,z,L]}],contrast:[{contrast:[K,z,L]}],"drop-shadow":[{"drop-shadow":["","none",g,ht,pt]}],"drop-shadow-color":[{"drop-shadow":A()}],grayscale:[{grayscale:["",K,z,L]}],"hue-rotate":[{"hue-rotate":[K,z,L]}],invert:[{invert:["",K,z,L]}],saturate:[{saturate:[K,z,L]}],sepia:[{sepia:["",K,z,L]}],"backdrop-filter":[{"backdrop-filter":["","none",z,L]}],"backdrop-blur":[{"backdrop-blur":Ue()}],"backdrop-brightness":[{"backdrop-brightness":[K,z,L]}],"backdrop-contrast":[{"backdrop-contrast":[K,z,L]}],"backdrop-grayscale":[{"backdrop-grayscale":["",K,z,L]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[K,z,L]}],"backdrop-invert":[{"backdrop-invert":["",K,z,L]}],"backdrop-opacity":[{"backdrop-opacity":[K,z,L]}],"backdrop-saturate":[{"backdrop-saturate":[K,z,L]}],"backdrop-sepia":[{"backdrop-sepia":["",K,z,L]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":O()}],"border-spacing-x":[{"border-spacing-x":O()}],"border-spacing-y":[{"border-spacing-y":O()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",z,L]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[K,"initial",z,L]}],ease:[{ease:["linear","initial",w,z,L]}],delay:[{delay:[K,z,L]}],animate:[{animate:["none",y,z,L]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[b,z,L]}],"perspective-origin":[{"perspective-origin":C()}],rotate:[{rotate:ke()}],"rotate-x":[{"rotate-x":ke()}],"rotate-y":[{"rotate-y":ke()}],"rotate-z":[{"rotate-z":ke()}],scale:[{scale:Ne()}],"scale-x":[{"scale-x":Ne()}],"scale-y":[{"scale-y":Ne()}],"scale-z":[{"scale-z":Ne()}],"scale-3d":["scale-3d"],skew:[{skew:We()}],"skew-x":[{"skew-x":We()}],"skew-y":[{"skew-y":We()}],transform:[{transform:[z,L,"","none","gpu","cpu"]}],"transform-origin":[{origin:C()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Ce()}],"translate-x":[{"translate-x":Ce()}],"translate-y":[{"translate-y":Ce()}],"translate-z":[{"translate-z":Ce()}],"translate-none":["translate-none"],accent:[{accent:A()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:A()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",z,L]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":O()}],"scroll-mx":[{"scroll-mx":O()}],"scroll-my":[{"scroll-my":O()}],"scroll-ms":[{"scroll-ms":O()}],"scroll-me":[{"scroll-me":O()}],"scroll-mt":[{"scroll-mt":O()}],"scroll-mr":[{"scroll-mr":O()}],"scroll-mb":[{"scroll-mb":O()}],"scroll-ml":[{"scroll-ml":O()}],"scroll-p":[{"scroll-p":O()}],"scroll-px":[{"scroll-px":O()}],"scroll-py":[{"scroll-py":O()}],"scroll-ps":[{"scroll-ps":O()}],"scroll-pe":[{"scroll-pe":O()}],"scroll-pt":[{"scroll-pt":O()}],"scroll-pr":[{"scroll-pr":O()}],"scroll-pb":[{"scroll-pb":O()}],"scroll-pl":[{"scroll-pl":O()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",z,L]}],fill:[{fill:["none",...A()]}],"stroke-w":[{stroke:[K,tt,Le,Ft]}],stroke:[{stroke:["none",...A()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},sa=xs($s);function $(...e){return sa(_r(e))}function Gd(...e){return sa(_r(...e))}const Ud=["focus:ring-2","focus:ring-blue-200 dark:focus:ring-blue-700/30","focus:border-blue-500 dark:focus:border-blue-700"],Wd=["outline outline-offset-2 outline-0 focus-visible:outline-2","outline-blue-500 dark:outline-blue-500"],Hd=["ring-2","border-red-500 dark:border-red-700","ring-red-200 dark:ring-red-700/30"],Yd={currency:({number:e,maxFractionDigits:t=2,currency:r="USD"})=>new Intl.NumberFormat("en-US",{style:"currency",currency:r,maximumFractionDigits:t}).format(e),unit:e=>new Intl.NumberFormat("en-US",{style:"decimal"}).format(e),percentage:({number:e,decimals:t=1})=>new Intl.NumberFormat("en-US",{style:"percent",minimumFractionDigits:t,maximumFractionDigits:t}).format(e),million:({number:e,decimals:t=1})=>`${new Intl.NumberFormat("en-US",{style:"decimal",minimumFractionDigits:t,maximumFractionDigits:t}).format(e)}M`},Xd={ROLES:"AbpIdentity.Roles",USERS:"AbpIdentity.Users",TENANTS:"AbpTenantManagement.Tenants",MANAGE_HOST_FEATURES:"FeatureManagement.ManageHostFeatures",SETTINGS:"SettingManagement.Emailing",IDENTITY_PROVIDER_CLAIMS:"IdentityServer.Claims",IDENTITY_PROVIDER_CLAIM_TYPES:"IdentityServer.ClaimTypes",IDENTITY_PROVIDER_OPENIDDICT_APPLICATIONS:"IdentityServer.OpenIddictApplications",IDENTITY_PROVIDER_OPENIDDICT_SCOPES:"IdentityServer.OpenIddictScopes",IDENTITY_PROVIDER_OPENIDDICT_RESOURCES:"IdentityServer.OpenIddictResources"},Jd={ADMIN:"admin"},Qd={U:"U",R:"R",T:"T"},mt=(e,t)=>{let r=e;const n=[];for(;r<=t;)n.push(r),r++;return n},Zd=(e,t)=>{if(e>7){const a=Math.max(2,t-1),s=Math.min(e-1,t+1);let o=mt(a,s);const c=a>2,i=e-s>1,u=5-(o.length+3);switch(!0){case(c&&!i):{o=["SPACER",...mt(a-u,a-1),...o];break}case(!c&&i):{const f=mt(s+1,s+u);o=[...o,...f,"SPACER"];break}case(c&&i):default:{o=["SPACER",...o,"SPACER"];break}}return[1,...o,e]}return mt(1,e)},Bs=Sr("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",primary:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90 focus-visible:ring-primary/20 dark:focus-visible:ring-primary/40",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",success:"bg-green-600 text-white shadow-xs hover:bg-green-700 focus-visible:ring-green-600/20 dark:focus-visible:ring-green-400/40 dark:bg-green-600/80",warning:"bg-yellow-600 text-white shadow-xs hover:bg-yellow-700 focus-visible:ring-yellow-600/20 dark:focus-visible:ring-yellow-400/40 dark:bg-yellow-600/80",info:"bg-blue-600 text-white shadow-xs hover:bg-blue-700 focus-visible:ring-blue-600/20 dark:focus-visible:ring-blue-400/40 dark:bg-blue-600/80",error:"bg-red-600 text-white shadow-xs hover:bg-red-700 focus-visible:ring-red-600/20 dark:focus-visible:ring-red-400/40 dark:bg-red-600/80"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function Mt({className:e,variant:t,size:r,asChild:n=!1,...a}){const s=n?it:"button";return l.jsx(s,{"data-slot":"button",className:$(Bs({variant:t,size:r,className:e})),...a})}/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fs=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Ks=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,r,n)=>n?n.toUpperCase():r.toLowerCase()),qr=e=>{const t=Ks(e);return t.charAt(0).toUpperCase()+t.slice(1)},ia=(...e)=>e.filter((t,r,n)=>!!t&&t.trim()!==""&&n.indexOf(t)===r).join(" ").trim(),qs=e=>{for(const t in e)if(t.startsWith("aria-")||t==="role"||t==="title")return!0};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Vs={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gs=R.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:n,className:a="",children:s,iconNode:o,...c},i)=>R.createElement("svg",{ref:i,...Vs,width:t,height:t,stroke:e,strokeWidth:n?Number(r)*24/Number(t):r,className:ia("lucide",a),...!s&&!qs(c)&&{"aria-hidden":"true"},...c},[...o.map(([u,f])=>R.createElement(u,f)),...Array.isArray(s)?s:[s]]));/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ve=(e,t)=>{const r=R.forwardRef(({className:n,...a},s)=>R.createElement(Gs,{ref:s,iconNode:t,className:ia(`lucide-${Fs(qr(e))}`,`lucide-${e}`,n),...a}));return r.displayName=qr(e),r};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Us=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],Or=Ve("check",Us);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ws=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],ca=Ve("chevron-down",Ws);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hs=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],Ys=Ve("chevron-right",Hs);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xs=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],Js=Ve("chevron-up",Xs);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qs=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]],Zs=Ve("panel-left",Qs);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ei=[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"M12 8v4",key:"1got3b"}],["path",{d:"M12 16h.01",key:"1drbdi"}]],ti=Ve("shield-alert",ei);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ri=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],Er=Ve("x",ri);function ni({...e}){return l.jsx(fo,{"data-slot":"dropdown-menu",...e})}function ai({...e}){return l.jsx(po,{"data-slot":"dropdown-menu-trigger",...e})}function oi({className:e,sideOffset:t=4,...r}){return l.jsx(ho,{children:l.jsx(mo,{"data-slot":"dropdown-menu-content",sideOffset:t,className:$("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...r})})}function si({...e}){return l.jsx(wo,{"data-slot":"dropdown-menu-group",...e})}function vt({className:e,inset:t,variant:r="default",...n}){return l.jsx(yo,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":r,className:$("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n})}function ef({className:e,children:t,checked:r,...n}){return l.jsxs(vo,{"data-slot":"dropdown-menu-checkbox-item",className:$("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),checked:r,...n,children:[l.jsx("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:l.jsx(go,{children:l.jsx(Or,{className:"size-4"})})}),t]})}function ii({className:e,inset:t,...r}){return l.jsx(bo,{"data-slot":"dropdown-menu-label","data-inset":t,className:$("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...r})}function Vr({className:e,...t}){return l.jsx(xo,{"data-slot":"dropdown-menu-separator",className:$("bg-border -mx-1 my-1 h-px",e),...t})}function tf({className:e,...t}){return l.jsx("span",{"data-slot":"dropdown-menu-shortcut",className:$("text-muted-foreground ml-auto text-xs tracking-widest",e),...t})}function rf({className:e,type:t,...r}){return l.jsx("input",{type:t,"data-slot":"input",className:$("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground","flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none","file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium","disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50","md:text-sm","border-input dark:bg-input/30","focus-visible:border-primary focus-visible:ring-primary/20 dark:focus-visible:ring-primary/40 focus-visible:ring-[3px]","aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40","hover:border-primary/50 dark:hover:border-primary/30","active:border-primary/70 dark:active:border-primary/50",e),...r})}function ci({...e}){return l.jsx(_o,{"data-slot":"select",...e})}function Kt({...e}){return l.jsx(Mo,{"data-slot":"select-group",...e})}function li({...e}){return l.jsx(Oo,{"data-slot":"select-value",...e})}function ui({className:e,size:t="default",children:r,clearable:n,onClear:a,...s}){return l.jsxs(So,{"data-slot":"select-trigger","data-size":t,className:$("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...s,children:[r,n&&a&&l.jsx("button",{type:"button",onClick:o=>{o.stopPropagation(),a()},className:"hover:bg-muted rounded-sm p-1 transition-colors",children:l.jsx(Er,{className:"size-3 opacity-50"})}),l.jsx(jo,{asChild:!0,children:l.jsx(ca,{className:"size-4 opacity-50"})})]})}function di({className:e,children:t,position:r="popper",...n}){return l.jsx(Eo,{children:l.jsxs(Co,{"data-slot":"select-content",className:$("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md",r==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...n,children:[l.jsx(pi,{}),l.jsx(Io,{className:$("p-1",r==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),l.jsx(hi,{})]})})}function qt({className:e,...t}){return l.jsx(Ao,{"data-slot":"select-label",className:$("text-muted-foreground px-2 py-1.5 text-xs",e),...t})}function Vt({className:e,children:t,...r}){return l.jsxs(Bn,{"data-slot":"select-item",className:$("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...r,children:[l.jsx("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:l.jsx(Fn,{children:l.jsx(Or,{className:"size-4"})})}),l.jsx(Kn,{children:t})]})}function fi({className:e,...t}){return l.jsx(Po,{"data-slot":"select-separator",className:$("bg-border pointer-events-none -mx-1 my-1 h-px",e),...t})}function pi({className:e,...t}){return l.jsx(To,{"data-slot":"select-scroll-up-button",className:$("flex cursor-default items-center justify-center py-1",e),...t,children:l.jsx(Js,{className:"size-4"})})}function hi({className:e,...t}){return l.jsx(Ro,{"data-slot":"select-scroll-down-button",className:$("flex cursor-default items-center justify-center py-1",e),...t,children:l.jsx(ca,{className:"size-4"})})}function nf({className:e,option:t,description:r,icon:n,clearable:a=!0,...s}){return l.jsxs(Bn,{"data-slot":"select-item",className:$("focus:bg-accent focus:text-accent-foreground relative flex w-full cursor-default select-none items-center justify-between rounded-sm py-1.5 pl-2 pr-8 text-sm outline-hidden data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[l.jsx("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:a&&l.jsx(Fn,{children:l.jsx(Or,{className:"size-4"})})}),l.jsxs("div",{className:"flex flex-col items-start gap-1",children:[l.jsxs("div",{className:"flex items-center gap-2",children:[n&&l.jsx(n,{className:"size-4 shrink-0 opacity-50"}),l.jsx(Kn,{children:t})]}),l.jsx("span",{className:"text-muted-foreground text-xs",children:r})]})]})}/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var mi={outline:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},filled:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",stroke:"none"}};/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const ce=(e,t,r,n)=>{const a=R.forwardRef(({color:s="currentColor",size:o=24,stroke:c=2,title:i,className:u,children:f,...d},m)=>R.createElement("svg",{ref:m,...mi[e],width:o,height:o,className:["tabler-icon",`tabler-icon-${t}`,u].join(" "),strokeWidth:c,stroke:s,...d},[i&&R.createElement("title",{key:"svg-title"},i),...n.map(([g,p])=>R.createElement(g,p)),...Array.isArray(f)?f:[f]]));return a.displayName=`${r}`,a};/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var vi=ce("outline","app-window","IconAppWindow",[["path",{d:"M3 5m0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v10a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z",key:"svg-0"}],["path",{d:"M6 8h.01",key:"svg-1"}],["path",{d:"M9 8h.01",key:"svg-2"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var gi=ce("outline","brightness","IconBrightness",[["path",{d:"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0",key:"svg-0"}],["path",{d:"M12 3l0 18",key:"svg-1"}],["path",{d:"M12 9l4.65 -4.65",key:"svg-2"}],["path",{d:"M12 14.3l7.37 -7.37",key:"svg-3"}],["path",{d:"M12 19.6l8.85 -8.85",key:"svg-4"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var bi=ce("outline","chevron-right","IconChevronRight",[["path",{d:"M9 6l6 6l-6 6",key:"svg-0"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var yi=ce("outline","clipboard-check","IconClipboardCheck",[["path",{d:"M9 5h-2a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-12a2 2 0 0 0 -2 -2h-2",key:"svg-0"}],["path",{d:"M9 3m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z",key:"svg-1"}],["path",{d:"M9 14l2 2l4 -4",key:"svg-2"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var xi=ce("outline","credit-card","IconCreditCard",[["path",{d:"M3 5m0 3a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v8a3 3 0 0 1 -3 3h-12a3 3 0 0 1 -3 -3z",key:"svg-0"}],["path",{d:"M3 10l18 0",key:"svg-1"}],["path",{d:"M7 15l.01 0",key:"svg-2"}],["path",{d:"M11 15l2 0",key:"svg-3"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var wi=ce("outline","dashboard","IconDashboard",[["path",{d:"M12 13m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0",key:"svg-0"}],["path",{d:"M13.45 11.55l2.05 -2.05",key:"svg-1"}],["path",{d:"M6.4 20a9 9 0 1 1 11.2 0z",key:"svg-2"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var _i=ce("outline","database","IconDatabase",[["path",{d:"M12 6m-8 0a8 3 0 1 0 16 0a8 3 0 1 0 -16 0",key:"svg-0"}],["path",{d:"M4 6v6a8 3 0 0 0 16 0v-6",key:"svg-1"}],["path",{d:"M4 12v6a8 3 0 0 0 16 0v-6",key:"svg-2"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var Si=ce("outline","dots-vertical","IconDotsVertical",[["path",{d:"M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-0"}],["path",{d:"M12 19m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-1"}],["path",{d:"M12 5m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-2"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var ji=ce("outline","inner-shadow-top","IconInnerShadowTop",[["path",{d:"M5.636 5.636a9 9 0 1 0 12.728 12.728a9 9 0 0 0 -12.728 -12.728z",key:"svg-0"}],["path",{d:"M16.243 7.757a6 6 0 0 0 -8.486 0",key:"svg-1"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var Oi=ce("outline","logout","IconLogout",[["path",{d:"M14 8v-2a2 2 0 0 0 -2 -2h-7a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h7a2 2 0 0 0 2 -2v-2",key:"svg-0"}],["path",{d:"M9 12h12l-3 -3",key:"svg-1"}],["path",{d:"M18 15l3 -3",key:"svg-2"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var Ei=ce("outline","notification","IconNotification",[["path",{d:"M10 6h-3a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-3",key:"svg-0"}],["path",{d:"M17 7m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0",key:"svg-1"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var Ci=ce("outline","report-analytics","IconReportAnalytics",[["path",{d:"M9 5h-2a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-12a2 2 0 0 0 -2 -2h-2",key:"svg-0"}],["path",{d:"M9 3m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z",key:"svg-1"}],["path",{d:"M9 17v-5",key:"svg-2"}],["path",{d:"M12 17v-1",key:"svg-3"}],["path",{d:"M15 17v-3",key:"svg-4"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var Ii=ce("outline","search","IconSearch",[["path",{d:"M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0",key:"svg-0"}],["path",{d:"M21 21l-6 -6",key:"svg-1"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var Ti=ce("outline","ship","IconShip",[["path",{d:"M2 20a2.4 2.4 0 0 0 2 1a2.4 2.4 0 0 0 2 -1a2.4 2.4 0 0 1 2 -1a2.4 2.4 0 0 1 2 1a2.4 2.4 0 0 0 2 1a2.4 2.4 0 0 0 2 -1a2.4 2.4 0 0 1 2 -1a2.4 2.4 0 0 1 2 1a2.4 2.4 0 0 0 2 1a2.4 2.4 0 0 0 2 -1",key:"svg-0"}],["path",{d:"M4 18l-1 -5h18l-2 4",key:"svg-1"}],["path",{d:"M5 13v-6h8l4 6",key:"svg-2"}],["path",{d:"M7 7v-4h-1",key:"svg-3"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var Ri=ce("outline","user-circle","IconUserCircle",[["path",{d:"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0",key:"svg-0"}],["path",{d:"M12 10m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0",key:"svg-1"}],["path",{d:"M6.168 18.849a4 4 0 0 1 3.832 -2.849h4a4 4 0 0 1 3.834 2.855",key:"svg-2"}]]);const Gt=768;function Mi(){const[e,t]=R.useState(void 0);return R.useEffect(()=>{const r=window.matchMedia(`(max-width: ${Gt-1}px)`),n=()=>{t(window.innerWidth<Gt)};return r.addEventListener("change",n),t(window.innerWidth<Gt),()=>r.removeEventListener("change",n)},[]),!!e}var Ai="Separator",Gr="horizontal",Pi=["horizontal","vertical"],la=R.forwardRef((e,t)=>{const{decorative:r,orientation:n=Gr,...a}=e,s=ki(n)?n:Gr,c=r?{role:"none"}:{"aria-orientation":s==="vertical"?s:void 0,role:"separator"};return l.jsx(Me.div,{"data-orientation":s,...c,...a,ref:t})});la.displayName=Ai;function ki(e){return Pi.includes(e)}var Ni=la;function Di({className:e,orientation:t="horizontal",decorative:r=!0,...n}){return l.jsx(Ni,{"data-slot":"separator",decorative:r,orientation:t,className:$("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",e),...n})}function Li({...e}){return l.jsx(ko,{"data-slot":"sheet",...e})}function zi({...e}){return l.jsx($o,{"data-slot":"sheet-portal",...e})}function $i({className:e,...t}){return l.jsx(Bo,{"data-slot":"sheet-overlay",className:$("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function Bi({className:e,children:t,side:r="right",...n}){return l.jsxs(zi,{children:[l.jsx($i,{}),l.jsxs(No,{"data-slot":"sheet-content",className:$("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500",r==="right"&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm",r==="left"&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm",r==="top"&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b",r==="bottom"&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",e),...n,children:[t,l.jsxs(Do,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[l.jsx(Er,{className:"size-4"}),l.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}function Fi({className:e,...t}){return l.jsx("div",{"data-slot":"sheet-header",className:$("flex flex-col gap-1.5 p-4",e),...t})}function Ki({className:e,...t}){return l.jsx(Lo,{"data-slot":"sheet-title",className:$("text-foreground font-semibold",e),...t})}function qi({className:e,...t}){return l.jsx(zo,{"data-slot":"sheet-description",className:$("text-muted-foreground text-sm",e),...t})}var[At,af]=qn("Tooltip",[Vn]),Pt=Vn(),ua="TooltipProvider",Vi=700,ir="tooltip.open",[Gi,Cr]=At(ua),da=e=>{const{__scopeTooltip:t,delayDuration:r=Vi,skipDelayDuration:n=300,disableHoverableContent:a=!1,children:s}=e,o=R.useRef(!0),c=R.useRef(!1),i=R.useRef(0);return R.useEffect(()=>{const u=i.current;return()=>window.clearTimeout(u)},[]),l.jsx(Gi,{scope:t,isOpenDelayedRef:o,delayDuration:r,onOpen:R.useCallback(()=>{window.clearTimeout(i.current),o.current=!1},[]),onClose:R.useCallback(()=>{window.clearTimeout(i.current),i.current=window.setTimeout(()=>o.current=!0,n)},[n]),isPointerInTransitRef:c,onPointerInTransitChange:R.useCallback(u=>{c.current=u},[]),disableHoverableContent:a,children:s})};da.displayName=ua;var st="Tooltip",[Ui,ct]=At(st),fa=e=>{const{__scopeTooltip:t,children:r,open:n,defaultOpen:a,onOpenChange:s,disableHoverableContent:o,delayDuration:c}=e,i=Cr(st,e.__scopeTooltip),u=Pt(t),[f,d]=R.useState(null),m=Fo(),g=R.useRef(0),p=o??i.disableHoverableContent,b=c??i.delayDuration,h=R.useRef(!1),[w,y]=Gn({prop:n,defaultProp:a??!1,onChange:E=>{E?(i.onOpen(),document.dispatchEvent(new CustomEvent(ir))):i.onClose(),s?.(E)},caller:st}),v=R.useMemo(()=>w?h.current?"delayed-open":"instant-open":"closed",[w]),S=R.useCallback(()=>{window.clearTimeout(g.current),g.current=0,h.current=!1,y(!0)},[y]),C=R.useCallback(()=>{window.clearTimeout(g.current),g.current=0,y(!1)},[y]),T=R.useCallback(()=>{window.clearTimeout(g.current),g.current=window.setTimeout(()=>{h.current=!0,y(!0),g.current=0},b)},[b,y]);return R.useEffect(()=>()=>{g.current&&(window.clearTimeout(g.current),g.current=0)},[]),l.jsx(Ko,{...u,children:l.jsx(Ui,{scope:t,contentId:m,open:w,stateAttribute:v,trigger:f,onTriggerChange:d,onTriggerEnter:R.useCallback(()=>{i.isOpenDelayedRef.current?T():S()},[i.isOpenDelayedRef,T,S]),onTriggerLeave:R.useCallback(()=>{p?C():(window.clearTimeout(g.current),g.current=0)},[C,p]),onOpen:S,onClose:C,disableHoverableContent:p,children:r})})};fa.displayName=st;var cr="TooltipTrigger",pa=R.forwardRef((e,t)=>{const{__scopeTooltip:r,...n}=e,a=ct(cr,r),s=Cr(cr,r),o=Pt(r),c=R.useRef(null),i=Rt(t,c,a.onTriggerChange),u=R.useRef(!1),f=R.useRef(!1),d=R.useCallback(()=>u.current=!1,[]);return R.useEffect(()=>()=>document.removeEventListener("pointerup",d),[d]),l.jsx(qo,{asChild:!0,...o,children:l.jsx(Me.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...n,ref:i,onPointerMove:ae(e.onPointerMove,m=>{m.pointerType!=="touch"&&!f.current&&!s.isPointerInTransitRef.current&&(a.onTriggerEnter(),f.current=!0)}),onPointerLeave:ae(e.onPointerLeave,()=>{a.onTriggerLeave(),f.current=!1}),onPointerDown:ae(e.onPointerDown,()=>{a.open&&a.onClose(),u.current=!0,document.addEventListener("pointerup",d,{once:!0})}),onFocus:ae(e.onFocus,()=>{u.current||a.onOpen()}),onBlur:ae(e.onBlur,a.onClose),onClick:ae(e.onClick,a.onClose)})})});pa.displayName=cr;var Ir="TooltipPortal",[Wi,Hi]=At(Ir,{forceMount:void 0}),ha=e=>{const{__scopeTooltip:t,forceMount:r,children:n,container:a}=e,s=ct(Ir,t);return l.jsx(Wi,{scope:t,forceMount:r,children:l.jsx(wr,{present:r||s.open,children:l.jsx(Un,{asChild:!0,container:a,children:n})})})};ha.displayName=Ir;var Ye="TooltipContent",ma=R.forwardRef((e,t)=>{const r=Hi(Ye,e.__scopeTooltip),{forceMount:n=r.forceMount,side:a="top",...s}=e,o=ct(Ye,e.__scopeTooltip);return l.jsx(wr,{present:n||o.open,children:o.disableHoverableContent?l.jsx(va,{side:a,...s,ref:t}):l.jsx(Yi,{side:a,...s,ref:t})})}),Yi=R.forwardRef((e,t)=>{const r=ct(Ye,e.__scopeTooltip),n=Cr(Ye,e.__scopeTooltip),a=R.useRef(null),s=Rt(t,a),[o,c]=R.useState(null),{trigger:i,onClose:u}=r,f=a.current,{onPointerInTransitChange:d}=n,m=R.useCallback(()=>{c(null),d(!1)},[d]),g=R.useCallback((p,b)=>{const h=p.currentTarget,w={x:p.clientX,y:p.clientY},y=Zi(w,h.getBoundingClientRect()),v=ec(w,y),S=tc(b.getBoundingClientRect()),C=nc([...v,...S]);c(C),d(!0)},[d]);return R.useEffect(()=>()=>m(),[m]),R.useEffect(()=>{if(i&&f){const p=h=>g(h,f),b=h=>g(h,i);return i.addEventListener("pointerleave",p),f.addEventListener("pointerleave",b),()=>{i.removeEventListener("pointerleave",p),f.removeEventListener("pointerleave",b)}}},[i,f,g,m]),R.useEffect(()=>{if(o){const p=b=>{const h=b.target,w={x:b.clientX,y:b.clientY},y=i?.contains(h)||f?.contains(h),v=!rc(w,o);y?m():v&&(m(),u())};return document.addEventListener("pointermove",p),()=>document.removeEventListener("pointermove",p)}},[i,f,o,u,m]),l.jsx(va,{...e,ref:s})}),[Xi,Ji]=At(st,{isInside:!1}),Qi=Wo("TooltipContent"),va=R.forwardRef((e,t)=>{const{__scopeTooltip:r,children:n,"aria-label":a,onEscapeKeyDown:s,onPointerDownOutside:o,...c}=e,i=ct(Ye,r),u=Pt(r),{onClose:f}=i;return R.useEffect(()=>(document.addEventListener(ir,f),()=>document.removeEventListener(ir,f)),[f]),R.useEffect(()=>{if(i.trigger){const d=m=>{m.target?.contains(i.trigger)&&f()};return window.addEventListener("scroll",d,{capture:!0}),()=>window.removeEventListener("scroll",d,{capture:!0})}},[i.trigger,f]),l.jsx(Go,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:s,onPointerDownOutside:o,onFocusOutside:d=>d.preventDefault(),onDismiss:f,children:l.jsxs(Uo,{"data-state":i.stateAttribute,...u,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[l.jsx(Qi,{children:n}),l.jsx(Xi,{scope:r,isInside:!0,children:l.jsx(Ho,{id:i.contentId,role:"tooltip",children:a||n})})]})})});ma.displayName=Ye;var ga="TooltipArrow",ba=R.forwardRef((e,t)=>{const{__scopeTooltip:r,...n}=e,a=Pt(r);return Ji(ga,r).isInside?null:l.jsx(Vo,{...a,...n,ref:t})});ba.displayName=ga;function Zi(e,t){const r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),a=Math.abs(t.right-e.x),s=Math.abs(t.left-e.x);switch(Math.min(r,n,a,s)){case s:return"left";case a:return"right";case r:return"top";case n:return"bottom";default:throw new Error("unreachable")}}function ec(e,t,r=5){const n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r});break}return n}function tc(e){const{top:t,right:r,bottom:n,left:a}=e;return[{x:a,y:t},{x:r,y:t},{x:r,y:n},{x:a,y:n}]}function rc(e,t){const{x:r,y:n}=e;let a=!1;for(let s=0,o=t.length-1;s<t.length;o=s++){const c=t[s],i=t[o],u=c.x,f=c.y,d=i.x,m=i.y;f>n!=m>n&&r<(d-u)*(n-f)/(m-f)+u&&(a=!a)}return a}function nc(e){const t=e.slice();return t.sort((r,n)=>r.x<n.x?-1:r.x>n.x?1:r.y<n.y?-1:r.y>n.y?1:0),ac(t)}function ac(e){if(e.length<=1)return e.slice();const t=[];for(let n=0;n<e.length;n++){const a=e[n];for(;t.length>=2;){const s=t[t.length-1],o=t[t.length-2];if((s.x-o.x)*(a.y-o.y)>=(s.y-o.y)*(a.x-o.x))t.pop();else break}t.push(a)}t.pop();const r=[];for(let n=e.length-1;n>=0;n--){const a=e[n];for(;r.length>=2;){const s=r[r.length-1],o=r[r.length-2];if((s.x-o.x)*(a.y-o.y)>=(s.y-o.y)*(a.x-o.x))r.pop();else break}r.push(a)}return r.pop(),t.length===1&&r.length===1&&t[0].x===r[0].x&&t[0].y===r[0].y?t:t.concat(r)}var oc=da,sc=fa,ic=pa,cc=ha,lc=ma,uc=ba;function ya({delayDuration:e=0,...t}){return l.jsx(oc,{"data-slot":"tooltip-provider",delayDuration:e,...t})}function dc({...e}){return l.jsx(ya,{children:l.jsx(sc,{"data-slot":"tooltip",...e})})}function fc({...e}){return l.jsx(ic,{"data-slot":"tooltip-trigger",...e})}function pc({className:e,sideOffset:t=0,children:r,...n}){return l.jsx(cc,{children:l.jsxs(lc,{"data-slot":"tooltip-content",sideOffset:t,className:$("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",e),...n,children:[r,l.jsx(uc,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}const hc="sidebar_state",mc=60*60*24*7,vc="16rem",gc="18rem",bc="3rem",yc="b",xa=R.createContext(null);function lt(){const e=R.useContext(xa);if(!e)throw new Error("useSidebar must be used within a SidebarProvider.");return e}function xc({defaultOpen:e=!0,open:t,onOpenChange:r,className:n,style:a,children:s,...o}){const c=Mi(),[i,u]=R.useState(!1),[f,d]=R.useState(e),m=t??f,g=R.useCallback(w=>{const y=typeof w=="function"?w(m):w;r?r(y):d(y),document.cookie=`${hc}=${y}; path=/; max-age=${mc}`},[r,m]),p=R.useCallback(()=>c?u(w=>!w):g(w=>!w),[c,g,u]);R.useEffect(()=>{const w=y=>{y.key===yc&&(y.metaKey||y.ctrlKey)&&(y.preventDefault(),p())};return window.addEventListener("keydown",w),()=>window.removeEventListener("keydown",w)},[p]);const b=m?"expanded":"collapsed",h=R.useMemo(()=>({state:b,open:m,setOpen:g,isMobile:c,openMobile:i,setOpenMobile:u,toggleSidebar:p}),[b,m,g,c,i,u,p]);return l.jsx(xa.Provider,{value:h,children:l.jsx(ya,{delayDuration:0,children:l.jsx("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":vc,"--sidebar-width-icon":bc,...a},className:$("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",n),...o,children:s})})})}function wc({side:e="left",variant:t="sidebar",collapsible:r="offcanvas",className:n,children:a,...s}){const{isMobile:o,state:c,openMobile:i,setOpenMobile:u}=lt();return r==="none"?l.jsx("div",{"data-slot":"sidebar",className:$("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",n),...s,children:a}):o?l.jsx(Li,{open:i,onOpenChange:u,...s,children:l.jsxs(Bi,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":gc},side:e,children:[l.jsxs(Fi,{className:"sr-only",children:[l.jsx(Ki,{children:"Sidebar"}),l.jsx(qi,{children:"Displays the mobile sidebar."})]}),l.jsx("div",{className:"flex h-full w-full flex-col",children:a})]})}):l.jsxs("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":c,"data-collapsible":c==="collapsed"?r:"","data-variant":t,"data-side":e,"data-slot":"sidebar",children:[l.jsx("div",{"data-slot":"sidebar-gap",className:$("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180",t==="floating"||t==="inset"?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),l.jsx("div",{"data-slot":"sidebar-container",className:$("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex",e==="left"?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]",t==="floating"||t==="inset"?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",n),...s,children:l.jsx("div",{"data-sidebar":"sidebar","data-slot":"sidebar-inner",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:a})})]})}function _c({className:e,onClick:t,...r}){const{toggleSidebar:n}=lt();return l.jsxs(Mt,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:$("size-7",e),onClick:a=>{t?.(a),n()},...r,children:[l.jsx(Zs,{}),l.jsx("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function Sc({className:e,...t}){const{toggleSidebar:r}=lt();return l.jsx("button",{"data-sidebar":"rail","data-slot":"sidebar-rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:r,title:"Toggle Sidebar",className:$("hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex","in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",e),...t})}function jc({className:e,...t}){return l.jsx("main",{"data-slot":"sidebar-inset",className:$("bg-background relative flex w-full flex-1 flex-col","md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2",e),...t})}function Oc({className:e,...t}){return l.jsx("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:$("flex flex-col gap-2 p-2",e),...t})}function Ec({className:e,...t}){return l.jsx("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:$("flex flex-col gap-2 p-2",e),...t})}function Cc({className:e,...t}){return l.jsx("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:$("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...t})}function Ic({className:e,...t}){return l.jsx("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:$("relative flex w-full min-w-0 flex-col p-2",e),...t})}function Tc({className:e,asChild:t=!1,...r}){const n=t?it:"div";return l.jsx(n,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:$("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",e),...r})}function of({className:e,...t}){return l.jsx("div",{"data-slot":"sidebar-group-content","data-sidebar":"group-content",className:$("w-full text-sm",e),...t})}function lr({className:e,...t}){return l.jsx("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:$("flex w-full min-w-0 flex-col gap-1",e),...t})}function wt({className:e,...t}){return l.jsx("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:$("group/menu-item relative",e),...t})}const Rc=Sr("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function _t({asChild:e=!1,isActive:t=!1,variant:r="default",size:n="default",tooltip:a,className:s,...o}){const c=e?it:"button",{isMobile:i,state:u}=lt(),f=l.jsx(c,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":n,"data-active":t,className:$(Rc({variant:r,size:n}),s),...o});return a?(typeof a=="string"&&(a={children:a}),l.jsxs(dc,{children:[l.jsx(fc,{asChild:!0,children:f}),l.jsx(pc,{side:"right",align:"center",hidden:u!=="collapsed"||i,...a})]})):f}function Mc({className:e,...t}){return l.jsx("ul",{"data-slot":"sidebar-menu-sub","data-sidebar":"menu-sub",className:$("border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",e),...t})}function Ac({className:e,...t}){return l.jsx("li",{"data-slot":"sidebar-menu-sub-item","data-sidebar":"menu-sub-item",className:$("group/menu-sub-item relative",e),...t})}function Pc({asChild:e=!1,size:t="md",isActive:r=!1,className:n,...a}){const s=e?it:"a";return l.jsx(s,{"data-slot":"sidebar-menu-sub-button","data-sidebar":"menu-sub-button","data-size":t,"data-active":r,className:$("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground",t==="sm"&&"text-xs",t==="md"&&"text-sm","group-data-[collapsible=icon]:hidden",n),...a})}var kc=async(e,t)=>{let r=typeof t=="function"?await t(e):t;if(r)return e.scheme==="bearer"?`Bearer ${r}`:e.scheme==="basic"?`Basic ${btoa(r)}`:r},Ur=(e,t,r)=>{typeof r=="string"||r instanceof Blob?e.append(t,r):e.append(t,JSON.stringify(r))},Nc={bodySerializer:e=>{let t=new FormData;return Object.entries(e).forEach(([r,n])=>{n!=null&&(Array.isArray(n)?n.forEach(a=>Ur(t,r,a)):Ur(t,r,n))}),t}},Dc={bodySerializer:e=>JSON.stringify(e,(t,r)=>typeof r=="bigint"?r.toString():r)},Lc=e=>{switch(e){case"label":return".";case"matrix":return";";case"simple":return",";default:return"&"}},zc=e=>{switch(e){case"form":return",";case"pipeDelimited":return"|";case"spaceDelimited":return"%20";default:return","}},$c=e=>{switch(e){case"label":return".";case"matrix":return";";case"simple":return",";default:return"&"}},wa=({allowReserved:e,explode:t,name:r,style:n,value:a})=>{if(!t){let c=(e?a:a.map(i=>encodeURIComponent(i))).join(zc(n));switch(n){case"label":return`.${c}`;case"matrix":return`;${r}=${c}`;case"simple":return c;default:return`${r}=${c}`}}let s=Lc(n),o=a.map(c=>n==="label"||n==="simple"?e?c:encodeURIComponent(c):kt({allowReserved:e,name:r,value:c})).join(s);return n==="label"||n==="matrix"?s+o:o},kt=({allowReserved:e,name:t,value:r})=>{if(r==null)return"";if(typeof r=="object")throw new Error("Deeply-nested arrays/objects aren’t supported. Provide your own `querySerializer()` to handle these.");return`${t}=${e?r:encodeURIComponent(r)}`},_a=({allowReserved:e,explode:t,name:r,style:n,value:a})=>{if(a instanceof Date)return`${r}=${a.toISOString()}`;if(n!=="deepObject"&&!t){let c=[];Object.entries(a).forEach(([u,f])=>{c=[...c,u,e?f:encodeURIComponent(f)]});let i=c.join(",");switch(n){case"form":return`${r}=${i}`;case"label":return`.${i}`;case"matrix":return`;${r}=${i}`;default:return i}}let s=$c(n),o=Object.entries(a).map(([c,i])=>kt({allowReserved:e,name:n==="deepObject"?`${r}[${c}]`:c,value:i})).join(s);return n==="label"||n==="matrix"?s+o:o},Bc=/\{[^{}]+\}/g,Fc=({path:e,url:t})=>{let r=t,n=t.match(Bc);if(n)for(let a of n){let s=!1,o=a.substring(1,a.length-1),c="simple";o.endsWith("*")&&(s=!0,o=o.substring(0,o.length-1)),o.startsWith(".")?(o=o.substring(1),c="label"):o.startsWith(";")&&(o=o.substring(1),c="matrix");let i=e[o];if(i==null)continue;if(Array.isArray(i)){r=r.replace(a,wa({explode:s,name:o,style:c,value:i}));continue}if(typeof i=="object"){r=r.replace(a,_a({explode:s,name:o,style:c,value:i}));continue}if(c==="matrix"){r=r.replace(a,`;${kt({name:o,value:i})}`);continue}let u=encodeURIComponent(c==="label"?`.${i}`:i);r=r.replace(a,u)}return r},Sa=({allowReserved:e,array:t,object:r}={})=>n=>{let a=[];if(n&&typeof n=="object")for(let s in n){let o=n[s];if(o!=null)if(Array.isArray(o)){let c=wa({allowReserved:e,explode:!0,name:s,style:"form",value:o,...t});c&&a.push(c)}else if(typeof o=="object"){let c=_a({allowReserved:e,explode:!0,name:s,style:"deepObject",value:o,...r});c&&a.push(c)}else{let c=kt({allowReserved:e,name:s,value:o});c&&a.push(c)}}return a.join("&")},Kc=e=>{if(!e)return"stream";let t=e.split(";")[0]?.trim();if(t){if(t.startsWith("application/json")||t.endsWith("+json"))return"json";if(t==="multipart/form-data")return"formData";if(["application/","audio/","image/","video/"].some(r=>t.startsWith(r)))return"blob";if(t.startsWith("text/"))return"text"}},qc=async({security:e,...t})=>{for(let r of e){let n=await kc(r,t.auth);if(!n)continue;let a=r.name??"Authorization";switch(r.in){case"query":t.query||(t.query={}),t.query[a]=n;break;case"cookie":t.headers.append("Cookie",`${a}=${n}`);break;case"header":default:t.headers.set(a,n);break}return}},Wr=e=>Vc({baseUrl:e.baseUrl,path:e.path,query:e.query,querySerializer:typeof e.querySerializer=="function"?e.querySerializer:Sa(e.querySerializer),url:e.url}),Vc=({baseUrl:e,path:t,query:r,querySerializer:n,url:a})=>{let s=a.startsWith("/")?a:`/${a}`,o=(e??"")+s;t&&(o=Fc({path:t,url:o}));let c=r?n(r):"";return c.startsWith("?")&&(c=c.substring(1)),c&&(o+=`?${c}`),o},Hr=(e,t)=>{let r={...e,...t};return r.baseUrl?.endsWith("/")&&(r.baseUrl=r.baseUrl.substring(0,r.baseUrl.length-1)),r.headers=ja(e.headers,t.headers),r},ja=(...e)=>{let t=new Headers;for(let r of e){if(!r||typeof r!="object")continue;let n=r instanceof Headers?r.entries():Object.entries(r);for(let[a,s]of n)if(s===null)t.delete(a);else if(Array.isArray(s))for(let o of s)t.append(a,o);else s!==void 0&&t.set(a,typeof s=="object"?JSON.stringify(s):s)}return t},Ut=class{constructor(){Dr(this,"_fns");this._fns=[]}clear(){this._fns=[]}getInterceptorIndex(e){return typeof e=="number"?this._fns[e]?e:-1:this._fns.indexOf(e)}exists(e){let t=this.getInterceptorIndex(e);return!!this._fns[t]}eject(e){let t=this.getInterceptorIndex(e);this._fns[t]&&(this._fns[t]=null)}update(e,t){let r=this.getInterceptorIndex(e);return this._fns[r]?(this._fns[r]=t,e):!1}use(e){return this._fns=[...this._fns,e],this._fns.length-1}},Gc=()=>({error:new Ut,request:new Ut,response:new Ut}),Uc=Sa({allowReserved:!1,array:{explode:!0,style:"form"},object:{explode:!0,style:"deepObject"}}),Wc={"Content-Type":"application/json"},Oa=(e={})=>({...Dc,headers:Wc,parseAs:"auto",querySerializer:Uc,...e}),Hc=(e={})=>{let t=Hr(Oa(),e),r=()=>({...t}),n=o=>(t=Hr(t,o),r()),a=Gc(),s=async o=>{let c={...t,...o,fetch:o.fetch??t.fetch??globalThis.fetch,headers:ja(t.headers,o.headers)};c.security&&await qc({...c,security:c.security}),c.body&&c.bodySerializer&&(c.body=c.bodySerializer(c.body)),(c.body===void 0||c.body==="")&&c.headers.delete("Content-Type");let i=Wr(c),u={redirect:"follow",...c},f=new Request(i,u);for(let h of a.request._fns)h&&(f=await h(f,c));let d=c.fetch,m=await d(f);for(let h of a.response._fns)h&&(m=await h(m,f,c));let g={request:f,response:m};if(m.ok){if(m.status===204||m.headers.get("Content-Length")==="0")return c.responseStyle==="data"?{}:{data:{},...g};let h=(c.parseAs==="auto"?Kc(m.headers.get("Content-Type")):c.parseAs)??"json";if(h==="stream")return c.responseStyle==="data"?m.body:{data:m.body,...g};let w=await m[h]();return h==="json"&&(c.responseValidator&&await c.responseValidator(w),c.responseTransformer&&(w=await c.responseTransformer(w))),c.responseStyle==="data"?w:{data:w,...g}}let p=await m.text();try{p=JSON.parse(p)}catch{}let b=p;for(let h of a.error._fns)h&&(b=await h(p,m,f,c));if(b=b||{},c.throwOnError)throw b;return c.responseStyle==="data"?void 0:{error:b,...g}};return{buildUrl:Wr,connect:o=>s({...o,method:"CONNECT"}),delete:o=>s({...o,method:"DELETE"}),get:o=>s({...o,method:"GET"}),getConfig:r,head:o=>s({...o,method:"HEAD"}),interceptors:a,options:o=>s({...o,method:"OPTIONS"}),patch:o=>s({...o,method:"PATCH"}),post:o=>s({...o,method:"POST"}),put:o=>s({...o,method:"PUT"}),request:s,setConfig:n,trace:o=>s({...o,method:"TRACE"})}};const Yc=(e,t)=>{const r=document.querySelector('meta[name="csrf-token"]')?.getAttribute("content");return fetch(e,{credentials:"include",duplex:"half",headers:{"Content-Type":"application/json","X-CSRF-TOKEN":r||"","X-Requested-With":"XMLHttpRequest"},...t})},B=Hc(Oa({throwOnError:!0,baseUrl:"/",fetch:Yc})),Xc=e=>(e?.client??B).get({url:"/api/abp/application-configuration",...e}),sf=e=>(e?.client??B).get({url:"/api/active-directory-settings",...e}),cf=e=>(e?.client??B).put({url:"/api/active-directory-settings",...e,headers:{"Content-Type":"application/json",...e?.headers}}),lf=e=>(e?.client??B).post({url:"/api/active-directory-settings/test-connection",...e,headers:{"Content-Type":"application/json",...e?.headers}}),uf=e=>(e?.client??B).post({url:"/api/identity/claim-types/list",...e,headers:{"Content-Type":"application/json",...e?.headers}}),df=e=>(e.client??B).delete({url:"/api/identity/claim-types/{id}",...e}),ff=e=>(e.client??B).put({url:"/api/identity/claim-types/{id}",...e,headers:{"Content-Type":"application/json",...e.headers}}),pf=e=>(e?.client??B).post({url:"/api/identity/claim-types",...e,headers:{"Content-Type":"application/json",...e?.headers}}),hf=e=>(e?.client??B).get({url:"/api/setting-management/emailing",...e}),mf=e=>(e?.client??B).post({url:"/api/setting-management/emailing",...e,headers:{"Content-Type":"application/json",...e?.headers}}),vf=e=>(e?.client??B).post({url:"/api/setting-management/emailing/send-test-email",...e,headers:{"Content-Type":"application/json",...e?.headers}}),gf=e=>(e?.client??B).delete({url:"/api/feature-management/features",...e}),bf=e=>(e?.client??B).get({url:"/api/feature-management/features",...e}),yf=e=>(e?.client??B).put({url:"/api/feature-management/features",...e,headers:{"Content-Type":"application/json",...e?.headers}}),xf=e=>(e?.client??B).post({...Nc,url:"/api/import/csv",...e,headers:{"Content-Type":null,...e?.headers}}),wf=e=>(e?.client??B).get({url:"/api/import/templates",...e}),_f=e=>(e?.client??B).post({url:"/api/openiddict/applications/list",...e,headers:{"Content-Type":"application/json",...e?.headers}}),Sf=e=>(e.client??B).delete({url:"/api/openiddict/applications/{id}",...e}),jf=e=>(e.client??B).put({url:"/api/openiddict/applications/{id}",...e,headers:{"Content-Type":"application/json",...e.headers}}),Of=e=>(e?.client??B).post({url:"/api/openiddict/applications",...e,headers:{"Content-Type":"application/json",...e?.headers}}),Ef=e=>(e?.client??B).get({url:"/api/openiddict/applications/permissions",...e}),Cf=e=>(e?.client??B).get({url:"/api/openiddict/requirements",...e}),If=e=>(e?.client??B).post({url:"/api/openiddict/resources/list",...e,headers:{"Content-Type":"application/json",...e?.headers}}),Tf=e=>(e.client??B).delete({url:"/api/openiddict/resources/{id}",...e}),Rf=e=>(e.client??B).put({url:"/api/openiddict/resources/{id}",...e,headers:{"Content-Type":"application/json",...e.headers}}),Mf=e=>(e?.client??B).post({url:"/api/openiddict/resources",...e,headers:{"Content-Type":"application/json",...e?.headers}}),Af=e=>(e?.client??B).get({url:"/api/openiddict/resources/available-resources",...e}),Pf=e=>(e?.client??B).post({url:"/api/openiddict/scopes/list",...e,headers:{"Content-Type":"application/json",...e?.headers}}),kf=e=>(e.client??B).delete({url:"/api/openiddict/scopes/{id}",...e}),Nf=e=>(e.client??B).put({url:"/api/openiddict/scopes/{id}",...e,headers:{"Content-Type":"application/json",...e.headers}}),Df=e=>(e?.client??B).post({url:"/api/openiddict/scopes",...e,headers:{"Content-Type":"application/json",...e?.headers}}),Lf=e=>(e?.client??B).get({url:"/api/permission-management/permissions",...e}),zf=e=>(e?.client??B).put({url:"/api/permission-management/permissions",...e,headers:{"Content-Type":"application/json",...e?.headers}}),$f=e=>(e?.client??B).get({url:"/api/identity/roles",...e}),Bf=e=>(e?.client??B).post({url:"/api/identity/roles",...e,headers:{"Content-Type":"application/json",...e?.headers}}),Ff=e=>(e.client??B).delete({url:"/api/identity/roles/{id}",...e}),Kf=e=>(e.client??B).put({url:"/api/identity/roles/{id}",...e,headers:{"Content-Type":"application/json",...e.headers}}),qf=e=>(e.client??B).delete({url:"/api/multi-tenancy/tenants/{id}",...e}),Vf=e=>(e.client??B).put({url:"/api/multi-tenancy/tenants/{id}",...e,headers:{"Content-Type":"application/json",...e.headers}}),Gf=e=>(e?.client??B).get({url:"/api/multi-tenancy/tenants",...e}),Uf=e=>(e.client??B).delete({url:"/api/identity/users/{id}",...e}),Wf=e=>(e.client??B).put({url:"/api/identity/users/{id}",...e,headers:{"Content-Type":"application/json",...e.headers}}),Hf=e=>(e?.client??B).get({url:"/api/identity/users",...e}),Yf=e=>(e?.client??B).post({url:"/api/identity/users",...e,headers:{"Content-Type":"application/json",...e?.headers}}),Xf=e=>(e.client??B).get({url:"/api/identity/users/{id}/roles",...e}),Jf=e=>(e?.client??B).get({url:"/api/identity/users/assignable-roles",...e}),Jc={GetUsers:"GetUsers",GetTenants:"GetTenants",GetRoles:"GetRoles",GetAppConfig:"GetAppConfig",GetFeatures:"GetFeatures",GetTranslations:"GetTranslations",GetPermissions:"GetPermissions",GetUserRoles:"GetUserRoles",GetEmailing:"GetEmailing",GetActiveDirectory:"GetActiveDirectory",GetAssignableRoles:"GetAssignableRoles",GetProfile:"GetProfile",GetSession:"GetSession",GetOpeniddictApplications:"GetOpeniddictApplications",GetOpeniddictApplicationsEdit:"GetOpeniddictApplicationsEdit",GetOpeniddictApplicationsPermissions:"GetOpeniddictApplicationsPermissions",GetOpeniddictApplicationsResources:"GetOpeniddictApplicationsResources",GetOpeniddictGrantTypes:"GetOpeniddictGrantTypes",GetOpeniddictResources:"GetOpeniddictResources",GetOpeniddictScopes:"GetOpeniddictScopes",GetIdentityClaims:"GetIdentityClaims",GetIdentityClaimTypes:"GetIdentityClaimTypes",GetOpeniddictResourcesAvailableResources:"GetOpeniddictResourcesAvailableResources",GetOpeniddictScopesAvailableScopes:"GetOpeniddictScopesAvailableScopes",GetOpeniddictRequirements:"GetOpeniddictRequirements"},Ea=()=>co({queryKey:[Jc.GetAppConfig],queryFn:async()=>{const{data:e}=await Xc();return e},staleTime:60*60*1e3}),Qc=()=>{const{data:e}=Ea();return e?.currentUser};function Zc({...e}){return l.jsx(Yo,{"data-slot":"collapsible",...e})}function el({...e}){return l.jsx(Xo,{"data-slot":"collapsible-trigger",...e})}function tl({...e}){return l.jsx(Jo,{"data-slot":"collapsible-content",...e})}const rl={name:"JETTY APPROVAL"},nl=[{title:"Dashboard",url:"/",isActive:!1,icon:wi},{title:"Jetty Application",url:"#",isActive:!1,icon:vi,permission:"IdentityServer.OpenIddictApplications",items:[{title:"New Application",url:"/application/create",permission:"IdentityServer.OpenIddictApplications"},{title:"History",url:"/application",permission:"IdentityServer.OpenIddictScopes"},{title:"Application List",url:"/application/list",permission:"IdentityServer.OpenIddictApplications"}]},{title:"Approval Management",url:"#",isActive:!1,icon:yi,permission:"IdentityServer.OpenIddictApplications",items:[{title:"Incoming Approval",url:"/approval",permission:"IdentityServer.OpenIddictApplications"},{title:"History",url:"/approval/history",permission:"IdentityServer.OpenIddictScopes"}]},{title:"Jetty Operations",url:"#",isActive:!1,icon:Ti,permission:"IdentityServer.OpenIddictApplications",items:[{title:"Jetty Schedule",url:"/jetty/schedule",permission:"IdentityServer.OpenIddictApplications"},{title:"Docked Vessel List",url:"/jetty/docked-vessel",permission:"IdentityServer.OpenIddictScopes"}]},{title:"Master Data",url:"#",isActive:!1,icon:_i,permission:"IdentityServer.OpenIddictApplications",items:[{title:"Manage Jetty",url:"/jetty",permission:"IdentityServer.OpenIddictApplications"}]},{title:"Reports",url:"/report",isActive:!1,icon:Ci,permission:"IdentityServer.ClaimTypes"}];function al(){const[e,t]=R.useState(!1);return R.useEffect(()=>{const r=window.matchMedia("(max-width: 768px)");t(r.matches);const n=a=>{t(a.matches)};return r.addEventListener("change",n),()=>r.removeEventListener("change",n)},[]),{isOpen:e}}function Yr({className:e,...t}){return l.jsx(Qo,{"data-slot":"avatar",className:$("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function Xr({className:e,...t}){return l.jsx(Zo,{"data-slot":"avatar-fallback",className:$("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}function ol({user:e}){const{isMobile:t}=lt(),r=n=>n?n.slice(0,2).toUpperCase():"";return l.jsx(lr,{children:l.jsx(wt,{children:l.jsxs(ni,{children:[l.jsx(ai,{asChild:!0,children:l.jsxs(_t,{size:"lg",className:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",children:[l.jsx(Yr,{className:"h-8 w-8 rounded-lg grayscale",children:l.jsx(Xr,{className:"rounded-lg",children:r(e?.name??"")})}),l.jsxs("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[l.jsx("span",{className:"truncate font-medium",children:e.name}),l.jsx("span",{className:"text-muted-foreground truncate text-xs",children:e.email})]}),l.jsx(Si,{className:"ml-auto size-4"})]})}),l.jsxs(oi,{className:"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg",side:t?"bottom":"right",align:"end",sideOffset:4,children:[l.jsx(ii,{className:"p-0 font-normal",children:l.jsxs("div",{className:"flex items-center gap-2 px-1 py-1.5 text-left text-sm",children:[l.jsx(Yr,{className:"h-8 w-8 rounded-lg",children:l.jsx(Xr,{className:"rounded-lg",children:r(e?.name??"")})}),l.jsxs("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[l.jsx("span",{className:"truncate font-medium",children:e.name}),l.jsx("span",{className:"text-muted-foreground truncate text-xs",children:e.email})]})]})}),l.jsx(Vr,{}),l.jsxs(si,{children:[l.jsxs(vt,{children:[l.jsx(Ri,{}),"Account"]}),l.jsxs(vt,{children:[l.jsx(xi,{}),"Billing"]}),l.jsxs(vt,{children:[l.jsx(Ei,{}),"Notifications"]})]}),l.jsx(Vr,{}),l.jsxs(vt,{children:[l.jsx(Oi,{}),"Log out"]})]})]})})})}function sl(){const e=Qc(),{url:t}=Hn(),r=t,{isOpen:n}=al();return R.useEffect(()=>{},[n]),l.jsxs(wc,{collapsible:"icon",children:[l.jsx(Oc,{children:l.jsx(lr,{children:l.jsx(wt,{children:l.jsx(_t,{asChild:!0,className:"data-[slot=sidebar-menu-button]:!p-1.5",children:l.jsxs("a",{href:"#",children:[l.jsx(ji,{className:"!size-5"}),l.jsx("span",{className:"text-base font-semibold",children:rl.name})]})})})})}),l.jsx(Cc,{className:"overflow-x-hidden",children:l.jsxs(Ic,{children:[l.jsx(Tc,{children:"Overview"}),l.jsx(lr,{children:nl.map(a=>{const s=a.items?.some(o=>r.startsWith(o.url));return a?.items&&a?.items?.length>0?l.jsx(Zc,{asChild:!0,defaultOpen:s||a.isActive,className:"group/collapsible",children:l.jsxs(wt,{children:[l.jsx(el,{asChild:!0,children:l.jsxs(_t,{tooltip:a.title,isActive:r===a.url,children:[a.icon&&l.jsx(a.icon,{}),l.jsx("span",{children:a.title}),l.jsx(bi,{className:"ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90"})]})}),l.jsx(tl,{children:l.jsx(Mc,{children:a.items?.map(o=>l.jsx(Ac,{children:l.jsx(Pc,{asChild:!0,isActive:r===o.url,children:l.jsx(Ct,{href:o.url,children:l.jsx("span",{children:o.title})})})},o.title))})})]})},a.title):l.jsx(wt,{children:l.jsx(_t,{asChild:!0,tooltip:a.title,isActive:r===a.url,children:l.jsxs(Ct,{href:a.url,children:[l.jsx(a.icon,{}),l.jsx("span",{children:a.title})]})})},a.title)})})]})}),l.jsx(Ec,{children:e&&l.jsx(ol,{user:e})}),l.jsx(Sc,{})]})}function il({...e}){return l.jsx("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",...e})}function cl({className:e,...t}){return l.jsx("ol",{"data-slot":"breadcrumb-list",className:$("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",e),...t})}function Jr({className:e,...t}){return l.jsx("li",{"data-slot":"breadcrumb-item",className:$("inline-flex items-center gap-1.5",e),...t})}function Qr({asChild:e,className:t,...r}){const n=e?it:"a";return l.jsx(n,{"data-slot":"breadcrumb-link",className:$("hover:text-foreground transition-colors",t),...r})}function ll({className:e,...t}){return l.jsx("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",className:$("text-foreground font-normal",e),...t})}function Zr({children:e,className:t,...r}){return l.jsx("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",className:$("[&>svg]:size-3.5",t),...r,children:e??l.jsx(Ys,{})})}function ul(){const{url:e}=Hn();if(e==="/")return null;const t=e.split("/").filter(Boolean),r=n=>n.split("-").map(a=>a.charAt(0).toUpperCase()+a.slice(1)).join(" ");return l.jsx(il,{children:l.jsxs(cl,{children:[l.jsx(Jr,{children:l.jsx(Qr,{asChild:!0,children:l.jsx(Ct,{href:"/",children:"Home"})})}),l.jsx(Zr,{}),t.map((n,a)=>{const s=`/${t.slice(0,a+1).join("/")}`,o=a===t.length-1;return l.jsxs(R.Fragment,{children:[l.jsx(Jr,{children:o?l.jsx(ll,{children:r(n)}):l.jsx(Qr,{asChild:!0,children:l.jsx(Ct,{href:s,children:r(n)})})}),!o&&l.jsx(Zr,{})]},s)})]})})}var ze={},q={},en;function Ge(){if(en)return q;en=1;var e=q&&q.__assign||function(){return e=Object.assign||function(y){for(var v,S=1,C=arguments.length;S<C;S++){v=arguments[S];for(var T in v)Object.prototype.hasOwnProperty.call(v,T)&&(y[T]=v[T])}return y},e.apply(this,arguments)},t=q&&q.__createBinding||(Object.create?function(y,v,S,C){C===void 0&&(C=S),Object.defineProperty(y,C,{enumerable:!0,get:function(){return v[S]}})}:function(y,v,S,C){C===void 0&&(C=S),y[C]=v[S]}),r=q&&q.__setModuleDefault||(Object.create?function(y,v){Object.defineProperty(y,"default",{enumerable:!0,value:v})}:function(y,v){y.default=v}),n=q&&q.__importStar||function(y){if(y&&y.__esModule)return y;var v={};if(y!=null)for(var S in y)S!=="default"&&Object.prototype.hasOwnProperty.call(y,S)&&t(v,y,S);return r(v,y),v},a=q&&q.__spreadArray||function(y,v,S){if(S||arguments.length===2)for(var C=0,T=v.length,E;C<T;C++)(E||!(C in v))&&(E||(E=Array.prototype.slice.call(v,0,C)),E[C]=v[C]);return y.concat(E||Array.prototype.slice.call(v))};Object.defineProperty(q,"__esModule",{value:!0}),q.Priority=q.isModKey=q.shouldRejectKeystrokes=q.useThrottledValue=q.getScrollbarWidth=q.useIsomorphicLayout=q.noop=q.createAction=q.randomId=q.usePointerMovedSinceMount=q.useOuterClick=q.swallowEvent=void 0;var s=n(ne());function o(y){y.stopPropagation(),y.preventDefault()}q.swallowEvent=o;function c(y,v){var S=s.useRef(v);S.current=v,s.useEffect(function(){function C(T){var E,O;!((E=y.current)===null||E===void 0)&&E.contains(T.target)||T.target===((O=y.current)===null||O===void 0?void 0:O.getRootNode().host)||(T.preventDefault(),T.stopPropagation(),S.current())}return window.addEventListener("pointerdown",C,!0),function(){return window.removeEventListener("pointerdown",C,!0)}},[y])}q.useOuterClick=c;function i(){var y=s.useState(!1),v=y[0],S=y[1];return s.useEffect(function(){function C(){S(!0)}if(!v)return window.addEventListener("pointermove",C),function(){return window.removeEventListener("pointermove",C)}},[v]),v}q.usePointerMovedSinceMount=i;function u(){return Math.random().toString(36).substring(2,9)}q.randomId=u;function f(y){return e({id:u()},y)}q.createAction=f;function d(){}q.noop=d,q.useIsomorphicLayout=typeof window>"u"?d:s.useLayoutEffect;function m(){var y=document.createElement("div");y.style.visibility="hidden",y.style.overflow="scroll",document.body.appendChild(y);var v=document.createElement("div");y.appendChild(v);var S=y.offsetWidth-v.offsetWidth;return y.parentNode.removeChild(y),S}q.getScrollbarWidth=m;function g(y,v){v===void 0&&(v=100);var S=s.useState(y),C=S[0],T=S[1],E=s.useRef(Date.now());return s.useEffect(function(){if(v!==0){var O=setTimeout(function(){T(y),E.current=Date.now()},E.current-(Date.now()-v));return function(){clearTimeout(O)}}},[v,y]),v===0?y:C}q.useThrottledValue=g;function p(y){var v,S,C,T=y===void 0?{ignoreWhenFocused:[]}:y,E=T.ignoreWhenFocused,O=a(["input","textarea"],E,!0).map(function(_){return _.toLowerCase()}),I=document.activeElement,x=I&&(O.indexOf(I.tagName.toLowerCase())!==-1||((v=I.attributes.getNamedItem("role"))===null||v===void 0?void 0:v.value)==="textbox"||((S=I.attributes.getNamedItem("contenteditable"))===null||S===void 0?void 0:S.value)==="true"||((C=I.attributes.getNamedItem("contenteditable"))===null||C===void 0?void 0:C.value)==="plaintext-only");return x}q.shouldRejectKeystrokes=p;var b=typeof window>"u",h=!b&&window.navigator.platform==="MacIntel";function w(y){return h?y.metaKey:y.ctrlKey}return q.isModKey=w,q.Priority={HIGH:1,NORMAL:0,LOW:-1},q}var ve={},le={},je={},se={},nt={exports:{}},dl=nt.exports,tn;function fl(){return tn||(tn=1,function(e,t){(function(r,n){n(t)})(dl,function(r){var n=typeof WeakSet=="function",a=Object.keys;function s(x,_){return x===_||x!==x&&_!==_}function o(x){return x.constructor===Object||x.constructor==null}function c(x){return!!x&&typeof x.then=="function"}function i(x){return!!(x&&x.$$typeof)}function u(){var x=[];return{add:function(_){x.push(_)},has:function(_){return x.indexOf(_)!==-1}}}var f=function(x){return x?function(){return new WeakSet}:u}(n);function d(x){return function(P){var M=x||P;return function(N,k,D){D===void 0&&(D=f());var A=!!N&&typeof N=="object",V=!!k&&typeof k=="object";if(A||V){var W=A&&D.has(N),Q=V&&D.has(k);if(W||Q)return W&&Q;A&&D.add(N),V&&D.add(k)}return M(N,k,D)}}}function m(x,_,P,M){var j=x.length;if(_.length!==j)return!1;for(;j-- >0;)if(!P(x[j],_[j],M))return!1;return!0}function g(x,_,P,M){var j=x.size===_.size;if(j&&x.size){var N={};x.forEach(function(k,D){if(j){var A=!1,V=0;_.forEach(function(W,Q){!A&&!N[V]&&(A=P(D,Q,M)&&P(k,W,M),A&&(N[V]=!0)),V++}),j=A}})}return j}var p="_owner",b=Function.prototype.bind.call(Function.prototype.call,Object.prototype.hasOwnProperty);function h(x,_,P,M){var j=a(x),N=j.length;if(a(_).length!==N)return!1;if(N)for(var k=void 0;N-- >0;){if(k=j[N],k===p){var D=i(x),A=i(_);if((D||A)&&D!==A)return!1}if(!b(_,k)||!P(x[k],_[k],M))return!1}return!0}function w(x,_){return x.source===_.source&&x.global===_.global&&x.ignoreCase===_.ignoreCase&&x.multiline===_.multiline&&x.unicode===_.unicode&&x.sticky===_.sticky&&x.lastIndex===_.lastIndex}function y(x,_,P,M){var j=x.size===_.size;if(j&&x.size){var N={};x.forEach(function(k){if(j){var D=!1,A=0;_.forEach(function(V){!D&&!N[A]&&(D=P(k,V,M),D&&(N[A]=!0)),A++}),j=D}})}return j}var v=typeof Map=="function",S=typeof Set=="function";function C(x){var _=typeof x=="function"?x(P):P;function P(M,j,N){if(M===j)return!0;if(M&&j&&typeof M=="object"&&typeof j=="object"){if(o(M)&&o(j))return h(M,j,_,N);var k=Array.isArray(M),D=Array.isArray(j);return k||D?k===D&&m(M,j,_,N):(k=M instanceof Date,D=j instanceof Date,k||D?k===D&&s(M.getTime(),j.getTime()):(k=M instanceof RegExp,D=j instanceof RegExp,k||D?k===D&&w(M,j):c(M)||c(j)?M===j:v&&(k=M instanceof Map,D=j instanceof Map,k||D)?k===D&&g(M,j,_,N):S&&(k=M instanceof Set,D=j instanceof Set,k||D)?k===D&&y(M,j,_,N):h(M,j,_,N)))}return M!==M&&j!==j}return P}var T=C(),E=C(function(){return s}),O=C(d()),I=C(d(s));r.circularDeepEqual=O,r.circularShallowEqual=I,r.createCustomEqual=C,r.deepEqual=T,r.sameValueZeroEqual=s,r.shallowEqual=E,Object.defineProperty(r,"__esModule",{value:!0})})}(nt,nt.exports)),nt.exports}var Wt,rn;function Tr(){if(rn)return Wt;rn=1;var e="Invariant failed";function t(r,n){if(!r)throw new Error(e)}return Wt=t,Wt}var Oe={},$e={},rt={},nn;function pl(){if(nn)return rt;nn=1,Object.defineProperty(rt,"__esModule",{value:!0}),rt.Command=void 0;var e=function(){function t(r,n){var a=this;n===void 0&&(n={}),this.perform=function(){var s=r.perform();if(typeof s=="function"){var o=n.history;o&&(a.historyItem&&o.remove(a.historyItem),a.historyItem=o.add({perform:r.perform,negate:s}),a.history={undo:function(){return o.undo(a.historyItem)},redo:function(){return o.redo(a.historyItem)}})}}}return t}();return rt.Command=e,rt}var an;function Ca(){if(an)return $e;an=1;var e=$e&&$e.__importDefault||function(o){return o&&o.__esModule?o:{default:o}};Object.defineProperty($e,"__esModule",{value:!0}),$e.ActionImpl=void 0;var t=e(Tr()),r=pl(),n=Ge(),a=function(o){var c=o.keywords,i=c===void 0?"":c,u=o.section,f=u===void 0?"":u;return(i+" "+(typeof f=="string"?f:f.name)).trim()},s=function(){function o(c,i){var u=this,f;this.priority=n.Priority.NORMAL,this.ancestors=[],this.children=[],Object.assign(this,c),this.id=c.id,this.name=c.name,this.keywords=a(c);var d=c.perform;if(this.command=d&&new r.Command({perform:function(){return d(u)}},{history:i.history}),this.perform=(f=this.command)===null||f===void 0?void 0:f.perform,c.parent){var m=i.store[c.parent];(0,t.default)(m,"attempted to create an action whos parent: "+c.parent+" does not exist in the store."),m.addChild(this)}}return o.prototype.addChild=function(c){c.ancestors.unshift(this);for(var i=this.parentActionImpl;i;)c.ancestors.unshift(i),i=i.parentActionImpl;this.children.push(c)},o.prototype.removeChild=function(c){var i=this,u=this.children.indexOf(c);u!==-1&&this.children.splice(u,1),c.children&&c.children.forEach(function(f){i.removeChild(f)})},Object.defineProperty(o.prototype,"parentActionImpl",{get:function(){return this.ancestors[this.ancestors.length-1]},enumerable:!1,configurable:!0}),o.create=function(c,i){return new o(c,i)},o}();return $e.ActionImpl=s,$e}var on;function Ia(){if(on)return Oe;on=1;var e=Oe&&Oe.__assign||function(){return e=Object.assign||function(s){for(var o,c=1,i=arguments.length;c<i;c++){o=arguments[c];for(var u in o)Object.prototype.hasOwnProperty.call(o,u)&&(s[u]=o[u])}return s},e.apply(this,arguments)},t=Oe&&Oe.__importDefault||function(s){return s&&s.__esModule?s:{default:s}};Object.defineProperty(Oe,"__esModule",{value:!0}),Oe.ActionInterface=void 0;var r=t(Tr()),n=Ca(),a=function(){function s(o,c){o===void 0&&(o=[]),c===void 0&&(c={}),this.actions={},this.options=c,this.add(o)}return s.prototype.add=function(o){for(var c=0;c<o.length;c++){var i=o[c];i.parent&&(0,r.default)(this.actions[i.parent],'Attempted to create action "'+i.name+'" without registering its parent "'+i.parent+'" first.'),this.actions[i.id]=n.ActionImpl.create(i,{history:this.options.historyManager,store:this.actions})}return e({},this.actions)},s.prototype.remove=function(o){var c=this;return o.forEach(function(i){var u=c.actions[i.id];if(u){for(var f=u.children;f.length;){var d=f.pop();if(!d)return;delete c.actions[d.id],d.parentActionImpl&&d.parentActionImpl.removeChild(d),d.children&&f.push.apply(f,d.children)}u.parentActionImpl&&u.parentActionImpl.removeChild(u),delete c.actions[i.id]}}),e({},this.actions)},s}();return Oe.ActionInterface=a,Oe}var Be={},sn;function hl(){if(sn)return Be;sn=1,Object.defineProperty(Be,"__esModule",{value:!0}),Be.history=Be.HistoryItemImpl=void 0;var e=Ge(),t=function(){function a(s){this.perform=s.perform,this.negate=s.negate}return a.create=function(s){return new a(s)},a}();Be.HistoryItemImpl=t;var r=function(){function a(){return this.undoStack=[],this.redoStack=[],a.instance||(a.instance=this,this.init()),a.instance}return a.prototype.init=function(){var s=this;typeof window>"u"||window.addEventListener("keydown",function(o){var c;if(!(!s.redoStack.length&&!s.undoStack.length||(0,e.shouldRejectKeystrokes)())){var i=(c=o.key)===null||c===void 0?void 0:c.toLowerCase();o.metaKey&&i==="z"&&o.shiftKey?s.redo():o.metaKey&&i==="z"&&s.undo()}})},a.prototype.add=function(s){var o=t.create(s);return this.undoStack.push(o),o},a.prototype.remove=function(s){var o=this.undoStack.findIndex(function(i){return i===s});if(o!==-1){this.undoStack.splice(o,1);return}var c=this.redoStack.findIndex(function(i){return i===s});c!==-1&&this.redoStack.splice(c,1)},a.prototype.undo=function(s){if(!s){var o=this.undoStack.pop();return o?(o?.negate(),this.redoStack.push(o),o):void 0}var c=this.undoStack.findIndex(function(i){return i===s});if(c!==-1)return this.undoStack.splice(c,1),s.negate(),this.redoStack.push(s),s},a.prototype.redo=function(s){if(!s){var o=this.redoStack.pop();return o?(o?.perform(),this.undoStack.push(o),o):void 0}var c=this.redoStack.findIndex(function(i){return i===s});if(c!==-1)return this.redoStack.splice(c,1),s.perform(),this.undoStack.push(s),s},a.prototype.reset=function(){this.undoStack.splice(0),this.redoStack.splice(0)},a}(),n=new r;return Be.history=n,Object.freeze(n),Be}var Ht={},cn;function Qe(){return cn||(cn=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.VisualState=void 0,function(t){t.animatingIn="animating-in",t.showing="showing",t.animatingOut="animating-out",t.hidden="hidden"}(e.VisualState||(e.VisualState={}))}(Ht)),Ht}var ln;function ml(){if(ln)return se;ln=1;var e=se&&se.__assign||function(){return e=Object.assign||function(p){for(var b,h=1,w=arguments.length;h<w;h++){b=arguments[h];for(var y in b)Object.prototype.hasOwnProperty.call(b,y)&&(p[y]=b[y])}return p},e.apply(this,arguments)},t=se&&se.__createBinding||(Object.create?function(p,b,h,w){w===void 0&&(w=h),Object.defineProperty(p,w,{enumerable:!0,get:function(){return b[h]}})}:function(p,b,h,w){w===void 0&&(w=h),p[w]=b[h]}),r=se&&se.__setModuleDefault||(Object.create?function(p,b){Object.defineProperty(p,"default",{enumerable:!0,value:b})}:function(p,b){p.default=b}),n=se&&se.__importStar||function(p){if(p&&p.__esModule)return p;var b={};if(p!=null)for(var h in p)h!=="default"&&Object.prototype.hasOwnProperty.call(p,h)&&t(b,p,h);return r(b,p),b},a=se&&se.__importDefault||function(p){return p&&p.__esModule?p:{default:p}};Object.defineProperty(se,"__esModule",{value:!0}),se.useStore=void 0;var s=fl(),o=n(ne()),c=a(Tr()),i=Ia(),u=hl(),f=Qe();function d(p){var b=o.useRef(e({animations:{enterMs:200,exitMs:100}},p.options)),h=o.useMemo(function(){return new i.ActionInterface(p.actions||[],{historyManager:b.current.enableHistory?u.history:void 0})},[]),w=o.useState({searchQuery:"",currentRootActionId:null,visualState:f.VisualState.hidden,actions:e({},h.actions),activeIndex:0,disabled:!1}),y=w[0],v=w[1],S=o.useRef(y);S.current=y;var C=o.useCallback(function(){return S.current},[]),T=o.useMemo(function(){return new m(C)},[C]);o.useEffect(function(){S.current=y,T.notify()},[y,T]);var E=o.useCallback(function(I){return v(function(x){return e(e({},x),{actions:h.add(I)})}),function(){v(function(_){return e(e({},_),{actions:h.remove(I)})})}},[h]),O=o.useRef(null);return o.useMemo(function(){var I={setCurrentRootAction:function(x){v(function(_){return e(e({},_),{currentRootActionId:x})})},setVisualState:function(x){v(function(_){return e(e({},_),{visualState:typeof x=="function"?x(_.visualState):x})})},setSearch:function(x){return v(function(_){return e(e({},_),{searchQuery:x})})},registerActions:E,toggle:function(){return v(function(x){return e(e({},x),{visualState:[f.VisualState.animatingOut,f.VisualState.hidden].includes(x.visualState)?f.VisualState.animatingIn:f.VisualState.animatingOut})})},setActiveIndex:function(x){return v(function(_){return e(e({},_),{activeIndex:typeof x=="number"?x:x(_.activeIndex)})})},inputRefSetter:function(x){O.current=x},getInput:function(){return(0,c.default)(O.current,"Input ref is undefined, make sure you attach `query.inputRefSetter` to your search input."),O.current},disable:function(x){v(function(_){return e(e({},_),{disabled:x})})}};return{getState:C,query:I,options:b.current,subscribe:function(x,_){return T.subscribe(x,_)}}},[C,T,E])}se.useStore=d;var m=function(){function p(b){this.subscribers=[],this.getState=b}return p.prototype.subscribe=function(b,h){var w=this,y=new g(function(){return b(w.getState())},h);return this.subscribers.push(y),this.unsubscribe.bind(this,y)},p.prototype.unsubscribe=function(b){if(this.subscribers.length){var h=this.subscribers.indexOf(b);if(h>-1)return this.subscribers.splice(h,1)}},p.prototype.notify=function(){this.subscribers.forEach(function(b){return b.collect()})},p}(),g=function(){function p(b,h){this.collector=b,this.onChange=h}return p.prototype.collect=function(){try{var b=this.collector();(0,s.deepEqual)(b,this.collected)||(this.collected=b,this.onChange&&this.onChange(this.collected))}catch{}},p}();return se}var ue={},gt={},un;function vl(){if(un)return gt;un=1,Object.defineProperty(gt,"__esModule",{value:!0});var e=["Shift","Meta","Alt","Control"],t=1e3,r="keydown",n=typeof navigator=="object"&&/Mac|iPod|iPhone|iPad/.test(navigator.platform)?"Meta":"Control";function a(i,u){return typeof i.getModifierState=="function"?i.getModifierState(u):!1}function s(i){return i.trim().split(" ").map(function(u){var f=u.split(/\b\+/),d=f.pop();return f=f.map(function(m){return m==="$mod"?n:m}),[f,d]})}function o(i,u){return/^[^A-Za-z0-9]$/.test(i.key)&&u[1]===i.key?!0:!(u[1].toUpperCase()!==i.key.toUpperCase()&&u[1]!==i.code||u[0].find(function(f){return!a(i,f)})||e.find(function(f){return!u[0].includes(f)&&u[1]!==f&&a(i,f)}))}function c(i,u,f){var d,m;f===void 0&&(f={});var g=(d=f.timeout)!==null&&d!==void 0?d:t,p=(m=f.event)!==null&&m!==void 0?m:r,b=Object.keys(u).map(function(v){return[s(v),u[v]]}),h=new Map,w=null,y=function(v){v instanceof KeyboardEvent&&(b.forEach(function(S){var C=S[0],T=S[1],E=h.get(C),O=E||C,I=O[0],x=o(v,I);x?O.length>1?h.set(C,O.slice(1)):(h.delete(C),T(v)):a(v,v.key)||h.delete(C)}),w&&clearTimeout(w),w=setTimeout(h.clear.bind(h),g))};return i.addEventListener(p,y),function(){i.removeEventListener(p,y)}}return gt.default=c,gt}var dn;function gl(){if(dn)return ue;dn=1;var e=ue&&ue.__createBinding||(Object.create?function(h,w,y,v){v===void 0&&(v=y),Object.defineProperty(h,v,{enumerable:!0,get:function(){return w[y]}})}:function(h,w,y,v){v===void 0&&(v=y),h[v]=w[y]}),t=ue&&ue.__setModuleDefault||(Object.create?function(h,w){Object.defineProperty(h,"default",{enumerable:!0,value:w})}:function(h,w){h.default=w}),r=ue&&ue.__importStar||function(h){if(h&&h.__esModule)return h;var w={};if(h!=null)for(var y in h)y!=="default"&&Object.prototype.hasOwnProperty.call(h,y)&&e(w,h,y);return t(w,h),w},n=ue&&ue.__importDefault||function(h){return h&&h.__esModule?h:{default:h}};Object.defineProperty(ue,"__esModule",{value:!0}),ue.InternalEvents=void 0;var a=r(ne()),s=n(vl()),o=Qe(),c=Ae(),i=Ge();function u(){return f(),d(),p(),b(),null}ue.InternalEvents=u;function f(){var h,w,y=(0,c.useKBar)(function(x){return{visualState:x.visualState,showing:x.visualState!==o.VisualState.hidden,disabled:x.disabled}}),v=y.query,S=y.options,C=y.visualState,T=y.showing,E=y.disabled;a.useEffect(function(){var x,_=function(){v.setVisualState(function(j){return j===o.VisualState.hidden||j===o.VisualState.animatingOut?j:o.VisualState.animatingOut})};if(E){_();return}var P=S.toggleShortcut||"$mod+k",M=(0,s.default)(window,(x={},x[P]=function(j){var N,k,D,A;j.defaultPrevented||(j.preventDefault(),v.toggle(),T?(k=(N=S.callbacks)===null||N===void 0?void 0:N.onClose)===null||k===void 0||k.call(N):(A=(D=S.callbacks)===null||D===void 0?void 0:D.onOpen)===null||A===void 0||A.call(D))},x.Escape=function(j){var N,k;T&&(j.stopPropagation(),j.preventDefault(),(k=(N=S.callbacks)===null||N===void 0?void 0:N.onClose)===null||k===void 0||k.call(N)),_()},x));return function(){M()}},[S.callbacks,S.toggleShortcut,v,T,E]);var O=a.useRef(),I=a.useCallback(function(x){var _,P,M=0;x===o.VisualState.animatingIn&&(M=((_=S.animations)===null||_===void 0?void 0:_.enterMs)||0),x===o.VisualState.animatingOut&&(M=((P=S.animations)===null||P===void 0?void 0:P.exitMs)||0),clearTimeout(O.current),O.current=setTimeout(function(){var j=!1;v.setVisualState(function(){var N=x===o.VisualState.animatingIn?o.VisualState.showing:o.VisualState.hidden;return N===o.VisualState.hidden&&(j=!0),N}),j&&v.setCurrentRootAction(null)},M)},[(h=S.animations)===null||h===void 0?void 0:h.enterMs,(w=S.animations)===null||w===void 0?void 0:w.exitMs,v]);a.useEffect(function(){switch(C){case o.VisualState.animatingIn:case o.VisualState.animatingOut:I(C);break}},[I,C])}function d(){var h=(0,c.useKBar)(function(v){return{visualState:v.visualState}}),w=h.visualState,y=h.options;a.useEffect(function(){if(!y.disableDocumentLock)if(w===o.VisualState.animatingIn){if(document.body.style.overflow="hidden",!y.disableScrollbarManagement){var v=(0,i.getScrollbarWidth)(),S=getComputedStyle(document.body)["margin-right"];S&&(v+=Number(S.replace(/\D/g,""))),document.body.style.marginRight=v+"px"}}else w===o.VisualState.hidden&&(document.body.style.removeProperty("overflow"),y.disableScrollbarManagement||document.body.style.removeProperty("margin-right"))},[y.disableDocumentLock,y.disableScrollbarManagement,w])}var m=new WeakSet;function g(h){return function(w){m.has(w)||(h(w),m.add(w))}}function p(){var h=(0,c.useKBar)(function(T){return{actions:T.actions,open:T.visualState===o.VisualState.showing,disabled:T.disabled}}),w=h.actions,y=h.query,v=h.open,S=h.options,C=h.disabled;a.useEffect(function(){var T;if(!(v||C)){for(var E=Object.keys(w).map(function(D){return w[D]}),O=[],I=0,x=E;I<x.length;I++){var _=x[I];!((T=_.shortcut)===null||T===void 0)&&T.length&&O.push(_)}O=O.sort(function(D,A){return A.shortcut.join(" ").length-D.shortcut.join(" ").length});for(var P={},M=function(D){var A=D.shortcut.join(" ");P[A]=g(function(V){var W,Q,ee,G,U,oe;(0,i.shouldRejectKeystrokes)()||(V.preventDefault(),!((W=D.children)===null||W===void 0)&&W.length?(y.setCurrentRootAction(D.id),y.toggle(),(ee=(Q=S.callbacks)===null||Q===void 0?void 0:Q.onOpen)===null||ee===void 0||ee.call(Q)):((G=D.command)===null||G===void 0||G.perform(),(oe=(U=S.callbacks)===null||U===void 0?void 0:U.onSelectAction)===null||oe===void 0||oe.call(U,D)))})},j=0,N=O;j<N.length;j++){var _=N[j];M(_)}var k=(0,s.default)(window,P,{timeout:400});return function(){k()}}},[w,v,S.callbacks,y,C])}function b(){var h=a.useRef(!0),w=(0,c.useKBar)(function(C){return{isShowing:C.visualState===o.VisualState.showing||C.visualState===o.VisualState.animatingIn}}),y=w.isShowing,v=w.query,S=a.useRef(null);a.useEffect(function(){if(h.current){h.current=!1;return}if(y){S.current=document.activeElement;return}var C=document.activeElement;C?.tagName.toLowerCase()==="input"&&C.blur();var T=S.current;T&&T!==C&&T.focus()},[y]),a.useEffect(function(){function C(T){var E=v.getInput();T.target!==E&&E.focus()}if(y)return window.addEventListener("keydown",C),function(){window.removeEventListener("keydown",C)}},[y,v])}return ue}var fn;function Ta(){return fn||(fn=1,function(e){var t=je&&je.__createBinding||(Object.create?function(i,u,f,d){d===void 0&&(d=f),Object.defineProperty(i,d,{enumerable:!0,get:function(){return u[f]}})}:function(i,u,f,d){d===void 0&&(d=f),i[d]=u[f]}),r=je&&je.__setModuleDefault||(Object.create?function(i,u){Object.defineProperty(i,"default",{enumerable:!0,value:u})}:function(i,u){i.default=u}),n=je&&je.__importStar||function(i){if(i&&i.__esModule)return i;var u={};if(i!=null)for(var f in i)f!=="default"&&Object.prototype.hasOwnProperty.call(i,f)&&t(u,i,f);return r(u,i),u};Object.defineProperty(e,"__esModule",{value:!0}),e.KBarProvider=e.KBarContext=void 0;var a=ml(),s=n(ne()),o=gl();e.KBarContext=s.createContext({});var c=function(i){var u=(0,a.useStore)(i);return s.createElement(e.KBarContext.Provider,{value:u},s.createElement(o.InternalEvents,null),i.children)};e.KBarProvider=c}(je)),je}var pn;function Ae(){if(pn)return le;pn=1;var e=le&&le.__assign||function(){return e=Object.assign||function(c){for(var i,u=1,f=arguments.length;u<f;u++){i=arguments[u];for(var d in i)Object.prototype.hasOwnProperty.call(i,d)&&(c[d]=i[d])}return c},e.apply(this,arguments)},t=le&&le.__createBinding||(Object.create?function(c,i,u,f){f===void 0&&(f=u),Object.defineProperty(c,f,{enumerable:!0,get:function(){return i[u]}})}:function(c,i,u,f){f===void 0&&(f=u),c[f]=i[u]}),r=le&&le.__setModuleDefault||(Object.create?function(c,i){Object.defineProperty(c,"default",{enumerable:!0,value:i})}:function(c,i){c.default=i}),n=le&&le.__importStar||function(c){if(c&&c.__esModule)return c;var i={};if(c!=null)for(var u in c)u!=="default"&&Object.prototype.hasOwnProperty.call(c,u)&&t(i,c,u);return r(i,c),i};Object.defineProperty(le,"__esModule",{value:!0}),le.useKBar=void 0;var a=n(ne()),s=Ta();function o(c){var i=a.useContext(s.KBarContext),u=i.query,f=i.getState,d=i.subscribe,m=i.options,g=a.useRef(c?.(f())),p=a.useRef(c),b=a.useCallback(function(v){return e(e({},v),{query:u,options:m})},[u,m]),h=a.useState(b(g.current)),w=h[0],y=h[1];return a.useEffect(function(){var v;return p.current&&(v=d(function(S){return p.current(S)},function(S){return y(b(S))})),function(){v&&v()}},[b,d]),w}return le.useKBar=o,le}function Ee(e){return Array.isArray?Array.isArray(e):Aa(e)==="[object Array]"}function bl(e){if(typeof e=="string")return e;let t=e+"";return t=="0"&&1/e==-1/0?"-0":t}function yl(e){return e==null?"":bl(e)}function we(e){return typeof e=="string"}function Ra(e){return typeof e=="number"}function xl(e){return e===!0||e===!1||wl(e)&&Aa(e)=="[object Boolean]"}function Ma(e){return typeof e=="object"}function wl(e){return Ma(e)&&e!==null}function he(e){return e!=null}function Yt(e){return!e.trim().length}function Aa(e){return e==null?e===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}const _l="Incorrect 'index' type",Sl=e=>`Invalid value for key ${e}`,jl=e=>`Pattern length exceeds max of ${e}.`,Ol=e=>`Missing ${e} property in key`,El=e=>`Property 'weight' in key '${e}' must be a positive integer`,hn=Object.prototype.hasOwnProperty;class Cl{constructor(t){this._keys=[],this._keyMap={};let r=0;t.forEach(n=>{let a=Pa(n);r+=a.weight,this._keys.push(a),this._keyMap[a.id]=a,r+=a.weight}),this._keys.forEach(n=>{n.weight/=r})}get(t){return this._keyMap[t]}keys(){return this._keys}toJSON(){return JSON.stringify(this._keys)}}function Pa(e){let t=null,r=null,n=null,a=1,s=null;if(we(e)||Ee(e))n=e,t=mn(e),r=ur(e);else{if(!hn.call(e,"name"))throw new Error(Ol("name"));const o=e.name;if(n=o,hn.call(e,"weight")&&(a=e.weight,a<=0))throw new Error(El(o));t=mn(o),r=ur(o),s=e.getFn}return{path:t,id:r,weight:a,src:n,getFn:s}}function mn(e){return Ee(e)?e:e.split(".")}function ur(e){return Ee(e)?e.join("."):e}function Il(e,t){let r=[],n=!1;const a=(s,o,c)=>{if(he(s))if(!o[c])r.push(s);else{let i=o[c];const u=s[i];if(!he(u))return;if(c===o.length-1&&(we(u)||Ra(u)||xl(u)))r.push(yl(u));else if(Ee(u)){n=!0;for(let f=0,d=u.length;f<d;f+=1)a(u[f],o,c+1)}else o.length&&a(u,o,c+1)}};return a(e,we(t)?t.split("."):t,0),n?r:r[0]}const Tl={includeMatches:!1,findAllMatches:!1,minMatchCharLength:1},Rl={isCaseSensitive:!1,includeScore:!1,keys:[],shouldSort:!0,sortFn:(e,t)=>e.score===t.score?e.idx<t.idx?-1:1:e.score<t.score?-1:1},Ml={location:0,threshold:.6,distance:100},Al={useExtendedSearch:!1,getFn:Il,ignoreLocation:!1,ignoreFieldNorm:!1,fieldNormWeight:1};var F={...Rl,...Tl,...Ml,...Al};const Pl=/[^ ]+/g;function kl(e=1,t=3){const r=new Map,n=Math.pow(10,t);return{get(a){const s=a.match(Pl).length;if(r.has(s))return r.get(s);const o=1/Math.pow(s,.5*e),c=parseFloat(Math.round(o*n)/n);return r.set(s,c),c},clear(){r.clear()}}}class Rr{constructor({getFn:t=F.getFn,fieldNormWeight:r=F.fieldNormWeight}={}){this.norm=kl(r,3),this.getFn=t,this.isCreated=!1,this.setIndexRecords()}setSources(t=[]){this.docs=t}setIndexRecords(t=[]){this.records=t}setKeys(t=[]){this.keys=t,this._keysMap={},t.forEach((r,n)=>{this._keysMap[r.id]=n})}create(){this.isCreated||!this.docs.length||(this.isCreated=!0,we(this.docs[0])?this.docs.forEach((t,r)=>{this._addString(t,r)}):this.docs.forEach((t,r)=>{this._addObject(t,r)}),this.norm.clear())}add(t){const r=this.size();we(t)?this._addString(t,r):this._addObject(t,r)}removeAt(t){this.records.splice(t,1);for(let r=t,n=this.size();r<n;r+=1)this.records[r].i-=1}getValueForItemAtKeyId(t,r){return t[this._keysMap[r]]}size(){return this.records.length}_addString(t,r){if(!he(t)||Yt(t))return;let n={v:t,i:r,n:this.norm.get(t)};this.records.push(n)}_addObject(t,r){let n={i:r,$:{}};this.keys.forEach((a,s)=>{let o=a.getFn?a.getFn(t):this.getFn(t,a.path);if(he(o)){if(Ee(o)){let c=[];const i=[{nestedArrIndex:-1,value:o}];for(;i.length;){const{nestedArrIndex:u,value:f}=i.pop();if(he(f))if(we(f)&&!Yt(f)){let d={v:f,i:u,n:this.norm.get(f)};c.push(d)}else Ee(f)&&f.forEach((d,m)=>{i.push({nestedArrIndex:m,value:d})})}n.$[s]=c}else if(we(o)&&!Yt(o)){let c={v:o,n:this.norm.get(o)};n.$[s]=c}}}),this.records.push(n)}toJSON(){return{keys:this.keys,records:this.records}}}function ka(e,t,{getFn:r=F.getFn,fieldNormWeight:n=F.fieldNormWeight}={}){const a=new Rr({getFn:r,fieldNormWeight:n});return a.setKeys(e.map(Pa)),a.setSources(t),a.create(),a}function Nl(e,{getFn:t=F.getFn,fieldNormWeight:r=F.fieldNormWeight}={}){const{keys:n,records:a}=e,s=new Rr({getFn:t,fieldNormWeight:r});return s.setKeys(n),s.setIndexRecords(a),s}function bt(e,{errors:t=0,currentLocation:r=0,expectedLocation:n=0,distance:a=F.distance,ignoreLocation:s=F.ignoreLocation}={}){const o=t/e.length;if(s)return o;const c=Math.abs(n-r);return a?o+c/a:c?1:o}function Dl(e=[],t=F.minMatchCharLength){let r=[],n=-1,a=-1,s=0;for(let o=e.length;s<o;s+=1){let c=e[s];c&&n===-1?n=s:!c&&n!==-1&&(a=s-1,a-n+1>=t&&r.push([n,a]),n=-1)}return e[s-1]&&s-n>=t&&r.push([n,s-1]),r}const Ke=32;function Ll(e,t,r,{location:n=F.location,distance:a=F.distance,threshold:s=F.threshold,findAllMatches:o=F.findAllMatches,minMatchCharLength:c=F.minMatchCharLength,includeMatches:i=F.includeMatches,ignoreLocation:u=F.ignoreLocation}={}){if(t.length>Ke)throw new Error(jl(Ke));const f=t.length,d=e.length,m=Math.max(0,Math.min(n,d));let g=s,p=m;const b=c>1||i,h=b?Array(d):[];let w;for(;(w=e.indexOf(t,p))>-1;){let E=bt(t,{currentLocation:w,expectedLocation:m,distance:a,ignoreLocation:u});if(g=Math.min(E,g),p=w+f,b){let O=0;for(;O<f;)h[w+O]=1,O+=1}}p=-1;let y=[],v=1,S=f+d;const C=1<<f-1;for(let E=0;E<f;E+=1){let O=0,I=S;for(;O<I;)bt(t,{errors:E,currentLocation:m+I,expectedLocation:m,distance:a,ignoreLocation:u})<=g?O=I:S=I,I=Math.floor((S-O)/2+O);S=I;let x=Math.max(1,m-I+1),_=o?d:Math.min(m+I,d)+f,P=Array(_+2);P[_+1]=(1<<E)-1;for(let j=_;j>=x;j-=1){let N=j-1,k=r[e.charAt(N)];if(b&&(h[N]=+!!k),P[j]=(P[j+1]<<1|1)&k,E&&(P[j]|=(y[j+1]|y[j])<<1|1|y[j+1]),P[j]&C&&(v=bt(t,{errors:E,currentLocation:N,expectedLocation:m,distance:a,ignoreLocation:u}),v<=g)){if(g=v,p=N,p<=m)break;x=Math.max(1,2*m-p)}}if(bt(t,{errors:E+1,currentLocation:m,expectedLocation:m,distance:a,ignoreLocation:u})>g)break;y=P}const T={isMatch:p>=0,score:Math.max(.001,v)};if(b){const E=Dl(h,c);E.length?i&&(T.indices=E):T.isMatch=!1}return T}function zl(e){let t={};for(let r=0,n=e.length;r<n;r+=1){const a=e.charAt(r);t[a]=(t[a]||0)|1<<n-r-1}return t}class Na{constructor(t,{location:r=F.location,threshold:n=F.threshold,distance:a=F.distance,includeMatches:s=F.includeMatches,findAllMatches:o=F.findAllMatches,minMatchCharLength:c=F.minMatchCharLength,isCaseSensitive:i=F.isCaseSensitive,ignoreLocation:u=F.ignoreLocation}={}){if(this.options={location:r,threshold:n,distance:a,includeMatches:s,findAllMatches:o,minMatchCharLength:c,isCaseSensitive:i,ignoreLocation:u},this.pattern=i?t:t.toLowerCase(),this.chunks=[],!this.pattern.length)return;const f=(m,g)=>{this.chunks.push({pattern:m,alphabet:zl(m),startIndex:g})},d=this.pattern.length;if(d>Ke){let m=0;const g=d%Ke,p=d-g;for(;m<p;)f(this.pattern.substr(m,Ke),m),m+=Ke;if(g){const b=d-Ke;f(this.pattern.substr(b),b)}}else f(this.pattern,0)}searchIn(t){const{isCaseSensitive:r,includeMatches:n}=this.options;if(r||(t=t.toLowerCase()),this.pattern===t){let p={isMatch:!0,score:0};return n&&(p.indices=[[0,t.length-1]]),p}const{location:a,distance:s,threshold:o,findAllMatches:c,minMatchCharLength:i,ignoreLocation:u}=this.options;let f=[],d=0,m=!1;this.chunks.forEach(({pattern:p,alphabet:b,startIndex:h})=>{const{isMatch:w,score:y,indices:v}=Ll(t,p,b,{location:a+h,distance:s,threshold:o,findAllMatches:c,minMatchCharLength:i,includeMatches:n,ignoreLocation:u});w&&(m=!0),d+=y,w&&v&&(f=[...f,...v])});let g={isMatch:m,score:m?d/this.chunks.length:1};return m&&n&&(g.indices=f),g}}class Pe{constructor(t){this.pattern=t}static isMultiMatch(t){return vn(t,this.multiRegex)}static isSingleMatch(t){return vn(t,this.singleRegex)}search(){}}function vn(e,t){const r=e.match(t);return r?r[1]:null}class $l extends Pe{constructor(t){super(t)}static get type(){return"exact"}static get multiRegex(){return/^="(.*)"$/}static get singleRegex(){return/^=(.*)$/}search(t){const r=t===this.pattern;return{isMatch:r,score:r?0:1,indices:[0,this.pattern.length-1]}}}class Bl extends Pe{constructor(t){super(t)}static get type(){return"inverse-exact"}static get multiRegex(){return/^!"(.*)"$/}static get singleRegex(){return/^!(.*)$/}search(t){const n=t.indexOf(this.pattern)===-1;return{isMatch:n,score:n?0:1,indices:[0,t.length-1]}}}class Fl extends Pe{constructor(t){super(t)}static get type(){return"prefix-exact"}static get multiRegex(){return/^\^"(.*)"$/}static get singleRegex(){return/^\^(.*)$/}search(t){const r=t.startsWith(this.pattern);return{isMatch:r,score:r?0:1,indices:[0,this.pattern.length-1]}}}class Kl extends Pe{constructor(t){super(t)}static get type(){return"inverse-prefix-exact"}static get multiRegex(){return/^!\^"(.*)"$/}static get singleRegex(){return/^!\^(.*)$/}search(t){const r=!t.startsWith(this.pattern);return{isMatch:r,score:r?0:1,indices:[0,t.length-1]}}}class ql extends Pe{constructor(t){super(t)}static get type(){return"suffix-exact"}static get multiRegex(){return/^"(.*)"\$$/}static get singleRegex(){return/^(.*)\$$/}search(t){const r=t.endsWith(this.pattern);return{isMatch:r,score:r?0:1,indices:[t.length-this.pattern.length,t.length-1]}}}class Vl extends Pe{constructor(t){super(t)}static get type(){return"inverse-suffix-exact"}static get multiRegex(){return/^!"(.*)"\$$/}static get singleRegex(){return/^!(.*)\$$/}search(t){const r=!t.endsWith(this.pattern);return{isMatch:r,score:r?0:1,indices:[0,t.length-1]}}}class Da extends Pe{constructor(t,{location:r=F.location,threshold:n=F.threshold,distance:a=F.distance,includeMatches:s=F.includeMatches,findAllMatches:o=F.findAllMatches,minMatchCharLength:c=F.minMatchCharLength,isCaseSensitive:i=F.isCaseSensitive,ignoreLocation:u=F.ignoreLocation}={}){super(t),this._bitapSearch=new Na(t,{location:r,threshold:n,distance:a,includeMatches:s,findAllMatches:o,minMatchCharLength:c,isCaseSensitive:i,ignoreLocation:u})}static get type(){return"fuzzy"}static get multiRegex(){return/^"(.*)"$/}static get singleRegex(){return/^(.*)$/}search(t){return this._bitapSearch.searchIn(t)}}class La extends Pe{constructor(t){super(t)}static get type(){return"include"}static get multiRegex(){return/^'"(.*)"$/}static get singleRegex(){return/^'(.*)$/}search(t){let r=0,n;const a=[],s=this.pattern.length;for(;(n=t.indexOf(this.pattern,r))>-1;)r=n+s,a.push([n,r-1]);const o=!!a.length;return{isMatch:o,score:o?0:1,indices:a}}}const dr=[$l,La,Fl,Kl,Vl,ql,Bl,Da],gn=dr.length,Gl=/ +(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)/,Ul="|";function Wl(e,t={}){return e.split(Ul).map(r=>{let n=r.trim().split(Gl).filter(s=>s&&!!s.trim()),a=[];for(let s=0,o=n.length;s<o;s+=1){const c=n[s];let i=!1,u=-1;for(;!i&&++u<gn;){const f=dr[u];let d=f.isMultiMatch(c);d&&(a.push(new f(d,t)),i=!0)}if(!i)for(u=-1;++u<gn;){const f=dr[u];let d=f.isSingleMatch(c);if(d){a.push(new f(d,t));break}}}return a})}const Hl=new Set([Da.type,La.type]);class Yl{constructor(t,{isCaseSensitive:r=F.isCaseSensitive,includeMatches:n=F.includeMatches,minMatchCharLength:a=F.minMatchCharLength,ignoreLocation:s=F.ignoreLocation,findAllMatches:o=F.findAllMatches,location:c=F.location,threshold:i=F.threshold,distance:u=F.distance}={}){this.query=null,this.options={isCaseSensitive:r,includeMatches:n,minMatchCharLength:a,findAllMatches:o,ignoreLocation:s,location:c,threshold:i,distance:u},this.pattern=r?t:t.toLowerCase(),this.query=Wl(this.pattern,this.options)}static condition(t,r){return r.useExtendedSearch}searchIn(t){const r=this.query;if(!r)return{isMatch:!1,score:1};const{includeMatches:n,isCaseSensitive:a}=this.options;t=a?t:t.toLowerCase();let s=0,o=[],c=0;for(let i=0,u=r.length;i<u;i+=1){const f=r[i];o.length=0,s=0;for(let d=0,m=f.length;d<m;d+=1){const g=f[d],{isMatch:p,indices:b,score:h}=g.search(t);if(p){if(s+=1,c+=h,n){const w=g.constructor.type;Hl.has(w)?o=[...o,...b]:o.push(b)}}else{c=0,s=0,o.length=0;break}}if(s){let d={isMatch:!0,score:c/s};return n&&(d.indices=o),d}}return{isMatch:!1,score:1}}}const fr=[];function Xl(...e){fr.push(...e)}function pr(e,t){for(let r=0,n=fr.length;r<n;r+=1){let a=fr[r];if(a.condition(e,t))return new a(e,t)}return new Na(e,t)}const It={AND:"$and",OR:"$or"},hr={PATH:"$path",PATTERN:"$val"},mr=e=>!!(e[It.AND]||e[It.OR]),Jl=e=>!!e[hr.PATH],Ql=e=>!Ee(e)&&Ma(e)&&!mr(e),bn=e=>({[It.AND]:Object.keys(e).map(t=>({[t]:e[t]}))});function za(e,t,{auto:r=!0}={}){const n=a=>{let s=Object.keys(a);const o=Jl(a);if(!o&&s.length>1&&!mr(a))return n(bn(a));if(Ql(a)){const i=o?a[hr.PATH]:s[0],u=o?a[hr.PATTERN]:a[i];if(!we(u))throw new Error(Sl(i));const f={keyId:ur(i),pattern:u};return r&&(f.searcher=pr(u,t)),f}let c={children:[],operator:s[0]};return s.forEach(i=>{const u=a[i];Ee(u)&&u.forEach(f=>{c.children.push(n(f))})}),c};return mr(e)||(e=bn(e)),n(e)}function Zl(e,{ignoreFieldNorm:t=F.ignoreFieldNorm}){e.forEach(r=>{let n=1;r.matches.forEach(({key:a,norm:s,score:o})=>{const c=a?a.weight:null;n*=Math.pow(o===0&&c?Number.EPSILON:o,(c||1)*(t?1:s))}),r.score=n})}function eu(e,t){const r=e.matches;t.matches=[],he(r)&&r.forEach(n=>{if(!he(n.indices)||!n.indices.length)return;const{indices:a,value:s}=n;let o={indices:a,value:s};n.key&&(o.key=n.key.src),n.idx>-1&&(o.refIndex=n.idx),t.matches.push(o)})}function tu(e,t){t.score=e.score}function ru(e,t,{includeMatches:r=F.includeMatches,includeScore:n=F.includeScore}={}){const a=[];return r&&a.push(eu),n&&a.push(tu),e.map(s=>{const{idx:o}=s,c={item:t[o],refIndex:o};return a.length&&a.forEach(i=>{i(s,c)}),c})}class Ze{constructor(t,r={},n){this.options={...F,...r},this.options.useExtendedSearch,this._keyStore=new Cl(this.options.keys),this.setCollection(t,n)}setCollection(t,r){if(this._docs=t,r&&!(r instanceof Rr))throw new Error(_l);this._myIndex=r||ka(this.options.keys,this._docs,{getFn:this.options.getFn,fieldNormWeight:this.options.fieldNormWeight})}add(t){he(t)&&(this._docs.push(t),this._myIndex.add(t))}remove(t=()=>!1){const r=[];for(let n=0,a=this._docs.length;n<a;n+=1){const s=this._docs[n];t(s,n)&&(this.removeAt(n),n-=1,a-=1,r.push(s))}return r}removeAt(t){this._docs.splice(t,1),this._myIndex.removeAt(t)}getIndex(){return this._myIndex}search(t,{limit:r=-1}={}){const{includeMatches:n,includeScore:a,shouldSort:s,sortFn:o,ignoreFieldNorm:c}=this.options;let i=we(t)?we(this._docs[0])?this._searchStringList(t):this._searchObjectList(t):this._searchLogical(t);return Zl(i,{ignoreFieldNorm:c}),s&&i.sort(o),Ra(r)&&r>-1&&(i=i.slice(0,r)),ru(i,this._docs,{includeMatches:n,includeScore:a})}_searchStringList(t){const r=pr(t,this.options),{records:n}=this._myIndex,a=[];return n.forEach(({v:s,i:o,n:c})=>{if(!he(s))return;const{isMatch:i,score:u,indices:f}=r.searchIn(s);i&&a.push({item:s,idx:o,matches:[{score:u,value:s,norm:c,indices:f}]})}),a}_searchLogical(t){const r=za(t,this.options),n=(c,i,u)=>{if(!c.children){const{keyId:d,searcher:m}=c,g=this._findMatches({key:this._keyStore.get(d),value:this._myIndex.getValueForItemAtKeyId(i,d),searcher:m});return g&&g.length?[{idx:u,item:i,matches:g}]:[]}const f=[];for(let d=0,m=c.children.length;d<m;d+=1){const g=c.children[d],p=n(g,i,u);if(p.length)f.push(...p);else if(c.operator===It.AND)return[]}return f},a=this._myIndex.records,s={},o=[];return a.forEach(({$:c,i})=>{if(he(c)){let u=n(r,c,i);u.length&&(s[i]||(s[i]={idx:i,item:c,matches:[]},o.push(s[i])),u.forEach(({matches:f})=>{s[i].matches.push(...f)}))}}),o}_searchObjectList(t){const r=pr(t,this.options),{keys:n,records:a}=this._myIndex,s=[];return a.forEach(({$:o,i:c})=>{if(!he(o))return;let i=[];n.forEach((u,f)=>{i.push(...this._findMatches({key:u,value:o[f],searcher:r}))}),i.length&&s.push({idx:c,item:o,matches:i})}),s}_findMatches({key:t,value:r,searcher:n}){if(!he(r))return[];let a=[];if(Ee(r))r.forEach(({v:s,i:o,n:c})=>{if(!he(s))return;const{isMatch:i,score:u,indices:f}=n.searchIn(s);i&&a.push({score:u,key:t,value:s,idx:o,norm:c,indices:f})});else{const{v:s,n:o}=r,{isMatch:c,score:i,indices:u}=n.searchIn(s);c&&a.push({score:i,key:t,value:s,norm:o,indices:u})}return a}}Ze.version="6.6.2";Ze.createIndex=ka;Ze.parseIndex=Nl;Ze.config=F;Ze.parseQuery=za;Xl(Yl);const nu=Object.freeze(Object.defineProperty({__proto__:null,default:Ze},Symbol.toStringTag,{value:"Module"})),au=Ln(nu);var yn;function ou(){return yn||(yn=1,function(e){var t=ve&&ve.__createBinding||(Object.create?function(g,p,b,h){h===void 0&&(h=b),Object.defineProperty(g,h,{enumerable:!0,get:function(){return p[b]}})}:function(g,p,b,h){h===void 0&&(h=b),g[h]=p[b]}),r=ve&&ve.__setModuleDefault||(Object.create?function(g,p){Object.defineProperty(g,"default",{enumerable:!0,value:p})}:function(g,p){g.default=p}),n=ve&&ve.__importStar||function(g){if(g&&g.__esModule)return g;var p={};if(g!=null)for(var b in g)b!=="default"&&Object.prototype.hasOwnProperty.call(g,b)&&t(p,g,b);return r(p,g),p},a=ve&&ve.__importDefault||function(g){return g&&g.__esModule?g:{default:g}};Object.defineProperty(e,"__esModule",{value:!0}),e.useDeepMatches=e.useMatches=e.NO_GROUP=void 0;var s=n(ne()),o=Ae(),c=Ge(),i=a(au);e.NO_GROUP={name:"none",priority:c.Priority.NORMAL};var u={keys:[{name:"name",weight:.5},{name:"keywords",getFn:function(g){var p;return((p=g.keywords)!==null&&p!==void 0?p:"").split(",")},weight:.5},"subtitle"],ignoreLocation:!0,includeScore:!0,includeMatches:!0,threshold:.2,minMatchCharLength:1};function f(g,p){return p.priority-g.priority}function d(){var g=(0,o.useKBar)(function(I){return{search:I.searchQuery,actions:I.actions,rootActionId:I.currentRootActionId}}),p=g.search,b=g.actions,h=g.rootActionId,w=s.useMemo(function(){return Object.keys(b).reduce(function(I,x){var _=b[x];if(!_.parent&&!h&&I.push(_),_.id===h)for(var P=0;P<_.children.length;P++)I.push(_.children[P]);return I},[]).sort(f)},[b,h]),y=s.useCallback(function(I){for(var x=[],_=0;_<I.length;_++)x.push(I[_]);return function P(M,j){j===void 0&&(j=x);for(var N=0;N<M.length;N++)if(M[N].children.length>0){for(var k=M[N].children,D=0;D<k.length;D++)j.push(k[D]);P(M[N].children,j)}return j}(I)},[]),v=!p,S=s.useMemo(function(){return v?w:y(w)},[y,w,v]),C=s.useMemo(function(){return new i.default(S,u)},[S]),T=m(S,p,C),E=s.useMemo(function(){for(var I,x,_={},P=[],M=[],j=0;j<T.length;j++){var N=T[j],k=N.action,D=N.score||c.Priority.NORMAL,A={name:typeof k.section=="string"?k.section:((I=k.section)===null||I===void 0?void 0:I.name)||e.NO_GROUP.name,priority:typeof k.section=="string"?D:((x=k.section)===null||x===void 0?void 0:x.priority)||0+D};_[A.name]||(_[A.name]=[],P.push(A)),_[A.name].push({priority:k.priority+D,action:k})}M=P.sort(f).map(function(ee){return{name:ee.name,actions:_[ee.name].sort(f).map(function(G){return G.action})}});for(var V=[],j=0;j<M.length;j++){var W=M[j];W.name!==e.NO_GROUP.name&&V.push(W.name);for(var Q=0;Q<W.actions.length;Q++)V.push(W.actions[Q])}return V},[T]),O=s.useMemo(function(){return h},[E]);return s.useMemo(function(){return{results:E,rootActionId:O}},[O,E])}e.useMatches=d;function m(g,p,b){var h=s.useMemo(function(){return{filtered:g,search:p}},[g,p]),w=(0,c.useThrottledValue)(h),y=w.filtered,v=w.search;return s.useMemo(function(){if(v.trim()==="")return y.map(function(T){return{score:0,action:T}});var S=[],C=b.search(v);return S=C.map(function(T){var E=T.item,O=T.score;return{score:1/((O??0)+1),action:E}}),S},[y,v,b])}e.useDeepMatches=d}(ve)),ve}var ge={},Xt,xn;function su(){if(xn)return Xt;xn=1;var e=Object.create,t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,a=Object.getPrototypeOf,s=Object.prototype.hasOwnProperty,o=(b,h)=>{for(var w in h)t(b,w,{get:h[w],enumerable:!0})},c=(b,h,w,y)=>{if(h&&typeof h=="object"||typeof h=="function")for(let v of n(h))!s.call(b,v)&&v!==w&&t(b,v,{get:()=>h[v],enumerable:!(y=r(h,v))||y.enumerable});return b},i=(b,h,w)=>(w=b!=null?e(a(b)):{},c(!b||!b.__esModule?t(w,"default",{value:b,enumerable:!0}):w,b)),u=b=>c(t({},"__esModule",{value:!0}),b),f={};o(f,{composeRefs:()=>g,useComposedRefs:()=>p}),Xt=u(f);var d=i(ne());function m(b,h){if(typeof b=="function")return b(h);b!=null&&(b.current=h)}function g(...b){return h=>{let w=!1;const y=b.map(v=>{const S=m(v,h);return!w&&typeof S=="function"&&(w=!0),S});if(w)return()=>{for(let v=0;v<y.length;v++){const S=y[v];typeof S=="function"?S():m(b[v],null)}}}}function p(...b){return d.useCallback(g(...b),b)}return Xt}var Jt,wn;function iu(){if(wn)return Jt;wn=1;var e=Object.create,t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,a=Object.getPrototypeOf,s=Object.prototype.hasOwnProperty,o=(E,O)=>{for(var I in O)t(E,I,{get:O[I],enumerable:!0})},c=(E,O,I,x)=>{if(O&&typeof O=="object"||typeof O=="function")for(let _ of n(O))!s.call(E,_)&&_!==I&&t(E,_,{get:()=>O[_],enumerable:!(x=r(O,_))||x.enumerable});return E},i=(E,O,I)=>(I=E!=null?e(a(E)):{},c(!E||!E.__esModule?t(I,"default",{value:E,enumerable:!0}):I,E)),u=E=>c(t({},"__esModule",{value:!0}),E),f={};o(f,{Root:()=>b,Slot:()=>b,Slottable:()=>v,createSlot:()=>p,createSlottable:()=>y}),Jt=u(f);var d=i(ne()),m=su(),g=xr();function p(E){const O=h(E),I=d.forwardRef((x,_)=>{const{children:P,...M}=x,j=d.Children.toArray(P),N=j.find(S);if(N){const k=N.props.children,D=j.map(A=>A===N?d.Children.count(k)>1?d.Children.only(null):d.isValidElement(k)?k.props.children:null:A);return(0,g.jsx)(O,{...M,ref:_,children:d.isValidElement(k)?d.cloneElement(k,void 0,D):null})}return(0,g.jsx)(O,{...M,ref:_,children:P})});return I.displayName=`${E}.Slot`,I}var b=p("Slot");function h(E){const O=d.forwardRef((I,x)=>{const{children:_,...P}=I;if(d.isValidElement(_)){const M=T(_),j=C(P,_.props);return _.type!==d.Fragment&&(j.ref=x?(0,m.composeRefs)(x,M):M),d.cloneElement(_,j)}return d.Children.count(_)>1?d.Children.only(null):null});return O.displayName=`${E}.SlotClone`,O}var w=Symbol("radix.slottable");function y(E){const O=({children:I})=>(0,g.jsx)(g.Fragment,{children:I});return O.displayName=`${E}.Slottable`,O.__radixId=w,O}var v=y("Slottable");function S(E){return d.isValidElement(E)&&typeof E.type=="function"&&"__radixId"in E.type&&E.type.__radixId===w}function C(E,O){const I={...O};for(const x in O){const _=E[x],P=O[x];/^on[A-Z]/.test(x)?_&&P?I[x]=(...j)=>{const N=P(...j);return _(...j),N}:_&&(I[x]=_):x==="style"?I[x]={..._,...P}:x==="className"&&(I[x]=[_,P].filter(Boolean).join(" "))}return{...E,...I}}function T(E){let O=Object.getOwnPropertyDescriptor(E.props,"ref")?.get,I=O&&"isReactWarning"in O&&O.isReactWarning;return I?E.ref:(O=Object.getOwnPropertyDescriptor(E,"ref")?.get,I=O&&"isReactWarning"in O&&O.isReactWarning,I?E.props.ref:E.props.ref||E.ref)}return Jt}var Qt,_n;function cu(){if(_n)return Qt;_n=1;var e=Object.create,t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,a=Object.getPrototypeOf,s=Object.prototype.hasOwnProperty,o=(v,S)=>{for(var C in S)t(v,C,{get:S[C],enumerable:!0})},c=(v,S,C,T)=>{if(S&&typeof S=="object"||typeof S=="function")for(let E of n(S))!s.call(v,E)&&E!==C&&t(v,E,{get:()=>S[E],enumerable:!(T=r(S,E))||T.enumerable});return v},i=(v,S,C)=>(C=v!=null?e(a(v)):{},c(!v||!v.__esModule?t(C,"default",{value:v,enumerable:!0}):C,v)),u=v=>c(t({},"__esModule",{value:!0}),v),f={};o(f,{Primitive:()=>h,Root:()=>y,dispatchDiscreteCustomEvent:()=>w}),Qt=u(f);var d=i(ne()),m=i(zn()),g=iu(),p=xr(),b=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],h=b.reduce((v,S)=>{const C=(0,g.createSlot)(`Primitive.${S}`),T=d.forwardRef((E,O)=>{const{asChild:I,...x}=E,_=I?C:S;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),(0,p.jsx)(_,{...x,ref:O})});return T.displayName=`Primitive.${S}`,{...v,[S]:T}},{});function w(v,S){v&&m.flushSync(()=>v.dispatchEvent(S))}var y=h;return Qt}var Zt,Sn;function lu(){if(Sn)return Zt;Sn=1;var e=Object.create,t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,a=Object.getPrototypeOf,s=Object.prototype.hasOwnProperty,o=(g,p)=>{for(var b in p)t(g,b,{get:p[b],enumerable:!0})},c=(g,p,b,h)=>{if(p&&typeof p=="object"||typeof p=="function")for(let w of n(p))!s.call(g,w)&&w!==b&&t(g,w,{get:()=>p[w],enumerable:!(h=r(p,w))||h.enumerable});return g},i=(g,p,b)=>(b=g!=null?e(a(g)):{},c(!g||!g.__esModule?t(b,"default",{value:g,enumerable:!0}):b,g)),u=g=>c(t({},"__esModule",{value:!0}),g),f={};o(f,{useLayoutEffect:()=>m}),Zt=u(f);var d=i(ne()),m=globalThis?.document?d.useLayoutEffect:()=>{};return Zt}var er,jn;function uu(){if(jn)return er;jn=1;var e=Object.create,t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,a=Object.getPrototypeOf,s=Object.prototype.hasOwnProperty,o=(v,S)=>{for(var C in S)t(v,C,{get:S[C],enumerable:!0})},c=(v,S,C,T)=>{if(S&&typeof S=="object"||typeof S=="function")for(let E of n(S))!s.call(v,E)&&E!==C&&t(v,E,{get:()=>S[E],enumerable:!(T=r(S,E))||T.enumerable});return v},i=(v,S,C)=>(C=v!=null?e(a(v)):{},c(!v||!v.__esModule?t(C,"default",{value:v,enumerable:!0}):C,v)),u=v=>c(t({},"__esModule",{value:!0}),v),f={};o(f,{Portal:()=>w,Root:()=>y}),er=u(f);var d=i(ne()),m=i(zn()),g=cu(),p=lu(),b=xr(),h="Portal",w=d.forwardRef((v,S)=>{const{container:C,...T}=v,[E,O]=d.useState(!1);(0,p.useLayoutEffect)(()=>O(!0),[]);const I=C||E&&globalThis?.document?.body;return I?m.default.createPortal((0,b.jsx)(g.Primitive.div,{...T,ref:S}),I):null});w.displayName=h;var y=w;return er}var On;function du(){if(On)return ge;On=1;var e=ge&&ge.__createBinding||(Object.create?function(i,u,f,d){d===void 0&&(d=f),Object.defineProperty(i,d,{enumerable:!0,get:function(){return u[f]}})}:function(i,u,f,d){d===void 0&&(d=f),i[d]=u[f]}),t=ge&&ge.__setModuleDefault||(Object.create?function(i,u){Object.defineProperty(i,"default",{enumerable:!0,value:u})}:function(i,u){i.default=u}),r=ge&&ge.__importStar||function(i){if(i&&i.__esModule)return i;var u={};if(i!=null)for(var f in i)f!=="default"&&Object.prototype.hasOwnProperty.call(i,f)&&e(u,i,f);return t(u,i),u};Object.defineProperty(ge,"__esModule",{value:!0}),ge.KBarPortal=void 0;var n=uu(),a=r(ne()),s=Qe(),o=Ae();function c(i){var u=i.children,f=i.container,d=(0,o.useKBar)(function(m){return{showing:m.visualState!==s.VisualState.hidden}}).showing;return d?a.createElement(n.Portal,{container:f},u):null}return ge.KBarPortal=c,ge}var ie={},En;function fu(){if(En)return ie;En=1;var e=ie&&ie.__assign||function(){return e=Object.assign||function(i){for(var u,f=1,d=arguments.length;f<d;f++){u=arguments[f];for(var m in u)Object.prototype.hasOwnProperty.call(u,m)&&(i[m]=u[m])}return i},e.apply(this,arguments)},t=ie&&ie.__createBinding||(Object.create?function(i,u,f,d){d===void 0&&(d=f),Object.defineProperty(i,d,{enumerable:!0,get:function(){return u[f]}})}:function(i,u,f,d){d===void 0&&(d=f),i[d]=u[f]}),r=ie&&ie.__setModuleDefault||(Object.create?function(i,u){Object.defineProperty(i,"default",{enumerable:!0,value:u})}:function(i,u){i.default=u}),n=ie&&ie.__importStar||function(i){if(i&&i.__esModule)return i;var u={};if(i!=null)for(var f in i)f!=="default"&&Object.prototype.hasOwnProperty.call(i,f)&&t(u,i,f);return r(u,i),u},a=ie&&ie.__rest||function(i,u){var f={};for(var d in i)Object.prototype.hasOwnProperty.call(i,d)&&u.indexOf(d)<0&&(f[d]=i[d]);if(i!=null&&typeof Object.getOwnPropertySymbols=="function")for(var m=0,d=Object.getOwnPropertySymbols(i);m<d.length;m++)u.indexOf(d[m])<0&&Object.prototype.propertyIsEnumerable.call(i,d[m])&&(f[d[m]]=i[d[m]]);return f};Object.defineProperty(ie,"__esModule",{value:!0}),ie.KBarPositioner=void 0;var s=n(ne()),o={position:"fixed",display:"flex",alignItems:"flex-start",justifyContent:"center",width:"100%",inset:"0px",padding:"14vh 16px 16px"};function c(i){return i?e(e({},o),i):o}return ie.KBarPositioner=s.forwardRef(function(i,u){var f=i.style,d=i.children,m=a(i,["style","children"]);return s.createElement("div",e({ref:u,style:c(f)},m),d)}),ie}var de={},Cn;function $a(){return Cn||(Cn=1,function(e){var t=de&&de.__assign||function(){return t=Object.assign||function(d){for(var m,g=1,p=arguments.length;g<p;g++){m=arguments[g];for(var b in m)Object.prototype.hasOwnProperty.call(m,b)&&(d[b]=m[b])}return d},t.apply(this,arguments)},r=de&&de.__createBinding||(Object.create?function(d,m,g,p){p===void 0&&(p=g),Object.defineProperty(d,p,{enumerable:!0,get:function(){return m[g]}})}:function(d,m,g,p){p===void 0&&(p=g),d[p]=m[g]}),n=de&&de.__setModuleDefault||(Object.create?function(d,m){Object.defineProperty(d,"default",{enumerable:!0,value:m})}:function(d,m){d.default=m}),a=de&&de.__importStar||function(d){if(d&&d.__esModule)return d;var m={};if(d!=null)for(var g in d)g!=="default"&&Object.prototype.hasOwnProperty.call(d,g)&&r(m,d,g);return n(m,d),m},s=de&&de.__rest||function(d,m){var g={};for(var p in d)Object.prototype.hasOwnProperty.call(d,p)&&m.indexOf(p)<0&&(g[p]=d[p]);if(d!=null&&typeof Object.getOwnPropertySymbols=="function")for(var b=0,p=Object.getOwnPropertySymbols(d);b<p.length;b++)m.indexOf(p[b])<0&&Object.prototype.propertyIsEnumerable.call(d,p[b])&&(g[p[b]]=d[p[b]]);return g};Object.defineProperty(e,"__esModule",{value:!0}),e.KBarSearch=e.getListboxItemId=e.KBAR_LISTBOX=void 0;var o=a(ne()),c=Qe(),i=Ae();e.KBAR_LISTBOX="kbar-listbox";var u=function(d){return"kbar-listbox-item-"+d};e.getListboxItemId=u;function f(d){var m=(0,i.useKBar)(function(x){return{search:x.searchQuery,currentRootActionId:x.currentRootActionId,actions:x.actions,activeIndex:x.activeIndex,showing:x.visualState===c.VisualState.showing}}),g=m.query,p=m.search,b=m.actions,h=m.currentRootActionId,w=m.activeIndex,y=m.showing,v=m.options,S=o.useState(p),C=S[0],T=S[1];o.useEffect(function(){g.setSearch(C)},[C,g]);var E=d.defaultPlaceholder,O=s(d,["defaultPlaceholder"]);o.useEffect(function(){return g.setSearch(""),g.getInput().focus(),function(){return g.setSearch("")}},[h,g]);var I=o.useMemo(function(){var x=E??"Type a command or search…";return h&&b[h]?b[h].name:x},[b,h,E]);return o.createElement("input",t({},O,{ref:g.inputRefSetter,autoFocus:!0,autoComplete:"off",role:"combobox",spellCheck:"false","aria-expanded":y,"aria-controls":e.KBAR_LISTBOX,"aria-activedescendant":(0,e.getListboxItemId)(w),value:C,placeholder:I,onChange:function(x){var _,P,M;(_=d.onChange)===null||_===void 0||_.call(d,x),T(x.target.value),(M=(P=v?.callbacks)===null||P===void 0?void 0:P.onQueryChange)===null||M===void 0||M.call(P,x.target.value)},onKeyDown:function(x){var _;if((_=d.onKeyDown)===null||_===void 0||_.call(d,x),h&&!p&&x.key==="Backspace"){var P=b[h].parent;g.setCurrentRootAction(P)}}}))}e.KBarSearch=f}(de)),de}var fe={};function qe(){return qe=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},qe.apply(this,arguments)}function pu(e,t){if(e==null)return{};var r={},n=Object.keys(e),a,s;for(s=0;s<n.length;s++)a=n[s],!(t.indexOf(a)>=0)&&(r[a]=e[a]);return r}var hu=["bottom","height","left","right","top","width"],mu=function(t,r){return t===void 0&&(t={}),r===void 0&&(r={}),hu.some(function(n){return t[n]!==r[n]})},Re=new Map,Ba,vu=function e(){var t=[];Re.forEach(function(r,n){var a=n.getBoundingClientRect();mu(a,r.rect)&&(r.rect=a,t.push(r))}),t.forEach(function(r){r.callbacks.forEach(function(n){return n(r.rect)})}),Ba=window.requestAnimationFrame(e)};function gu(e,t){return{observe:function(){var n=Re.size===0;Re.has(e)?Re.get(e).callbacks.push(t):Re.set(e,{rect:void 0,hasRectChanged:!1,callbacks:[t]}),n&&vu()},unobserve:function(){var n=Re.get(e);if(n){var a=n.callbacks.indexOf(t);a>=0&&n.callbacks.splice(a,1),n.callbacks.length||Re.delete(e),Re.size||cancelAnimationFrame(Ba)}}}}var Tt=typeof window<"u"?J.useLayoutEffect:J.useEffect;function bu(e,t){t===void 0&&(t={width:0,height:0});var r=J.useState(e.current),n=r[0],a=r[1],s=J.useReducer(yu,t),o=s[0],c=s[1],i=J.useRef(!1);return Tt(function(){e.current!==n&&a(e.current)}),Tt(function(){if(n&&!i.current){i.current=!0;var u=n.getBoundingClientRect();c({rect:u})}},[n]),J.useEffect(function(){if(n){var u=gu(n,function(f){c({rect:f})});return u.observe(),function(){u.unobserve()}}},[n]),o}function yu(e,t){var r=t.rect;return e.height!==r.height||e.width!==r.width?r:e}var xu=function(){return 50},wu=function(t){return t},_u=function(t,r){var n=r?"offsetWidth":"offsetHeight";return t[n]},Fa=function(t){for(var r=Math.max(t.start-t.overscan,0),n=Math.min(t.end+t.overscan,t.size-1),a=[],s=r;s<=n;s++)a.push(s);return a};function Su(e){var t,r=e.size,n=r===void 0?0:r,a=e.estimateSize,s=a===void 0?xu:a,o=e.overscan,c=o===void 0?1:o,i=e.paddingStart,u=i===void 0?0:i,f=e.paddingEnd,d=f===void 0?0:f,m=e.parentRef,g=e.horizontal,p=e.scrollToFn,b=e.useObserver,h=e.initialRect,w=e.onScrollElement,y=e.scrollOffsetFn,v=e.keyExtractor,S=v===void 0?wu:v,C=e.measureSize,T=C===void 0?_u:C,E=e.rangeExtractor,O=E===void 0?Fa:E,I=g?"width":"height",x=g?"scrollLeft":"scrollTop",_=J.useRef({scrollOffset:0,measurements:[]}),P=J.useState(0),M=P[0],j=P[1];_.current.scrollOffset=M;var N=b||bu,k=N(m,h),D=k[I];_.current.outerSize=D;var A=J.useCallback(function(Y){m.current&&(m.current[x]=Y)},[m,x]),V=p||A;p=J.useCallback(function(Y){V(Y,A)},[A,V]);var W=J.useState({}),Q=W[0],ee=W[1],G=J.useCallback(function(){return ee({})},[]),U=J.useRef([]),oe=J.useMemo(function(){var Y=U.current.length>0?Math.min.apply(Math,U.current):0;U.current=[];for(var re=_.current.measurements.slice(0,Y),X=Y;X<n;X++){var me=S(X),te=Q[me],_e=re[X-1]?re[X-1].end:u,ye=typeof te=="number"?te:s(X),xe=_e+ye;re[X]={index:X,start:_e,size:ye,end:xe,key:me}}return re},[s,Q,u,n,S]),et=(((t=oe[n-1])==null?void 0:t.end)||u)+d;_.current.measurements=oe,_.current.totalSize=et;var H=w?w.current:m.current,Ue=J.useRef(y);Ue.current=y,Tt(function(){if(!H){j(0);return}var Y=function(X){var me=Ue.current?Ue.current(X):H[x];j(me)};return Y(),H.addEventListener("scroll",Y,{capture:!1,passive:!0}),function(){H.removeEventListener("scroll",Y)}},[H,x]);var ke=Ou(_.current),Ne=ke.start,We=ke.end,Ce=J.useMemo(function(){return O({start:Ne,end:We,overscan:c,size:oe.length})},[Ne,We,c,oe.length,O]),Pr=J.useRef(T);Pr.current=T;var no=J.useMemo(function(){for(var Y=[],re=function(_e,ye){var xe=Ce[_e],dt=oe[xe],De=qe(qe({},dt),{},{measureRef:function(ft){if(ft){var zt=Pr.current(ft,g);if(zt!==De.size){var Nr=_.current.scrollOffset;De.start<Nr&&A(Nr+(zt-De.size)),U.current.push(xe),ee(function(oo){var $t;return qe(qe({},oo),{},($t={},$t[De.key]=zt,$t))})}}}});Y.push(De)},X=0,me=Ce.length;X<me;X++)re(X);return Y},[Ce,A,g,oe]),kr=J.useRef(!1);Tt(function(){kr.current&&ee({}),kr.current=!0},[s]);var Dt=J.useCallback(function(Y,re){var X=re===void 0?{}:re,me=X.align,te=me===void 0?"start":me,_e=_.current,ye=_e.scrollOffset,xe=_e.outerSize;te==="auto"&&(Y<=ye?te="start":Y>=ye+xe?te="end":te="start"),te==="start"?p(Y):te==="end"?p(Y-xe):te==="center"&&p(Y-xe/2)},[p]),Lt=J.useCallback(function(Y,re){var X=re===void 0?{}:re,me=X.align,te=me===void 0?"auto":me,_e=pu(X,["align"]),ye=_.current,xe=ye.measurements,dt=ye.scrollOffset,De=ye.outerSize,Ie=xe[Math.max(0,Math.min(Y,n-1))];if(Ie){if(te==="auto")if(Ie.end>=dt+De)te="end";else if(Ie.start<=dt)te="start";else return;var ft=te==="center"?Ie.start+Ie.size/2:te==="end"?Ie.end:Ie.start;Dt(ft,qe({align:te},_e))}},[Dt,n]),ao=J.useCallback(function(){for(var Y=arguments.length,re=new Array(Y),X=0;X<Y;X++)re[X]=arguments[X];Lt.apply(void 0,re),requestAnimationFrame(function(){Lt.apply(void 0,re)})},[Lt]);return{virtualItems:no,totalSize:et,scrollToOffset:Dt,scrollToIndex:ao,measure:G}}var ju=function(t,r,n,a){for(;t<=r;){var s=(t+r)/2|0,o=n(s);if(o<a)t=s+1;else if(o>a)r=s-1;else return s}return t>0?t-1:0};function Ou(e){for(var t=e.measurements,r=e.outerSize,n=e.scrollOffset,a=t.length-1,s=function(u){return t[u].start},o=ju(0,a,s,n),c=o;c<a&&t[c].end<n+r;)c++;return{start:o,end:c}}const Eu=Object.freeze(Object.defineProperty({__proto__:null,defaultRangeExtractor:Fa,useVirtual:Su},Symbol.toStringTag,{value:"Module"})),Cu=Ln(Eu);var In;function Iu(){if(In)return fe;In=1;var e=fe&&fe.__assign||function(){return e=Object.assign||function(d){for(var m,g=1,p=arguments.length;g<p;g++){m=arguments[g];for(var b in m)Object.prototype.hasOwnProperty.call(m,b)&&(d[b]=m[b])}return d},e.apply(this,arguments)},t=fe&&fe.__createBinding||(Object.create?function(d,m,g,p){p===void 0&&(p=g),Object.defineProperty(d,p,{enumerable:!0,get:function(){return m[g]}})}:function(d,m,g,p){p===void 0&&(p=g),d[p]=m[g]}),r=fe&&fe.__setModuleDefault||(Object.create?function(d,m){Object.defineProperty(d,"default",{enumerable:!0,value:m})}:function(d,m){d.default=m}),n=fe&&fe.__importStar||function(d){if(d&&d.__esModule)return d;var m={};if(d!=null)for(var g in d)g!=="default"&&Object.prototype.hasOwnProperty.call(d,g)&&t(m,d,g);return r(m,d),m};Object.defineProperty(fe,"__esModule",{value:!0}),fe.KBarResults=void 0;var a=n(ne()),s=Cu,o=$a(),c=Ae(),i=Ge(),u=0,f=function(d){var m=a.useRef(null),g=a.useRef(null),p=a.useRef(d.items);p.current=d.items;var b=(0,s.useVirtual)({size:p.current.length,parentRef:g}),h=(0,c.useKBar)(function(I){return{search:I.searchQuery,currentRootActionId:I.currentRootActionId,activeIndex:I.activeIndex}}),w=h.query,y=h.search,v=h.currentRootActionId,S=h.activeIndex,C=h.options;a.useEffect(function(){var I=function(x){var _;x.isComposing||(x.key==="ArrowUp"||x.ctrlKey&&x.key==="p"?(x.preventDefault(),x.stopPropagation(),w.setActiveIndex(function(P){var M=P>u?P-1:P;if(typeof p.current[M]=="string"){if(M===0)return P;M-=1}return M})):x.key==="ArrowDown"||x.ctrlKey&&x.key==="n"?(x.preventDefault(),x.stopPropagation(),w.setActiveIndex(function(P){var M=P<p.current.length-1?P+1:P;if(typeof p.current[M]=="string"){if(M===p.current.length-1)return P;M+=1}return M})):x.key==="Enter"&&(x.preventDefault(),x.stopPropagation(),(_=m.current)===null||_===void 0||_.click()))};return window.addEventListener("keydown",I,{capture:!0}),function(){return window.removeEventListener("keydown",I,{capture:!0})}},[w]);var T=b.scrollToIndex;a.useEffect(function(){T(S,{align:S<=1?"end":"auto"})},[S,T]),a.useEffect(function(){w.setActiveIndex(typeof d.items[u]=="string"?u+1:u)},[y,v,d.items,w]);var E=a.useCallback(function(I){var x,_;typeof I!="string"&&(I.command?(I.command.perform(I),w.toggle()):(w.setSearch(""),w.setCurrentRootAction(I.id)),(_=(x=C.callbacks)===null||x===void 0?void 0:x.onSelectAction)===null||_===void 0||_.call(x,I))},[w,C]),O=(0,i.usePointerMovedSinceMount)();return a.createElement("div",{ref:g,style:{maxHeight:d.maxHeight||400,position:"relative",overflow:"auto"}},a.createElement("div",{role:"listbox",id:o.KBAR_LISTBOX,style:{height:b.totalSize+"px",width:"100%"}},b.virtualItems.map(function(I){var x=p.current[I.index],_=typeof x!="string"&&{onPointerMove:function(){return O&&S!==I.index&&w.setActiveIndex(I.index)},onPointerDown:function(){return w.setActiveIndex(I.index)},onClick:function(){return E(x)}},P=I.index===S;return a.createElement("div",e({ref:P?m:null,id:(0,o.getListboxItemId)(I.index),role:"option","aria-selected":P,key:I.index,style:{position:"absolute",top:0,left:0,width:"100%",transform:"translateY("+I.start+"px)"}},_),a.cloneElement(d.onRender({item:x,active:P}),{ref:I.measureRef}))})))};return fe.KBarResults=f,fe}var be={},Tn;function Tu(){if(Tn)return be;Tn=1;var e=be&&be.__createBinding||(Object.create?function(o,c,i,u){u===void 0&&(u=i),Object.defineProperty(o,u,{enumerable:!0,get:function(){return c[i]}})}:function(o,c,i,u){u===void 0&&(u=i),o[u]=c[i]}),t=be&&be.__setModuleDefault||(Object.create?function(o,c){Object.defineProperty(o,"default",{enumerable:!0,value:c})}:function(o,c){o.default=c}),r=be&&be.__importStar||function(o){if(o&&o.__esModule)return o;var c={};if(o!=null)for(var i in o)i!=="default"&&Object.prototype.hasOwnProperty.call(o,i)&&e(c,o,i);return t(c,o),c};Object.defineProperty(be,"__esModule",{value:!0}),be.useRegisterActions=void 0;var n=r(ne()),a=Ae();function s(o,c){c===void 0&&(c=[]);var i=(0,a.useKBar)().query,u=n.useMemo(function(){return o},c);n.useEffect(function(){if(u.length){var f=i.registerActions(u);return function(){f()}}},[i,u])}return be.useRegisterActions=s,be}var pe={},Rn;function Ru(){if(Rn)return pe;Rn=1;var e=pe&&pe.__assign||function(){return e=Object.assign||function(d){for(var m,g=1,p=arguments.length;g<p;g++){m=arguments[g];for(var b in m)Object.prototype.hasOwnProperty.call(m,b)&&(d[b]=m[b])}return d},e.apply(this,arguments)},t=pe&&pe.__createBinding||(Object.create?function(d,m,g,p){p===void 0&&(p=g),Object.defineProperty(d,p,{enumerable:!0,get:function(){return m[g]}})}:function(d,m,g,p){p===void 0&&(p=g),d[p]=m[g]}),r=pe&&pe.__setModuleDefault||(Object.create?function(d,m){Object.defineProperty(d,"default",{enumerable:!0,value:m})}:function(d,m){d.default=m}),n=pe&&pe.__importStar||function(d){if(d&&d.__esModule)return d;var m={};if(d!=null)for(var g in d)g!=="default"&&Object.prototype.hasOwnProperty.call(d,g)&&t(m,d,g);return r(m,d),m};Object.defineProperty(pe,"__esModule",{value:!0}),pe.KBarAnimator=void 0;var a=n(ne()),s=Qe(),o=Ae(),c=Ge(),i=[{opacity:0,transform:"scale(.99)"},{opacity:1,transform:"scale(1.01)"},{opacity:1,transform:"scale(1)"}],u=[{transform:"scale(1)"},{transform:"scale(.98)"},{transform:"scale(1)"}],f=function(d){var m,g,p=d.children,b=d.style,h=d.className,w=d.disableCloseOnOuterClick,y=(0,o.useKBar)(function(M){return{visualState:M.visualState,currentRootActionId:M.currentRootActionId}}),v=y.visualState,S=y.currentRootActionId,C=y.query,T=y.options,E=a.useRef(null),O=a.useRef(null),I=((m=T?.animations)===null||m===void 0?void 0:m.enterMs)||0,x=((g=T?.animations)===null||g===void 0?void 0:g.exitMs)||0;a.useEffect(function(){if(v!==s.VisualState.showing){var M=v===s.VisualState.animatingIn?I:x,j=E.current;j?.animate(i,{duration:M,easing:v===s.VisualState.animatingOut?"ease-in":"ease-out",direction:v===s.VisualState.animatingOut?"reverse":"normal",fill:"forwards"})}},[T,v,I,x]);var _=a.useRef();a.useEffect(function(){if(v===s.VisualState.showing){var M=E.current,j=O.current;if(!M||!j)return;var N=new ResizeObserver(function(k){for(var D=0,A=k;D<A.length;D++){var V=A[D],W=V.contentRect;_.current||(_.current=W.height),M.animate([{height:_.current+"px"},{height:W.height+"px"}],{duration:I/2,easing:"ease-out",fill:"forwards"}),_.current=W.height}});return N.observe(j),function(){N.unobserve(j)}}},[v,T,I,x]);var P=a.useRef(!0);return a.useEffect(function(){if(P.current){P.current=!1;return}var M=E.current;M&&M.animate(u,{duration:I,easing:"ease-out"})},[S,I]),(0,c.useOuterClick)(E,function(){var M,j;w||(C.setVisualState(s.VisualState.animatingOut),(j=(M=T.callbacks)===null||M===void 0?void 0:M.onClose)===null||j===void 0||j.call(M))}),a.createElement("div",{ref:E,style:e(e(e({},i[0]),b),{pointerEvents:"auto"}),className:h},a.createElement("div",{ref:O},p))};return pe.KBarAnimator=f,pe}var Fe={},Mn;function Mu(){return Mn||(Mn=1,function(e){var t=Fe&&Fe.__createBinding||(Object.create?function(n,a,s,o){o===void 0&&(o=s),Object.defineProperty(n,o,{enumerable:!0,get:function(){return a[s]}})}:function(n,a,s,o){o===void 0&&(o=s),n[o]=a[s]}),r=Fe&&Fe.__exportStar||function(n,a){for(var s in n)s!=="default"&&!Object.prototype.hasOwnProperty.call(a,s)&&t(a,n,s)};Object.defineProperty(e,"__esModule",{value:!0}),r(Ia(),e),r(Ca(),e)}(Fe)),Fe}var An;function Au(){return An||(An=1,function(e){var t=ze&&ze.__createBinding||(Object.create?function(a,s,o,c){c===void 0&&(c=o),Object.defineProperty(a,c,{enumerable:!0,get:function(){return s[o]}})}:function(a,s,o,c){c===void 0&&(c=o),a[c]=s[o]}),r=ze&&ze.__exportStar||function(a,s){for(var o in a)o!=="default"&&!Object.prototype.hasOwnProperty.call(s,o)&&t(s,a,o)};Object.defineProperty(e,"__esModule",{value:!0}),e.Priority=e.createAction=void 0;var n=Ge();Object.defineProperty(e,"createAction",{enumerable:!0,get:function(){return n.createAction}}),Object.defineProperty(e,"Priority",{enumerable:!0,get:function(){return n.Priority}}),r(ou(),e),r(du(),e),r(fu(),e),r($a(),e),r(Iu(),e),r(Ae(),e),r(Tu(),e),r(Ta(),e),r(Ru(),e),r(Qe(),e),r(Mu(),e)}(ze)),ze}var Pu=Au();function ku(){const{query:e}=Pu.useKBar();return l.jsx("div",{className:"w-full space-y-2",children:l.jsxs(Mt,{variant:"outline",className:"bg-background text-muted-foreground relative h-9 w-full justify-start rounded-[0.5rem] text-sm font-normal shadow-none sm:pr-12 md:w-40 lg:w-64",onClick:e?.toggle,children:[l.jsx(Ii,{className:"mr-2 h-4 w-4"}),"Search...",l.jsxs("kbd",{className:"bg-muted pointer-events-none absolute top-[0.3rem] right-[0.3rem] hidden h-6 items-center gap-1 rounded border px-1.5 font-mono text-[10px] font-medium opacity-100 select-none sm:flex",children:[l.jsx("span",{className:"text-xs",children:"⌘"}),"K"]})]})})}/*! js-cookie v3.0.5 | MIT */function yt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)e[n]=r[n]}return e}var Nu={read:function(e){return e[0]==='"'&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}};function vr(e,t){function r(a,s,o){if(!(typeof document>"u")){o=yt({},t,o),typeof o.expires=="number"&&(o.expires=new Date(Date.now()+o.expires*864e5)),o.expires&&(o.expires=o.expires.toUTCString()),a=encodeURIComponent(a).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var c="";for(var i in o)o[i]&&(c+="; "+i,o[i]!==!0&&(c+="="+o[i].split(";")[0]));return document.cookie=a+"="+e.write(s,a)+c}}function n(a){if(!(typeof document>"u"||arguments.length&&!a)){for(var s=document.cookie?document.cookie.split("; "):[],o={},c=0;c<s.length;c++){var i=s[c].split("="),u=i.slice(1).join("=");try{var f=decodeURIComponent(i[0]);if(o[f]=e.read(u,f),a===f)break}catch{}}return a?o[a]:o}}return Object.create({set:r,get:n,remove:function(a,s){r(a,"",yt({},s,{expires:-1}))},withAttributes:function(a){return vr(this.converter,yt({},this.attributes,a))},withConverter:function(a){return vr(yt({},this.converter,a),this.attributes)}},{attributes:{value:Object.freeze(t)},converter:{value:Object.freeze(e)}})}var at=vr(Nu,{path:"/"});const St="active_theme",Du="default-scaled";function Lu(e){if(!(typeof window>"u"))try{at.remove(St,{path:"/"}),at.set(St,e,{path:"/",expires:365,sameSite:"lax"});const t=at.get(St)}catch{}}const Ka=R.createContext(void 0);function zu({children:e,initialTheme:t}){const[r,n]=R.useState(()=>{if(typeof window<"u"){const a=at.get(St);if(a)return a}return t||Du});return R.useEffect(()=>{Lu(r);const a=document.body;Array.from(a.classList).filter(o=>o.startsWith("theme-")).forEach(o=>a.classList.remove(o)),a.classList.add(`theme-${r}`),r.endsWith("-scaled")&&a.classList.add("theme-scaled")},[r]),l.jsx(Ka.Provider,{value:{activeTheme:r,setActiveTheme:n},children:e})}function qa(){const e=R.useContext(Ka);if(e===void 0)throw new Error("useThemeConfig must be used within an ActiveThemeProvider");return e}function $u({className:e,...t}){return l.jsx(es,{"data-slot":"label",className:$("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}const Bu=[{name:"Default",value:"default"},{name:"Blue",value:"blue"},{name:"Green",value:"green"},{name:"Amber",value:"amber"}],Fu=[{name:"Default",value:"default-scaled"},{name:"Blue",value:"blue-scaled"},{name:"Green",value:"green-scaled"}],Ku=[{name:"Mono",value:"mono-scaled"}];function qu(){const{activeTheme:e,setActiveTheme:t}=qa(),r=n=>{t(n)};return l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx($u,{htmlFor:"theme-selector",className:"sr-only",children:"Theme"}),l.jsxs(ci,{value:e,onValueChange:r,children:[l.jsxs(ui,{id:"theme-selector",className:"justify-start *:data-[slot=select-value]:w-12",children:[l.jsx("span",{className:"text-muted-foreground hidden sm:block",children:"Select a theme:"}),l.jsx("span",{className:"text-muted-foreground block sm:hidden",children:"Theme"}),l.jsx(li,{placeholder:"Select a theme"})]}),l.jsxs(di,{align:"end",children:[l.jsxs(Kt,{children:[l.jsx(qt,{children:"Default"}),Bu.map(n=>l.jsx(Vt,{value:n.value,children:n.name},n.name))]}),l.jsx(fi,{}),l.jsxs(Kt,{children:[l.jsx(qt,{children:"Scaled"}),Fu.map(n=>l.jsx(Vt,{value:n.value,children:n.name},n.name))]}),l.jsxs(Kt,{children:[l.jsx(qt,{children:"Monospaced"}),Ku.map(n=>l.jsx(Vt,{value:n.value,children:n.name},n.name))]})]})]})]})}const{useContext:Vu}=$n,Gu={theme:"system",setTheme:()=>null,resolvedTheme:void 0},Va=R.createContext(Gu);function Uu(){const e=Vu(Va);if(e===void 0)throw new Error("useTheme must be used within a ThemeProvider");return e}function Wu(){const{setTheme:e,resolvedTheme:t}=Uu(),r=R.useCallback(n=>{const a=t==="dark"?"light":"dark",s=document.documentElement;if(!document.startViewTransition){e(a);return}n&&(s.style.setProperty("--x",`${n.clientX}px`),s.style.setProperty("--y",`${n.clientY}px`)),document.startViewTransition(()=>{e(a)})},[t,e]);return l.jsxs(Mt,{variant:"secondary",size:"icon",className:"group/toggle size-8",onClick:r,children:[l.jsx(gi,{}),l.jsx("span",{className:"sr-only",children:"Toggle theme"})]})}function Hu(){return l.jsxs("header",{className:"sticky top-0 z-50 flex h-16 shrink-0 items-center justify-between gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 bg-background border-b",children:[l.jsxs("div",{className:"flex items-center gap-2 px-4",children:[l.jsx(_c,{className:"-ml-1"}),l.jsx(Di,{orientation:"vertical",className:"mr-2 h-4"}),l.jsx(ul,{})]}),l.jsxs("div",{className:"flex items-center gap-2 px-4",children:[l.jsx("div",{className:"hidden md:flex",children:l.jsx(ku,{})}),l.jsx(Wu,{}),l.jsx(qu,{})]})]})}const Yu=()=>{const{data:e}=Ea();return{can:R.useCallback(r=>!!(e?.auth?.grantedPolicies&&e.auth.grantedPolicies[r]),[e?.auth?.grantedPolicies])}};function Xu({message:e="You don't have permission to access this page.",showBackButton:t=!0}){return l.jsx("div",{className:"flex flex-col items-center justify-center min-h-[50vh] p-6",children:l.jsxs("div",{className:"flex flex-col items-center text-center space-y-4 max-w-md",children:[l.jsx(ti,{className:"h-12 w-12 text-gray-400"}),l.jsx("h2",{className:"text-2xl font-semibold tracking-tight",children:"Access Restricted"}),l.jsx("p",{className:"text-gray-500",children:e}),t&&l.jsx(Mt,{onClick:()=>window.history.back(),variant:"outline",className:"mt-4",children:"Go Back"})]})})}function Ju({policy:e,children:t,fallback:r,message:n="You don't have permission to access this page."}){const{can:a}=Yu();return a(e)?l.jsx(l.Fragment,{children:t}):r?l.jsx(l.Fragment,{children:r}):l.jsx(Xu,{message:n})}const Qu=new uo({defaultOptions:{queries:{staleTime:10*1e3,refetchOnWindowFocus:!0}}});function Zu({children:e}){return l.jsx(lo,{client:Qu,children:e})}const{useEffect:Pn,useState:kn}=$n;function ed({children:e,defaultTheme:t="system",storageKey:r="vite-ui-theme",...n}){const[a,s]=kn(()=>localStorage.getItem(r)||t),[o,c]=kn(()=>window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light");Pn(()=>{const u=window.document.documentElement;if(u.classList.remove("light","dark"),a==="system"){u.classList.add(o);return}u.classList.add(a)},[a,o]),Pn(()=>{const u=window.matchMedia("(prefers-color-scheme: dark)"),f=()=>{const d=window.document.documentElement;d.classList.remove("light","dark");const m=u.matches?"dark":"light";c(m),a==="system"&&d.classList.add(m)};return u.addEventListener("change",f),()=>u.removeEventListener("change",f)},[a]);const i={theme:a,setTheme:u=>{localStorage.setItem(r,u),s(u)},resolvedTheme:a==="system"?o:a};return l.jsx(Va.Provider,{...n,value:i,children:e})}const td=1,rd=1e6;let tr=0;function nd(){return tr=(tr+1)%Number.MAX_SAFE_INTEGER,tr.toString()}const rr=new Map,Nn=e=>{if(rr.has(e))return;const t=setTimeout(()=>{rr.delete(e),ot({type:"REMOVE_TOAST",toastId:e})},rd);rr.set(e,t)},ad=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,td)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(r=>r.id===t.toast.id?{...r,...t.toast}:r)};case"DISMISS_TOAST":{const{toastId:r}=t;return r?Nn(r):e.toasts.forEach(n=>{Nn(n.id)}),{...e,toasts:e.toasts.map(n=>n.id===r||r===void 0?{...n,open:!1}:n)}}case"REMOVE_TOAST":return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(r=>r.id!==t.toastId)}}},jt=[];let Ot={toasts:[]};function ot(e){Ot=ad(Ot,e),jt.forEach(t=>{t(Ot)})}function od({...e}){const t=nd(),r=a=>ot({type:"UPDATE_TOAST",toast:{...a,id:t}}),n=()=>ot({type:"DISMISS_TOAST",toastId:t});return ot({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:a=>{a||n()}}}),{id:t,dismiss:n,update:r}}function sd(){const[e,t]=R.useState(Ot);return R.useEffect(()=>(jt.push(t),()=>{const r=jt.indexOf(t);r>-1&&jt.splice(r,1)}),[e]),{...e,toast:od,dismiss:r=>ot({type:"DISMISS_TOAST",toastId:r})}}var Ga="ToastProvider",[Mr,id,cd]=ts("Toast"),[Ua,Qf]=qn("Toast",[cd]),[ld,Nt]=Ua(Ga),Wa=e=>{const{__scopeToast:t,label:r="Notification",duration:n=5e3,swipeDirection:a="right",swipeThreshold:s=50,children:o}=e,[c,i]=R.useState(null),[u,f]=R.useState(0),d=R.useRef(!1),m=R.useRef(!1);return r.trim(),l.jsx(Mr.Provider,{scope:t,children:l.jsx(ld,{scope:t,label:r,duration:n,swipeDirection:a,swipeThreshold:s,toastCount:u,viewport:c,onViewportChange:i,onToastAdd:R.useCallback(()=>f(g=>g+1),[]),onToastRemove:R.useCallback(()=>f(g=>g-1),[]),isFocusedToastEscapeKeyDownRef:d,isClosePausedRef:m,children:o})})};Wa.displayName=Ga;var Ha="ToastViewport",ud=["F8"],gr="toast.viewportPause",br="toast.viewportResume",Ya=R.forwardRef((e,t)=>{const{__scopeToast:r,hotkey:n=ud,label:a="Notifications ({hotkey})",...s}=e,o=Nt(Ha,r),c=id(r),i=R.useRef(null),u=R.useRef(null),f=R.useRef(null),d=R.useRef(null),m=Rt(t,d,o.onViewportChange),g=n.join("+").replace(/Key/g,"").replace(/Digit/g,""),p=o.toastCount>0;R.useEffect(()=>{const h=w=>{n.length!==0&&n.every(v=>w[v]||w.code===v)&&d.current?.focus()};return document.addEventListener("keydown",h),()=>document.removeEventListener("keydown",h)},[n]),R.useEffect(()=>{const h=i.current,w=d.current;if(p&&h&&w){const y=()=>{if(!o.isClosePausedRef.current){const T=new CustomEvent(gr);w.dispatchEvent(T),o.isClosePausedRef.current=!0}},v=()=>{if(o.isClosePausedRef.current){const T=new CustomEvent(br);w.dispatchEvent(T),o.isClosePausedRef.current=!1}},S=T=>{!h.contains(T.relatedTarget)&&v()},C=()=>{h.contains(document.activeElement)||v()};return h.addEventListener("focusin",y),h.addEventListener("focusout",S),h.addEventListener("pointermove",y),h.addEventListener("pointerleave",C),window.addEventListener("blur",y),window.addEventListener("focus",v),()=>{h.removeEventListener("focusin",y),h.removeEventListener("focusout",S),h.removeEventListener("pointermove",y),h.removeEventListener("pointerleave",C),window.removeEventListener("blur",y),window.removeEventListener("focus",v)}}},[p,o.isClosePausedRef]);const b=R.useCallback(({tabbingDirection:h})=>{const y=c().map(v=>{const S=v.ref.current,C=[S,...Od(S)];return h==="forwards"?C:C.reverse()});return(h==="forwards"?y.reverse():y).flat()},[c]);return R.useEffect(()=>{const h=d.current;if(h){const w=y=>{const v=y.altKey||y.ctrlKey||y.metaKey;if(y.key==="Tab"&&!v){const C=document.activeElement,T=y.shiftKey;if(y.target===h&&T){u.current?.focus();return}const I=b({tabbingDirection:T?"backwards":"forwards"}),x=I.findIndex(_=>_===C);nr(I.slice(x+1))?y.preventDefault():T?u.current?.focus():f.current?.focus()}};return h.addEventListener("keydown",w),()=>h.removeEventListener("keydown",w)}},[c,b]),l.jsxs(rs,{ref:i,role:"region","aria-label":a.replace("{hotkey}",g),tabIndex:-1,style:{pointerEvents:p?void 0:"none"},children:[p&&l.jsx(yr,{ref:u,onFocusFromOutsideViewport:()=>{const h=b({tabbingDirection:"forwards"});nr(h)}}),l.jsx(Mr.Slot,{scope:r,children:l.jsx(Me.ol,{tabIndex:-1,...s,ref:m})}),p&&l.jsx(yr,{ref:f,onFocusFromOutsideViewport:()=>{const h=b({tabbingDirection:"backwards"});nr(h)}})]})});Ya.displayName=Ha;var Xa="ToastFocusProxy",yr=R.forwardRef((e,t)=>{const{__scopeToast:r,onFocusFromOutsideViewport:n,...a}=e,s=Nt(Xa,r);return l.jsx(Wn,{"aria-hidden":!0,tabIndex:0,...a,ref:t,style:{position:"fixed"},onFocus:o=>{const c=o.relatedTarget;!s.viewport?.contains(c)&&n()}})});yr.displayName=Xa;var ut="Toast",dd="toast.swipeStart",fd="toast.swipeMove",pd="toast.swipeCancel",hd="toast.swipeEnd",Ja=R.forwardRef((e,t)=>{const{forceMount:r,open:n,defaultOpen:a,onOpenChange:s,...o}=e,[c,i]=Gn({prop:n,defaultProp:a??!0,onChange:s,caller:ut});return l.jsx(wr,{present:r||c,children:l.jsx(gd,{open:c,...o,ref:t,onClose:()=>i(!1),onPause:Et(e.onPause),onResume:Et(e.onResume),onSwipeStart:ae(e.onSwipeStart,u=>{u.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:ae(e.onSwipeMove,u=>{const{x:f,y:d}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","move"),u.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${f}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${d}px`)}),onSwipeCancel:ae(e.onSwipeCancel,u=>{u.currentTarget.setAttribute("data-swipe","cancel"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:ae(e.onSwipeEnd,u=>{const{x:f,y:d}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","end"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${f}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${d}px`),i(!1)})})})});Ja.displayName=ut;var[md,vd]=Ua(ut,{onClose(){}}),gd=R.forwardRef((e,t)=>{const{__scopeToast:r,type:n="foreground",duration:a,open:s,onClose:o,onEscapeKeyDown:c,onPause:i,onResume:u,onSwipeStart:f,onSwipeMove:d,onSwipeCancel:m,onSwipeEnd:g,...p}=e,b=Nt(ut,r),[h,w]=R.useState(null),y=Rt(t,j=>w(j)),v=R.useRef(null),S=R.useRef(null),C=a||b.duration,T=R.useRef(0),E=R.useRef(C),O=R.useRef(0),{onToastAdd:I,onToastRemove:x}=b,_=Et(()=>{h?.contains(document.activeElement)&&b.viewport?.focus(),o()}),P=R.useCallback(j=>{!j||j===1/0||(window.clearTimeout(O.current),T.current=new Date().getTime(),O.current=window.setTimeout(_,j))},[_]);R.useEffect(()=>{const j=b.viewport;if(j){const N=()=>{P(E.current),u?.()},k=()=>{const D=new Date().getTime()-T.current;E.current=E.current-D,window.clearTimeout(O.current),i?.()};return j.addEventListener(gr,k),j.addEventListener(br,N),()=>{j.removeEventListener(gr,k),j.removeEventListener(br,N)}}},[b.viewport,C,i,u,P]),R.useEffect(()=>{s&&!b.isClosePausedRef.current&&P(C)},[s,C,b.isClosePausedRef,P]),R.useEffect(()=>(I(),()=>x()),[I,x]);const M=R.useMemo(()=>h?ro(h):null,[h]);return b.viewport?l.jsxs(l.Fragment,{children:[M&&l.jsx(bd,{__scopeToast:r,role:"status","aria-live":n==="foreground"?"assertive":"polite","aria-atomic":!0,children:M}),l.jsx(md,{scope:r,onClose:_,children:ns.createPortal(l.jsx(Mr.ItemSlot,{scope:r,children:l.jsx(as,{asChild:!0,onEscapeKeyDown:ae(c,()=>{b.isFocusedToastEscapeKeyDownRef.current||_(),b.isFocusedToastEscapeKeyDownRef.current=!1}),children:l.jsx(Me.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":s?"open":"closed","data-swipe-direction":b.swipeDirection,...p,ref:y,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:ae(e.onKeyDown,j=>{j.key==="Escape"&&(c?.(j.nativeEvent),j.nativeEvent.defaultPrevented||(b.isFocusedToastEscapeKeyDownRef.current=!0,_()))}),onPointerDown:ae(e.onPointerDown,j=>{j.button===0&&(v.current={x:j.clientX,y:j.clientY})}),onPointerMove:ae(e.onPointerMove,j=>{if(!v.current)return;const N=j.clientX-v.current.x,k=j.clientY-v.current.y,D=!!S.current,A=["left","right"].includes(b.swipeDirection),V=["left","up"].includes(b.swipeDirection)?Math.min:Math.max,W=A?V(0,N):0,Q=A?0:V(0,k),ee=j.pointerType==="touch"?10:2,G={x:W,y:Q},U={originalEvent:j,delta:G};D?(S.current=G,xt(fd,d,U,{discrete:!1})):Dn(G,b.swipeDirection,ee)?(S.current=G,xt(dd,f,U,{discrete:!1}),j.target.setPointerCapture(j.pointerId)):(Math.abs(N)>ee||Math.abs(k)>ee)&&(v.current=null)}),onPointerUp:ae(e.onPointerUp,j=>{const N=S.current,k=j.target;if(k.hasPointerCapture(j.pointerId)&&k.releasePointerCapture(j.pointerId),S.current=null,v.current=null,N){const D=j.currentTarget,A={originalEvent:j,delta:N};Dn(N,b.swipeDirection,b.swipeThreshold)?xt(hd,g,A,{discrete:!0}):xt(pd,m,A,{discrete:!0}),D.addEventListener("click",V=>V.preventDefault(),{once:!0})}})})})}),b.viewport)})]}):null}),bd=e=>{const{__scopeToast:t,children:r,...n}=e,a=Nt(ut,t),[s,o]=R.useState(!1),[c,i]=R.useState(!1);return Sd(()=>o(!0)),R.useEffect(()=>{const u=window.setTimeout(()=>i(!0),1e3);return()=>window.clearTimeout(u)},[]),c?null:l.jsx(Un,{asChild:!0,children:l.jsx(Wn,{...n,children:s&&l.jsxs(l.Fragment,{children:[a.label," ",r]})})})},yd="ToastTitle",Qa=R.forwardRef((e,t)=>{const{__scopeToast:r,...n}=e;return l.jsx(Me.div,{...n,ref:t})});Qa.displayName=yd;var xd="ToastDescription",Za=R.forwardRef((e,t)=>{const{__scopeToast:r,...n}=e;return l.jsx(Me.div,{...n,ref:t})});Za.displayName=xd;var wd="ToastAction",_d=R.forwardRef((e,t)=>{const{altText:r,...n}=e;return r.trim()?l.jsx(to,{altText:r,asChild:!0,children:l.jsx(Ar,{...n,ref:t})}):null});_d.displayName=wd;var eo="ToastClose",Ar=R.forwardRef((e,t)=>{const{__scopeToast:r,...n}=e,a=vd(eo,r);return l.jsx(to,{asChild:!0,children:l.jsx(Me.button,{type:"button",...n,ref:t,onClick:ae(e.onClick,a.onClose)})})});Ar.displayName=eo;var to=R.forwardRef((e,t)=>{const{__scopeToast:r,altText:n,...a}=e;return l.jsx(Me.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":n||void 0,...a,ref:t})});function ro(e){const t=[];return Array.from(e.childNodes).forEach(n=>{if(n.nodeType===n.TEXT_NODE&&n.textContent&&t.push(n.textContent),jd(n)){const a=n.ariaHidden||n.hidden||n.style.display==="none",s=n.dataset.radixToastAnnounceExclude==="";if(!a)if(s){const o=n.dataset.radixToastAnnounceAlt;o&&t.push(o)}else t.push(...ro(n))}}),t}function xt(e,t,r,{discrete:n}){const a=r.originalEvent.currentTarget,s=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:r});t&&a.addEventListener(e,t,{once:!0}),n?os(a,s):a.dispatchEvent(s)}var Dn=(e,t,r=0)=>{const n=Math.abs(e.x),a=Math.abs(e.y),s=n>a;return t==="left"||t==="right"?s&&n>r:!s&&a>r};function Sd(e=()=>{}){const t=Et(e);ss(()=>{let r=0,n=0;return r=window.requestAnimationFrame(()=>n=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(r),window.cancelAnimationFrame(n)}},[t])}function jd(e){return e.nodeType===e.ELEMENT_NODE}function Od(e){const t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:n=>{const a=n.tagName==="INPUT"&&n.type==="hidden";return n.disabled||n.hidden||a?NodeFilter.FILTER_SKIP:n.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function nr(e){const t=document.activeElement;return e.some(r=>r===t?!0:(r.focus(),document.activeElement!==t))}var Ed=Wa,Cd=Ya,Id=Ja,Td=Qa,Rd=Za,Md=Ar;const Ad=Ed;function Pd({className:e,...t}){return l.jsx(Cd,{className:$("fixed top-0 right-0 z-50 flex max-h-screen w-full flex-col-reverse p-4 sm:top-auto sm:bottom-0 sm:flex-col md:max-w-[400px]",e),...t})}const kd=Sr("group pointer-events-auto relative flex w-full items-center justify-between overflow-hidden rounded-md border p-4 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:data-[swipe-direction=left]:slide-out-to-left-full data-[state=closed]:data-[swipe-direction=right]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-white",success:"border-green-500 bg-green-50 text-green-900 dark:border-green-400 dark:bg-green-950 dark:text-green-100",error:"border-red-500 bg-red-50 text-red-900 dark:border-red-400 dark:bg-red-950 dark:text-red-100",info:"border-blue-500 bg-blue-50 text-blue-900 dark:border-blue-400 dark:bg-blue-950 dark:text-blue-100",warning:"border-yellow-500 bg-yellow-50 text-yellow-900 dark:border-yellow-400 dark:bg-yellow-950 dark:text-yellow-100"}},defaultVariants:{variant:"default"}});function Nd({className:e,variant:t,...r}){return l.jsx(Id,{className:$(kd({variant:t}),e),...r})}function Dd({className:e,asChild:t=!1,...r}){return l.jsx(Md,{className:$(!t&&"group focus-visible:border-ring focus-visible:ring-ring/50 absolute top-3 right-3 flex size-7 items-center justify-center rounded transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:pointer-events-none",e),"toast-close":"",asChild:t,...r,children:t?r.children:l.jsx(Er,{size:16,className:"opacity-60 transition-opacity group-hover:opacity-100","aria-hidden":"true"})})}function Ld({className:e,...t}){return l.jsx(Td,{className:$("text-sm font-medium",e),...t})}function zd({className:e,...t}){return l.jsx(Rd,{className:$("text-muted-foreground text-sm",e),...t})}function $d(){const{toasts:e}=sd();return l.jsxs(Ad,{children:[e.map(function({id:t,title:r,description:n,action:a,...s}){return l.jsx(Nd,{...s,children:l.jsxs("div",{className:"flex w-full justify-between gap-2",children:[l.jsxs("div",{className:"flex flex-col gap-3",children:[l.jsxs("div",{className:"space-y-1",children:[r&&l.jsx(Ld,{children:r}),n&&l.jsx(zd,{children:n})]}),l.jsx("div",{children:a})]}),l.jsx("div",{children:l.jsx(Dd,{})})]})},t)}),l.jsx(Pd,{})]})}function Bd({children:e,policy:t,message:r}){const n=at.get("sidebar_state")==="true",{activeTheme:a}=qa(),s=a.endsWith("-scaled");return l.jsxs("div",{className:$("bg-background font-sans antialiased",`theme-${a}`,s?"theme-scaled":""),children:[l.jsx($d,{}),l.jsxs(xc,{defaultOpen:n,children:[l.jsx(sl,{}),l.jsxs(jc,{children:[l.jsx(Hu,{}),l.jsx("div",{className:"flex flex-1 flex-col overflow-auto",children:l.jsx("div",{className:"@container/main flex flex-1 flex-col gap-2",children:l.jsx("div",{className:"flex flex-col gap-4 py-4 px-4 md:gap-6 md:py-6 md:px-6",children:t!==void 0?l.jsx(Ju,{policy:t,message:r,children:e}):e})})})]})]})]})}function Zf(e){return l.jsx(R.StrictMode,{children:l.jsx(Zu,{children:l.jsx(ed,{defaultTheme:"system",storageKey:"vite-ui-theme",children:l.jsx(zu,{children:l.jsx(Bd,{...e})})})})})}export{$f as $,Zf as A,Mt as B,Mf as C,ni as D,Rf as E,kf as F,Pf as G,Af as H,rf as I,Df as J,Nf as K,$u as L,$ as M,Or as N,ce as O,Ys as P,Jc as Q,Bs as R,ci as S,$d as T,ca as U,tf as V,_r as W,Er as X,Ff as Y,Bf as Z,Kf as _,ui as a,Qd as a0,zf as a1,Jd as a2,hf as a3,vf as a4,mf as a5,sf as a6,lf as a7,cf as a8,xc as a9,Ud as aA,Hd as aB,Wd as aC,Yd as aD,Sr as aE,Vr as aF,wc as aa,Cc as ab,Ic as ac,of as ad,lr as ae,wt as af,_t as ag,Ju as ah,Zd as ai,Gf as aj,qf as ak,bf as al,gf as am,yf as an,Vf as ao,Hf as ap,Uf as aq,Jf as ar,Xf as as,Wf as at,Yf as au,wf as av,xf as aw,Lf as ax,Xd as ay,Gd as az,li as b,di as c,Vt as d,ai as e,oi as f,ef as g,Si as h,ii as i,vt as j,df as k,ff as l,uf as m,Ve as n,sd as o,pf as p,Sf as q,Ef as r,Cf as s,nf as t,Yu as u,jf as v,Of as w,_f as x,Tf as y,If as z};
//# sourceMappingURL=app-layout-CNB1Wtrx.js.map
