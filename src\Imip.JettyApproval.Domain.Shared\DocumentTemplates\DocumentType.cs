using System.ComponentModel.DataAnnotations;

namespace Imip.JettyApproval.DocumentTemplates;

/// <summary>
/// Enum for document types
/// </summary>
public enum DocumentType
{
    [Display(Name = "Invoice")]
    Invoice = 1,

    [Display(Name = "Report")]
    Report = 2,

    [Display(Name = "Contract")]
    Contract = 3,

    [Display(Name = "Letter")]
    Letter = 4,

    [Display(Name = "Certificate")]
    Certificate = 5,

    [Display(Name = "RegistrationCard")]
    RegistrationCard = 6,

    [Display(Name = "Other")]
    Other = 99
}

