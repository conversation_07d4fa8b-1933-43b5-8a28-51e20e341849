using System;
using System.ComponentModel.DataAnnotations;

namespace Imip.JettyApproval.Approvals.ApprovalDelegations;

/// <summary>
/// DTO for creating and updating ApprovalDelegation entity
/// </summary>
public class CreateUpdateApprovalDelegationDto
{
    /// <summary>
    /// ID of the original approver
    /// </summary>
    [Required]
    public Guid ApproverId { get; set; }

    /// <summary>
    /// ID of the substitute approver
    /// </summary>
    [Required]
    public Guid SubstituteId { get; set; }

    /// <summary>
    /// Start date of the delegation
    /// </summary>
    [Required]
    public DateTime StartDate { get; set; }

    /// <summary>
    /// End date of the delegation
    /// </summary>
    [Required]
    public DateTime EndDate { get; set; }

    /// <summary>
    /// Whether the delegation is currently active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Additional notes for the delegation
    /// </summary>
    [StringLength(1000)]
    public string? Notes { get; set; }
}