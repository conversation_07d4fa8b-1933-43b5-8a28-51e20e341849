using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace Imip.JettyApproval.JettyRequests;

/// <summary>
/// Repository interface for JettyRequestItem entity
/// </summary>
public interface IJettyRequestItemRepository : IRepository<JettyRequestItem, Guid>
{
    /// <summary>
    /// Gets items by jetty request ID
    /// </summary>
    Task<List<JettyRequestItem>> GetByJettyRequestIdAsync(Guid jettyRequestId);

    /// <summary>
    /// Gets items by tenant name
    /// </summary>
    Task<List<JettyRequestItem>> GetByTenantNameAsync(string tenantName);

    /// <summary>
    /// Gets items by status
    /// </summary>
    Task<List<JettyRequestItem>> GetByStatusAsync(string status);

    /// <summary>
    /// Gets items by item name
    /// </summary>
    Task<List<JettyRequestItem>> GetByItemNameAsync(string itemName);
}