﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.JettyApproval.Migrations
{
    /// <inheritdoc />
    public partial class FixApprovalDelegationRelationships : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ApprovalDelegations_AbpUsers_ApproverId1",
                table: "ApprovalDelegations");

            migrationBuilder.DropForeignKey(
                name: "FK_ApprovalDelegations_AbpUsers_SubstituteId1",
                table: "ApprovalDelegations");

            migrationBuilder.DropIndex(
                name: "IX_ApprovalDelegations_ApproverId1",
                table: "ApprovalDelegations");

            migrationBuilder.DropIndex(
                name: "IX_ApprovalDelegations_SubstituteId1",
                table: "ApprovalDelegations");

            migrationBuilder.DropColumn(
                name: "ApproverId1",
                table: "ApprovalDelegations");

            migrationBuilder.DropColumn(
                name: "SubstituteId1",
                table: "ApprovalDelegations");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "ApproverId1",
                table: "ApprovalDelegations",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "SubstituteId1",
                table: "ApprovalDelegations",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_ApprovalDelegations_ApproverId1",
                table: "ApprovalDelegations",
                column: "ApproverId1");

            migrationBuilder.CreateIndex(
                name: "IX_ApprovalDelegations_SubstituteId1",
                table: "ApprovalDelegations",
                column: "SubstituteId1");

            migrationBuilder.AddForeignKey(
                name: "FK_ApprovalDelegations_AbpUsers_ApproverId1",
                table: "ApprovalDelegations",
                column: "ApproverId1",
                principalTable: "AbpUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_ApprovalDelegations_AbpUsers_SubstituteId1",
                table: "ApprovalDelegations",
                column: "SubstituteId1",
                principalTable: "AbpUsers",
                principalColumn: "Id");
        }
    }
}
