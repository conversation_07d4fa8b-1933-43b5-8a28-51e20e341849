# Other App Integration Example

This document shows how other ABP Framework apps can integrate with the Jetty Approval app using JWT tokens from the same SSO server.

## Overview

When another ABP Framework app wants to call the Jetty Approval app's APIs, it needs to:

1. Get a JWT token from the SSO server
2. Include the token in API requests to the Jetty Approval app
3. Handle token refresh and error scenarios

## Configuration in Other App

### 1. appsettings.json

```json
{
  "AuthServer": {
    "Authority": "https://your-sso-server.com",
    "ClientId": "other-app-client-id",
    "ClientSecret": "other-app-secret",
    "RequireHttpsMetadata": true
  },
  "ExternalApis": {
    "JettyApproval": {
      "BaseUrl": "https://jetty-approval-app.com",
      "TimeoutSeconds": 30
    }
  }
}
```

### 2. Module Configuration

In the other app's module (e.g., `OtherAppWebModule.cs`):

```csharp
public override void ConfigureServices(ServiceConfigurationContext context)
{
    var configuration = context.Services.GetConfiguration();

    // Configure OIDC for user login (same as Jetty Approval app)
    context.Services.AddAuthentication(options =>
    {
        options.DefaultScheme = "Cookies";
        options.DefaultChallengeScheme = "oidc";
    })
    .AddCookie("Cookies", options =>
    {
        options.ExpireTimeSpan = TimeSpan.FromMinutes(60);
        options.SlidingExpiration = true;
    })
    .AddOpenIdConnect("oidc", options =>
    {
        options.Authority = configuration["AuthServer:Authority"];
        options.ClientId = configuration["AuthServer:ClientId"];
        options.ClientSecret = configuration["AuthServer:ClientSecret"];
        options.ResponseType = "code";
        options.SaveTokens = true;
        options.GetClaimsFromUserInfoEndpoint = true;
        
        options.Scope.Clear();
        options.Scope.Add("openid");
        options.Scope.Add("profile");
        options.Scope.Add("email");
    });

    // Register services for calling Jetty Approval app
    context.Services.AddHttpClient<IJettyApprovalApiService, JettyApprovalApiService>(client =>
    {
        client.BaseAddress = new Uri(configuration["ExternalApis:JettyApproval:BaseUrl"]);
        client.Timeout = TimeSpan.FromSeconds(
            configuration.GetValue<int>("ExternalApis:JettyApproval:TimeoutSeconds", 30)
        );
    });

    // Register token service
    context.Services.AddScoped<ITokenService, TokenService>();
}
```

## Services Implementation

### 1. Token Service

```csharp
public interface ITokenService
{
    Task<string> GetAccessTokenAsync();
    Task<string> GetRefreshTokenAsync();
    Task<bool> RefreshTokenAsync();
}

public class TokenService : ITokenService
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ILogger<TokenService> _logger;

    public TokenService(IHttpContextAccessor httpContextAccessor, ILogger<TokenService> logger)
    {
        _httpContextAccessor = httpContextAccessor;
        _logger = logger;
    }

    public async Task<string> GetAccessTokenAsync()
    {
        var httpContext = _httpContextAccessor.HttpContext;
        if (httpContext?.User?.Identity?.IsAuthenticated != true)
        {
            throw new UnauthorizedAccessException("User is not authenticated");
        }

        // Get token from user properties (stored during OIDC login)
        var accessToken = httpContext.User.Claims
            .FirstOrDefault(c => c.Type == "access_token")?.Value;

        if (string.IsNullOrEmpty(accessToken))
        {
            throw new InvalidOperationException("Access token not found");
        }

        return accessToken;
    }

    public async Task<string> GetRefreshTokenAsync()
    {
        var httpContext = _httpContextAccessor.HttpContext;
        var refreshToken = httpContext?.User?.Claims
            .FirstOrDefault(c => c.Type == "refresh_token")?.Value;

        if (string.IsNullOrEmpty(refreshToken))
        {
            throw new InvalidOperationException("Refresh token not found");
        }

        return refreshToken;
    }

    public async Task<bool> RefreshTokenAsync()
    {
        // Implementation for token refresh
        // This would typically involve calling the SSO server's token endpoint
        throw new NotImplementedException("Token refresh not implemented in this example");
    }
}
```

### 2. Jetty Approval API Service

```csharp
public interface IJettyApprovalApiService
{
    Task<JettyApprovalData> GetDataAsync();
    Task<bool> ValidateTokenAsync(string token);
    Task<JettyApprovalUser> GetCurrentUserAsync();
}

public class JettyApprovalApiService : IJettyApprovalApiService
{
    private readonly HttpClient _httpClient;
    private readonly ITokenService _tokenService;
    private readonly ILogger<JettyApprovalApiService> _logger;

    public JettyApprovalApiService(
        HttpClient httpClient,
        ITokenService tokenService,
        ILogger<JettyApprovalApiService> logger)
    {
        _httpClient = httpClient;
        _tokenService = tokenService;
        _logger = logger;
    }

    public async Task<JettyApprovalData> GetDataAsync()
    {
        try
        {
            var token = await _tokenService.GetAccessTokenAsync();
            
            var request = new HttpRequestMessage(HttpMethod.Get, "/api/some-endpoint");
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);
            
            var response = await _httpClient.SendAsync(request);
            
            if (response.StatusCode == HttpStatusCode.Unauthorized)
            {
                _logger.LogWarning("Token expired, attempting refresh");
                // Handle token refresh here
                throw new UnauthorizedAccessException("Token expired");
            }
            
            response.EnsureSuccessStatusCode();
            
            var data = await response.Content.ReadFromJsonAsync<JettyApprovalData>();
            return data ?? new JettyApprovalData();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calling Jetty Approval API");
            throw;
        }
    }

    public async Task<bool> ValidateTokenAsync(string token)
    {
        try
        {
            var request = new HttpRequestMessage(HttpMethod.Post, "/api/token-validation/validate");
            request.Content = new StringContent(
                JsonSerializer.Serialize(new { token }),
                Encoding.UTF8,
                "application/json"
            );
            
            var response = await _httpClient.SendAsync(request);
            
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<TokenValidationResponse>();
                return result?.IsValid ?? false;
            }
            
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating token with Jetty Approval");
            return false;
        }
    }

    public async Task<JettyApprovalUser> GetCurrentUserAsync()
    {
        try
        {
            var token = await _tokenService.GetAccessTokenAsync();
            
            var request = new HttpRequestMessage(HttpMethod.Get, "/api/token-validation/protected");
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);
            
            var response = await _httpClient.SendAsync(request);
            response.EnsureSuccessStatusCode();
            
            var userData = await response.Content.ReadFromJsonAsync<JettyApprovalUser>();
            return userData ?? new JettyApprovalUser();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting current user from Jetty Approval");
            throw;
        }
    }
}
```

### 3. Data Models

```csharp
public class JettyApprovalData
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    // Add other properties as needed
}

public class JettyApprovalUser
{
    public string UserId { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public List<ClaimInfo> Claims { get; set; } = new List<ClaimInfo>();
}

public class TokenValidationResponse
{
    public bool IsValid { get; set; }
    public string? UserId { get; set; }
    public string? UserName { get; set; }
    public string? Email { get; set; }
    public string? Sub { get; set; }
    public List<ClaimInfo> Claims { get; set; } = new List<ClaimInfo>();
}

public class ClaimInfo
{
    public string Type { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
}
```

## Controller Example

```csharp
[Route("api/jetty-approval")]
[ApiController]
public class JettyApprovalController : AbpController
{
    private readonly IJettyApprovalApiService _jettyApprovalService;
    private readonly ILogger<JettyApprovalController> _logger;

    public JettyApprovalController(
        IJettyApprovalApiService jettyApprovalService,
        ILogger<JettyApprovalController> logger)
    {
        _jettyApprovalService = jettyApprovalService;
        _logger = logger;
    }

    [HttpGet("data")]
    public async Task<ActionResult<JettyApprovalData>> GetData()
    {
        try
        {
            var data = await _jettyApprovalService.GetDataAsync();
            return Ok(data);
        }
        catch (UnauthorizedAccessException)
        {
            return Unauthorized();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting data from Jetty Approval");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("current-user")]
    public async Task<ActionResult<JettyApprovalUser>> GetCurrentUser()
    {
        try
        {
            var user = await _jettyApprovalService.GetCurrentUserAsync();
            return Ok(user);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting current user from Jetty Approval");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("validate-token")]
    public async Task<ActionResult<bool>> ValidateToken([FromBody] string token)
    {
        try
        {
            var isValid = await _jettyApprovalService.ValidateTokenAsync(token);
            return Ok(isValid);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating token");
            return StatusCode(500, "Internal server error");
        }
    }
}
```

## Usage Examples

### 1. From Another Controller

```csharp
[Route("api/my-app")]
[ApiController]
public class MyAppController : AbpController
{
    private readonly IJettyApprovalApiService _jettyApprovalService;

    public MyAppController(IJettyApprovalApiService jettyApprovalService)
    {
        _jettyApprovalService = jettyApprovalService;
    }

    [HttpGet("combined-data")]
    public async Task<ActionResult<object>> GetCombinedData()
    {
        // Get data from Jetty Approval app
        var jettyData = await _jettyApprovalService.GetDataAsync();
        
        // Get current user from Jetty Approval app
        var jettyUser = await _jettyApprovalService.GetCurrentUserAsync();
        
        // Combine with local data
        var result = new
        {
            LocalData = "Some local data",
            JettyApprovalData = jettyData,
            JettyApprovalUser = jettyUser
        };
        
        return Ok(result);
    }
}
```

### 2. From a Background Service

```csharp
public class JettyApprovalBackgroundService : BackgroundService
{
    private readonly IJettyApprovalApiService _jettyApprovalService;
    private readonly ILogger<JettyApprovalBackgroundService> _logger;

    public JettyApprovalBackgroundService(
        IJettyApprovalApiService jettyApprovalService,
        ILogger<JettyApprovalBackgroundService> logger)
    {
        _jettyApprovalService = jettyApprovalService;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                // Call Jetty Approval API periodically
                var data = await _jettyApprovalService.GetDataAsync();
                _logger.LogInformation("Retrieved data from Jetty Approval: {DataId}", data.Id);
                
                // Process the data...
                
                await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in background service");
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
            }
        }
    }
}
```

## Error Handling

### 1. Token Expiration

```csharp
public class JettyApprovalApiServiceWithRetry : IJettyApprovalApiService
{
    private readonly IJettyApprovalApiService _innerService;
    private readonly ITokenService _tokenService;
    private readonly ILogger<JettyApprovalApiServiceWithRetry> _logger;

    public async Task<JettyApprovalData> GetDataAsync()
    {
        try
        {
            return await _innerService.GetDataAsync();
        }
        catch (UnauthorizedAccessException)
        {
            _logger.LogInformation("Token expired, attempting refresh");
            
            // Try to refresh the token
            var refreshed = await _tokenService.RefreshTokenAsync();
            if (refreshed)
            {
                // Retry the request with the new token
                return await _innerService.GetDataAsync();
            }
            
            throw new UnauthorizedAccessException("Token refresh failed");
        }
    }
}
```

### 2. Circuit Breaker Pattern

```csharp
public class JettyApprovalApiServiceWithCircuitBreaker : IJettyApprovalApiService
{
    private readonly IJettyApprovalApiService _innerService;
    private readonly ILogger<JettyApprovalApiServiceWithCircuitBreaker> _logger;
    private readonly SemaphoreSlim _circuitBreaker = new SemaphoreSlim(1, 1);
    private DateTime _lastFailure = DateTime.MinValue;
    private int _failureCount = 0;
    private const int MaxFailures = 5;
    private const int CircuitBreakerTimeoutMinutes = 5;

    public async Task<JettyApprovalData> GetDataAsync()
    {
        if (IsCircuitBreakerOpen())
        {
            throw new InvalidOperationException("Circuit breaker is open");
        }

        try
        {
            var result = await _innerService.GetDataAsync();
            ResetCircuitBreaker();
            return result;
        }
        catch (Exception ex)
        {
            await IncrementFailureCount();
            throw;
        }
    }

    private bool IsCircuitBreakerOpen()
    {
        return _failureCount >= MaxFailures && 
               DateTime.UtcNow - _lastFailure < TimeSpan.FromMinutes(CircuitBreakerTimeoutMinutes);
    }

    private async Task IncrementFailureCount()
    {
        await _circuitBreaker.WaitAsync();
        try
        {
            _failureCount++;
            _lastFailure = DateTime.UtcNow;
        }
        finally
        {
            _circuitBreaker.Release();
        }
    }

    private void ResetCircuitBreaker()
    {
        _failureCount = 0;
        _lastFailure = DateTime.MinValue;
    }
}
```

## Testing

### 1. Unit Tests

```csharp
[TestClass]
public class JettyApprovalApiServiceTests
{
    private Mock<HttpClient> _mockHttpClient;
    private Mock<ITokenService> _mockTokenService;
    private JettyApprovalApiService _service;

    [TestInitialize]
    public void Setup()
    {
        _mockHttpClient = new Mock<HttpClient>();
        _mockTokenService = new Mock<ITokenService>();
        _service = new JettyApprovalApiService(_mockHttpClient.Object, _mockTokenService.Object, Mock.Of<ILogger<JettyApprovalApiService>>());
    }

    [TestMethod]
    public async Task GetDataAsync_ValidToken_ReturnsData()
    {
        // Arrange
        var token = "valid-token";
        var expectedData = new JettyApprovalData { Id = "123", Name = "Test" };
        
        _mockTokenService.Setup(x => x.GetAccessTokenAsync()).ReturnsAsync(token);
        // Setup HTTP client mock...

        // Act
        var result = await _service.GetDataAsync();

        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual("123", result.Id);
    }
}
```

### 2. Integration Tests

```csharp
[TestClass]
public class JettyApprovalIntegrationTests
{
    private TestServer _server;
    private HttpClient _client;

    [TestInitialize]
    public void Setup()
    {
        var builder = new WebHostBuilder()
            .UseStartup<Startup>();
        _server = new TestServer(builder);
        _client = _server.CreateClient();
    }

    [TestMethod]
    public async Task ValidateToken_ValidToken_ReturnsTrue()
    {
        // Arrange
        var token = "valid-jwt-token";
        var request = new { token };

        // Act
        var response = await _client.PostAsJsonAsync("/api/token-validation/validate", request);

        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadFromJsonAsync<TokenValidationResponse>();
        Assert.IsTrue(result?.IsValid);
    }
}
```

This comprehensive example shows how other ABP Framework apps can integrate with the Jetty Approval app using JWT tokens, including proper error handling, retry logic, and testing strategies. 