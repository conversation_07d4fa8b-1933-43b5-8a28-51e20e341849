import{b as d,j as a}from"./vendor-CrSBzUoz.js";import{az as r,M as x}from"./app-layout-CNB1Wtrx.js";const l=d.forwardRef(({className:s,children:e,...t},i)=>a.jsx("div",{ref:i,className:r("mx-auto my-6 flex w-full items-center justify-between gap-3 text-sm","text-gray-500 dark:text-gray-500",s),"tremor-id":"tremor-raw",...t,children:e?a.jsxs(a.Fragment,{children:[a.jsx("div",{className:r("h-[1px] w-full","bg-gray-200 dark:bg-gray-800")}),a.jsx("div",{className:"whitespace-nowrap text-inherit",children:e}),a.jsx("div",{className:r("h-[1px] w-full","bg-gray-200 dark:bg-gray-800")})]}):a.jsx("div",{className:r("h-[1px] w-full","bg-gray-200 dark:bg-gray-800")})}));l.displayName="Divider";function n({label:s,description:e,className:t,labelWidth:i="200px",children:m}){return a.jsxs("div",{className:x("grid items-start gap-4",t),style:{gridTemplateColumns:`${i} 1fr`},children:[a.jsxs("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:s}),e&&a.jsx("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:e})]}),a.jsx("div",{children:m})]})}function o({children:s,className:e}){return a.jsxs("div",{className:x("space-y-4",e),children:[s,a.jsx(l,{})]})}export{o as F,n as a};
//# sourceMappingURL=FormField-BFBouSal.js.map
