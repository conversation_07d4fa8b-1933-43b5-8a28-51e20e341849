using Imip.JettyApproval.Web.Services;
using Imip.JettyApproval.Web.Services.Dtos.ExternalVessel;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using System.Threading.Tasks;
using Volo.Abp.AspNetCore.Mvc;

namespace Imip.JettyApproval.Web.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ExternalVesselController : AbpController
{
    private readonly AppToAppService _appToAppService;
    private readonly IConfiguration _configuration;

    public ExternalVesselController(AppToAppService appToAppService, IConfiguration configuration)
    {
        _appToAppService = appToAppService;
        _configuration = configuration;
    }

    [HttpPost("search-vessels")]
    public async Task<IActionResult> SearchVessels([FromBody] VesselQueryRequestDto request)
    {
        var apiUrl = _configuration["ExternalApps:EkbdApi:BaseUrl"];
        var endpoint = "api/ekb/vessel/vessel-headers";
        var result = await _appToAppService.CallOtherAppAsync<object>(apiUrl, endpoint, request);
        return Ok(result);
    }
}