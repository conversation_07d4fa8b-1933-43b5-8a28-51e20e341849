using System;
using System.ComponentModel.DataAnnotations;

namespace Imip.JettyApproval.Approvals.ApprovalStages;

/// <summary>
/// DTO for creating and updating ApprovalStage entity
/// </summary>
public class CreateUpdateApprovalStageDto
{
    /// <summary>
    /// Foreign key to the approval template
    /// </summary>
    [Required]
    public Guid ApprovalTemplateId { get; set; }

    /// <summary>
    /// ID of the approver for this stage
    /// </summary>
    [Required]
    public Guid ApproverId { get; set; }

    /// <summary>
    /// Action date and time
    /// </summary>
    public DateTime? ActionDate { get; set; }

    /// <summary>
    /// Document ID for this stage
    /// </summary>
    [StringLength(100)]
    public string? DocumentId { get; set; }

    /// <summary>
    /// ID of the requester
    /// </summary>
    [StringLength(100)]
    public string? RequesterId { get; set; }

    /// <summary>
    /// Request date and time
    /// </summary>
    public DateTime? RequestDate { get; set; }

    /// <summary>
    /// Status of the approval stage
    /// </summary>
    [StringLength(50)]
    public string? Status { get; set; }

    /// <summary>
    /// Additional notes for the stage
    /// </summary>
    [StringLength(1000)]
    public string? Notes { get; set; }
}