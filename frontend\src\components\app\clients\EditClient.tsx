'use client'
import { type CreateUpdateOpenIddictApplicationDto, type OpenIddictApplicationDto, putApiOpeniddictApplicationsById } from '@/client'
import { Button } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { useToast } from '@/lib/useToast'
import { QueryNames } from '@/lib/hooks/QueryConstants'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { Toaster } from '@/components/ui/toaster'
import { RiRefreshLine } from '@remixicon/react'
import {
  Select,
  SelectContent,
  SelectItemExtended,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { MultiSelect, type MultiSelectOption } from '@/components/ui/multi-select'
import { useOpeniddictApplicationsPermissions, useOpeniddictRequirements } from '@/lib/hooks/useOpeniddictApplicationsPermissions'
import { FormField, FormSection } from '@/components/ui/FormField'
import { APPLICATION_TYPES } from '@/data/applicationType'
import { CLIENT_TYPES } from '@/data/clientType'
import { CONSENT_TYPES } from '@/data/consentType'
import { convertCommaSeparatedToArray, formatPermissionLabel, generateClientId } from '@/lib/utils/client'
import { handleApiError } from '@/lib/handleApiError'

export type EditClientProps = {
  dataEdit: OpenIddictApplicationDto
  dataId: string
  onDismiss: () => void
}

export const EditClient = ({ dataEdit, dataId, onDismiss }: EditClientProps) => {
  const [open, setOpen] = useState(true)
  const { toast } = useToast()
  const queryClient = useQueryClient()
  const { handleSubmit, register, setValue, reset } = useForm<CreateUpdateOpenIddictApplicationDto>()
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>(dataEdit.permissions ?? [])
  const [selectedRequirements, setSelectedRequirements] = useState<string[]>(dataEdit.requirements ?? [])
  // Fetch available permissions from the API
  const { data: permissionsData, isLoading: permissionsLoading } = useOpeniddictApplicationsPermissions()
  const { data: requirementsData, isLoading: requirementsLoading } = useOpeniddictRequirements()

  const resetForm = () => {
    reset({
      applicationType: '',
      clientType: '',
      clientId: '',
      clientSecret: '',
      displayName: '',
      consentType: '',
      redirectUris: [],
      postLogoutRedirectUris: [],
      permissions: [],
      requirements: []
    })
  }

  // Convert API permissions (string array) to MultiSelectOption format
  const permissionOptions: MultiSelectOption[] = permissionsData
    ? (Array.isArray(permissionsData)
      ? permissionsData.map(permission => ({
        value: permission,
        label: formatPermissionLabel(permission)
      }))
      : [])
    : []

  const requirementOptions: MultiSelectOption[] = requirementsData
    ? (Array.isArray(requirementsData)
      ? (requirementsData as Array<MultiSelectOption>).map(requirement => ({
        value: requirement.value,
        label: requirement.label
      }))
      : [])
    : []

  const createUserMutation = useMutation({
    mutationFn: async (dataMutation: CreateUpdateOpenIddictApplicationDto) =>
      putApiOpeniddictApplicationsById({
        path: { id: dataId },
        body: dataMutation
      }),
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Client Created Successfully',
        variant: 'success',
      })
      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetOpeniddictApplications] })
      resetForm()
      setOpen(false)
      onDismiss()
    },
    onError: (err: unknown) => {
      const error = handleApiError(err);
      toast({
        title: error.title,
        description: error.description,
        variant: 'error',
      })
    }
  })

  const onSubmit = (formData: CreateUpdateOpenIddictApplicationDto) => {
    // Merge form data with consent type and permissions
    const dataMutation: CreateUpdateOpenIddictApplicationDto = {
      ...formData,
      applicationType: formData.applicationType ?? dataEdit.applicationType ?? '', // Include applicationType
      clientType: formData.clientType ?? dataEdit.clientType ?? '', // Include clientType
      permissions: selectedPermissions,
      redirectUris: convertCommaSeparatedToArray(formData.redirectUris),
      postLogoutRedirectUris: convertCommaSeparatedToArray(formData.postLogoutRedirectUris),
      // clientSecret: null
    }
    createUserMutation.mutate(dataMutation)
  }

  const handleOpenChange = (newOpen: boolean) => {
    if (newOpen) {
      resetForm()
      onDismiss()
    }
    setOpen(newOpen)
  }

  return (
    <section>
      <Toaster />
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogContent style={{ maxWidth: "800px" }}>
          <DialogHeader>
            <DialogTitle>Edit Client</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSubmit(onSubmit)} className='mt-2'>
            <section className="flex w-full flex-col space-y-2">
              <FormSection>
                <FormField
                  label='Application Type'
                  description='The type of the application'
                >
                  <Select
                    defaultValue={dataEdit.applicationType ?? ''}
                    onValueChange={(value) => setValue('applicationType', value)}
                  >
                    <SelectTrigger
                      className="w-full"
                      clearable={true}
                      onClear={() => setValue('applicationType', '')}
                    >
                      <SelectValue placeholder="Select application type" />
                    </SelectTrigger>
                    <SelectContent>
                      {APPLICATION_TYPES.map((type) => (
                        <SelectItemExtended
                          key={type.value}
                          value={type.value}
                          option={type.option}
                          description={type.description}
                          icon={type.icon}
                          clearable={true}
                        />
                      ))}
                    </SelectContent>
                  </Select>
                </FormField>
                <FormField
                  label='Client Type'
                  description='The type of the client'
                >
                  <Select
                    defaultValue={dataEdit.clientType ?? ''}
                    onValueChange={(value) => setValue('clientType', value)}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select client type" />
                    </SelectTrigger>
                    <SelectContent>
                      {CLIENT_TYPES.map((type) => (
                        <SelectItemExtended
                          key={type.value}
                          value={type.value}
                          option={type.option}
                          description={type.description}
                          icon={type.icon}
                        />
                      ))}
                    </SelectContent>
                  </Select>
                </FormField>
                <FormField
                  label="Client Id"
                  description="The client id of the client"
                >
                  <div className="flex items-center gap-2">
                    <Input required {...register('clientId')} defaultValue={dataEdit.clientId ?? ''} placeholder="Client Id" />
                    <Button
                      type="button"
                      variant="secondary"
                      onClick={generateClientId}
                      title="Generate Client ID"
                    >
                      <RiRefreshLine className="size-4" />
                    </Button>
                  </div>
                </FormField>

                {/* <FormField
                  label="Client Secret"
                  description="The client secret of the client"
                >
                  <div className="flex items-center gap-2">
                    <Input required {...register('clientSecret')} defaultValue={dataEdit.clientSecret ?? ''} placeholder="Client Secret" />
                    <Button
                      type="button"
                      variant="secondary"
                      onClick={generateClientSecret}
                      title="Generate Client Secret"
                    >
                      <RiRefreshLine className="size-4" />
                    </Button>
                  </div>
                </FormField> */}

                <FormField
                  label="Display name"
                  description="The display name of the application"
                >
                  <Input placeholder="Display name" {...register('displayName')} defaultValue={dataEdit.displayName ?? ''} />
                </FormField>
                <FormField
                  label="Consent type"
                  description="The consent type of the client"
                >
                  <Select {...register('consentType')} defaultValue={dataEdit.consentType ?? ''}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select consent type" />
                    </SelectTrigger>
                    <SelectContent>
                      {CONSENT_TYPES.map((type) => (
                        <SelectItemExtended
                          key={type.value}
                          value={type.value}
                          option={type.option}
                          description={type.description}
                          icon={type.icon}
                        />
                      ))}
                    </SelectContent>
                  </Select>
                </FormField>

                <FormField
                  label="Client Uri"
                  description="The client uri of the client"
                >
                  <Input placeholder="Client Uri" {...register('clientUri')} defaultValue={dataEdit.clientUri ?? ''} />
                </FormField>

                <FormField
                  label="Redirect Uris"
                  description="The redirect uris of the client"
                >
                  <Input placeholder="Redirect Uris" {...register('redirectUris')} defaultValue={dataEdit.redirectUris ?? ''} />
                </FormField>
                <FormField
                  label="Post logout redirect Uris"
                  description="The post logout redirect uris of the client"
                >
                  <Input placeholder="Post logout redirect Uris" {...register('postLogoutRedirectUris')} defaultValue={dataEdit.postLogoutRedirectUris ?? ''} />
                </FormField>

                <FormField
                  label="Permissions"
                  description="The permissions of the client"
                >
                  <MultiSelect
                    options={permissionOptions}
                    value={selectedPermissions}
                    onChange={(values) => {
                      setSelectedPermissions(values);
                      setValue('permissions', values);
                    }}
                    placeholder={permissionsLoading ? "Loading permissions..." : "Select permissions"}
                    disabled={permissionsLoading}
                    maxHeight={300}
                  />
                </FormField>

                <FormField
                  label="Requirements"
                  description="The requirements of the client"
                >
                  <MultiSelect
                    mode='single'
                    options={requirementOptions}
                    value={selectedRequirements}
                    onChange={(values) => {
                      setSelectedRequirements(values);
                      setValue('requirements', values);
                    }}
                    placeholder={requirementsLoading ? "Loading requirements..." : "Select requirements"}
                    disabled={requirementsLoading}
                    maxHeight={300}
                  />
                </FormField>
              </FormSection>
            </section>
            <DialogFooter className="mt-5">
              <Button
                variant="ghost"
                onClick={(e) => {
                  e.preventDefault()
                  setOpen(false)
                }}
                disabled={createUserMutation.isPending}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={createUserMutation.isPending}>
                {createUserMutation.isPending ? 'Saving...' : 'Save'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </section>
  )
}


