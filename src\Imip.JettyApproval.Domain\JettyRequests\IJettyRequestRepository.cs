using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace Imip.JettyApproval.JettyRequests;

/// <summary>
/// Repository interface for JettyRequest entity
/// </summary>
public interface IJettyRequestRepository : IRepository<JettyRequest, Guid>
{
    /// <summary>
    /// Gets jetty requests by vessel name
    /// </summary>
    Task<List<JettyRequest>> GetByVesselNameAsync(string vesselName);

    /// <summary>
    /// Gets jetty requests by jetty location
    /// </summary>
    Task<List<JettyRequest>> GetByJettyAsync(string jetty);

    /// <summary>
    /// Gets jetty requests by date range
    /// </summary>
    Task<List<JettyRequest>> GetByDateRangeAsync(DateTime startDate, DateTime endDate);

    /// <summary>
    /// Gets jetty requests with items included
    /// </summary>
    Task<JettyRequest?> GetWithItemsAsync(Guid id);

    /// <summary>
    /// Gets all jetty requests with items included
    /// </summary>
    Task<List<JettyRequest>> GetListWithItemsAsync();
}